#!/bin/bash
set -e

echo $BASH_VERSION

ROOT="$(cd $(dirname -- $0) && pwd)"
module=""
module_camel_case=""
module_pascal_case=""
module_full_name=""
entity=""
entity_camel_case=""
entity_pascal_case=""
# This script is used to generate the code for a new entity.(like Employee, RiskArea, etc)

# The script will create the following files:
## api
# src/api/__MODULE_CAMEL__/__ENTITY_CAMEL__.ts
# src/api/__MODULE_CAMEL__/index.ts
## atom
# src/atoms/__MODULE_CAMEL__/__ENTITY_CAMEL__.tsx
# src/atoms/__MODULE_CAMEL__/index.ts
## page
# src/pages/__MODULE_CAMEL__/content/__ENTITY_CAMEL__/{index.tsx, content.tsx, filter.tsx}
# src/pages/__MODULE_CAMEL__/content/index.tsx
# src/pages/__MODULE_CAMEL__/__ENTITY_CAMEL__Page.tsx
# src/pages/__MODULE_CAMEL__/index.tsx
## modal
# src/pages/__MODULE_CAMEL__/modal/__ENTITY_CAMEL__Modal.tsx
# src/pages/__MODULE_CAMEL__/modal/index.tsx

# the files should be edited manually to add the required code
# src/App.tsx
# atoms in src/atoms/__MODULE_CAMEL__/__ENTITY_CAMEL__.tsx
# {name, useEffect, handleOK, columns} in src/pages/__MODULE_CAMEL__/modal/__ENTITY_CAMEL__Modal.tsx
# {columns} in src/pages/__MODULE_CAMEL__/content/__ENTITY_CAMEL__/filter.tsx

CURFILE=$(basename -- $0)
_ERR_HDR_FMT="%.23s"
_ERR_MSG_FMT="[${_ERR_HDR_FMT}][${CURFILE}:%s][ %b ]: %b\n"
log_msg() {
  local lineno=$1
  local tp=$2
  local msg="$3"
  printf "$_ERR_MSG_FMT" $(date +%F.%T.%N) ${lineno} "${tp}" "${msg}" 1>&2
}
log_info() {
  local msg="$1"
  log_msg ${BASH_LINENO[0]} "\033[32mINFO\033[0m" "$msg"
}
log_warning() {
  local msg="$1"
  log_msg ${BASH_LINENO[0]} "\033[33mWARN\033[0m" "$msg"
}
log_error() {
  local msg="$1"
  log_msg ${BASH_LINENO[0]} "\033[31mERROR\033[0m" "$msg"
}

# 转换蛇形命名为驼峰命名
snake_to_camel() {
  local input="$1"
  local result=""
  local capitalize_next_word=false

  for ((i = 0; i < ${#input}; i++)); do
    char="${input:$i:1}"
    if [[ "$char" == "_" ]]; then
      capitalize_next_word=true
    else
      if [ "$capitalize_next_word" = true ]; then
        result+=$(tr '[:lower:]' '[:upper:]' <<<"$char")
        capitalize_next_word=false
      else
        result+="$char"
      fi
    fi
  done

  echo "$result"
}

# 转换蛇形命名为大驼峰命名
snake_to_pascal() {
  local input="$1"
  local camel_case=$(snake_to_camel "$input")
  echo "${camel_case^}"
  # echo "$(tr '[:upper:]' '[:lower:]' <<<"${camel_case:0:1}")${camel_case:1}"
}

# 示例
# snake_case="hello_world_example"
# camel_case_result=$(snake_to_camel "$snake_case")
# pascal_case_result=$(snake_to_pascal "$snake_case")

pre_process() {
  ## api
  # src/api/__MODULE_CAMEL__/__ENTITY_CAMEL__.ts
  api_template_file="${ROOT}/templates/entity/api.ts"
  api_file="${ROOT}/../src/api/${module_camel_case}/${entity_camel_case}.ts"
  # src/api/__MODULE_CAMEL__/index.ts
  api_index_file="${ROOT}/../src/api/${module_camel_case}/index.ts"

  ## atom
  # src/atoms/__MODULE_CAMEL__/__ENTITY_CAMEL__.tsx
  atom_template_file="${ROOT}/templates/entity/atom.tsx"
  atom_file="${ROOT}/../src/atoms/${module_camel_case}/${entity_camel_case}.tsx"
  # src/atoms/__MODULE_CAMEL__/index.ts
  atom_index_file="${ROOT}/../src/atoms/${module_camel_case}/index.ts"

  ## page content
  # src/pages/__MODULE_CAMEL__/content/__ENTITY_CAMEL__/{index.tsx, content.tsx, filter.tsx}
  page_content_template_file="${ROOT}/templates/entity/content.tsx"
  page_content_file="${ROOT}/../src/pages/${module_camel_case}/content/${entity_camel_case}/content.tsx"
  page_content_filter_template_file="${ROOT}/templates/entity/filter.tsx"
  page_content_filter_file="${ROOT}/../src/pages/${module_camel_case}/content/${entity_camel_case}/filter.tsx"
  page_content_index_file="${ROOT}/../src/pages/${module_camel_case}/content/${entity_camel_case}/index.tsx"
  # src/pages/__MODULE_CAMEL__/content/index.tsx
  page_content_home_index_file="${ROOT}/../src/pages/${module_camel_case}/content/index.tsx"
  ## page home
  # src/pages/__MODULE_CAMEL__/__ENTITY_CAMEL__Page.tsx
  page_template_file="${ROOT}/templates/entity/page.tsx"
  page_file="${ROOT}/../src/pages/${module_camel_case}/${entity_camel_case}Page.tsx"
  # src/pages/__MODULE_CAMEL__/index.tsx
  page_index_file="${ROOT}/../src/pages/${module_camel_case}/index.tsx"

  ## modal
  # src/pages/__MODULE_CAMEL__/modal/__ENTITY_CAMEL__Modal.tsx
  modal_template_file="${ROOT}/templates/entity/modal.tsx"
  modal_file="${ROOT}/../src/pages/${module_camel_case}/modal/${entity_camel_case}Modal.tsx"
  # src/pages/__MODULE_CAMEL__/modal/index.tsx
  modal_index_file="${ROOT}/../src/pages/${module_camel_case}/modal/index.tsx"

}
process_common_index() {
  log_info "Processing Common Index"
  output_file=$1
  export_string="${2:-$entity_camel_case}"

  add_export="export * from './${export_string}'"
  escaped_string=$(printf "%s\n" "$add_export" | sed 's/[[\.*^$/]/\\&/g')

  if ! grep -q "$escaped_string" "$output_file"; then
    # 如果字符串不存在，则追加到文件末尾
    echo "$add_export" >>"$output_file"
    log_info "String '$add_export' appended to '$output_file'."
  else
    log_error "String '$add_export' already exists in '$output_file'."
  fi

  log_info "Common Index File Replacement completed. New file: $output_file"
}

process_common() {
  log_info "Processing Common $1 $2"
  input_file=$1
  output_file=$2

  dir=$(dirname "$output_file")
  if [ ! -d "$dir" ]; then
    mkdir -p "$dir"
  fi

  sed "s/__MODULE_FULL_NAME__/$module_full_name/g" "$input_file" >"$output_file"

  sed -e "s/__MODULE__/$module/g" "$output_file" >"$output_file.tmp" && mv "$output_file.tmp" "$output_file"

  sed -e "s/__MODULE_CAMEL__/$module_camel_case/g" "$output_file" >"$output_file.tmp" && mv "$output_file.tmp" "$output_file"

  sed -e "s/__MODULE_PASCAL__/$module_pascal_case/g" "$output_file" >"$output_file.tmp" && mv "$output_file.tmp" "$output_file"

  sed -e "s/__ENTITY__/$entity/g" "$output_file" >"$output_file.tmp" && mv "$output_file.tmp" "$output_file"

  sed -e "s/__ENTITY_CAMEL__/$entity_camel_case/g" "$output_file" >"$output_file.tmp" && mv "$output_file.tmp" "$output_file"

  sed -e "s/__ENTITY_PASCAL__/$entity_pascal_case/g" "$output_file" >"$output_file.tmp" && mv "$output_file.tmp" "$output_file"

  log_info "Common File Replacement completed. New file: $output_file"
}
process_api() {
  log_info "Processing API"

  input_file=$api_template_file
  output_file=$api_file
  process_common "$input_file" "$output_file"
  log_info "API File Replacement completed. New file: $output_file"

  process_common_index "$api_index_file"
  log_info "API Index File Replacement completed. File: $api_index_file"

  log_info "API Processing completed."
}
process_atom() {
  log_info "Processing Atom"

  input_file=$atom_template_file
  output_file=$atom_file
  process_common "$input_file" "$output_file"
  log_info "Atom File Replacement completed. New file: $output_file"

  process_common_index "$atom_index_file"
  log_info "Atom Index File Replacement completed. File: $atom_index_file"

  log_info "Atom Processing completed."
}
process_modal() {
  log_info "Processing Modal"

  input_file=$modal_template_file
  output_file=$modal_file
  process_common "$input_file" "$output_file"
  log_info "Modal File Replacement completed. New file: $modal_file"

  process_common_index "$modal_index_file" "${entity_camel_case}Modal"
  log_info "Modal Index File Replacement completed. File: $modal_index_file"

  log_info "Modal Processing completed."
}
process_page_home() {
  log_info "Processing Page Home"

  input_file=$page_template_file
  output_file=$page_file
  process_common "$input_file" "$output_file"
  log_info "Page Home File Replacement completed. New file: $page_file"

  process_common_index "$page_index_file" "${entity_camel_case}Page"
  log_info "Page Index File Replacement completed. File: $page_index_file"
  log_info "Page Home Processing completed."
}
process_page_content() {
  log_info "Processing Page Content"

  process_common "$page_content_template_file" "$page_content_file"
  process_common "$page_content_filter_template_file" "$page_content_filter_file"

  echo "export * from './content';
export * from './filter';" >>"$page_content_index_file"
  log_info "Page Content Index File Replacement completed. File: $page_content_index_file"
  process_common_index "$page_content_home_index_file"
  log_info "Page Content Home Index File Replacement completed. File: $page_content_home_index_file"

  log_info "Page Content Processing completed."
}
process_page() {
  log_info "Processing Page"

  process_page_home

  process_page_content

  log_info "Page Processing completed."
}

usage() {
  echo "Usage: $0 -m <module> -e <entity> [-h]"
  echo "  -m: module name"
  echo "  -f: module full name"
  echo "  -e: entity name"
  echo "  -h: help"
  exit 1
}

main() {
  major_version=${BASH_VERSION:0:1}

  # 检查主版本号是否小于 5
  if ((major_version < 5)); then
    echo "Bash version is less than 5. Exiting."
    exit 1
  else
    echo "Bash version is 5 or higher. Continuing..."
  fi

  while getopts ":m:f:e:h:" opt; do
    case "$opt" in
    m)
      module="${OPTARG}"
      ;;
    f)
      module_full_name="${OPTARG}"
      ;;
    e)
      entity="${OPTARG}"
      ;;
    h)
      usage
      ;;
    *)
      usage
      ;;
    esac
  done

  if [[ -z "$module_full_name" ]]; then
    module_full_name="$module"
  fi

  if [[ -z "$module" || -z "$module_full_name" || -z "$entity" ]]; then
    log_error "module, module_full_name and entity are required"
    usage
    exit 1
  fi

  module_camel_case=$(snake_to_camel "$module")
  module_pascal_case=$(snake_to_pascal "$module")
  entity_camel_case=$(snake_to_camel "$entity")
  entity_pascal_case=$(snake_to_pascal "$entity")

  echo -e "\033[32m ----------------------\033[0m"
  echo -e "\033[32m module:               \033[0m" "$module"
  echo -e "\033[32m module_camel_case:    \033[0m" "$module_camel_case"
  echo -e "\033[32m module_camel_case:    \033[0m" "$module_pascal_case"
  echo -e "\033[32m module_full_name:      \033[0m" "$module_full_name"
  echo -e "\033[32m entity:               \033[0m" "$entity"
  echo -e "\033[32m enitity_camel_case:   \033[0m" "$entity_camel_case"
  echo -e "\033[32m enitity_pascal_case:   \033[0m" "$entity_pascal_case"
  echo -e "\033[32m ----------------------\033[0m"
  echo

  log_info "Entity Code Generation started."

  pre_process
  process_api
  process_atom
  process_modal
  process_page

  log_info "Entity Code Generation completed."
}

# ===== main =====
if [[ "${BASH_SOURCE[0]}" == "$0" ]]; then
  main $*
fi
