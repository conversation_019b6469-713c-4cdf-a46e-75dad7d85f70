#!/bin/bash

# -----------------------------------------------------------------------------
# 前端目前必须在编译时指定API URI，经过测试后，发现NODE_ENV不起作用，还是用.env.local文件
# 在编译前，根据目标环境，修改.env.local文件中的 API_URI
# -----------------------------------------------------------------------------

# CHOGORI_DEPLOYDIR=/home/<USER>/Applications/docker-basics/data/www/test.vren-tech.com

ROOT="$(cd $(dirname -- $0) && pwd)"

SKIP_GIT="false"
ENV=""

CURFILE=$(basename -- $0)
_ERR_HDR_FMT="%.23s"
_ERR_MSG_FMT="[${_ERR_HDR_FMT}][${CURFILE}:%s][ %b ]: %b\n"
log_msg() {
  local lineno=$1
  local tp=$2
  local msg="$3"
  printf "$_ERR_MSG_FMT" $(date +%F.%T.%N) ${lineno} "${tp}" "${msg}" 1>&2
}
log_info() {
  local msg="$1"
  log_msg ${BASH_LINENO[0]} "\033[32mINFO\033[0m" "$msg"
}
log_warning() {
  local msg="$1"
  log_msg ${BASH_LINENO[0]} "\033[33mWARN\033[0m" "$msg"
}
log_error() {
  local msg="$1"
  log_msg ${BASH_LINENO[0]} "\033[31mERROR\033[0m" "$msg"
}

usage() {
  echo "$0 [OPTIONS]
            -p prefix path to install, e.g. /opt/vren
            -s skip backup or not, false without it, true otherwise.
         "
  exit 1
}

# fallocate -c -o 0 -l 10K /home/<USER>/bin/update_chogori.log
# sed -i 1,135d $LOG_FILE
# sed -i 1,$( ( $(wc -l < $LOG_FILE) -10000 ) )d $LOG_FILE

process_skip_git_var() {
  # SKIP_GIT=$(echo "${SKIP_GIT}" | tr '[:upper:]' '[:lower:]')
  if [ "${SKIP_GIT}" == "t" ] || [ "${SKIP_GIT}" == "true" ]; then
    SKIP_GIT="true"
  else
    SKIP_GIT="false"
  fi
}

prepare_chogori() {
  log_info "prepare"

  export NVM_DIR="$HOME/.nvm"
  [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"                   # This loads nvm
  [ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion" # This loads nvm bash_completion

  export PATH=/usr/local/go/bin:~/go/bin:$PATH
  export GO111MODULE=on
  export GOPROXY=https://goproxy.cn,direct
}

update_chogori() {
  cd ${ROOT}/.. || exit 1

  log_info "update code"
  git restore .
  git pull --rebase

  cd - || exit 1
}

make_chogori() {
  cd ${ROOT}/.. || exit 1

  log_info "build"
  #${YARN} && ${YARN} build
  yarn && yarn build

  cd - || exit 1
}

install_chogori() {
  cd ${ROOT}/.. || exit 1
  mkdir -p ${CHOGORI_DEPLOYDIR}

  log_info "deploy"
  cp -rf dist/* $CHOGORI_DEPLOYDIR

  cd - || exit 1
}

main() {
  while getopts ":p:s:e:h:" opt; do
    case "$opt" in
    p)
      prefix=${OPTARG%/}
      CHOGORI_DEPLOYDIR=${prefix}
      ;;
    s)
      SKIP_GIT="${OPTARG}"
      ;;
    e)
      ENV="${OPTARG}"
      ;;
    h)
      usage
      ;;
    *)
      usage
      ;;
    esac
  done

  echo -e "\033[32m ----------------------\033[0m"
  echo -e "\033[32m prefix:               \033[0m" "$prefix"
  echo -e "\033[32m skip git:             \033[0m" "$SKIP_GIT"
  echo -e "\033[32m env:                  \033[0m" "$ENV"
  echo -e "\033[32m ----------------------\033[0m"
  echo

  if [ -z ${CHOGORI_DEPLOYDIR+x} ]; then
    log_error "need -p prefix"
    usage # no prefix
  fi

  process_skip_git_var

  prepare_chogori
  if [ "${SKIP_GIT}" == "false" ]; then
    log_info "don't skip to update chogori code"
    update_chogori
  else
    log_info "skip to update chogori code"
  fi

  export NODE_ENV=${ENV}
  make_chogori
  install_chogori

}

# ===== main =====
if [[ "${BASH_SOURCE[0]}" == "$0" ]]; then
  main $*
fi
