/* import { <PERSON><PERSON><PERSON>er, RoleContent, RoleSide } from './content'
import { RoleModal } from './modal'
 */

import {
  __MODULE_PASCAL____ENTITY_PASCAL__Content,
  __MODULE_PASCAL____ENTITY_PASCAL__Filter,
} from "./content";
import { __MODULE_PASCAL____ENTITY_PASCAL__Modal } from "./modal/__ENTITY_CAMEL__Modal";

export function __MODULE_PASCAL____ENTITY_PASCAL__Page({
  readonly = false,
  filter = {},
  ...restProps
}) {
  return (
    <div className="flex flex-col gap-4 ">
      {/* <RoleSide />
      <RoleModal />
      <RoleFilter />
      <RoleContent /> */}
      <__MODULE_PASCAL____ENTITY_PASCAL__Filter filter={filter} />
      <__MODULE_PASCAL____ENTITY_PASCAL__Modal />
      <__MODULE_PASCAL____ENTITY_PASCAL__Content
        readonly={readonly}
        filter={filter}
        {...restProps}
      />
    </div>
  );
}
