import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { CommonAtoms } from "types";

export const __MODULE_CAMEL____ENTITY_PASCAL__FilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const __MODULE_CAMEL____ENTITY_PASCAL__FnAtom = atom({
  refetch: () => {},
});

export const __MODULE_CAMEL____ENTITY_PASCAL__EditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const __MODULE_CAMEL____ENTITY_PASCAL__ConfigModalAtom = atom(false);

const __MODULE_CAMEL____ENTITY_PASCAL__ShowColumns = [
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 48,
  },
  // user-defined code here
];

const __MODULE_CAMEL____ENTITY_PASCAL__ExtendColumns = [
  // user-defined code here
];

export const __MODULE_CAMEL____ENTITY_PASCAL__ShowColumnsAtom = atom(
  __MODULE_CAMEL____ENTITY_PASCAL__ShowColumns
);

export const __MODULE_CAMEL____ENTITY_PASCAL__ColumnsAtom = atom([
  ...__MODULE_CAMEL____ENTITY_PASCAL__ShowColumns,
  ...__MODULE_CAMEL____ENTITY_PASCAL__ExtendColumns,
]);

/*export const __MODULE_CAMEL____ENTITY_PASCAL__ColumnsAtom = atom(
  (get) => get(__MODULE_CAMEL____ENTITY_PASCAL__ShowColumnsAtom).concat(get(__MODULE_CAMEL____ENTITY_PASCAL__ExtendColumnsAtom)),
  (get, set, newValue: any) => {
    set(__MODULE_CAMEL____ENTITY_PASCAL__ShowColumnsAtom, newValue.slice(0, newValue.length / 2)); // 更新 ShowColumnsAtom
    set(__MODULE_CAMEL____ENTITY_PASCAL__ExtendColumnsAtom, newValue.slice(newValue.length / 2)); // 更新 ExtendColumnsAtom
  }
)*/

export const __MODULE_CAMEL____ENTITY_PASCAL__Atoms: CommonAtoms = {
  entity: "__MODULE_PASCAL____ENTITY_PASCAL__",
  filter: __MODULE_CAMEL____ENTITY_PASCAL__FilterAtom,
  Fn: __MODULE_CAMEL____ENTITY_PASCAL__FnAtom,
  editModal: __MODULE_CAMEL____ENTITY_PASCAL__EditModalAtom,
  configModal: __MODULE_CAMEL____ENTITY_PASCAL__ConfigModalAtom,
  columns: __MODULE_CAMEL____ENTITY_PASCAL__ColumnsAtom,
};
