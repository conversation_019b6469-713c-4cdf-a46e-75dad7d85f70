import { useQueryClient } from "@tanstack/react-query";
import { __MODULE_CAMEL____ENTITY_PASCAL__Apis } from "api";
import { __MODULE_CAMEL____ENTITY_PASCAL__Atoms } from "atoms";
import { ExportProvider, List } from "components";
import React, { FC } from "react";

type __MODULE_PASCAL____ENTITY_PASCAL__ContentProps = {
  callback?: any;
  layout?: string;
  referAtom?: any;
  readonly?: boolean;
  filter?: any;
};
export const __MODULE_PASCAL____ENTITY_PASCAL__Content: FC<
  __MODULE_PASCAL____ENTITY_PASCAL__ContentProps
> = ({
  callback,
  layout,
  referAtom,
  readonly = false,
  filter = {},
  ...restProps
}) => {
  if (layout === "modal") {
    readonly = true;
  }
  const queryClient = useQueryClient();
  const queryKey = "list" + __MODULE_CAMEL____ENTITY_PASCAL__Atoms.entity;

  return (
    <ExportProvider>
      <List
        atoms={__MODULE_CAMEL____ENTITY_PASCAL__Atoms}
        apis={__MODULE_CAMEL____ENTITY_PASCAL__Apis}
        // operations, dynamicOperationFuncs
        filter={filter}
        callback={callback}
        referAtom={referAtom}
        layout={layout}
        readonly={readonly}
        {...restProps}
      />
    </ExportProvider>
  );
};
