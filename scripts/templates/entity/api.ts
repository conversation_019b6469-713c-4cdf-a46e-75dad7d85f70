import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/__MODULE_FULL_NAME__/__ENTITY__/search

export const get__MODULE_PASCAL____ENTITY_PASCAL__List = async (params) => {
  const base_url = "/__MODULE_FULL_NAME__/__ENTITY__/search";
  const res = await post(base_url, params);
  return res;
};

export const get__MODULE_PASCAL____ENTITY_PASCAL__ = async (id) => {
  const base_url = `/__MODULE_FULL_NAME__/__ENTITY__/${id}`;
  return await get(base_url);
};

export const create__MODULE_PASCAL____ENTITY_PASCAL__ = async (params) => {
  const res = await post("/__MODULE_FULL_NAME__/__ENTITY__", params);
  return res;
};

export const del__MODULE_PASCAL____ENTITY_PASCAL__ = async (id: number) => {
  const res = await del(`/__MODULE_FULL_NAME__/__ENTITY__/${id}`);
  return res;
};

export const del__MODULE_PASCAL____ENTITY_PASCAL__s = async (ids) => {
  const res = await del(`/__MODULE_FULL_NAME__/__ENTITY__`, ids);
  return res;
};

export const update__MODULE_PASCAL____ENTITY_PASCAL__ = async (params) => {
  const res = await put(
    `/__MODULE_FULL_NAME__/__ENTITY__/${params.id}`,
    params?.values,
  );
  return res;
};

export const __MODULE_CAMEL____ENTITY_PASCAL__Apis: CommonApis = {
  entity: "__MODULE_PASCAL____ENTITY_PASCAL__",
  query: get__MODULE_PASCAL____ENTITY_PASCAL__List,
  create: create__MODULE_PASCAL____ENTITY_PASCAL__,
  remove: del__MODULE_PASCAL____ENTITY_PASCAL__,
  removes: del__MODULE_PASCAL____ENTITY_PASCAL__s,
  update: update__MODULE_PASCAL____ENTITY_PASCAL__,
  get: get__MODULE_PASCAL____ENTITY_PASCAL__,
};
