for bleu
动态菜单的api如下，你也可以在本目录的api-xx文件的第一个找到
1. https://apifox.com/apidoc/shared/5b90dd5c-07c8-4d4d-b014-9e963aff5f86/api-281362109
2. https://apifox.com/apidoc/shared/5b90dd5c-07c8-4d4d-b014-9e963aff5f86/api-292183594
3. https://apifox.com/apidoc/shared/5b90dd5c-07c8-4d4d-b014-9e963aff5f86/api-292410844
目前的效果是在右侧弄了个简单的页内链接，希望做到的是通过app.tsx里面的模式，在左侧生成同样的菜单


1. 在设计过程中，请参考整个项目的代码，尽量复用已有的代码，或者参考已有的组件以及类似实现。
1. 参考package.json文件中的依赖库，尽量使用已有的库。
1. 文件的修改，不要直接覆盖之前的内容
1. 涉及到后端api的，请于目录src/api下面创建文件或者利用已有文件独立维护。请注意，这个目录下每个模块一个目录
1. 我会给你提供相应的api文档，其中的url在引入的时候，不需要v1前缀，因为已经在src/api/request.js中统一封装添加了
1. 涉及到可视化方面的渲染，请单独一个函数，便于维护
1. 如果需要，可视化渲染用ECharts
1. 如果有图片提供，请符合图中样式
1. 生成的代码，不要有行尾空格
