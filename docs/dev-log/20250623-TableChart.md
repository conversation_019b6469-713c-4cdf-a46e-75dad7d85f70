# TableChart 组件多 Tab 功能增强开发日志

> 相关源码文件与文档引用：
>
> - TableChart 组件：[src/components/chart/TableChart.tsx](../../src/components/chart/TableChart.tsx)
> - TableChart 文档：[src/components/chart/TableChart.md](../../src/components/chart/TableChart.md)
> - 区域统计表格：[src/pages/alarm/components/AreaStatsTable.tsx](../../src/pages/alarm/components/AreaStatsTable.tsx)

---

## 一、需求与目标

本次开发目标是为 `TableChart` 通用统计表格组件增加多 Tab 功能，使其能够在同一个组件中支持不同 API 调用和不同列配置的切换展示。这一功能将极大提升组件的灵活性和复用性，减少重复代码，并为用户提供更便捷的数据查看体验。

---

## 二、功能设计与组件拆分

- 在 `TableChart` 组件中增加 Tab 切换功能，每个 Tab 可以调用不同的 API 并展示不同的列配置
- 设计 `TableChartTab` 接口，包含每个 Tab 的配置信息
- 保持原有单 API 调用方式的兼容性，同时支持新的多 Tab 模式
- 参考 `TrendChart` 和 `PieChart` 组件的 Tab 实现方式，保持风格一致

---

## 三、核心实现细节

### 1. TableChartTab 接口设计

```typescript
export interface TableChartTab {
  label: string; // Tab 显示名称
  value: any; // Tab 值，用于标识当前选中的 Tab
  queryKey: any[]; // 该 Tab 的 react-query 缓存 key
  queryFn: (params: any) => Promise<any>; // 该 Tab 的数据请求函数
  columns: TableChartColumn[]; // 该 Tab 的表头配置
  optionBuilder?: (data: any) => object; // 可选，该 Tab 的 ECharts option 生成函数
}
```

### 2. TableChartProps 扩展

```typescript
export interface TableChartProps {
  // 原有属性...
  tabList?: TableChartTab[]; // 多 API 时使用，每个 tab 对应不同 API 和 columns
  tabParamName?: string; // 默认为 'type'
}
```

### 3. 状态管理与数据流

- 使用 `useState` 管理当前选中的 Tab
- 根据当前 Tab 动态选择对应的 `queryKey`、`queryFn`、`columns` 和 `optionBuilder`
- 将当前 Tab 值作为参数传递给 API 请求函数

---

## 四、UI 实现与交互优化

- Tab 切换区域位于标题右侧，与 `TrendChart` 和 `PieChart` 保持一致的样式
- 当前选中的 Tab 显示为蓝底白字，未选中的 Tab 显示为白底蓝字
- 切换 Tab 时，表格内容和可能的图表都会随之更新

---

## 五、应用示例：区域统计表格升级

将原有的 `AreaStatsTable` 组件升级为支持 Tab 切换的组件，包含两个 Tab：

1. **区域统计** - 显示各区域的报警数量，调用 `getAreaStat` API
2. **设备统计** - 显示各设备的报警数量，调用 `getDeviceStat` API

```tsx
export default function AreaStatsTable({ filter }: AreaStatsTableProps) {
  const tabList = [
    {
      label: "区域统计",
      value: "area",
      queryKey: ["getAreaStat"],
      queryFn: getAreaStat,
      columns: [
        {
          title: "区域名称",
          dataIndex: "area.name",
          align: "left" as const,
        },
        {
          title: "报警数量",
          dataIndex: "num",
          align: "center" as const,
          render: (value: number) => (
            <span className="font-bold text-blue-600">{value}</span>
          ),
        },
      ],
    },
    {
      label: "设备统计",
      value: "device",
      queryKey: ["getDeviceStat"],
      queryFn: getDeviceStat,
      columns: [
        {
          title: "设备名称",
          dataIndex: "equipment.name",
          align: "left" as const,
        },
        {
          title: "报警数量",
          dataIndex: "num",
          align: "center" as const,
          render: (value: number) => (
            <span className="font-bold text-red-600">{value}</span>
          ),
        },
      ],
    },
  ];

  return (
    <TableChart
      title="报警统计"
      filter={filter}
      tabList={tabList}
      height={400}
      emptyText="暂无报警数据"
    />
  );
}
```

---

## 六、文档更新与最佳实践

- 更新了 `TableChart.md` 文档，添加了多 Tab 相关的参数说明和用法示例
- 提供了单 Tab 和多 Tab 两种用法的示例代码
- 明确了 `TableChartTab` 接口的各个字段含义和用法

---

## 七、问题修复与迭代优化

- 确保了 Tab 切换时数据正确刷新，避免数据混淆
- 优化了 Tab 样式，确保与其他图表组件保持一致
- 保持了对原有单 API 调用方式的兼容性

---

## 八、任务时间与耗时分析

| 阶段/子任务  | 开始时间             | 结束时间             | 耗时      | 主要内容/备注               | 主要错误/异常        |
| ------------ | -------------------- | -------------------- | --------- | --------------------------- | -------------------- |
| 需求分析     | 2025-06-23 09:00     | 2025-06-23 09:15     | 15min     | 分析需求，参考已有组件      | 无                   |
| 接口设计     | 2025-06-23 09:15     | 2025-06-23 09:30     | 15min     | 设计 TableChartTab 接口     | 无                   |
| 组件实现     | 2025-06-23 09:30     | 2025-06-23 10:15     | 45min     | 实现 TableChart 多 Tab 功能 | Tab 切换时数据未更新 |
| 示例应用     | 2025-06-23 10:15     | 2025-06-23 10:30     | 15min     | 升级 AreaStatsTable 组件    | 无                   |
| 文档更新     | 2025-06-23 10:30     | 2025-06-23 10:45     | 15min     | 更新 TableChart.md 文档     | 无                   |
| 测试与优化   | 2025-06-23 10:45     | 2025-06-23 11:00     | 15min     | 测试功能，优化样式和交互    | 无                   |
| 开发日志编写 | 2025-06-23 11:00     | 2025-06-23 11:15     | 15min     | 编写开发日志                | 无                   |
| **总计**     | **2025-06-23 09:00** | **2025-06-23 11:15** | **2.25h** |                             |                      |

---

## 九、开发总结与迁移建议

- 本次开发为 `TableChart` 组件增加了多 Tab 功能，使其能够在同一个组件中支持不同 API 调用和不同列配置的切换展示，极大提升了组件的灵活性和复用性。
- 通过升级 `AreaStatsTable` 组件，将原来需要两个独立组件（区域统计和设备统计）的功能合并到一个组件中，减少了代码重复，提升了用户体验。
- 建议将类似的多 Tab 需求统一使用此模式实现，保持项目风格一致。
- 后续可考虑进一步增强 `TableChart` 组件，如添加排序、筛选、分页等功能。

---

## 十、用户 prompt 备忘录（时间序列，自动归纳版）

1. 为 TableChart 组件增加多 Tab 功能，支持不同 API 调用和不同列配置的切换展示。
2. 在 AreaStatsTable 组件中应用 TableChart 的多 Tab 功能，合并区域统计和设备统计。
3. 仿照已有开发日志编写本次开发的详细记录。

---

## 十一、用户 prompt 明细原文（时间序列，完整收录）

1. 为 @`/Users/<USER>/Workspace/vren/chogori/src/components/chart/TableChart.tsx` 增加一个功能，它有一个tab选择区，每个区调用的api不一样，导致table呈现的columns和数据不一样
2. 在 @`/Users/<USER>/Workspace/vren/chogori/src/pages/alarm/components/AreaStatsTable.tsx` 里用上示例
3. 仿照 @`/Users/<USER>/Workspace/vren/chogori/docs/dev-log/20250621-alarmIndexContent.md` 写一篇今天的开发日志
4. 以上内容放于 @`/Users/<USER>/Workspace/vren/chogori/docs/dev-log/` 下20250623-tableChart.md
