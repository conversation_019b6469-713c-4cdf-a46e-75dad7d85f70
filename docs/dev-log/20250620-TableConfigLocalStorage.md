# TableConfig 持久化存储功能开发日志

> 相关源码与文档引用：
>
> - TableConfig 组件：[src/components/tableConfig.tsx](../../src/components/tableConfig.tsx)
> - useTableConfig hook：[src/hooks/useTableConfig.tsx](../../src/hooks/useTableConfig.tsx)
> - List 相关页面示例：[src/components/list/list.tsx](../../src/components/list/list.tsx)
> - 迁移与用法文档：[src/components/tableConfig.md](../../src/components/tableConfig.md)

---

## 一、需求与目标

本次开发目标是为 TableConfig 组件实现**本地持久化存储**，让用户自定义的表格列配置在刷新或重新访问页面时自动恢复，并支持多页面隔离存储，提升用户体验和系统复用性。

---

## 二、方案设计与实现思路

### 1. 问题分析

- 原有 TableConfig 组件只在"保存"时更新 columns atom，页面初始化时并未读取 localStorage，导致配置无法持久化。
- 多页面共用同一存储 key，导致配置互相覆盖。

### 2. 技术方案

- 新增 `useTableConfig` hook，负责 columns atom 的初始化和持久化。
- 存储 key 支持 entity 或 pathname，保证多页面隔离。
- TableConfig 组件只负责 UI 交互，移除存储相关逻辑，保持受控组件模式。

### 3. 主要实现步骤

- 编写 `useTableConfig` hook，封装读取/写入 localStorage 逻辑。
- 替换 List、MyList、SimpleList、RightList 等组件中 columns atom 的用法。
- 简化 TableConfig 组件，移除存储相关逻辑。
- 完善文档，说明新用法和迁移建议。

---

## 三、核心代码与设计说明

### 1. useTableConfig hook

```tsx
export const useTableConfig = (columnsAtom: any, entity?: string) => {
  const [columns, setColumns] = useAtom(columnsAtom);
  const { pathname } = useLocation();

  useEffect(() => {
    if (!columns?.length) return;
    const savedConfig = loadTableConfig(entity, pathname);
    if (Object.keys(savedConfig).length === 0) return;
    const updatedColumns = columns.map((col) => ({
      ...col,
      isShow:
        savedConfig[col.dataIndex] !== undefined
          ? savedConfig[col.dataIndex]
          : col.isShow,
    }));
    const hasChanges = updatedColumns.some(
      (col, idx) => col.isShow !== columns[idx].isShow
    );
    if (hasChanges) setColumns(updatedColumns);
  }, [columns, entity, pathname, setColumns]);

  const setColumnsWithStorage = (newColumns: any[]) => {
    setColumns(newColumns);
    saveTableConfig(entity, newColumns, pathname);
  };

  return [columns, setColumnsWithStorage];
};
```

### 2. List 组件用法

```tsx
const [_columns, setColumns] = useTableConfig(atoms.columns, atoms.entity);
<TableConfig
  columns={_columns}
  handleSave={setColumns}
  visible={configModal}
  handleClose={setConfigModal}
/>;
```

### 3. TableConfig 组件

- 只负责 UI 交互，所有存储逻辑移除，简化为纯受控组件。

---

## 四、开发规范与最佳实践

- 组件、函数均有中文注释，注重说明"为什么"。
- 存储 key 规则：优先 entity，无则用 pathname，兜底 default，保证多页面隔离。
- TableConfig 组件 API 保持不变，兼容所有旧用法。
- 推荐所有涉及表格列配置的页面，务必用 useTableConfig 包裹 columns atom，保证配置持久化。
- entity 建议：如有业务唯一标识，建议传 entity，避免不同页面配置冲突。

---

## 五、主要问题与解决过程

| 阶段/子任务        | 开始时间  | 结束时间  | 耗时   | 主要内容/备注              | 主要错误/异常          |
| ------------------ | --------- | --------- | ------ | -------------------------- | ---------------------- |
| 需求分析与方案设计 | 09:00     | 09:30     | 30min  | 复盘现有实现，梳理痛点     | 方案初稿遗漏初始化问题 |
| useTableConfig实现 | 09:30     | 10:30     | 1h     | 编写hook，测试本地存储逻辑 | useEffect依赖遗漏      |
| 组件适配与联调     | 10:30     | 11:30     | 1h     | 替换各List组件，联调       | atom类型不兼容         |
| 文档与最佳实践沉淀 | 11:30     | 12:00     | 30min  | 编写用法文档，迁移建议     |                        |
| **总计**           | **09:00** | **12:00** | **3h** |                            |                        |

---

## 六、用户 prompt 备忘录（时间序列）

1. 这个组件有一个问题，没有将信息保存下来，导致浏览器刷新之后，就没有了。由你来完成这个需求。需要考虑几点：
2. 查看下这段代码，上述实现有一个问题，就是刷新页面或者重新访问页面的时候，list不会主动读取TableConfig组件上次存储的值，所以上述的实现只在save的时候生效，因为传入的参数handleSave，但是存储在localStorage里面的值并不会再下次打开页面的时候被读入list组件，导致实际并未有任何效果
3. 撰写本次开发日志，保存为20250620-TableConfigLocalStorage.md

---

## 七、总结与迁移建议

- 本次改造极大提升了表格配置的用户体验和代码复用性，建议后续所有表格相关页面均采用此方案。
- 老代码无需大改，TableConfig 组件 API 保持不变。
- 后续可扩展为支持列顺序、宽度等更多配置项。

---

如需详细 prompt 原文或源码引用，可参考本日志顶部的"相关源码与文档引用"部分。
