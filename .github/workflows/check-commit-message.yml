name: Check Commit Message

on:
  pull_request:
    branches:
      - main # 你可以指定具体的分支，或者使用 `- '*'` 来表示所有分支
      - release-*      # 应用到所有以 'release-' 开头的分支


jobs:
  check-commit-message:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0 # 获取完整的 Git 历史

      - name: Fetch all branches
        run: |
          git fetch --all

      - name: Get commit messages
        run: |
          # 确保获取远程 main 分支的最新状态
          git fetch origin main

          # 获取当前 PR 中的所有 commit
          commits=$(git log origin/main..HEAD --pretty=format:'%s')
          echo "Commit messages in the PR:"
          echo "$commits"

          # 定义无效的 commit message 正则表达式，可以根据需要自定义
          invalid_commit_regex="^fix$|^update$|^test$|^.$|^\s*$"

          # 检查每一个提交信息
          while IFS= read -r commit_message; do
            if echo "$commit_message" | grep -iE "$invalid_commit_regex"; then
              echo "Error: Commit message '$commit_message' is too vague or invalid."
              exit 1
            fi
          done <<< "$commits"

          echo "All commit messages are valid."
