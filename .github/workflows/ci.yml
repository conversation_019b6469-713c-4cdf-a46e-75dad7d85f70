name: <PERSON>gori CI

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main
      - release-*

jobs:
  build-and-test:
    runs-on: ubuntu-latest

    env:
      NODE_ENV: production
      NODE_OPTIONS: "--max-old-space-size=4096" # 设置 Node.js 的最大内存

    steps:
      # Step 1: 检出代码
      - name: Checkout code
        uses: actions/checkout@v3

      # Step 2: 设置 Node.js 版本
      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: "20"

      # Step 3: 安装依赖 (使用 Yarn)
      - name: Install dependencies
        run: |
          yarn install --production=false

      # Step 4: 运行 Lint 检查
      #   - name: Run Lint check
      #     run: |
      #       yarn lint

      # Step 5: 运行测试
      #   - name: Run tests
      #     run: |
      #       yarn test --ci

      # Step 6: 构建项目
      - name: Build project
        run: |
          yarn build

    # 可选 Step 7: 上传构建产物供后续使用
    #   - name: Upload build artifacts
    #     uses: actions/upload-artifact@v3
    #     with:
    #       name: build
    #       path: build/
