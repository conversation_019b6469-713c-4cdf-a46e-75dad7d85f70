<!doctype html>
<html>

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title></title>
  <style type="text/css">
    body {
      padding-top: 30px;
    }

    .open-app {
      margin: 30px;
      border-radius: 5px;
      padding: 10px 20px;
      border: 1px solid #ccc;
    }
  </style>
</head>

<body>
  <!-- <a class="open-app">在浏览器中下载</a> -->

  <script type="text/javascript">
    const searchParams = new URLSearchParams(window.location.search);
    const app_download_url = searchParams.get("appdownload_url");
    const androidLinkurl = app_download_url;
    const iosLinkUrl = window.location.origin + "/iosAppDownload.html";

    // const iosLinkUrl = "http://apps.apple.com/cn/app/id387682726" // 淘宝 app store 下载地址
    // const androidLinkurl = "https://a.app.qq.com/o/simple.jsp?pkgname=com.lucky.luckyclient" //(找不到淘宝应用宝地址，这里以lucky coffee为例)


    const u = navigator.userAgent;
    const isAndroid = u.indexOf("Android") > -1 || u.indexOf("Linux") > -1;
    const isIOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/);

    window.onload = function () {
      const link = isIOS ? iosLinkUrl : androidLinkurl;

      // 设置链接的 href 属性
      const anchorElements = document.querySelectorAll("a");
      anchorElements.forEach(anchor => {
        anchor.setAttribute("href", link);
      });

      // 重定向
      window.location.href = link;
    };
  </script>
</body>

</html>