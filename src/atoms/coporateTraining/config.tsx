import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { CommonAtoms } from "types";

export const configFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const configFnAtom = atom({
  refetch: () => {},
});

export const configEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const configConfigModalAtom = atom(false);

const configShowColumns = [
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  // user-defined code here
];

const configExtendColumns = [
  // user-defined code here
];

export const configColumnsAtom = atom([
  ...configShowColumns,
  ...configExtendColumns,
]);

/*export const configColumnsAtom = atom(
  (get) => get(configShowColumnsAtom).concat(get(configExtendColumnsAtom)),
  (get, set, newValue: any) => {
    set(configShowColumnsAtom, newValue.slice(0, newValue.length / 2)); // 更新 ShowColumnsAtom
    set(configExtendColumnsAtom, newValue.slice(newValue.length / 2)); // 更新 ExtendColumnsAtom
  }
)*/

export const configAtoms: CommonAtoms = {
  entity: "Config",
  filter: configFilterAtom,
  Fn: configFnAtom,
  editModal: configEditModalAtom,
  configModal: configConfigModalAtom,
  columns: configColumnsAtom,
};
