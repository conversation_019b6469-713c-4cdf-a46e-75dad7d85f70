import { CT_QUESTION_TYPE_MAP, JOB_APPOINTMENT_STATUS } from "components";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";
import { Tag, Tooltip } from "tdesign-react";
import { CommonAtoms } from "types";

export const questionFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
  filter: {},
  subjectId: null,
});

//TODO: to delete
export const questionFnAtom = atom({
  refetch: () => {},
});

export const questionEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const questionConfigModalAtom = atom(false);

const quesiontBaseColumns = [
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
];

const questionPickerBaseColumns = [
  {
    title: "题目ID",
    dataIndex: "questionId",
    isShow: true,
    ellipsis: true,
  },
];

const questionShowColumns = [
  // user-defined code here
  {
    title: "题干",
    dataIndex: "stem",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "知识科目",
    dataIndex: "subject",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "试题类型",
    dataIndex: "type",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item ? item : 1, "id"))(CT_QUESTION_TYPE_MAP);
      return (
        <Tooltip content={i?.name}>
          <Tag color={i?.color} type="light">
            {i?.name}
          </Tag>
        </Tooltip>
      );
    },
  },
];

const questionExtendColumns = [
  // user-defined code here
  {
    title: "答案",
    dataIndex: "answerList",
    isShow: true,
    ellipsis: true,
    render: (text, record, index) => {
      const content = text
        ? text.map((item, index, array) =>
            index === array.length - 1 ? (
              <span key={index}>{item}</span>
            ) : (
              <span key={index}>{item},</span>
            )
          )
        : null;
      if (!content) return null;
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
  },
  {
    title: "审核状态",
    dataIndex: "status",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item ? item : 1, "id"))(JOB_APPOINTMENT_STATUS);
      return (
        <Tooltip content={i?.name}>
          <Tag color={i?.color} type="light">
            {i?.name}
          </Tag>
        </Tooltip>
      );
    },
  },
];

const questionPickerColumns = [
  {
    title: "分值",
    dataIndex: "score",
    isShow: true,
    ellipsis: true,
  },
];

export const questionShowColumnsAtom = atom([
  ...questionPickerBaseColumns,
  ...questionShowColumns,
  ...questionPickerColumns,
]);

export const questionColumnsAtom = atom([
  ...quesiontBaseColumns,
  ...questionShowColumns,
  ...questionExtendColumns,
]);

/*export const questionColumnsAtom = atom(
  (get) => get(questionShowColumnsAtom).concat(get(questionExtendColumnsAtom)),
  (get, set, newValue: any) => {
    set(questionShowColumnsAtom, newValue.slice(0, newValue.length / 2)); // 更新 ShowColumnsAtom
    set(questionExtendColumnsAtom, newValue.slice(newValue.length / 2)); // 更新 ExtendColumnsAtom
  }
)*/

export const questionAtoms: CommonAtoms = {
  entity: "Question",
  filter: questionFilterAtom,
  Fn: questionFnAtom,
  editModal: questionEditModalAtom,
  configModal: questionConfigModalAtom,
  columns: questionColumnsAtom,
};
