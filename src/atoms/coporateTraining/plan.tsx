import { Tag, Tooltip } from "@douyinfe/semi-ui";
import {
  IS_ISNOT_MAP,
  TRAINING_PLAN_OBJECTRANGETYPE_MAP,
  TRAINING_PLAN_OBJECTTYPE_MAP,
  TRAINING_STATUS_MAP,
} from "components";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";
import { CommonAtoms } from "types";
import { formatDateDay, formatNumToRate } from "utils";

export const coporateTrainingPlanFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const coporateTrainingPlanFnAtom = atom({
  refetch: () => {},
});

export const coporateTrainingPlanDetailSideAtom = atomWithReset({
  id: "",
  show: false,
});

export const coporateTrainingPlanEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const coporateTrainingPlanConfigModalAtom = atom(false);

const coporateTrainingPlanShowColumns = [
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  // user-defined code here
  {
    title: "培训计划名称",
    dataIndex: "name",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item ?? ""}>
        <p>{item ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "培训类型",
    dataIndex: "trainingTypeValue",
    isShow: true,
    ellipsis: true,
    render: (r) => (
      <Tooltip content={r?.dicValue ?? ""}>
        <p>{r?.dicValue ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "开始时间",
    dataIndex: "beginTime",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const content = formatDateDay(item);
      if (!content) {
        return null;
      }
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
  },
  {
    title: "结束时间",
    dataIndex: "endTime",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const content = formatDateDay(item);
      if (!content) {
        return null;
      }
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
  },
  {
    title: "培训对象",
    dataIndex: "objectType",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item ? item : 1, "id"))(
        TRAINING_PLAN_OBJECTTYPE_MAP
      );
      return (
        <Tooltip content={i?.name}>
          <Tag color={i?.color} type="light">
            {i?.name}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "培训对象范围",
    dataIndex: "objectRangeType",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item ? item : 1, "id"))(
        TRAINING_PLAN_OBJECTRANGETYPE_MAP
      );
      return (
        <Tooltip content={i?.name}>
          <Tag color={i?.color} type="light">
            {i?.name}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "是否获得入场资格",
    dataIndex: "isEntryPass",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item ? item : 1, "id"))(IS_ISNOT_MAP);
      return (
        <Tooltip content={i?.name}>
          <Tag color={i?.color} type="light">
            {i?.name}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "状态",
    dataIndex: "status",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item ? item : 1, "id"))(TRAINING_STATUS_MAP);
      return (
        <Tooltip content={i?.name}>
          <Tag color={i?.color} type="light">
            {i?.name}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "通过率",
    dataIndex: "passRate",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const content = formatNumToRate(item);
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
  },
];

const coporateTrainingPlanExtendColumns = [
  // user-defined code here
];

export const coporateTrainingPlanShowColumnsAtom = atom(
  ...coporateTrainingPlanShowColumns
);

export const coporateTrainingPlanColumnsAtom = atom([
  ...coporateTrainingPlanShowColumns,
  ...coporateTrainingPlanExtendColumns,
]);

/*export const coporateTrainingPlanColumnsAtom = atom(
  (get) => get(coporateTrainingPlanShowColumnsAtom).concat(get(coporateTrainingPlanExtendColumnsAtom)),
  (get, set, newValue: any) => {
    set(coporateTrainingPlanShowColumnsAtom, newValue.slice(0, newValue.length / 2)); // 更新 ShowColumnsAtom
    set(coporateTrainingPlanExtendColumnsAtom, newValue.slice(newValue.length / 2)); // 更新 ExtendColumnsAtom
  }
)*/

export const coporateTrainingPlanShareModalAtom = atomWithReset({
  plan: {},
  show: false,
});

export const coporateTrainingPlanAtoms: CommonAtoms = {
  entity: "CoporateTrainingPlan",
  filter: coporateTrainingPlanFilterAtom,
  Fn: coporateTrainingPlanFnAtom,
  editModal: coporateTrainingPlanEditModalAtom,
  configModal: coporateTrainingPlanConfigModalAtom,
  columns: coporateTrainingPlanColumnsAtom,
};
