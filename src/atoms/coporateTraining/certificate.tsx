import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { Tooltip } from "tdesign-react";
import { CommonAtoms } from "types";

export const coporateTrainingCertificateFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const coporateTrainingCertificateFnAtom = atom({
  refetch: () => {},
});

export const coporateTrainingCertificateEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const coporateTrainingCertificateConfigModalAtom = atom(false);

const coporateTrainingCertificateShowColumns = [
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  // user-defined code here
  {
    title: "证书类型",
    dataIndex: "certificateTypeValue",
    isShow: true,
    ellipsis: true,
    render: (r) => (
      <Tooltip content={r?.dicValue ?? ""}>
        <p>{r?.dicValue ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "发证机关",
    dataIndex: "authorityTypeValue",
    isShow: true,
    ellipsis: true,
    render: (r) => (
      <Tooltip content={r?.dicValue ?? ""}>
        <p>{r?.dicValue ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "有效期(月)",
    dataIndex: "validMonths",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item ?? ""}>
        <p>{item ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "已获取人数",
    dataIndex: "obtainedNum",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item ?? ""}>
        <p>{item ?? ""}</p>
      </Tooltip>
    ),
  },
];

const coporateTrainingCertificateExtendColumns = [
  // user-defined code here
];

export const coporateTrainingCertificateShowColumnsAtom = atom(
  ...coporateTrainingCertificateShowColumns,
);

export const coporateTrainingCertificateColumnsAtom = atom([
  ...coporateTrainingCertificateShowColumns,
  ...coporateTrainingCertificateExtendColumns,
]);

/*export const coporateTrainingCertificateColumnsAtom = atom(
  (get) => get(coporateTrainingCertificateShowColumnsAtom).concat(get(coporateTrainingCertificateExtendColumnsAtom)),
  (get, set, newValue: any) => {
    set(coporateTrainingCertificateShowColumnsAtom, newValue.slice(0, newValue.length / 2)); // 更新 ShowColumnsAtom
    set(coporateTrainingCertificateExtendColumnsAtom, newValue.slice(newValue.length / 2)); // 更新 ExtendColumnsAtom
  }
)*/

export const coporateTrainingCertificateAtoms: CommonAtoms = {
  entity: "CoporateTrainingCertificate",
  filter: coporateTrainingCertificateFilterAtom,
  Fn: coporateTrainingCertificateFnAtom,
  editModal: coporateTrainingCertificateEditModalAtom,
  configModal: coporateTrainingCertificateConfigModalAtom,
  columns: coporateTrainingCertificateColumnsAtom,
};
