import { TRAINING_EXAM_STATUS_MAP } from "components";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";
import { Tag, Tooltip } from "tdesign-react";
import { CommonAtoms } from "types";
import { formatDate } from "utils";

export const coporateTrainingMyExamRecordFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const coporateTrainingMyExamRecordFnAtom = atom({
  refetch: () => {},
});

export const coporateTrainingMyExamRecordEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const coporateTrainingMyExamRecordConfigModalAtom = atom(false);

const coporateTrainingMyExamRecordShowColumns = [
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  // user-defined code here
  {
    title: "培训计划名称",
    dataIndex: "plan",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "课程名称",
    dataIndex: "course",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "试卷名称",
    dataIndex: "paper",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "开考时间",
    dataIndex: "examBeginTime",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const content = formatDate(item);
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
  },
  {
    title: "交卷时间",
    dataIndex: "examEndTime",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const content = formatDate(item);
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
  },
  {
    title: "考试得分",
    dataIndex: "score",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "及格分",
    dataIndex: "passScore",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "是否通过",
    dataIndex: "status",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item ? item : 1, "id"))(TRAINING_EXAM_STATUS_MAP);
      return (
        <Tooltip content={i?.name}>
          <Tag color={i?.color} type="light">
            {i?.name}
          </Tag>
        </Tooltip>
      );
    },
  },
];

const coporateTrainingMyExamRecordExtendColumns = [
  // user-defined code here
];

export const coporateTrainingMyExamRecordShowColumnsAtom = atom(
  ...coporateTrainingMyExamRecordShowColumns,
);

export const coporateTrainingMyExamRecordColumnsAtom = atom([
  ...coporateTrainingMyExamRecordShowColumns,
  ...coporateTrainingMyExamRecordExtendColumns,
]);

/*export const coporateTrainingMyExamRecordColumnsAtom = atom(
  (get) => get(coporateTrainingMyExamRecordShowColumnsAtom).concat(get(coporateTrainingMyExamRecordExtendColumnsAtom)),
  (get, set, newValue: any) => {
    set(coporateTrainingMyExamRecordShowColumnsAtom, newValue.slice(0, newValue.length / 2)); // 更新 ShowColumnsAtom
    set(coporateTrainingMyExamRecordExtendColumnsAtom, newValue.slice(newValue.length / 2)); // 更新 ExtendColumnsAtom
  }
)*/

export const coporateTrainingMyExamRecordAtoms: CommonAtoms = {
  entity: "CoporateTrainingMyExamRecord",
  filter: coporateTrainingMyExamRecordFilterAtom,
  Fn: coporateTrainingMyExamRecordFnAtom,
  editModal: coporateTrainingMyExamRecordEditModalAtom,
  configModal: coporateTrainingMyExamRecordConfigModalAtom,
  columns: coporateTrainingMyExamRecordColumnsAtom,
};
