import { CT_COURSEWARE_TYPE_MAP, JOB_APPOINTMENT_STATUS } from "components";
import { base_url } from "config";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";
import { Tag, Tooltip } from "tdesign-react";
import { CommonAtoms } from "types";

export const coursewareFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
  filter: {},
  subjectId: null,
});

//TODO: to delete
export const coursewareFnAtom = atom({
  refetch: () => {},
});

export const coursewareEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const coursewareConfigModalAtom = atom(false);

const coursewareShowColumns = [
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  // user-defined code here
  {
    title: "课件名称",
    dataIndex: "name",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item ?? ""}>
        <p>{item ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "知识科目",
    dataIndex: "subject",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "课件类型",
    dataIndex: "type",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item ? item : 1, "id"))(CT_COURSEWARE_TYPE_MAP);
      return (
        <Tooltip content={i?.name}>
          <Tag color={i?.color} type="light">
            {i?.name}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "最低学习时长",
    dataIndex: "minStudyTime",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const hour = item?.hour > 0 ? item?.hour + "小时" : "";
      const minute = item?.minute > 0 ? item?.minute + "分钟" : "";
      const second = item?.second > 0 ? item?.second + "秒" : "";
      const content = hour + minute + second;
      return <Tooltip content={content}>{content}</Tooltip>;
    },
  },
  {
    title: "附件",
    dataIndex: "attachment",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      if (!item) return "";
      const url = base_url + item;
      return (
        <Tooltip content={item.split("/").pop()}>
          <a href={url} target="_blank">
            {" "}
            {item.split("/").pop()}
          </a>
        </Tooltip>
      );
    },
  },
];

const coursewareExtendColumns = [
  // user-defined code here
  {
    title: "课件时长",
    dataIndex: "originTime",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const hour = item?.hour > 0 ? item?.hour + "小时" : "";
      const minute = item?.minute > 0 ? item?.minute + "分钟" : "";
      const second = item?.second > 0 ? item?.second + "秒" : "";
      const content = hour + minute + second;
      return <Tooltip content={content}>{content}</Tooltip>;
    },
  },
  {
    title: "课件介绍",
    dataIndex: "abstraction",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item ?? ""}>
        <p>{item ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "审核状态",
    dataIndex: "status",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item ? item : 1, "id"))(JOB_APPOINTMENT_STATUS);
      return (
        <Tooltip content={i?.name}>
          <Tag color={i?.color} type="light">
            {i?.name}
          </Tag>
        </Tooltip>
      );
    },
  },
];

export const coursewareShowColumnsAtom = atom(coursewareShowColumns);

export const coursewareColumnsAtom = atom([
  ...coursewareShowColumns,
  ...coursewareExtendColumns,
]);

/*export const coursewareColumnsAtom = atom(
  (get) => get(coursewareShowColumnsAtom).concat(get(coursewareExtendColumnsAtom)),
  (get, set, newValue: any) => {
    set(coursewareShowColumnsAtom, newValue.slice(0, newValue.length / 2)); // 更新 ShowColumnsAtom
    set(coursewareExtendColumnsAtom, newValue.slice(newValue.length / 2)); // 更新 ExtendColumnsAtom
  }
)*/

export const coursewareAtoms: CommonAtoms = {
  entity: "Courseware",
  filter: coursewareFilterAtom,
  Fn: coursewareFnAtom,
  editModal: coursewareEditModalAtom,
  configModal: coursewareConfigModalAtom,
  columns: coursewareColumnsAtom,
};
