import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { Tooltip } from "tdesign-react";
import { CommonAtoms } from "types";
import { formatDate } from "utils";

export const coporateTrainingMyStudyRecordFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const coporateTrainingMyStudyRecordFnAtom = atom({
  refetch: () => {},
});

export const coporateTrainingMyStudyRecordEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const coporateTrainingMyStudyRecordConfigModalAtom = atom(false);

const coporateTrainingMyStudyRecordShowColumns = [
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  // user-defined code here
  {
    title: "培训计划名称",
    dataIndex: "plan",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "课程名称",
    dataIndex: "course",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "课件名称",
    dataIndex: "courseware",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "开始时间",
    dataIndex: "studyBeginTime",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const content = formatDate(item);
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
  },
  {
    title: "结束时间",
    dataIndex: "studyEndTime",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const content = formatDate(item);
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
  },
  {
    title: "学习时长",
    dataIndex: "studyTime",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const hourContent = item.hour > 0 ? `${item.hour}小时` : "";
      const minuteContent = item.minute > 0 ? `${item.minute}分钟` : "";
      const secondContent = item.second > 0 ? `${item.second}秒` : "";
      const content = `${hourContent}${minuteContent}${secondContent}`;
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
  },
];

const coporateTrainingMyStudyRecordExtendColumns = [
  // user-defined code here
];

export const coporateTrainingMyStudyRecordShowColumnsAtom = atom(
  ...coporateTrainingMyStudyRecordShowColumns,
);

export const coporateTrainingMyStudyRecordColumnsAtom = atom([
  ...coporateTrainingMyStudyRecordShowColumns,
  ...coporateTrainingMyStudyRecordExtendColumns,
]);

/*export const coporateTrainingMyStudyRecordColumnsAtom = atom(
  (get) => get(coporateTrainingMyStudyRecordShowColumnsAtom).concat(get(coporateTrainingMyStudyRecordExtendColumnsAtom)),
  (get, set, newValue: any) => {
    set(coporateTrainingMyStudyRecordShowColumnsAtom, newValue.slice(0, newValue.length / 2)); // 更新 ShowColumnsAtom
    set(coporateTrainingMyStudyRecordExtendColumnsAtom, newValue.slice(newValue.length / 2)); // 更新 ExtendColumnsAtom
  }
)*/

export const coporateTrainingMyStudyRecordAtoms: CommonAtoms = {
  entity: "CoporateTrainingPeopleStudyRecord",
  filter: coporateTrainingMyStudyRecordFilterAtom,
  Fn: coporateTrainingMyStudyRecordFnAtom,
  editModal: coporateTrainingMyStudyRecordEditModalAtom,
  configModal: coporateTrainingMyStudyRecordConfigModalAtom,
  columns: coporateTrainingMyStudyRecordColumnsAtom,
};
