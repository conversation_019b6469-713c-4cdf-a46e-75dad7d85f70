import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { CommonAtoms } from "types";

export const subjectFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const subjectFnAtom = atom({
  refetch: () => {},
});

export const subjectSelectAtom = atomWithReset<number>(0);

// 全部存储数据
export const subjectDataAtom = atom<any[]>([]);

export const subjectEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const subjectConfigModalAtom = atom(false);

const subjectShowColumns = [
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  // user-defined code here
  {
    title: "知识科目名称",
    dataIndex: "name",
    isShow: true,
    ellipsis: true,
  },
];

const subjectExtendColumns = [
  // user-defined code here
];

export const subjectColumnsAtom = atom([
  ...subjectShowColumns,
  ...subjectExtendColumns,
]);

/*export const subjectColumnsAtom = atom(
  (get) => get(subjectShowColumnsAtom).concat(get(subjectExtendColumnsAtom)),
  (get, set, newValue: any) => {
    set(subjectShowColumnsAtom, newValue.slice(0, newValue.length / 2)); // 更新 ShowColumnsAtom
    set(subjectExtendColumnsAtom, newValue.slice(newValue.length / 2)); // 更新 ExtendColumnsAtom
  }
)*/

export const subjectRootName = "知识科目分类";

export const subjectAtoms: CommonAtoms = {
  entity: "Subject",
  filter: subjectFilterAtom,
  Fn: subjectFnAtom,
  editModal: subjectEditModalAtom,
  configModal: subjectConfigModalAtom,
  columns: subjectColumnsAtom,
};
