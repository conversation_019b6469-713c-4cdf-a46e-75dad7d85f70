import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { Tooltip } from "tdesign-react";
import { CommonAtoms } from "types";
import { formatDate } from "utils";

export const coporateTrainingPeopleStudyRecordFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const coporateTrainingPeopleStudyRecordFnAtom = atom({
  refetch: () => {},
});

export const coporateTrainingPeopleStudyRecordEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const coporateTrainingPeopleStudyRecordConfigModalAtom = atom(false);

const coporateTrainingPeopleStudyRecordShowColumns = [
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  // user-defined code here
  {
    title: "姓名",
    dataIndex: "person",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
    renderText: (item) => item?.name ?? "",
  },
  {
    title: "部门/单位",
    dataIndex: "personUnit",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
    renderText: (item) => item?.name ?? "",
  },
  {
    title: "培训计划名称",
    dataIndex: "plan",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
    renderText: (item) => item?.name ?? "",
  },
  {
    title: "课程名称",
    dataIndex: "course",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
    renderText: (item) => item?.name ?? "",
  },
  {
    title: "课件名称",
    dataIndex: "courseware",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
    renderText: (item) => item?.name ?? "",
  },
  {
    title: "开始时间",
    dataIndex: "studyBeginTime",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const content = formatDate(item);
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
    renderText: (item) => formatDate(item),
  },
  {
    title: "结束时间",
    dataIndex: "studyEndTime",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const content = formatDate(item);
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
    renderText: (item) => formatDate(item),
  },
  {
    title: "学习时长",
    dataIndex: "studyTime",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const hourContent = item.hour > 0 ? `${item.hour}小时` : "";
      const minuteContent = item.minute > 0 ? `${item.minute}分钟` : "";
      const secondContent = item.second > 0 ? `${item.second}秒` : "";
      const content = `${hourContent}${minuteContent}${secondContent}`;
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
    renderText: (item) => {
      const hourContent = item.hour > 0 ? `${item.hour}小时` : "";
      const minuteContent = item.minute > 0 ? `${item.minute}分钟` : "";
      const secondContent = item.second > 0 ? `${item.second}秒` : "";
      return `${hourContent}${minuteContent}${secondContent}`;
    },
  },
];

const coporateTrainingPeopleStudyRecordExtendColumns = [
  // user-defined code here
];

export const coporateTrainingPeopleStudyRecordShowColumnsAtom = atom(
  ...coporateTrainingPeopleStudyRecordShowColumns
);

export const coporateTrainingPeopleStudyRecordColumnsAtom = atom([
  ...coporateTrainingPeopleStudyRecordShowColumns,
  ...coporateTrainingPeopleStudyRecordExtendColumns,
]);

/*export const coporateTrainingPeopleStudyRecordColumnsAtom = atom(
  (get) => get(coporateTrainingPeopleStudyRecordShowColumnsAtom).concat(get(coporateTrainingPeopleStudyRecordExtendColumnsAtom)),
  (get, set, newValue: any) => {
    set(coporateTrainingPeopleStudyRecordShowColumnsAtom, newValue.slice(0, newValue.length / 2)); // 更新 ShowColumnsAtom
    set(coporateTrainingPeopleStudyRecordExtendColumnsAtom, newValue.slice(newValue.length / 2)); // 更新 ExtendColumnsAtom
  }
)*/

export const coporateTrainingPeopleStudyRecordAtoms: CommonAtoms = {
  entity: "CoporateTrainingPeopleStudyRecord",
  entityCName: "学员学习记录",
  filter: coporateTrainingPeopleStudyRecordFilterAtom,
  Fn: coporateTrainingPeopleStudyRecordFnAtom,
  editModal: coporateTrainingPeopleStudyRecordEditModalAtom,
  configModal: coporateTrainingPeopleStudyRecordConfigModalAtom,
  columns: coporateTrainingPeopleStudyRecordColumnsAtom,
};
