import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { Tooltip } from "tdesign-react";
import { CommonAtoms } from "types";

export const coporateTrainingPeopleFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const coporateTrainingPeopleFnAtom = atom({
  refetch: () => {},
});

export const coporateTrainingPeopleEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const coporateTrainingPeopleConfigModalAtom = atom(false);

const coporateTrainingPeopleShowColumns = [
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 80,
    fixed: true,
  },
  // user-defined code here
  {
    title: "姓名",
    dataIndex: "person",
    isShow: true,
    ellipsis: true,
    fixed: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "部门",
    dataIndex: "depatment",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  /* {
    title: "身份证号",
    dataIndex: "idNumber",
    ellipse: true,
    isShow: true,
    ellipsis: true,
  }, */
  {
    title: "工号",
    dataIndex: "employeeId",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "总学习时长",
    dataIndex: "studyTime",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const hourContent = item.hour > 0 ? `${item.hour}小时` : "";
      const minuteContent = item.minute > 0 ? `${item.minute}分钟` : "";
      const secondContent = item.second > 0 ? `${item.second}秒` : "";
      const content = `${hourContent}${minuteContent}${secondContent}`;
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
  },
  {
    title: "总学时",
    dataIndex: "studyHour",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "课程积分",
    dataIndex: "score",
    isShow: true,
    ellipsis: true,
  },
  /* {
    title: "积分排名",
    dataIndex: "",
    isShow: true,
    ellipsis: true,
  }, */
];

const coporateTrainingPeopleExtendColumns = [
  // user-defined code here
];

export const coporateTrainingPeopleShowColumnsAtom = atom(
  ...coporateTrainingPeopleShowColumns,
);

export const coporateTrainingPeopleColumnsAtom = atom([
  ...coporateTrainingPeopleShowColumns,
  ...coporateTrainingPeopleExtendColumns,
]);

/*export const coporateTrainingPeopleColumnsAtom = atom(
  (get) => get(coporateTrainingPeopleShowColumnsAtom).concat(get(coporateTrainingPeopleExtendColumnsAtom)),
  (get, set, newValue: any) => {
    set(coporateTrainingPeopleShowColumnsAtom, newValue.slice(0, newValue.length / 2)); // 更新 ShowColumnsAtom
    set(coporateTrainingPeopleExtendColumnsAtom, newValue.slice(newValue.length / 2)); // 更新 ExtendColumnsAtom
  }
)*/

export const coporateTrainingPeopleAtoms: CommonAtoms = {
  entity: "CoporateTrainingPeople",
  filter: coporateTrainingPeopleFilterAtom,
  Fn: coporateTrainingPeopleFnAtom,
  editModal: coporateTrainingPeopleEditModalAtom,
  configModal: coporateTrainingPeopleConfigModalAtom,
  columns: coporateTrainingPeopleColumnsAtom,
};
