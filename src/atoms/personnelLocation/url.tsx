import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { CommonAtoms } from "types";

export const urlFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const urlFnAtom = atom({
  refetch: () => {},
});

export const urlEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const urlConfigModalAtom = atom(false);

const urlShowColumns = [
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  // user-defined code here
];

const urlExtendColumns = [
  // user-defined code here
];

export const urlColumnsAtom = atom([...urlShowColumns, ...urlExtendColumns]);

export const urlAtoms: CommonAtoms = {
  entity: "Url",
  filter: url<PERSON>ilter<PERSON>tom,
  Fn: urlFnAtom,
  editModal: urlEditModalAtom,
  configModal: urlConfigModalAtom,
  columns: urlColumnsAtom,
};
