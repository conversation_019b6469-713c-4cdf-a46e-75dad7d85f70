import { getCurrentOem } from "config";
import { atom } from "jotai";
import { atomWithReset, atomWithStorage } from "jotai/utils";

export const platformConfigAtom = atom({
  id: "vren",
  display_name: getCurrentOem().display_name,
  logo_name: getCurrentOem().logo_name,
  departmentRootName: "公司",
  webMenus: [],
  webUrlPrefix: "",
  cesiumUris: [],
});

export const globalTabNav = atom([
  { tab: "首页", itemKey: "1" },
  { tab: "测试导航", itemKey: "2", closable: true },
]);

// 当前页面是否有权限-开关
export const globalRole = atom<boolean>(true);
globalRole.debugLabel = "权限开关-globalRole";

export const globalUserRole = atomWithStorage("globalUserRole", []);

// 组件类开关
export const certificatePickerAtom = atomWithReset(false);

export const certificatePickerStateAtom = atomWithReset({
  ids: [],
  record: [],
});

type certificateSelectAtomType = {
  visible: boolean;
  type: "temporary" | "guardian" | "";
};

export const certificateSelectAtom = atomWithReset<certificateSelectAtomType>({
  visible: false,
  type: "", // temporary | guardian
});

export const certificateSelectDataAtom = atomWithReset({
  record: null,
});
export const certificatePickerDataAtom = atomWithReset({
  record: null,
});

export const mapPickerAtom = atomWithReset({
  visible: false,
});

export const mapPolygonAtom = atomWithReset({
  visible: false,
  longitude: 0.0,
  latitude: 0.0,
  markers: [],
});

export const referJsAtom = atomWithReset({
  visible: false,
});

export const routerDraftAtom = atomWithReset(false);

export const safetyAnalysisReferValues = atomWithReset([]);
export const jobCertificatesReferValues = atomWithReset([]);

export const certificatePickerSearchAtom = atomWithReset({
  selected: null,
  checkedUser: [],
  lastUser: null,
  workCheckedUser: [],
});

type CertificatePickerPickType = {
  id: number;
  type: 1 | 2;
  certificateIds: number[];
};

export const certificatePickerPickAtom = atomWithReset<
  CertificatePickerPickType[]
>([]);
