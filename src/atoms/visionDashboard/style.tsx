import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { CommonAtoms } from "types";

export const visionDashboardStyleFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const visionDashboardStyleFnAtom = atom({
  refetch: () => {},
});

export const visionDashboardStyleEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const visionDashboardStyleConfigModalAtom = atom(false);

const visionDashboardStyleShowColumns = [
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  // user-defined code here
];

const visionDashboardStyleExtendColumns = [
  // user-defined code here
];

export const visionDashboardStyleShowColumnsAtom = atom(
  ...visionDashboardStyleShowColumns,
);

export const visionDashboardStyleColumnsAtom = atom([
  ...visionDashboardStyleShowColumns,
  ...visionDashboardStyleExtendColumns,
]);

/*export const visionDashboardStyleColumnsAtom = atom(
  (get) => get(visionDashboardStyleShowColumnsAtom).concat(get(visionDashboardStyleExtendColumnsAtom)),
  (get, set, newValue: any) => {
    set(visionDashboardStyleShowColumnsAtom, newValue.slice(0, newValue.length / 2)); // 更新 ShowColumnsAtom
    set(visionDashboardStyleExtendColumnsAtom, newValue.slice(newValue.length / 2)); // 更新 ExtendColumnsAtom
  }
)*/

export const visionDashboardStyleAtoms: CommonAtoms = {
  entity: "VisionDashboardStyle",
  filter: visionDashboardStyleFilterAtom,
  Fn: visionDashboardStyleFnAtom,
  editModal: visionDashboardStyleEditModalAtom,
  configModal: visionDashboardStyleConfigModalAtom,
  columns: visionDashboardStyleColumnsAtom,
};
