import { base_url } from "config";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { Tooltip } from "tdesign-react";
import { CommonAtoms } from "types";
import { formatDateDay } from "utils";

export const basicInfoDocumentInformationFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
  filter: {},
});

//TODO: to delete
export const basicInfoDocumentInformationFnAtom = atom({
  refetch: () => {},
});

export const basicInfoDocumentInformationEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const basicInfoDocumentInformationConfigModalAtom = atom(false);

// CategorySelect
export const basicInfoDocumentInformationWithCategorySelectAtom =
  atomWithReset<number>(0);

const basicInfoDocumentInformationShowColumns = [
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 48,
  },
  {
    title: "文档名称",
    dataIndex: "name",
    isShow: true,
  },
  {
    title: "文档类型",
    dataIndex: "category",
    isShow: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
    renderText: (item, record, index) => item?.name ?? "",
  },
  {
    title: "文档编号",
    dataIndex: "code",
    isShow: false,
  },
  {
    title: "文档版本号",
    dataIndex: "version",
    isShow: false,
  },
  {
    title: "文档简介",
    dataIndex: "abstraction",
    isShow: true,
  },
  {
    title: "审核日期",
    dataIndex: "approvalDate",
    isShow: true,
    render: (item) => {
      const content = formatDateDay(item);
      if (!content) {
        return null;
      }
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
  },
  {
    title: "生效日期",
    dataIndex: "effectiveDate",
    isShow: true,
    render: (item) => {
      const content = formatDateDay(item);
      if (!content) {
        return null;
      }
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
  },
  {
    title: "文档附件列表",
    dataIndex: "attachmentList",
    isShow: true,
    width: 240,
    render: (text, record, index) => {
      const content = text
        ? text.map((item, index, array) => (
            <>
              <span key={index}>
                {index + 1}:{" "}
                <a
                  href={base_url + item}
                  target="_blank"
                  style={{
                    color: "#0052cc",
                    textDecoration: "none",
                    cursor: "pointer",
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.textDecoration = "underline";
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.textDecoration = "none";
                  }}
                >
                  {item.split("/").pop()}
                </a>
              </span>
              <br />
            </>
          ))
        : null;
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
    renderText: (text, record, index) => {
      return text
        ? text.map((item, index, array) => {
            return item.split("/").pop();
          })
        : null;
    },
  },
  // user-defined code here
];

const basicInfoDocumentInformationExtendColumns = [
  // user-defined code here
];

export const basicInfoDocumentInformationShowColumnsAtom = atom(
  basicInfoDocumentInformationShowColumns
);

export const basicInfoDocumentInformationColumnsAtom = atom([
  ...basicInfoDocumentInformationShowColumns,
  ...basicInfoDocumentInformationExtendColumns,
]);

/*export const basicInfoDocumentInformationColumnsAtom = atom(
  (get) => get(basicInfoDocumentInformationShowColumnsAtom).concat(get(basicInfoDocumentInformationExtendColumnsAtom)),
  (get, set, newValue: any) => {
    set(basicInfoDocumentInformationShowColumnsAtom, newValue.slice(0, newValue.length / 2)); // 更新 ShowColumnsAtom
    set(basicInfoDocumentInformationExtendColumnsAtom, newValue.slice(newValue.length / 2)); // 更新 ExtendColumnsAtom
  }
)*/

export const basicInfoDocumentInformationAtoms: CommonAtoms = {
  entity: "BasicInfoDocumentInformation",
  entityCName: "文档信息",
  filter: basicInfoDocumentInformationFilterAtom,
  Fn: basicInfoDocumentInformationFnAtom,
  editModal: basicInfoDocumentInformationEditModalAtom,
  configModal: basicInfoDocumentInformationConfigModalAtom,
  columns: basicInfoDocumentInformationColumnsAtom,
};
