import {
  CONTRACTOR_EVALUATION_PHASE_MAP,
  CONTRACTOR_EVALUATION_RESULT_MAP,
} from "components";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";
import { Tag, Tooltip } from "tdesign-react";
import { CommonAtoms } from "types";
import { formatDateDay } from "utils";

export const basicInfoContractorEvaluationFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const basicInfoContractorEvaluationFnAtom = atom({
  refetch: () => {},
});

export const basicInfoContractorEvaluationEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const basicInfoContractorEvaluationConfigModalAtom = atom(false);

const basicInfoContractorEvaluationShowColumns = [
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 48,
  },
  {
    title: "名称",
    dataIndex: "name",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item ?? ""}>
        <p>{item ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "承包商",
    dataIndex: "contractor",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "评估时间",
    dataIndex: "evaluationTime",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const content = formatDateDay(item);
      if (!content) {
        return null;
      }
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
  },
  {
    title: "评估阶段", // From 'phase'
    dataIndex: "phase",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(CONTRACTOR_EVALUATION_PHASE_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "评分", // From 'score'
    dataIndex: "score",
    isShow: true,
  },
  {
    title: "评估结果", // From 'evaluationResult'
    dataIndex: "evaluationResult",
    isShow: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(CONTRACTOR_EVALUATION_RESULT_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
];

const basicInfoContractorEvaluationExtendColumns = [
  // user-defined code here
];

export const basicInfoContractorEvaluationShowColumnsAtom = atom(
  basicInfoContractorEvaluationShowColumns
);

export const basicInfoContractorEvaluationColumnsAtom = atom([
  ...basicInfoContractorEvaluationShowColumns,
  ...basicInfoContractorEvaluationExtendColumns,
]);

/*export const basicInfoContractorEvaluationColumnsAtom = atom(
  (get) => get(basicInfoContractorEvaluationShowColumnsAtom).concat(get(basicInfoContractorEvaluationExtendColumnsAtom)),
  (get, set, newValue: any) => {
    set(basicInfoContractorEvaluationShowColumnsAtom, newValue.slice(0, newValue.length / 2)); // 更新 ShowColumnsAtom
    set(basicInfoContractorEvaluationExtendColumnsAtom, newValue.slice(newValue.length / 2)); // 更新 ExtendColumnsAtom
  }
)*/

export const basicInfoContractorEvaluationAtoms: CommonAtoms = {
  entity: "BasicInfoContractorEvaluation",
  filter: basicInfoContractorEvaluationFilterAtom,
  Fn: basicInfoContractorEvaluationFnAtom,
  editModal: basicInfoContractorEvaluationEditModalAtom,
  configModal: basicInfoContractorEvaluationConfigModalAtom,
  columns: basicInfoContractorEvaluationColumnsAtom,
};
