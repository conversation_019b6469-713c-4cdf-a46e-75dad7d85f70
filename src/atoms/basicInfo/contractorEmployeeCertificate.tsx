import { Image, ImagePreview, Tag, Tooltip } from "@douyinfe/semi-ui";
import { VALID_NOTVALID_MAP } from "components";
import { base_url } from "config";
import dayjs from "dayjs";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";
import { millisecondsOfOnemonth } from "utils";

export const contractorEmployeeCertificateEditModal = atom({
  id: "",
  show: false,
});

export const contractorEmployeeCertificateConfigModalAtom = atom(false);

// 查询条件
export const contractorEmployeeCertificateFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

// 查询条件
export const contractorEmployeeCertificateFnAtom = atom({
  refetch: () => {},
});

export const contractorEmployeeCertificateColumnsAtom = atom([
  {
    title: <Tooltip content="ID">ID</Tooltip>,
    dataIndex: "id",
    isShow: true,
    width: 80,
  },

  {
    title: <Tooltip content="承包商名称">承包商名称</Tooltip>,
    dataIndex: "contractor",
    isShow: true,
    ellipsis: true,
    render: (item) => <p>{item?.name}</p>,
  },
  {
    title: <Tooltip content="人员姓名">人员姓名</Tooltip>,
    dataIndex: "contractorEmployee",
    isShow: true,
    ellipsis: true,
    render: (item) => <p>{item?.name}</p>,
  },
  {
    title: <Tooltip content="证书类型">证书类型</Tooltip>,
    dataIndex: "certificateTypeValue",
    isShow: true,
    ellipsis: true,
    render: (r) => <p>{r?.dicValue ?? ""}</p>,
  },
  {
    title: <Tooltip content="证书编码">证书编码</Tooltip>,
    dataIndex: "code",
    isShow: true,
    ellipsis: true,
  },
  /* {
    title: <Tooltip content="发证日期">发证日期</Tooltip>,
    dataIndex: 'issueDate',
    isShow: true,
    ellipsis: true,
    render: (text) => {
      return (
        <p>
          {text ? dayjs(text).format('YYYY-MM-DD') : null}
        </p>
      )
    }
  }, */
  {
    title: <Tooltip content="到期日期">到期日期</Tooltip>,
    dataIndex: "expireDate",
    isShow: true,
    ellipsis: true,
    render: (text) => {
      if (!text) {
        return null;
      }
      const date = dayjs(text);
      const diff = date.diff(dayjs());
      const dateShow = date.format("YYYY-MM-DD");

      return (
        <Tooltip content={dateShow}>
          <p>
            {diff < millisecondsOfOnemonth ? (
              <Tag color="red">{dateShow}</Tag>
            ) : diff < 3 * millisecondsOfOnemonth ? (
              <Tag color="yellow">{dateShow}</Tag>
            ) : (
              <Tag color="white">{dateShow}</Tag>
            )}
          </p>
        </Tooltip>
      );
    },
  },
  {
    title: <Tooltip content="发证机关">发证机关</Tooltip>,
    dataIndex: "authorityTypeValue",
    isShow: true,
    ellipsis: true,
    render: (r) => <p>{r?.dicValue ?? ""}</p>,
  },
  {
    title: <Tooltip content="证书图片">证书图片</Tooltip>,
    dataIndex: "images",
    isShow: true,
    ellipsis: true,
    render: (record) => {
      if (!record) {
        return null;
      }
      let list;
      try {
        list = JSON.parse(record);
        if (list && !Array.isArray(list) && list.values) {
          // 处理脏数据
          list = list.values;
        }
        if (!Array.isArray(list)) {
          list = [];
        }
      } catch (e) {
        list = [];
      }

      return (
        <ImagePreview>
          {list.map((o) => (
            <Image width={30} height={30} src={`${base_url}${o}`} />
          ))}
        </ImagePreview>
      );
    },
  },
  {
    title: "是否有效",
    dataIndex: "isValid",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(VALID_NOTVALID_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
]);
