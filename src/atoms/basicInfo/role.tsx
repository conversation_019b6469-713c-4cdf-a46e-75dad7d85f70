import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { Tooltip } from "@douyinfe/semi-ui";

export const roleEditModal = atomWithReset({
  id: "",
  show: false,
});

export const roleSettingAtom = atomWithReset({
  id: "",
  show: false,
});

export const roleConfigModalAtom = atom(false);
// 查询条件
export const roleFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

// 查询条件
export const roleFnAtom = atom({
  refetch: () => {},
});

export const roleColumnsAtom = atom([
  {
    title: <Tooltip content="ID">ID</Tooltip>,
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  {
    title: <Tooltip content="角色名称">角色名称</Tooltip>,
    dataIndex: "name",
    isShow: true,
    sorter: (a, b) => (a.name.length - b.name.length > 0 ? 1 : -1),
    ellipsis: true,
  },
  {
    title: <Tooltip content="角色描述">角色描述</Tooltip>,
    dataIndex: "description",
    isShow: true,
    ellipsis: true,
    render: (record) => (
      <Tooltip content={record}>
        <p>{record}</p>
      </Tooltip>
    ),
  },
]);
