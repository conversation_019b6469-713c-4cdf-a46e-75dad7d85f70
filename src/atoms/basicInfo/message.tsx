import { Tooltip } from "@douyinfe/semi-ui";
import type { MessageParams } from "api/user";
import dayjs from "dayjs";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";

// 查询条件
export const messageFilterAtom = atomWithReset<MessageParams>({
  pageNumber: 1,
  pageSize: 10,
  filter: {},
});

// 查询条件
export const messageFnAtom = atom({
  refetch: () => {},
});

export enum MessageStatus {
  None,
  Read,
  Unread,
}

export enum EntityType {
  None,
  CheckTask, // 排查任务
  Snap, // 随手拍任务
  Danger, // 隐患任务
}

export const messageColumnsAtom = atom([
  {
    title: <Tooltip content="ID">ID</Tooltip>,
    dataIndex: "id",
    isShow: true,
    fixed: true,
    width: 80,
  },
  /* {
    title: <Tooltip content="模块">模块</Tooltip>,
    dataIndex: 'moduleType',
    isShow: true,
    ellipsis: true,
    width: 120,
    render: (item) => {
      const i = find(propEq((item ? item : 1), 'id'))(MODULE_MAP)
      return (<Tooltip content={i?.name}>
        {i?.name}
      </Tooltip>)
    }
  },
  {
    title: <Tooltip content="任务">任务</Tooltip>,
    dataIndex: 'entityType',
    isShow: true,
    ellipsis: true,
    width: 120,
    render: (item) => {
      const i = find(propEq((item ? item : 1), 'id'))(ENTITY_TYPE_MAP)
      return (<Tooltip content={i?.name}>
        {i?.name}
      </Tooltip>)
    }
  }, */
  {
    title: <Tooltip content="消息">消息</Tooltip>,
    dataIndex: "message",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="时间">时间</Tooltip>,
    dataIndex: "createdAt",
    isShow: true,
    ellipsis: true,
    width: 180,
    render: (t) => {
      const d = dayjs(t).format("YYYY-MM-DD HH:mm");
      return <Tooltip content={d}>{d}</Tooltip>;
    },
  },
]);

export const alarmMessageColumnsAtom = atom([
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    ellipsis: true,
    with: 80,
  },
  /* {
    title: "告警推送名称",
    dataIndex: "",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "监测名称",
    dataIndex: "",
    isShow: true,
    ellipsis: true,
  }, */
  {
    title: "消息",
    dataIndex: "message",
    isShow: true,
    ellipsis: true,
  },
  {
    title: <Tooltip content="时间">时间</Tooltip>,
    dataIndex: "createdAt",
    isShow: true,
    ellipsis: true,
    width: 180,
    render: (t) => {
      const d = dayjs(t).format("YYYY-MM-DD HH:mm");
      return <Tooltip content={d}>{d}</Tooltip>;
    },
  },
]);

export const noticeMessageColumnsAtom = atom([
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    ellipsis: true,
    with: 80,
  },
  /* {
    title: "提醒推送名称",
    dataIndex: "",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "触发事件",
    dataIndex: "",
    isShow: true,
    ellipsis: true,
  }, */
  {
    title: "消息",
    dataIndex: "message",
    isShow: true,
    ellipsis: true,
  },
  {
    title: <Tooltip content="时间">时间</Tooltip>,
    dataIndex: "createdAt",
    isShow: true,
    ellipsis: true,
    width: 180,
    render: (t) => {
      const d = dayjs(t).format("YYYY-MM-DD HH:mm");
      return <Tooltip content={d}>{d}</Tooltip>;
    },
  },
]);
