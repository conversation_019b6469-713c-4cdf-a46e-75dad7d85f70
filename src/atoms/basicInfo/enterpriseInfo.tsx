import { STATUS_MAP } from "components";
import { Tag, Tooltip } from "@douyinfe/semi-ui";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";
import { CommonAtoms } from "types";

export const enterpriseInfoFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const enterpriseInfoFnAtom = atom({
  refetch: () => {},
});

export const enterpriseInfoEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const enterpriseInfoConfigModalAtom = atom(false);

export const enterpriseInfoColumnsAtom = atom([
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  // user-defined code here
]);

export const enterpriseInfoAtoms: CommonAtoms = {
  entity: "EnterpriseInfo",
  filter: enterpriseInfoFilter<PERSON>tom,
  Fn: enterpriseInfoFnAtom,
  editModal: enterpriseInfoEditModalAtom,
  configModal: enterpriseInfoConfigModalAtom,
  columns: enterpriseInfoColumnsAtom,
};
