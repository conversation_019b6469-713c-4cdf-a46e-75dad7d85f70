import { Image, ImagePreview, Tag, Tooltip } from "@douyinfe/semi-ui";
import { EDUCATION_MAP, VALID_NOTVALID_MAP } from "components";
import { base_url } from "config";
import dayjs from "dayjs";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";
import { formatDateDay, millisecondsOfOnemonth } from "utils";
export const certificateEditModal = atom({
  id: "",
  show: false,
});

export const certificateConfigModalAtom = atom(false);
// 查询条件
export const certificateFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

// 查询条件
export const certificateFnAtom = atom({
  refetch: () => {},
});

const certificateColumns = [
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 80,
  },

  {
    title: "部门",
    dataIndex: "department",
    isShow: true,
    ellipsis: true,
    render: (t, record) => <p>{t?.name ?? record?.contractor?.name ?? ""}</p>,
    renderText: (t, record) => t?.name ?? record?.contractor?.name ?? "",
  },
  {
    title: "员工",
    dataIndex: "employee",
    isShow: true,
    ellipsis: true,
    render: (t, record) => (
      <p>{record?.employee?.name ?? record?.contractorEmployee?.name ?? ""}</p>
    ),
    renderText: (t, record) =>
      record?.employee?.name ?? record?.contractorEmployee?.name ?? "",
  },
  {
    title: "证书类型",
    dataIndex: "certificateTypeValue",
    isShow: true,
    ellipsis: true,
    render: (r) => <p>{r?.dicValue ?? ""}</p>,
    renderText: (r) => r?.dicValue ?? "",
  },
  {
    title: "证书编码",
    dataIndex: "code",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "发证日期",
    dataIndex: "issueDate",
    isShow: true,
    ellipsis: true,
    render: (text) => {
      if (!text) {
        return null;
      }
      return (
        <Tooltip content={dayjs(text).format("YYYY-MM-DD")}>
          {dayjs(text).format("YYYY-MM-DD")}
        </Tooltip>
      );
    },
    renderText: (text) => formatDateDay(text),
  },
  {
    title: "到期日期",
    dataIndex: "expireDate",
    isShow: true,
    ellipsis: true,
    render: (text) => {
      if (!text) {
        return null;
      }
      const date = dayjs(text);
      const diff = date.diff(dayjs());
      const dateShow = date.format("YYYY-MM-DD");

      return (
        <Tooltip content={dateShow}>
          <p>
            {diff < millisecondsOfOnemonth ? (
              <Tag color="red">{dateShow}</Tag>
            ) : diff < 3 * millisecondsOfOnemonth ? (
              <Tag color="yellow">{dateShow}</Tag>
            ) : (
              <Tag color="white">{dateShow}</Tag>
            )}
          </p>
        </Tooltip>
      );
    },
    renderText: (text) => formatDateDay(text),
  },
  {
    title: "发证机关类型",
    dataIndex: "authorityTypeValue",
    isShow: true,
    ellipsis: true,
    render: (r) => <p>{r?.dicValue ?? ""}</p>,
    renderText: (r) => r?.dicValue ?? "",
  },
  {
    title: "证书照片",
    dataIndex: "images",
    isShow: true,
    ellipsis: true,
    render: (record) => {
      if (!record) {
        return null;
      }
      let list;
      try {
        list = JSON.parse(record);
        if (list && !Array.isArray(list) && list.values) {
          // 处理脏数据
          list = list.values;
        }
        if (!Array.isArray(list)) {
          list = [];
        }
      } catch (e) {
        list = [];
      }

      return (
        <ImagePreview>
          {list.map((o) => (
            <Image width={30} height={30} src={`${base_url}${o}`} />
          ))}
        </ImagePreview>
      );
    },
  },
  {
    title: "是否有效",
    dataIndex: "isValid",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(VALID_NOTVALID_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
    renderText: (item, record, index) => {
      const i = find(propEq(item, "id"))(VALID_NOTVALID_MAP);
      return i?.name ?? "-";
    },
  },
  {
    title: "学历",
    dataIndex: "education",
    isShow: false,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(record?.employee?.education, "id"))(EDUCATION_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
    renderText: (item, record, index) => {
      const i = find(propEq(record?.employee?.education, "id"))(EDUCATION_MAP);
      return i?.name ?? "-";
    },
  },
  {
    title: "专业",
    dataIndex: "major",
    isShow: false,
    ellipsis: true,
    render: (item, record, index) => {
      const major = record?.employee?.major;
      return (
        <Tooltip content={major ?? "-"}>
          <Tag color={item?.color} type="light">
            {major ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
];

export const certificateColumnsAtom = atom(certificateColumns);

export const certificatePrintColumnsAtom = atom(
  certificateColumns.filter((o) => o.dataIndex !== "images")
);
