import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { CommonAtoms } from "types";

export const basicInfoContractorConfigFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const basicInfoContractorConfigFnAtom = atom({
  refetch: () => {},
});

export const basicInfoContractorConfigEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const basicInfoContractorConfigConfigModalAtom = atom(false);

const basicInfoContractorConfigShowColumns = [
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 48,
  },
  // user-defined code here
];

const basicInfoContractorConfigExtendColumns = [
  // user-defined code here
];

export const basicInfoContractorConfigShowColumnsAtom = atom(
  basicInfoContractorConfigShowColumns
);

export const basicInfoContractorConfigColumnsAtom = atom([
  ...basicInfoContractorConfigShowColumns,
  ...basicInfoContractorConfigExtendColumns,
]);

/*export const basicInfoContractorConfigColumnsAtom = atom(
  (get) => get(basicInfoContractorConfigShowColumnsAtom).concat(get(basicInfoContractorConfigExtendColumnsAtom)),
  (get, set, newValue: any) => {
    set(basicInfoContractorConfigShowColumnsAtom, newValue.slice(0, newValue.length / 2)); // 更新 ShowColumnsAtom
    set(basicInfoContractorConfigExtendColumnsAtom, newValue.slice(newValue.length / 2)); // 更新 ExtendColumnsAtom
  }
)*/

export const basicInfoContractorConfigAtoms: CommonAtoms = {
  entity: "BasicInfoContractorConfig",
  filter: basicInfoContractorConfigFilterAtom,
  Fn: basicInfoContractorConfigFnAtom,
  editModal: basicInfoContractorConfigEditModalAtom,
  configModal: basicInfoContractorConfigConfigModalAtom,
  columns: basicInfoContractorConfigColumnsAtom,
};
