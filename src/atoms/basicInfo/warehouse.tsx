import { Tag, Tooltip } from "@douyinfe/semi-ui";
import {
  FIREHAZARD_LEVEL_MAP,
  IS_ISNOT_MAP,
  WAREHOUSE_STRUCTURE_MAP,
} from "components";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";
import { CommonAtoms } from "types";

export const warehouseFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const warehouseFnAtom = atom({
  refetch: () => {},
});

export const warehouseEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const warehouseConfigModalAtom = atom(false);

export const warehouseColumnsAtom = atom([
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  // user-defined code here
  {
    title: "所属仓库区",
    dataIndex: "warehouseArea",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "库房名称",
    dataIndex: "name",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "库房编号",
    dataIndex: "code",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "是否剧毒化学品仓库",
    dataIndex: "isHighToxic",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item ? item : 1, "id"))(IS_ISNOT_MAP);
      return (
        <Tooltip content={i.name}>
          <Tag color={i.color} type="light">
            {i.name}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "贮存面积(㎡)",
    dataIndex: "area",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "库房结构",
    dataIndex: "structure",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item ? item : 1, "id"))(WAREHOUSE_STRUCTURE_MAP);
      return (
        <Tooltip content={i.name}>
          <Tag color={i.color} type="light">
            {i.name}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "危险货物编号",
    dataIndex: "dangerousGoodsNumber",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "火灾危险性等级",
    dataIndex: "fireHazardLevel",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item ? item : 1, "id"))(FIREHAZARD_LEVEL_MAP);
      return (
        <Tooltip content={i.name}>
          <Tag color={i.color} type="light">
            {i.name}
          </Tag>
        </Tooltip>
      );
    },
  },
]);

export const warehouseAtoms: CommonAtoms = {
  entity: "Warehouse",
  filter: warehouseFilterAtom,
  Fn: warehouseFnAtom,
  editModal: warehouseEditModalAtom,
  configModal: warehouseConfigModalAtom,
  columns: warehouseColumnsAtom,
};
