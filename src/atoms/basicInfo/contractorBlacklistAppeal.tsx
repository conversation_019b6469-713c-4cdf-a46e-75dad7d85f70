import { AUDIT_STATUS_MAP } from "components";
import { base_url } from "config";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";
import { Tag, Tooltip } from "tdesign-react";
import { CommonAtoms } from "types";
import { formatDateDay } from "utils";

export const basicInfoContractorBlacklistAppealFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const basicInfoContractorBlacklistAppealFnAtom = atom({
  refetch: () => {},
});

export const basicInfoContractorBlacklistAppealEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const basicInfoContractorBlacklistAppealAuditModalAtom = atomWithReset({
  record: {},
  show: false,
});

export const basicInfoContractorBlacklistAppealAuditDelegateModalAtom =
  atomWithReset({
    record: {},
    show: false,
  });

export const basicInfoContractorBlacklistAppealConfigModalAtom = atom(false);

const basicInfoContractorBlacklistAppealShowColumns = [
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 48,
  },
  // user-defined code here
  {
    title: "承包商",
    dataIndex: "contractor",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "申诉时间",
    dataIndex: "appealTime",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const content = formatDateDay(item);
      if (!content) {
        return null;
      }
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
  },
  {
    title: "申诉说明",
    dataIndex: "content",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item ?? ""}>
        <p>{item ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "证明材料附件",
    dataIndex: "attachmentList",
    isShow: true,
    ellipsis: true,
    render: (text) => {
      if (!text) {
        return null;
      }
      return (
        <p className="whitespace-nowrap">
          {text.map((item: any) => (
            <p>
              <a
                href={base_url + item}
                className="whitespace-nowrap"
                target="_blank"
                rel="noreferrer"
              >
                {item.split("/").pop()}
              </a>
            </p>
          ))}
        </p>
      );
    },
  },
  {
    title: "审核人候选",
    dataIndex: "auditorList",
    isShow: true,
    ellipsis: true,
    render: (text, record, index) => {
      const content = text
        ? text.map((item, index, array) =>
            index === array.length - 1 ? (
              <span key={index}>{item.name}</span>
            ) : (
              <span key={index}>{item.name},</span>
            )
          )
        : null;
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
  },
  {
    title: "审核人",
    dataIndex: "auditor",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "审核时间",
    dataIndex: "auditTime",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const content = formatDateDay(item);
      if (!content) {
        return null;
      }
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
  },
  {
    title: "审核意见",
    dataIndex: "auditComment",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item ?? ""}>
        <p>{item ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "状态",
    dataIndex: "status",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(AUDIT_STATUS_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
];

const basicInfoContractorBlacklistAppealExtendColumns = [
  // user-defined code here
];

export const basicInfoContractorBlacklistAppealShowColumnsAtom = atom(
  basicInfoContractorBlacklistAppealShowColumns
);

export const basicInfoContractorBlacklistAppealColumnsAtom = atom([
  ...basicInfoContractorBlacklistAppealShowColumns,
  ...basicInfoContractorBlacklistAppealExtendColumns,
]);

/*export const basicInfoContractorBlacklistAppealColumnsAtom = atom(
  (get) => get(basicInfoContractorBlacklistAppealShowColumnsAtom).concat(get(basicInfoContractorBlacklistAppealExtendColumnsAtom)),
  (get, set, newValue: any) => {
    set(basicInfoContractorBlacklistAppealShowColumnsAtom, newValue.slice(0, newValue.length / 2)); // 更新 ShowColumnsAtom
    set(basicInfoContractorBlacklistAppealExtendColumnsAtom, newValue.slice(newValue.length / 2)); // 更新 ExtendColumnsAtom
  }
)*/

export const basicInfoContractorBlacklistAppealAtoms: CommonAtoms = {
  entity: "BasicInfoContractorBlacklistAppeal",
  filter: basicInfoContractorBlacklistAppealFilterAtom,
  Fn: basicInfoContractorBlacklistAppealFnAtom,
  editModal: basicInfoContractorBlacklistAppealEditModalAtom,
  configModal: basicInfoContractorBlacklistAppealConfigModalAtom,
  columns: basicInfoContractorBlacklistAppealColumnsAtom,
};
