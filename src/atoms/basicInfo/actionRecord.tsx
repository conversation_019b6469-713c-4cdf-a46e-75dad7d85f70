import { Tooltip } from "@douyinfe/semi-ui";
import dayjs from "dayjs";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";

export const actionRecordEditModal = atom({
  id: "",
  show: false,
});

// 查询条件
export const actionRecordFilterAtom = atomWithReset<any>({
  pageNumber: 1,
  pageSize: 10,
  filter: {},
});

// 查询条件
export const actionRecordFnAtom = atom({
  refetch: () => {},
});

export const actionRecordColumnsAtom = atom([
  {
    title: <Tooltip content="ID">ID</Tooltip>,
    dataIndex: "id",
    fixed: true,
    width: 80,
  },
  {
    title: <Tooltip content="工号">工号</Tooltip>,
    dataIndex: "employee_id",
    ellipsis: true,
  },
  {
    title: <Tooltip content="登录IP">登录IP</Tooltip>,
    dataIndex: "ip",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="登录UA">登录UA</Tooltip>,
    dataIndex: "ua",
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="登录APP版本">登录APP版本</Tooltip>,
    dataIndex: "appVersion",
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="提交内容">提交内容</Tooltip>,
    dataIndex: "submitContent",
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="操作内容">操作内容</Tooltip>,
    dataIndex: "operationContent",
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="操作时间">操作时间</Tooltip>,
    dataIndex: "operationTime",
    isShow: true,
    ellipsis: true,
    render: (t) => {
      const d = dayjs(t).format("YYYY-MM-DD HH:mm:ss");
      return <Tooltip content={d}>{d}</Tooltip>;
    },
  },
]);
