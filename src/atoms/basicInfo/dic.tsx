import { Tooltip } from "@douyinfe/semi-ui";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";

export const dicItemEditModal = atom({
  id: "",
  show: false,
});

export const dicItemValuesEditModal = atomWithReset({
  id: "",
  name: "",
  show: false,
});

export const dicItemValueConfigModalAtom = atom(false);
// 查询条件
export const dicItemValueFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

// 查询条件
export const dicItemValueFnAtom = atom({
  refetch: () => {},
});

export const dicItemValueColumnsAtom = atom([
  {
    title: <Tooltip content="ID">ID</Tooltip>,
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  /* {
    title: <Tooltip content="所属模块">所属模块</Tooltip>,
    dataIndex: 'moduleType',
    isShow: true,
    ellipsis: true,
    render: (record) => {
      const item = find(propEq(record, 'id'))(DIC_MODULETYPE)
      return (
        <Tag color="blue" type="light">
          {item?.name ?? '-'}
        </Tag>
      )
    }
  }, */

  {
    title: <Tooltip content="字典名称">名称</Tooltip>,
    dataIndex: "name",
    isShow: true,
    ellipsis: true,
  },
  {
    title: <Tooltip content="中文名称">中文名称</Tooltip>,
    dataIndex: "cname",
    isShow: true,
    ellipsis: true,
  },
  {
    title: <Tooltip content="字典描述">描述</Tooltip>,
    dataIndex: "comment",
    isShow: true,
    ellipsis: true,
    render: (r) => {
      return <Tooltip content={r}>{r}</Tooltip>;
    },
  },
]);
