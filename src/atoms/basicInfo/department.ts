import { atom } from "jotai";
import { ButtonGroup, Button } from "@douyinfe/semi-ui";
import type { GetPositionParams } from "api/basicInfo";
import { atomWithReset } from "jotai/utils";

export const departmentEditModalAtom = atom({
  id: "",
  show: false,
});

// 选中项
export const departmentSelectAtom = atomWithReset<number>(0);

// 全部数据存储
export const departmentDataAtom = atom<any[]>([]);

// 刷新
export const departmentFnAtom = atom({
  refetch: () => {},
});

export const departmentColumnsAtom = atom([
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  {
    title: "部门名称",
    dataIndex: "name",
    isShow: true,
    sorter: (a, b) => (a.name.length - b.name.length > 0 ? 1 : -1),
    ellipsis: true,
  },
]);
