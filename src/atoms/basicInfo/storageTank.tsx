import { Tag, Tooltip } from "@douyinfe/semi-ui";
import {
  FIREHAZARD_LEVEL_MAP,
  STORAGE_TANK_FORMAT_MAP,
  STORAGE_TANK_MATERIAL_MAP,
} from "components";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";
import { CommonAtoms } from "types";

export const storageTankFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const storageTankFnAtom = atom({
  refetch: () => {},
});

export const storageTankEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const storageTankConfigModalAtom = atom(false);

export const storageTankColumnsAtom = atom([
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  // user-defined code here
  {
    title: "所属储罐区",
    dataIndex: "storageTankArea",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "储罐名称",
    dataIndex: "name",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "储罐编号",
    dataIndex: "code",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "储罐形式",
    dataIndex: "format",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item ? item : 1, "id"))(STORAGE_TANK_FORMAT_MAP);
      return <Tooltip content={i.name}>{i.name}</Tooltip>;
    },
  },
  {
    title: "储罐材质",
    dataIndex: "material",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item ? item : 1, "id"))(STORAGE_TANK_MATERIAL_MAP);
      return <Tooltip content={i.name}>{i.name}</Tooltip>;
    },
  },
  {
    title: "储存物质名称",
    dataIndex: "materialName",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "危险货物编号",
    dataIndex: "dangerousGoodsNumber",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "火灾危险性等级",
    dataIndex: "fireHazardLevel",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item ? item : 1, "id"))(FIREHAZARD_LEVEL_MAP);
      return (
        <Tooltip content={i.name}>
          <Tag color={i.color} type="light">
            {i.name}
          </Tag>
        </Tooltip>
      );
    },
  },
]);

export const storageTankAtoms: CommonAtoms = {
  entity: "StorageTank",
  filter: storageTankFilterAtom,
  Fn: storageTankFnAtom,
  editModal: storageTankEditModalAtom,
  configModal: storageTankConfigModalAtom,
  columns: storageTankColumnsAtom,
};
