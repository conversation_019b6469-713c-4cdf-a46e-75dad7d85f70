import { Tag, Tooltip } from "@douyinfe/semi-ui";
import { base_url } from "config";
import dayjs from "dayjs";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { CommonAtoms } from "types";
import { formatDateDay, millisecondsOfOnemonth } from "utils";

export const contractorCertificateFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const contractorCertificateFnAtom = atom({
  refetch: () => {},
});

export const contractorCertificateEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const contractorCertificateConfigModalAtom = atom(false);

export const contractorCertificateColumnsAtom = atom([
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  {
    title: "承包商名称",
    dataIndex: "contractor",
    isShow: true,
    ellipsis: true,
    render: (item) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "资质证书名称",
    dataIndex: "name",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "资质证书编号",
    dataIndex: "code",
    isShow: true,
  },
  {
    title: "发证日期",
    dataIndex: "issueDate",
    isShow: true,
    render: (item) => {
      const content = formatDateDay(item);
      if (!content) {
        return null;
      }
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
  },
  {
    title: "到期日期",
    dataIndex: "expireDate",
    isShow: true,
    render: (text) => {
      if (!text) {
        return null;
      }
      const date = dayjs(text);
      const diff = date.diff(dayjs());
      const dateShow = date.format("YYYY-MM-DD");

      return (
        <Tooltip content={dateShow}>
          <p>
            {diff < millisecondsOfOnemonth ? (
              <Tag color="red">{dateShow}</Tag>
            ) : diff < 3 * millisecondsOfOnemonth ? (
              <Tag color="yellow">{dateShow}</Tag>
            ) : (
              <Tag color="white">{dateShow}</Tag>
            )}
          </p>
        </Tooltip>
      );
    },
  },
  {
    title: "许可内容",
    dataIndex: "permitContent",
    isShow: true,
  },
  {
    title: "资质证书文件",
    dataIndex: "attachmentList",
    isShow: true,
    render: (text) => {
      if (!text) {
        return null;
      }
      return (
        <p className="whitespace-nowrap">
          {text.map((item: any) => (
            <p>
              <a
                href={base_url + item}
                className="whitespace-nowrap"
                target="_blank"
                rel="noreferrer"
              >
                {item.split("/").pop()}
              </a>
            </p>
          ))}
        </p>
      );
    },
  },
]);

export const contractorCertificateAtoms: CommonAtoms = {
  entity: "ContractorCertificate",
  filter: contractorCertificateFilterAtom,
  Fn: contractorCertificateFnAtom,
  editModal: contractorCertificateEditModalAtom,
  configModal: contractorCertificateConfigModalAtom,
  columns: contractorCertificateColumnsAtom,
};
