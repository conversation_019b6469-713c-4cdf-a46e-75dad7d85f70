import { AUDIT_STATUS_MAP } from "components";
import { base_url } from "config";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";
import { Tag, Tooltip } from "tdesign-react";
import { CommonAtoms } from "types";
import { formatDateDay } from "utils";

export const basicInfoContractorProjectResumptionFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const basicInfoContractorProjectResumptionFnAtom = atom({
  refetch: () => {},
});

export const basicInfoContractorProjectResumptionEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const basicInfoContractorProjectResumptionAuditModalAtom = atomWithReset(
  {
    record: {},
    show: false,
  }
);

export const basicInfoContractorProjectResumptionAuditDelegateModalAtom =
  atomWithReset({
    record: {},
    show: false,
  });

export const basicInfoContractorProjectResumptionConfigModalAtom = atom(false);

const basicInfoContractorProjectResumptionShowColumns = [
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 48,
  },
  // user-defined code here
  {
    title: "承包商",
    dataIndex: "contractor",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "项目",
    dataIndex: "project",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "状态",
    dataIndex: "status",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(AUDIT_STATUS_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "停工原因类型",
    dataIndex: "contractorProjectSuspensionTypeValue",
    isShow: true,
    render: (r) => (
      <Tooltip content={r?.dicValue ?? ""}>
        <p>{r?.dicValue ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "整改情况说明",
    dataIndex: "rectificationContent",
    isShow: true,
    ellipsis: true,
    render: (text) => {
      return (
        <Tooltip content={text}>
          <span className="block truncate">{text}</span>
        </Tooltip>
      );
    },
  },
  {
    title: "培训情况说明",
    dataIndex: "educationContent",
    isShow: true,
    ellipsis: true,
    render: (text) => {
      return (
        <Tooltip content={text}>
          <span className="block truncate">{text}</span>
        </Tooltip>
      );
    },
  },
  {
    title: "申请时间",
    dataIndex: "applyTime",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const content = formatDateDay(item);
      if (!content) {
        return null;
      }
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
  },
  {
    title: "申请人",
    dataIndex: "applyPerson",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "候选审核员",
    dataIndex: "auditorList",
    isShow: true,
    ellipsis: true,
    render: (text, record, index) => {
      const content = text
        ? text.map((item, index, array) =>
            index === array.length - 1 ? (
              <span key={index}>{item.name}</span>
            ) : (
              <span key={index}>{item.name},</span>
            )
          )
        : null;
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
  },
  {
    title: "审核员",
    dataIndex: "auditor",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "审核时间",
    dataIndex: "auditTime",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const content = formatDateDay(item);
      if (!content) {
        return null;
      }
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
  },
  {
    title: "审核理由",
    dataIndex: "auditComment",
    isShow: true,
    ellipsis: true,
    render: (text) => {
      return (
        <Tooltip content={text}>
          <span className="block truncate">{text}</span>
        </Tooltip>
      );
    },
  },
  {
    title: "证明材料附件",
    dataIndex: "attachmentList",
    isShow: true,
    render: (text) => {
      if (!text) {
        return null;
      }
      return (
        <p className="whitespace-nowrap">
          {text.map((item: any) => (
            <p>
              <a
                href={base_url + item}
                className="whitespace-nowrap"
                target="_blank"
                rel="noreferrer"
              >
                {item.split("/").pop()}
              </a>
            </p>
          ))}
        </p>
      );
    },
  },
];

const basicInfoContractorProjectResumptionExtendColumns = [
  // user-defined code here
];

export const basicInfoContractorProjectResumptionShowColumnsAtom = atom(
  basicInfoContractorProjectResumptionShowColumns
);

export const basicInfoContractorProjectResumptionColumnsAtom = atom([
  ...basicInfoContractorProjectResumptionShowColumns,
  ...basicInfoContractorProjectResumptionExtendColumns,
]);

/*export const basicInfoContractorProjectResumptionColumnsAtom = atom(
  (get) => get(basicInfoContractorProjectResumptionShowColumnsAtom).concat(get(basicInfoContractorProjectResumptionExtendColumnsAtom)),
  (get, set, newValue: any) => {
    set(basicInfoContractorProjectResumptionShowColumnsAtom, newValue.slice(0, newValue.length / 2)); // 更新 ShowColumnsAtom
    set(basicInfoContractorProjectResumptionExtendColumnsAtom, newValue.slice(newValue.length / 2)); // 更新 ExtendColumnsAtom
  }
)*/

export const basicInfoContractorProjectResumptionAtoms: CommonAtoms = {
  entity: "BasicInfoContractorProjectResumption",
  filter: basicInfoContractorProjectResumptionFilterAtom,
  Fn: basicInfoContractorProjectResumptionFnAtom,
  editModal: basicInfoContractorProjectResumptionEditModalAtom,
  configModal: basicInfoContractorProjectResumptionConfigModalAtom,
  columns: basicInfoContractorProjectResumptionColumnsAtom,
};
