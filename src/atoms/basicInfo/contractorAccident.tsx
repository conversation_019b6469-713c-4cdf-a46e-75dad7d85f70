import { Tooltip } from "@douyinfe/semi-ui";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { CommonAtoms } from "types";
import { formatDate } from "utils";

export const contractorAccidentFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const contractorAccidentFnAtom = atom({
  refetch: () => {},
});

export const contractorAccidentEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const contractorAccidentConfigModalAtom = atom(false);

export const contractorAccidentColumnsAtom = atom([
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  {
    title: "事故名称",
    dataIndex: "name",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "承包商",
    dataIndex: "contractor",
    isShow: true,
    ellipsis: true,
    render: (item) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "事故类型",
    dataIndex: "type",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "事故级别",
    dataIndex: "level",
    isShow: true,
  },
  {
    title: <Tooltip content="发生时间">发生时间</Tooltip>,
    dataIndex: "happenTime",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const content = formatDate(item);
      if (!content) {
        return null;
      }
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
  },
  {
    title: "发生地点",
    dataIndex: "happenPlace",
    isShow: true,
  },
]);

export const contractorAccidentAtoms: CommonAtoms = {
  entity: "ContractorAccident",
  filter: contractorAccidentFilterAtom,
  Fn: contractorAccidentFnAtom,
  editModal: contractorAccidentEditModalAtom,
  configModal: contractorAccidentConfigModalAtom,
  columns: contractorAccidentColumnsAtom,
};
