import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { Tooltip } from "tdesign-react";
import { CommonAtoms } from "types";
import { formatDate } from "utils";

export const basicInfoContractorProjectStopFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const basicInfoContractorProjectStopFnAtom = atom({
  refetch: () => {},
});

export const basicInfoContractorProjectStopEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const basicInfoContractorProjectStopConfigModalAtom = atom(false);

const basicInfoContractorProjectStopShowColumns = [
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 48,
  },
  {
    title: "项目",
    dataIndex: "project",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "承包商",
    dataIndex: "contractor",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "项目停工原因",
    dataIndex: "contractorProjectSuspensionTypeValue",
    isShow: true,
    ellipsis: true,
    render: (r) => (
      <Tooltip content={r?.dicValue ?? ""}>
        <p>{r?.dicValue ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "操作时间",
    dataIndex: "operationTime",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const content = formatDate(item);
      if (!content) {
        return null;
      }
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
  },
  {
    title: "操作人",
    dataIndex: "operationPerson",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  // user-defined code here
];

const basicInfoContractorProjectStopExtendColumns = [
  // user-defined code here
];

export const basicInfoContractorProjectStopShowColumnsAtom = atom(
  basicInfoContractorProjectStopShowColumns
);

export const basicInfoContractorProjectStopColumnsAtom = atom([
  ...basicInfoContractorProjectStopShowColumns,
  ...basicInfoContractorProjectStopExtendColumns,
]);

/*export const basicInfoContractorProjectStopColumnsAtom = atom(
  (get) => get(basicInfoContractorProjectStopShowColumnsAtom).concat(get(basicInfoContractorProjectStopExtendColumnsAtom)),
  (get, set, newValue: any) => {
    set(basicInfoContractorProjectStopShowColumnsAtom, newValue.slice(0, newValue.length / 2)); // 更新 ShowColumnsAtom
    set(basicInfoContractorProjectStopExtendColumnsAtom, newValue.slice(newValue.length / 2)); // 更新 ExtendColumnsAtom
  }
)*/

export const basicInfoContractorProjectStopAtoms: CommonAtoms = {
  entity: "BasicInfoContractorProjectStop",
  filter: basicInfoContractorProjectStopFilterAtom,
  Fn: basicInfoContractorProjectStopFnAtom,
  editModal: basicInfoContractorProjectStopEditModalAtom,
  configModal: basicInfoContractorProjectStopConfigModalAtom,
  columns: basicInfoContractorProjectStopColumnsAtom,
};
