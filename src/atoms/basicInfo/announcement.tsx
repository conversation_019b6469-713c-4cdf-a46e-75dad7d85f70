import dayjs from "dayjs";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { Tooltip } from "tdesign-react";
import { CommonAtoms } from "types";
import { formatDateDay } from "utils";

export const announcementFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const announcementFnAtom = atom({
  refetch: () => {},
});

export const announcementEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const announcementConfigModalAtom = atom(false);

const announcementShowColumns = [
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  // user-defined code here
  {
    title: "公告标题",
    dataIndex: "title",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "展示时间",
    dataIndex: "beginTime",
    isShow: true,
    ellipsis: true,
    width: 200,
    render: (text, record, index) => {
      return (
        <p>
          {text ? dayjs(text).format("YYYY-MM-DD hh:mm:ss") : ""}
          <br />
          {record.endTime
            ? ` ~ ${dayjs(record.endTime).format("YYYY-MM-DD hh:mm:ss")}`
            : ""}
        </p>
      );
    },
  },
  {
    title: "发布部门",
    dataIndex: "publishDepartment",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "发布日期",
    dataIndex: "publishTime",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const content = formatDateDay(item);
      if (!content) {
        return null;
      }
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
  },
];

const announcementExtendColumns = [
  // user-defined code here
];

export const announcementColumnsAtom = atom([
  ...announcementShowColumns,
  ...announcementExtendColumns,
]);

/*export const announcementColumnsAtom = atom(
  (get) => get(announcementShowColumnsAtom).concat(get(announcementExtendColumnsAtom)),
  (get, set, newValue: any) => {
    set(announcementShowColumnsAtom, newValue.slice(0, newValue.length / 2)); // 更新 ShowColumnsAtom
    set(announcementExtendColumnsAtom, newValue.slice(newValue.length / 2)); // 更新 ExtendColumnsAtom
  }
)*/

export const announcementAtoms: CommonAtoms = {
  entity: "Announcement",
  filter: announcementFilterAtom,
  Fn: announcementFnAtom,
  editModal: announcementEditModalAtom,
  configModal: announcementConfigModalAtom,
  columns: announcementColumnsAtom,
};
