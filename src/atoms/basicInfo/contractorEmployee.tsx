import { Image, ImagePreview, Tag, Tooltip } from "@douyinfe/semi-ui";
import { BIM_CONTRACTOR_ISBLACK_MAP, CONTRACTOR_STATUS_MAP } from "components";
import { base_url } from "config";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";
import { CommonAtoms } from "types";

export const contractorEmployeeFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const contractorEmployeeFnAtom = atom({
  refetch: () => {},
});

export const contractorEmployeeEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const contractorEmployeeConfigModalAtom = atom(false);

export const contractorEmployeeBlackModalAtom = atomWithReset({
  employee: {},
  show: false,
});

export const contractorEmployeeColumnsAtom = atom([
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  {
    title: "承包商名称",
    dataIndex: "contractor",
    isShow: true,
    ellipsis: true,
    render: (item) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "姓名",
    dataIndex: "name",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "手机号码",
    dataIndex: "mobile",
    isShow: true,
  },
  {
    title: "身份证号",
    dataIndex: "idNumber",
    isShow: true,
  },
  {
    title: <Tooltip content="人员定位卡">人员定位卡</Tooltip>,
    dataIndex: "locationMac",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "拥有证书数量",
    dataIndex: "certificateCount",
    isShow: true,
  },
  {
    title: "身份证照片",
    dataIndex: "idCardImageList",
    isShow: true,
    ellipsis: true,
    render: (record) => {
      if (!record) {
        return null;
      }
      let list;
      if (typeof record === "string") {
        try {
          list = JSON.parse(record);
          if (list && !Array.isArray(list) && list.values) {
            // 处理脏数据
            list = list.values;
          }
          if (!Array.isArray(list)) {
            list = [];
          }
        } catch (e) {
          list = [];
        }
      } else if (Array.isArray(record)) {
        list = record;
      }

      console.debug("list", list);

      return (
        <ImagePreview>
          {list.map((o) => (
            <Image width={30} height={30} src={`${base_url}${o}`} />
          ))}
        </ImagePreview>
      );
    },
  },
  {
    title: "安责险照片",
    dataIndex: "insuranceImageList",
    isShow: true,
    ellipsis: true,
    render: (record) => {
      if (!record) {
        return null;
      }
      let list;
      if (typeof record === "string") {
        try {
          list = JSON.parse(record);
          if (list && !Array.isArray(list) && list.values) {
            // 处理脏数据
            list = list.values;
          }
          if (!Array.isArray(list)) {
            list = [];
          }
        } catch (e) {
          list = [];
        }
      } else if (Array.isArray(record)) {
        list = record;
      }

      return (
        <ImagePreview>
          {list.map((o) => (
            <Image width={30} height={30} src={`${base_url}${o}`} />
          ))}
        </ImagePreview>
      );
    },
  },
  {
    title: "备注",
    dataIndex: "comment",
    isShow: true,
  },
  {
    title: "是否黑名单",
    dataIndex: "isBlack",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item ? item : 1, "id"))(BIM_CONTRACTOR_ISBLACK_MAP);
      return (
        <Tooltip content={i.name}>
          <Tag color={i.color} type="light">
            {i.name}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "状态",
    dataIndex: "status",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const i = find(propEq(item ? item : 1, "id"))(CONTRACTOR_STATUS_MAP);
      return (
        <Tooltip content={i.name}>
          <Tag color={i.color} type="light">
            {i.name}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "拉黑原因",
    dataIndex: "blackReason",
    isShow: false,
    ellipsis: true,
  },
]);

export const contractorEmployeeAtoms: CommonAtoms = {
  entity: "ContractorEmployee",
  filter: contractorEmployeeFilterAtom,
  Fn: contractorEmployeeFnAtom,
  editModal: contractorEmployeeEditModalAtom,
  configModal: contractorEmployeeConfigModalAtom,
  columns: contractorEmployeeColumnsAtom,
};
