import { Tag, Tooltip } from "@douyinfe/semi-ui";
import { IS_ISNOT_STOP_MAP } from "components";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";
import { CommonAtoms } from "types";
import { formatDateDay } from "utils";

export const contractorProjectFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const contractorProjectFnAtom = atom({
  refetch: () => {},
});

export const contractorProjectEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const contractorProjectOpenStopModalAtom = atomWithReset({
  record: {},
  show: false,
});

export const contractorProjectConfigModalAtom = atom(false);

export const contractorProjectColumnsAtom = atom([
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  {
    title: "承包商",
    dataIndex: "contractor",
    isShow: true,
    ellipsis: true,
    render: (item) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "项目名称",
    dataIndex: "name",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "项目编号",
    dataIndex: "code",
    isShow: true,
  },
  {
    title: "项目类型",
    dataIndex: "type",
    isShow: true,
  },
  {
    title: "项目负责人",
    dataIndex: "liablePerson",
    isShow: true,
    ellipsis: true,
    render: (item) => (
      <Tooltip content={item?.name ?? "xx"}>
        <p>{item?.name ?? "xx"}</p>
      </Tooltip>
    ),
  },
  {
    title: "是否停工",
    dataIndex: "isStop",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const i = find(propEq(item, "id"))(IS_ISNOT_STOP_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color ?? ""} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: <Tooltip content="开始日期">开始日期</Tooltip>,
    dataIndex: "beginTime",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const content = formatDateDay(item);
      if (!content) {
        return null;
      }
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
  },
  {
    title: <Tooltip content="结束日期">结束日期</Tooltip>,
    dataIndex: "endTime",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const content = formatDateDay(item);
      if (!content) {
        return null;
      }
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
  },
]);

export const contractorProjectAtoms: CommonAtoms = {
  entity: "ContractorProject",
  filter: contractorProjectFilterAtom,
  Fn: contractorProjectFnAtom,
  editModal: contractorProjectEditModalAtom,
  configModal: contractorProjectConfigModalAtom,
  columns: contractorProjectColumnsAtom,
};
