import { AUDIT_STATUS_MAP } from "components";
import { base_url } from "config";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";
import { Tag, Tooltip } from "tdesign-react";
import { CommonAtoms } from "types";
import { formatDate } from "utils";

export const basicInfoContractorEntryApplyFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const basicInfoContractorEntryApplyFnAtom = atom({
  refetch: () => {},
});

export const basicInfoContractorEntryApplyEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const basicInfoContractorEntryApplyAuditModalAtom = atomWithReset({
  record: {},
  show: false,
});

export const basicInfoContractorEntryApplyAuditDelegateModalAtom =
  atomWithReset({
    record: {},
    show: false,
  });

export const basicInfoContractorEntryApplyConfigModalAtom = atom(false);

const basicInfoContractorEntryApplyShowColumns = [
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 56,
  },
  {
    title: "承包商",
    dataIndex: "contractor",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "承包商员工",
    dataIndex: "contractorEmployee",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "入厂时间",
    dataIndex: "entryTime",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const content = formatDate(item);
      if (!content) {
        return null;
      }
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
  },
  {
    title: "入厂原因",
    dataIndex: "entryReason",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item ?? ""}>
        <p>{item ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "入厂携带物品",
    dataIndex: "goods",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item ?? ""}>
        <p>{item ?? ""}</p>
      </Tooltip>
    ),
  },
  /* {
    title: "申请时间",
    dataIndex: "applyTime",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const content = formatDate(item);
      if (!content) {
        return null;
      }
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
  },
  {
    title: "申请人",
    dataIndex: "applyPerson",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      let name = item?.name ?? "";
      record?.type === 2 ? (name = name + "(承包商)") : (name = name);
      return (
        <Tooltip content={name}>
          <p>{name}</p>
        </Tooltip>
      );
    },
  }, */
  /* {
    title: "人员类型",
    dataIndex: "type",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(UNIT_CATEGORY_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  }, */
  {
    title: "附件路径列表",
    dataIndex: "attachmentList",
    isShow: true,
    ellipsis: true,
    render: (text) => {
      if (!text) {
        return null;
      }
      return (
        <p className="whitespace-nowrap">
          {text.map((item: any) => (
            <p>
              <a
                href={base_url + item}
                className="whitespace-nowrap"
                target="_blank"
                rel="noreferrer"
              >
                {item.split("/").pop()}
              </a>
            </p>
          ))}
        </p>
      );
    },
  },
  {
    title: "审核员列表",
    dataIndex: "auditorList",
    isShow: true,
    ellipsis: true,
    render: (text, record, index) => {
      const content = text
        ? text.map((item, index, array) =>
            index === array.length - 1 ? (
              <span key={index}>{item.name}</span>
            ) : (
              <span key={index}>{item.name},</span>
            )
          )
        : null;
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
  },
  {
    title: "审核员",
    dataIndex: "auditor",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "审核时间",
    dataIndex: "auditTime",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const content = formatDate(item);
      if (!content) {
        return null;
      }
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
  },
  {
    title: "审核理由",
    dataIndex: "auditComment",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item ?? ""}>
        <p>{item ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "状态",
    dataIndex: "status",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(AUDIT_STATUS_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
];

const basicInfoContractorEntryApplyExtendColumns = [
  // user-defined code here
];

export const basicInfoContractorEntryApplyShowColumnsAtom = atom(
  basicInfoContractorEntryApplyShowColumns
);

export const basicInfoContractorEntryApplyColumnsAtom = atom([
  ...basicInfoContractorEntryApplyShowColumns,
  ...basicInfoContractorEntryApplyExtendColumns,
]);

/*export const basicInfoContractorEntryApplyColumnsAtom = atom(
  (get) => get(basicInfoContractorEntryApplyShowColumnsAtom).concat(get(basicInfoContractorEntryApplyExtendColumnsAtom)),
  (get, set, newValue: any) => {
    set(basicInfoContractorEntryApplyShowColumnsAtom, newValue.slice(0, newValue.length / 2)); // 更新 ShowColumnsAtom
    set(basicInfoContractorEntryApplyExtendColumnsAtom, newValue.slice(newValue.length / 2)); // 更新 ExtendColumnsAtom
  }
)*/

export const basicInfoContractorEntryApplyAtoms: CommonAtoms = {
  entity: "BasicInfoContractorEntryApply",
  filter: basicInfoContractorEntryApplyFilterAtom,
  Fn: basicInfoContractorEntryApplyFnAtom,
  editModal: basicInfoContractorEntryApplyEditModalAtom,
  configModal: basicInfoContractorEntryApplyConfigModalAtom,
  columns: basicInfoContractorEntryApplyColumnsAtom,
};
