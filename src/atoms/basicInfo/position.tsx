import React from "react";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { ButtonGroup, Button, Tooltip } from "@douyinfe/semi-ui";
import type { GetPositionParams } from "api/basicInfo";

export const positionEditModal = atom({
  id: "",
  show: false,
});

export const showBatchModalAtom = atom(false);

// 查询条件
export const positionFilterAtom = atomWithReset<GetPositionParams>({
  pageNumber: 1,
  pageSize: 10,
});

// 查询条件
export const positionFnAtom = atom({
  refetch: () => {},
});

export const columnsAtom = atom([
  {
    title: <Tooltip content="ID">ID</Tooltip>,
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  {
    title: <Tooltip content="岗位名称">岗位名称</Tooltip>,
    dataIndex: "name",
    isShow: true,
    sorter: (a, b) => (a.name.length - b.name.length > 0 ? 1 : -1),
    ellipsis: true,
  },
]);
