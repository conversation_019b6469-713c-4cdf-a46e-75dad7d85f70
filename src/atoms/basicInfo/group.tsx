import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { Tag, Tooltip } from "@douyinfe/semi-ui";
import { find, propEq } from "ramda";
import type { GroupParams } from "api/basicInfo";
import { LOGIN_STATUS_MAP, STATUS_MAP } from "components";

export const groupEditModal = atom({
  id: "",
  show: false,
});

// 查询条件
export const groupFilterAtom = atomWithReset<GroupParams>({
  pageNumber: 1,
  pageSize: 10,
  filter: {},
});

// 查询条件
export const groupFnAtom = atom({
  refetch: () => {},
});

export const groupColumnsAtom = atom([
  {
    title: <Tooltip content="ID">ID</Tooltip>,
    dataIndex: "id",
    isShow: true,
    fixed: true,
    width: 80,
  },
  {
    title: <Tooltip content="工作组名称">工作组名称</Tooltip>,
    dataIndex: "name",
    isShow: true,
    ellipsis: true,
    fixed: true,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="成员列表">成员列表</Tooltip>,
    dataIndex: "members",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      return (
        <Tooltip content={(item ?? []).map((i: any) => i.name).join("、")}>
          <div className="flex gap-2">
            {(item ?? []).map((i: any) => (
              <Tag type="light" key={i.id} className="min-w-fit">
                {i.name}
              </Tag>
            ))}
          </div>
        </Tooltip>
      );
    },
  },
  {
    title: <Tooltip content="备注">备注</Tooltip>,
    dataIndex: "note",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
]);
