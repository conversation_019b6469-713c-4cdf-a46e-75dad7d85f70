import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { Tooltip } from "tdesign-react";
import { CommonAtoms } from "types";
import { formatDate } from "utils";

export const basicInfoContractorBlacklistAddFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const basicInfoContractorBlacklistAddFnAtom = atom({
  refetch: () => {},
});

export const basicInfoContractorBlacklistAddEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const basicInfoContractorBlacklistAddConfigModalAtom = atom(false);

const basicInfoContractorBlacklistAddShowColumns = [
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 48,
  },
  {
    title: "承包商",
    dataIndex: "contractor",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "拉黑原因",
    dataIndex: "blackReason",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item ?? ""}>
        <p>{item ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "操作时间",
    dataIndex: "operationTime",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const content = formatDate(item);
      if (!content) {
        return null;
      }
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
  },
  {
    title: "操作人",
    dataIndex: "operationPerson",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  // user-defined code here
];

const basicInfoContractorBlacklistAddExtendColumns = [
  // user-defined code here
];

export const basicInfoContractorBlacklistAddShowColumnsAtom = atom(
  basicInfoContractorBlacklistAddShowColumns
);

export const basicInfoContractorBlacklistAddColumnsAtom = atom([
  ...basicInfoContractorBlacklistAddShowColumns,
  ...basicInfoContractorBlacklistAddExtendColumns,
]);

/*export const basicInfoContractorBlacklistAddColumnsAtom = atom(
  (get) => get(basicInfoContractorBlacklistAddShowColumnsAtom).concat(get(basicInfoContractorBlacklistAddExtendColumnsAtom)),
  (get, set, newValue: any) => {
    set(basicInfoContractorBlacklistAddShowColumnsAtom, newValue.slice(0, newValue.length / 2)); // 更新 ShowColumnsAtom
    set(basicInfoContractorBlacklistAddExtendColumnsAtom, newValue.slice(newValue.length / 2)); // 更新 ExtendColumnsAtom
  }
)*/

export const basicInfoContractorBlacklistAddAtoms: CommonAtoms = {
  entity: "BasicInfoContractorBlacklistAdd",
  filter: basicInfoContractorBlacklistAddFilterAtom,
  Fn: basicInfoContractorBlacklistAddFnAtom,
  editModal: basicInfoContractorBlacklistAddEditModalAtom,
  configModal: basicInfoContractorBlacklistAddConfigModalAtom,
  columns: basicInfoContractorBlacklistAddColumnsAtom,
};
