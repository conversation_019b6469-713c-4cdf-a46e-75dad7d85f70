import { Tooltip } from "@douyinfe/semi-ui";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { CommonAtoms } from "types";

export const warehouseAreaFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const warehouseAreaFnAtom = atom({
  refetch: () => {},
});

export const warehouseAreaEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const warehouseAreaConfigModalAtom = atom(false);

export const warehouseAreaColumnsAtom = atom([
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  // user-defined code here
  {
    title: "仓库区名称",
    dataIndex: "name",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "仓库区编号",
    dataIndex: "code",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "仓库区面积(平方米)", //TODO 平方米
    dataIndex: "area",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "库房个数",
    dataIndex: "warehouseNum",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "相邻库房最小间距(平方米)", //TODO 平方米
    dataIndex: "minDistance",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "所属重大危险源",
    dataIndex: "majorHazard",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
]);

export const warehouseAreaAtoms: CommonAtoms = {
  entity: "WarehouseArea",
  filter: warehouseAreaFilterAtom,
  Fn: warehouseAreaFnAtom,
  editModal: warehouseAreaEditModalAtom,
  configModal: warehouseAreaConfigModalAtom,
  columns: warehouseAreaColumnsAtom,
};
