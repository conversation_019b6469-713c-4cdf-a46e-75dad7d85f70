import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { Tooltip, Tag } from "@douyinfe/semi-ui";

export const dicModal = atom({
  id: "",
  show: false,
});

export const dicValueConfigModalAtom = atom(false);
// 查询条件
export const dicValueFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
  filter: {},
});

// 查询条件
export const dicValueFnAtom = atom({
  refetch: () => {},
});

export const dicValueColumnsAtom = atom([
  {
    title: <Tooltip content="ID">ID</Tooltip>,
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  {
    title: <Tooltip content="字典索引">字典索引</Tooltip>,
    dataIndex: "dicKey",
    isShow: true,
    ellipsis: true,
  },
  {
    title: <Tooltip content="字典值">字典值</Tooltip>,
    dataIndex: "dicValue",
    isShow: true,
    ellipsis: true,
  },
]);
