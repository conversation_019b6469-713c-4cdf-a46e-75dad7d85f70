import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { Tooltip } from "tdesign-react";
import { CommonAtoms } from "types";
import { formatDateDay } from "utils";

export const basicInfoContractorViolationFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const basicInfoContractorViolationFnAtom = atom({
  refetch: () => {},
});

export const basicInfoContractorViolationEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const basicInfoContractorViolationConfigModalAtom = atom(false);

const basicInfoContractorViolationShowColumns = [
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 48,
  },
  // user-defined code here
  {
    title: "承包商",
    dataIndex: "contractor",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "承包商项目",
    dataIndex: "contractorProject",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "发生时间",
    dataIndex: "happenTime",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const content = formatDateDay(item);
      if (!content) {
        return null;
      }
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
  },
  {
    title: "发生地点",
    dataIndex: "happenPlace",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "违规内容",
    dataIndex: "violationContent",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item ?? ""}>
        <p>{item ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "处理结果",
    dataIndex: "processResult",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item ?? ""}>
        <p>{item ?? ""}</p>
      </Tooltip>
    ),
  },
];

const basicInfoContractorViolationExtendColumns = [
  // user-defined code here
];

export const basicInfoContractorViolationShowColumnsAtom = atom(
  basicInfoContractorViolationShowColumns
);

export const basicInfoContractorViolationColumnsAtom = atom([
  ...basicInfoContractorViolationShowColumns,
  ...basicInfoContractorViolationExtendColumns,
]);

/*export const basicInfoContractorViolationColumnsAtom = atom(
  (get) => get(basicInfoContractorViolationShowColumnsAtom).concat(get(basicInfoContractorViolationExtendColumnsAtom)),
  (get, set, newValue: any) => {
    set(basicInfoContractorViolationShowColumnsAtom, newValue.slice(0, newValue.length / 2)); // 更新 ShowColumnsAtom
    set(basicInfoContractorViolationExtendColumnsAtom, newValue.slice(newValue.length / 2)); // 更新 ExtendColumnsAtom
  }
)*/

export const basicInfoContractorViolationAtoms: CommonAtoms = {
  entity: "BasicInfoContractorViolation",
  filter: basicInfoContractorViolationFilterAtom,
  Fn: basicInfoContractorViolationFnAtom,
  editModal: basicInfoContractorViolationEditModalAtom,
  configModal: basicInfoContractorViolationConfigModalAtom,
  columns: basicInfoContractorViolationColumnsAtom,
};
