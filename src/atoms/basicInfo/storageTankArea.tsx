import { Tooltip } from "@douyinfe/semi-ui";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { CommonAtoms } from "types";

export const storageTankAreaFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const storageTankAreaFnAtom = atom({
  refetch: () => {},
});

export const storageTankAreaEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const storageTankAreaConfigModalAtom = atom(false);

export const storageTankAreaColumnsAtom = atom([
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  // user-defined code here
  {
    title: "储罐区名称",
    dataIndex: "name",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "储罐区编号",
    dataIndex: "code",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "储罐区面积(平方米)", //TODO: 平方米的数学表示法
    dataIndex: "area",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "储罐个数",
    dataIndex: "tankNum",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "罐见最小间距(m)",
    dataIndex: "minTankDistance",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "所属重大危险源",
    dataIndex: "majorHazard",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
]);

export const storageTankAreaAtoms: CommonAtoms = {
  entity: "StorageTankArea",
  filter: storageTankAreaFilterAtom,
  Fn: storageTankAreaFnAtom,
  editModal: storageTankAreaEditModalAtom,
  configModal: storageTankAreaConfigModalAtom,
  columns: storageTankAreaColumnsAtom,
};
