import { Tag, Tooltip } from "@douyinfe/semi-ui";
import { MAJORHAZARD_GAS_TYPE } from "components";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";
import { CommonAtoms } from "types";

export const toxicFlammableGasFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const toxicFlammableGasFnAtom = atom({
  refetch: () => {},
});

export const toxicFlammableGasEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const toxicFlammableGasConfigModalAtom = atom(false);

export const toxicFlammableGasColumnsAtom = atom([
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  {
    title: "气体名称",
    dataIndex: "name",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "气体类型",
    dataIndex: "type",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const i = find(propEq(item ? item : 1, "id"))(MAJORHAZARD_GAS_TYPE);
      return (
        <Tooltip content={i.name}>
          <Tag type="light">{i.name}</Tag>
        </Tooltip>
      );
    },
  },
]);

export const toxicFlammableGasAtoms: CommonAtoms = {
  entity: "ToxicFlammableGas",
  filter: toxicFlammableGasFilterAtom,
  Fn: toxicFlammableGasFnAtom,
  editModal: toxicFlammableGasEditModalAtom,
  configModal: toxicFlammableGasConfigModalAtom,
  columns: toxicFlammableGasColumnsAtom,
};
