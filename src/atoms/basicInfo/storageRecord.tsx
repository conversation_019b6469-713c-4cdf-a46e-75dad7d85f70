import { Tag, Tooltip } from "@douyinfe/semi-ui";
import { STORAGERECORD_TYPE_MAP } from "components";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";
import { CommonAtoms } from "types";

export const storageRecordFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const storageRecordFnAtom = atom({
  refetch: () => {},
});

export const storageRecordEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const storageRecordConfigModalAtom = atom(false);

export const storageRecordColumnsAtom = atom([
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  // user-defined code here
  {
    title: "化学品中文名",
    dataIndex: "chemical",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "操作类型",
    dataIndex: "type",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item ? item : 1, "id"))(STORAGERECORD_TYPE_MAP);
      return (
        <Tooltip content={i.name}>
          <Tag color={i.color} type="light">
            {i.name}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "存储装置(单元)",
    dataIndex: "storageUnit",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "数量(吨)",
    dataIndex: "count",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "经办人",
    dataIndex: "handlingPerson",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "经办时间",
    dataIndex: "handlingTime",
    isShow: true,
    ellipsis: true,
  },
]);

export const storageRecordAtoms: CommonAtoms = {
  entity: "StorageRecord",
  filter: storageRecordFilterAtom,
  Fn: storageRecordFnAtom,
  editModal: storageRecordEditModalAtom,
  configModal: storageRecordConfigModalAtom,
  columns: storageRecordColumnsAtom,
};
