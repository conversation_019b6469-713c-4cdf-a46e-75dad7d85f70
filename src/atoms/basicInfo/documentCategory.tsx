import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { CommonAtoms } from "types";

export const basicInfoDocumentCategoryFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const basicInfoDocumentCategoryFnAtom = atom({
  refetch: () => {},
});

export const basicInfoDocumentCategoryEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const basicInfoDocumentCategoryConfigModalAtom = atom(false);

export const basicInfoDocumentCategoryDataAtom = atom<any[]>([]);

const basicInfoDocumentCategoryShowColumns = [
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 48,
  },
  {
    title: "文档类型",
    dataIndex: "name",
    isShow: true,
  },
  {
    title: "文件数量",
    dataIndex: "documentNum",
    isShow: true,
  },
  {
    title: "备注信息",
    dataIndex: "note",
    isShow: true,
  },
  // user-defined code here
];

const basicInfoDocumentCategoryExtendColumns = [
  // user-defined code here
];

export const basicInfoDocumentCategoryShowColumnsAtom = atom(
  basicInfoDocumentCategoryShowColumns
);

export const basicInfoDocumentCategoryColumnsAtom = atom([
  ...basicInfoDocumentCategoryShowColumns,
  ...basicInfoDocumentCategoryExtendColumns,
]);

/*export const basicInfoDocumentCategoryColumnsAtom = atom(
  (get) => get(basicInfoDocumentCategoryShowColumnsAtom).concat(get(basicInfoDocumentCategoryExtendColumnsAtom)),
  (get, set, newValue: any) => {
    set(basicInfoDocumentCategoryShowColumnsAtom, newValue.slice(0, newValue.length / 2)); // 更新 ShowColumnsAtom
    set(basicInfoDocumentCategoryExtendColumnsAtom, newValue.slice(newValue.length / 2)); // 更新 ExtendColumnsAtom
  }
)*/

export const basicInfoDocumentCategoryRootName = "文档类型";

export const basicInfoDocumentCategoryAtoms: CommonAtoms = {
  entity: "BasicInfoDocumentCategory",
  filter: basicInfoDocumentCategoryFilterAtom,
  Fn: basicInfoDocumentCategoryFnAtom,
  editModal: basicInfoDocumentCategoryEditModalAtom,
  configModal: basicInfoDocumentCategoryConfigModalAtom,
  columns: basicInfoDocumentCategoryColumnsAtom,
};
