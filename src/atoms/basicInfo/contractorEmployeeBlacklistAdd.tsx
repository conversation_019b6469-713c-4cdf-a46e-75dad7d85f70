import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { Tooltip } from "tdesign-react";
import { CommonAtoms } from "types";
import { formatDate } from "utils";

export const basicInfoContractorEmployeeBlacklistAddFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const basicInfoContractorEmployeeBlacklistAddFnAtom = atom({
  refetch: () => {},
});

export const basicInfoContractorEmployeeBlacklistAddEditModalAtom =
  atomWithReset({
    id: "",
    show: false,
  });

export const basicInfoContractorEmployeeBlacklistAddConfigModalAtom =
  atom(false);

const basicInfoContractorEmployeeBlacklistAddShowColumns = [
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 48,
  },
  {
    title: "承包商",
    dataIndex: "contractor",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "承包商员工",
    dataIndex: "contractorEmployee",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "拉黑原因",
    dataIndex: "blackReason",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item ?? ""}>
        <p>{item ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "操作时间",
    dataIndex: "operationTime",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const content = formatDate(item);
      if (!content) {
        return null;
      }
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
  },
  {
    title: "操作人",
    dataIndex: "operationPerson",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  // user-defined code here
];

const basicInfoContractorEmployeeBlacklistAddExtendColumns = [
  // user-defined code here
];

export const basicInfoContractorEmployeeBlacklistAddShowColumnsAtom = atom(
  basicInfoContractorEmployeeBlacklistAddShowColumns
);

export const basicInfoContractorEmployeeBlacklistAddColumnsAtom = atom([
  ...basicInfoContractorEmployeeBlacklistAddShowColumns,
  ...basicInfoContractorEmployeeBlacklistAddExtendColumns,
]);

/*export const basicInfoContractorEmployeeBlacklistAddColumnsAtom = atom(
  (get) => get(basicInfoContractorEmployeeBlacklistAddShowColumnsAtom).concat(get(basicInfoContractorEmployeeBlacklistAddExtendColumnsAtom)),
  (get, set, newValue: any) => {
    set(basicInfoContractorEmployeeBlacklistAddShowColumnsAtom, newValue.slice(0, newValue.length / 2)); // 更新 ShowColumnsAtom
    set(basicInfoContractorEmployeeBlacklistAddExtendColumnsAtom, newValue.slice(newValue.length / 2)); // 更新 ExtendColumnsAtom
  }
)*/

export const basicInfoContractorEmployeeBlacklistAddAtoms: CommonAtoms = {
  entity: "BasicInfoContractorEmployeeBlacklistAdd",
  filter: basicInfoContractorEmployeeBlacklistAddFilterAtom,
  Fn: basicInfoContractorEmployeeBlacklistAddFnAtom,
  editModal: basicInfoContractorEmployeeBlacklistAddEditModalAtom,
  configModal: basicInfoContractorEmployeeBlacklistAddConfigModalAtom,
  columns: basicInfoContractorEmployeeBlacklistAddColumnsAtom,
};
