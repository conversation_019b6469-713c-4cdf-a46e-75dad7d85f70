import { Tag, Tooltip } from "@douyinfe/semi-ui";
import { EQUIPMENT_DETECTION_STATUS_MAP, IS_ISNOT_MAP } from "components";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";
import { CommonAtoms } from "types";
import { formatDateDay } from "utils";

export const equipmentManagementDetectionFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const equipmentManagementDetectionFnAtom = atom({
  refetch: () => {},
});

export const equipmentManagementDetectionEditModalAtom = atomWithReset({
  id: "",
  show: false,
  equipment: null,
});

export const equipmentManagementDetectionConfigModalAtom = atom(false);

const equipmentManagementDetectionShowColumns = [
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 48,
  },
  // user-defined code here
  {
    title: "设备名称",
    dataIndex: "equipment",
    isShow: true,
    ellipses: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
    renderText: (item, record, index) => item?.name ?? "",
  },
  {
    title: "设备类型",
    dataIndex: "equipmentCategory",
    isShow: true,
    ellipses: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
    renderText: (item, record, index) => item?.name ?? "",
  },
  {
    title: "检测人",
    dataIndex: "detectionPerson",
    isShow: true,
    ellipses: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
    renderText: (item, record, index) => item?.name ?? "",
  },
  {
    title: "检测日期",
    dataIndex: "detectionTime",
    isShow: true,
    ellipses: true,
    render: (item) => {
      const content = formatDateDay(item);
      if (!content) {
        return null;
      }
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
    renderText: (item) => formatDateDay(item) || "",
  },
  {
    title: "检测状态",
    dataIndex: "detectionStatus",
    isShow: true,
    ellipses: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(EQUIPMENT_DETECTION_STATUS_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
    renderText: (item, record, index) => {
      const i = find(propEq(item, "id"))(EQUIPMENT_DETECTION_STATUS_MAP);
      return i?.name ?? "-";
    },
  },
  {
    title: "是否本单位检测",
    dataIndex: "isSelfOperation",
    isShow: true,
    ellipses: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(IS_ISNOT_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
    renderText: (item, record, index) => {
      const i = find(propEq(item, "id"))(IS_ISNOT_MAP);
      return i?.name ?? "-";
    },
  },
];

const equipmentManagementDetectionExtendColumns = [
  // user-defined code here
];

export const equipmentManagementDetectionShowColumnsAtom = atom(
  equipmentManagementDetectionShowColumns
);

export const equipmentManagementDetectionColumnsAtom = atom([
  ...equipmentManagementDetectionShowColumns,
  ...equipmentManagementDetectionExtendColumns,
]);

/*export const equipmentManagementDetectionColumnsAtom = atom(
  (get) => get(equipmentManagementDetectionShowColumnsAtom).concat(get(equipmentManagementDetectionExtendColumnsAtom)),
  (get, set, newValue: any) => {
    set(equipmentManagementDetectionShowColumnsAtom, newValue.slice(0, newValue.length / 2)); // 更新 ShowColumnsAtom
    set(equipmentManagementDetectionExtendColumnsAtom, newValue.slice(newValue.length / 2)); // 更新 ExtendColumnsAtom
  }
)*/

export const equipmentManagementDetectionAtoms: CommonAtoms = {
  entity: "EquipmentManagementDetection",
  entityCName: "检测设备",
  filter: equipmentManagementDetectionFilterAtom,
  Fn: equipmentManagementDetectionFnAtom,
  editModal: equipmentManagementDetectionEditModalAtom,
  configModal: equipmentManagementDetectionConfigModalAtom,
  columns: equipmentManagementDetectionColumnsAtom,
};
