import { Tooltip } from "@douyinfe/semi-ui";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { CommonAtoms } from "types";
import { formatDate } from "utils";

export const equipmentManagementResumeFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const equipmentManagementResumeFnAtom = atom({
  refetch: () => {},
});

export const equipmentManagementResumeEditModalAtom = atomWithReset({
  id: "",
  show: false,
  equipment: null,
});

export const equipmentManagementResumeConfigModalAtom = atom(false);

const equipmentManagementResumeShowColumns = [
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 48,
  },
  // user-defined code here
  {
    title: "设备",
    dataIndex: "equipment",
    isShow: true,
    ellipses: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
    renderText: (item, record, index) => item?.name ?? "",
  },
  {
    title: "设备类型",
    dataIndex: "equipmentCategory",
    isShow: true,
    ellipses: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
    renderText: (item, record, index) => item?.name ?? "",
  },
  {
    title: "登记人",
    dataIndex: "registrationPerson",
    isShow: true,
    ellipses: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
    renderText: (item, record, index) => item?.name ?? "",
  },
  {
    title: "登记时间",
    dataIndex: "registrationTime",
    isShow: true,
    ellipses: true,
    render: (item) => {
      const content = formatDate(item);
      if (!content) {
        return null;
      }
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
    renderText: (item) => formatDate(item) || "",
  },
  {
    title: "实际恢复时间",
    dataIndex: "resumeTime",
    isShow: true,
    ellipses: true,
    render: (item) => {
      const content = formatDate(item);
      if (!content) {
        return null;
      }
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
    renderText: (item) => formatDate(item) || "",
  },
];

const equipmentManagementResumeExtendColumns = [
  // user-defined code here
];

export const equipmentManagementResumeShowColumnsAtom = atom(
  equipmentManagementResumeShowColumns
);

export const equipmentManagementResumeColumnsAtom = atom([
  ...equipmentManagementResumeShowColumns,
  ...equipmentManagementResumeExtendColumns,
]);

/*export const equipmentManagementResumeColumnsAtom = atom(
  (get) => get(equipmentManagementResumeShowColumnsAtom).concat(get(equipmentManagementResumeExtendColumnsAtom)),
  (get, set, newValue: any) => {
    set(equipmentManagementResumeShowColumnsAtom, newValue.slice(0, newValue.length / 2)); // 更新 ShowColumnsAtom
    set(equipmentManagementResumeExtendColumnsAtom, newValue.slice(newValue.length / 2)); // 更新 ExtendColumnsAtom
  }
)*/

export const equipmentManagementResumeAtoms: CommonAtoms = {
  entity: "EquipmentManagementResume",
  entityCName: "恢复设备",
  filter: equipmentManagementResumeFilterAtom,
  Fn: equipmentManagementResumeFnAtom,
  editModal: equipmentManagementResumeEditModalAtom,
  configModal: equipmentManagementResumeConfigModalAtom,
  columns: equipmentManagementResumeColumnsAtom,
};
