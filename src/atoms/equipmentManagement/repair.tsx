import { Tag, Tooltip } from "@douyinfe/semi-ui";
import { EQUIPMENT_REPAIRTYPE_MAP, IS_ISNOT_MAP } from "components";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";
import { CommonAtoms } from "types";
import { formatDate } from "utils";

export const equipmentManagementRepairFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const equipmentManagementRepairFnAtom = atom({
  refetch: () => {},
});

export const equipmentManagementRepairEditModalAtom = atomWithReset({
  id: "",
  show: false,
  equipment: null,
});

export const equipmentManagementRepairConfigModalAtom = atom(false);

const equipmentManagementRepairShowColumns = [
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 48,
  },
  // user-defined code here
  {
    title: "设备",
    dataIndex: "equipment",
    isShow: true,
    ellipses: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
    renderText: (item, record, index) => item?.name ?? "",
  },
  {
    title: "设备类型",
    dataIndex: "equipmentCategory",
    isShow: true,
    ellipses: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
    renderText: (item, record, index) => item?.name ?? "",
  },
  {
    title: "负责人",
    dataIndex: "liablePerson",
    isShow: true,
    ellipses: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
    renderText: (item, record, index) => item?.name ?? "",
  },
  {
    title: "维修类型",
    dataIndex: "repairType",
    isShow: true,
    ellipses: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(EQUIPMENT_REPAIRTYPE_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
    renderText: (item, record, index) => {
      const i = find(propEq(item, "id"))(EQUIPMENT_REPAIRTYPE_MAP);
      return i?.name ?? "-";
    },
  },
  {
    title: "维修开始时间",
    dataIndex: "repairBeginTime",
    isShow: true,
    ellipses: true,
    render: (item) => {
      const content = formatDate(item);
      if (!content) {
        return null;
      }
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
    renderText: (item) => formatDate(item) || "",
  },
  {
    title: "维修结束时间",
    dataIndex: "repairEndTime",
    isShow: true,
    ellipses: true,
    render: (item) => {
      const content = formatDate(item);
      if (!content) {
        return null;
      }
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
    renderText: (item) => formatDate(item) || "",
  },
  {
    title: "是否本单位保养",
    dataIndex: "isSelfOperation",
    isShow: true,
    ellipses: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(IS_ISNOT_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
    renderText: (item, record, index) => {
      const i = find(propEq(item, "id"))(IS_ISNOT_MAP);
      return i?.name ?? "-";
    },
  },
];

const equipmentManagementRepairExtendColumns = [
  // user-defined code here
];

export const equipmentManagementRepairShowColumnsAtom = atom(
  equipmentManagementRepairShowColumns
);

export const equipmentManagementRepairColumnsAtom = atom([
  ...equipmentManagementRepairShowColumns,
  ...equipmentManagementRepairExtendColumns,
]);

/*export const equipmentManagementRepairColumnsAtom = atom(
  (get) => get(equipmentManagementRepairShowColumnsAtom).concat(get(equipmentManagementRepairExtendColumnsAtom)),
  (get, set, newValue: any) => {
    set(equipmentManagementRepairShowColumnsAtom, newValue.slice(0, newValue.length / 2)); // 更新 ShowColumnsAtom
    set(equipmentManagementRepairExtendColumnsAtom, newValue.slice(newValue.length / 2)); // 更新 ExtendColumnsAtom
  }
)*/

export const equipmentManagementRepairAtoms: CommonAtoms = {
  entity: "EquipmentManagementRepair",
  entityCName: "维修设备",
  filter: equipmentManagementRepairFilterAtom,
  Fn: equipmentManagementRepairFnAtom,
  editModal: equipmentManagementRepairEditModalAtom,
  configModal: equipmentManagementRepairConfigModalAtom,
  columns: equipmentManagementRepairColumnsAtom,
};
