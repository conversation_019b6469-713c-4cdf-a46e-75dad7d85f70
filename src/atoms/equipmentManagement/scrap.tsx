import { Tooltip } from "@douyinfe/semi-ui";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { CommonAtoms } from "types";
import { formatDate } from "utils";

export const equipmentManagementScrapFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const equipmentManagementScrapFnAtom = atom({
  refetch: () => {},
});

export const equipmentManagementScrapEditModalAtom = atomWithReset({
  id: "",
  show: false,
  equipment: null,
});

export const equipmentManagementScrapConfigModalAtom = atom(false);

const equipmentManagementScrapShowColumns = [
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 48,
  },
  // user-defined code here
  {
    title: "设备",
    dataIndex: "equipment",
    isShow: true,
    ellipses: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
    renderText: (item, record, index) => item?.name ?? "",
  },
  {
    title: "设备类型",
    dataIndex: "equipmentCategory",
    isShow: true,
    ellipses: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
    renderText: (item, record, index) => item?.name ?? "",
  },
  {
    title: "登记人",
    dataIndex: "registrationPerson",
    isShow: true,
    ellipses: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
    renderText: (item, record, index) => item?.name ?? "",
  },
  {
    title: "登记时间",
    dataIndex: "registrationTime",
    isShow: true,
    ellipses: true,
    render: (item) => {
      const content = formatDate(item);
      if (!content) {
        return null;
      }
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
    renderText: (item) => formatDate(item) || "",
  },
  {
    title: "实际报废时间",
    dataIndex: "scrapTime",
    isShow: true,
    ellipses: true,
    render: (item) => {
      const content = formatDate(item);
      if (!content) {
        return null;
      }
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
    renderText: (item) => formatDate(item) || "",
  },
];

const equipmentManagementScrapExtendColumns = [
  // user-defined code here
];

export const equipmentManagementScrapShowColumnsAtom = atom(
  equipmentManagementScrapShowColumns
);

export const equipmentManagementScrapColumnsAtom = atom([
  ...equipmentManagementScrapShowColumns,
  ...equipmentManagementScrapExtendColumns,
]);

/*export const equipmentManagementScrapColumnsAtom = atom(
  (get) => get(equipmentManagementScrapShowColumnsAtom).concat(get(equipmentManagementScrapExtendColumnsAtom)),
  (get, set, newValue: any) => {
    set(equipmentManagementScrapShowColumnsAtom, newValue.slice(0, newValue.length / 2)); // 更新 ShowColumnsAtom
    set(equipmentManagementScrapExtendColumnsAtom, newValue.slice(newValue.length / 2)); // 更新 ExtendColumnsAtom
  }
)*/

export const equipmentManagementScrapAtoms: CommonAtoms = {
  entity: "EquipmentManagementScrap",
  entityCName: "报废设备",
  filter: equipmentManagementScrapFilterAtom,
  Fn: equipmentManagementScrapFnAtom,
  editModal: equipmentManagementScrapEditModalAtom,
  configModal: equipmentManagementScrapConfigModalAtom,
  columns: equipmentManagementScrapColumnsAtom,
};
