import { Tag, Tooltip } from "@douyinfe/semi-ui";
import {
  ALLOW_REPORT_STATUS_MAP,
  INTELLIGENTINSPECTION_STATUS_MAP,
  ISABNORMAL_MAP,
  REPORT_STATUS_MAP,
} from "components";
import { EQUIPMENT_STATUS_MAP } from "components/enum/equipmentManagement";
import dayjs from "dayjs";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";
import { CommonAtoms } from "types";
import { formatDate, formatDateDay, millisecondsOfOnemonth } from "utils";

export const equipmentManagementEquipmentFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
  equipmentCategoryId: null,
  filter: {},
});

//TODO: to delete
export const equipmentManagementEquipmentFnAtom = atom({
  refetch: () => {},
});

export const equipmentManagementEquipmentEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const equipmentManagementEquipmentDetailSideAtom = atomWithReset({
  id: "",
  show: false,
});

export const equipmentManagementEquipmentConfigModalAtom = atom(false);

export const equipmentManagementEquipmentCommonColumns = [
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 48,
  },
  // user-defined code here
  {
    title: "设备名称",
    dataIndex: "name",
    isShow: true,
    ellipses: true,
  },
  {
    title: "设备编号",
    dataIndex: "code",
    isShow: true,
    ellipses: true,
  },
  {
    title: "设备状态",
    dataIndex: "status",
    isShow: true,
    ellipses: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(EQUIPMENT_STATUS_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
    renderText: (text) => {
      const i = find(propEq(text, "id"))(EQUIPMENT_STATUS_MAP);
      return i?.name ?? "-";
    },
  },
  {
    title: "设备类型",
    dataIndex: "equipmentCategory",
    isShow: true,
    ellipses: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
    renderText: (text) => {
      return text?.name ?? "";
    },
  },
  {
    title: "所属区域",
    dataIndex: "area",
    isShow: true,
    ellipses: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
    renderText: (text) => {
      return text?.name ?? "";
    },
  },
  {
    title: "使用期限(月)",
    dataIndex: "expirationMonth",
    isShow: true,
    ellipses: true,
  },
  {
    title: "过期日期",
    dataIndex: "expireDate",
    isShow: true,
    ellipses: true,
    render: (text) => {
      if (!text) {
        return null; // 如果 text 是 null 或 undefined，直接返回 null，不渲染任何内容
      }

      const date = dayjs(text);
      const diff = date.diff(dayjs());

      return (
        <p>
          {diff < millisecondsOfOnemonth ? (
            <Tag color="red">{date.format("YYYY-MM-DD")}</Tag>
          ) : diff < 3 * millisecondsOfOnemonth ? (
            <Tag color="yellow">{date.format("YYYY-MM-DD")}</Tag>
          ) : (
            <Tag color="white">{date.format("YYYY-MM-DD")}</Tag>
          )}
        </p>
      );
    },
    renderText: (text) => {
      const content = formatDateDay(text);
      return content;
    },
  },
  {
    title: "定期检测间隔(天)",
    dataIndex: "detectionDayInterval",
    isShow: true,
    ellipses: true,
  },
  {
    title: "定期保养间隔(天)",
    dataIndex: "maintenanceDayInterval",
    isShow: true,
    ellipses: true,
  },
  {
    title: "是否上报",
    dataIndex: "needReport",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      console.debug("item", item);
      const i = find(propEq(item, "id"))(ALLOW_REPORT_STATUS_MAP);
      console.debug("i", i);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "上报状态",
    dataIndex: "reportStatus",
    isShow: false,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(REPORT_STATUS_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "上报时间",
    dataIndex: "reportTime",
    isShow: false,
    ellipsis: true,
    render: (item) => {
      const content = formatDate(item);
      return (
        <Tooltip content={content || "-"}>
          <p>{content || "-"}</p>
        </Tooltip>
      );
    },
  },
  {
    title: "上报应答",
    dataIndex: "reportResult",
    isShow: false,
    ellipsis: true,
    render: (item) => {
      return (
        <Tooltip content={item || "-"}>
          <p>{item || "-"}</p>
        </Tooltip>
      );
    },
  },
];

const equipmentManagementEquipmentShowColumns = [
  ...equipmentManagementEquipmentCommonColumns,
];

const equipmentManagementEquipmentExtendColumns = [
  // user-defined code here
];

export const equipmentManagementEquipmentShowColumnsAtom = atom(
  equipmentManagementEquipmentShowColumns
);

export const equipmentManagementEquipmentColumnsAtom = atom([
  ...equipmentManagementEquipmentShowColumns,
  ...equipmentManagementEquipmentExtendColumns,
]);

/*export const equipmentManagementEquipmentColumnsAtom = atom(
  (get) => get(equipmentManagementEquipmentShowColumnsAtom).concat(get(equipmentManagementEquipmentExtendColumnsAtom)),
  (get, set, newValue: any) => {
    set(equipmentManagementEquipmentShowColumnsAtom, newValue.slice(0, newValue.length / 2)); // 更新 ShowColumnsAtom
    set(equipmentManagementEquipmentExtendColumnsAtom, newValue.slice(newValue.length / 2)); // 更新 ExtendColumnsAtom
  }
)*/

export const equipmentManagementEquipmentAtoms: CommonAtoms = {
  entity: "EquipmentManagementEquipment",
  entityCName: "设备管理台账",
  filter: equipmentManagementEquipmentFilterAtom,
  Fn: equipmentManagementEquipmentFnAtom,
  editModal: equipmentManagementEquipmentEditModalAtom,
  configModal: equipmentManagementEquipmentConfigModalAtom,
  columns: equipmentManagementEquipmentColumnsAtom,
};

export const equipmentInspectionFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});
export const equipmentInspectionColumnsAtom = atom([
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 96,
  },
  {
    title: "巡检任务ID",
    dataIndex: "taskId",
    isShow: true,
    ellipses: true,
  },
  {
    title: "巡检点",
    dataIndex: "place",
    isShow: true,
    ellipses: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
    renderText: (item) => item?.name ?? "",
  },
  {
    title: "更新时间",
    dataIndex: "updatedAt",
    isShow: true,
    ellipses: true,
    render: (item) => {
      const content = formatDate(item);
      if (!content) {
        return null;
      }
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
    renderText: (item) => formatDate(item) || "",
  },
  {
    title: "巡检状态",
    dataIndex: "status",
    isShow: true,
    ellipses: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(INTELLIGENTINSPECTION_STATUS_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
    renderText: (text) => {
      const i = find(propEq(text, "id"))(INTELLIGENTINSPECTION_STATUS_MAP);
      return i?.name ?? "-";
    },
  },
  {
    title: "是否异常",
    dataIndex: "isAbnormal",
    isShow: true,
    ellipses: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(ISABNORMAL_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
    renderText: (text) => {
      const i = find(propEq(text, "id"))(ISABNORMAL_MAP);
      return i?.name ?? "-";
    },
  },
]);
