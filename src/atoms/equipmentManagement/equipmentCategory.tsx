import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { CommonAtoms } from "types";

export const equipmentManagementEquipmentCategoryFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

// 选中项
export const equipmentManagementEquipmentCategorySelectAtom =
  atomWithReset<number>(0);

// 全部数据存储
export const equipmentManagementEquipmentCategoryDataAtom = atom<any[]>([]);

//TODO: to delete
export const equipmentManagementEquipmentCategoryFnAtom = atom({
  refetch: () => {},
});

export const equipmentManagementEquipmentCategoryEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const equipmentManagementEquipmentCategoryConfigModalAtom = atom(false);

const equipmentManagementEquipmentCategoryShowColumns = [
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 48,
  },
  {
    title: "设备类型名称",
    dataIndex: "name",
    isShow: true,
    ellipsis: true,
  },
  // user-defined code here
];

const equipmentManagementEquipmentCategoryExtendColumns = [
  // user-defined code here
];

export const equipmentManagementEquipmentCategoryShowColumnsAtom = atom(
  equipmentManagementEquipmentCategoryShowColumns
);

export const equipmentManagementEquipmentCategoryColumnsAtom = atom([
  ...equipmentManagementEquipmentCategoryShowColumns,
  ...equipmentManagementEquipmentCategoryExtendColumns,
]);

/*export const equipmentManagementEquipmentCategoryColumnsAtom = atom(
  (get) => get(equipmentManagementEquipmentCategoryShowColumnsAtom).concat(get(equipmentManagementEquipmentCategoryExtendColumnsAtom)),
  (get, set, newValue: any) => {
    set(equipmentManagementEquipmentCategoryShowColumnsAtom, newValue.slice(0, newValue.length / 2)); // 更新 ShowColumnsAtom
    set(equipmentManagementEquipmentCategoryExtendColumnsAtom, newValue.slice(newValue.length / 2)); // 更新 ExtendColumnsAtom
  }
)*/

export const equipmentManagementEquipmentCategoryAtoms: CommonAtoms = {
  entity: "EquipmentManagementEquipmentCategory",
  filter: equipmentManagementEquipmentCategoryFilterAtom,
  Fn: equipmentManagementEquipmentCategoryFnAtom,
  editModal: equipmentManagementEquipmentCategoryEditModalAtom,
  configModal: equipmentManagementEquipmentCategoryConfigModalAtom,
  columns: equipmentManagementEquipmentCategoryColumnsAtom,
};
