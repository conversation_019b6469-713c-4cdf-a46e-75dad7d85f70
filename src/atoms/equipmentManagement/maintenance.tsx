import { Tag, Tooltip } from "@douyinfe/semi-ui";
import { IS_ISNOT_MAP } from "components";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";
import { CommonAtoms } from "types";
import { formatDateDay } from "utils";

export const equipmentManagementMaintenanceFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const equipmentManagementMaintenanceFnAtom = atom({
  refetch: () => {},
});

export const equipmentManagementMaintenanceEditModalAtom = atomWithReset({
  id: "",
  show: false,
  equipment: null,
});

export const equipmentManagementMaintenanceConfigModalAtom = atom(false);

const equipmentManagementMaintenanceShowColumns = [
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 48,
  },
  // user-defined code here
  {
    title: "设备名称",
    dataIndex: "equipment",
    isShow: true,
    ellipses: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
    renderText: (item, record, index) => item?.name ?? "",
  },
  {
    title: "设备类型",
    dataIndex: "equipmentCategory",
    isShow: true,
    ellipses: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
    renderText: (item, record, index) => item?.name ?? "",
  },
  {
    title: "保养负责人",
    dataIndex: "liablePersonList",
    isShow: true,
    ellipses: true,
    render: (text, record, index) => {
      const content = text
        ? text.map((item, index, array) =>
            index === array.length - 1 ? (
              <span key={index}>{item.name}</span>
            ) : (
              <span key={index}>{item.name},</span>
            )
          )
        : null;
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
    renderText: (text, record, index) => {
      const content = text
        ? text.map((item, index, array) =>
            index === array.length - 1 ? `${item.name}` : `${item.name},`
          )
        : "";
      return content;
    },
  },
  {
    title: "保养日期",
    dataIndex: "maintenanceTime",
    isShow: true,
    ellipses: true,
    render: (item) => {
      const content = formatDateDay(item);
      if (!content) {
        return null;
      }
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
    renderText: (item) => {
      const content = formatDateDay(item);
      return content;
    },
  },
  {
    title: "是否本单位保养",
    dataIndex: "isSelfOperation",
    isShow: true,
    ellipses: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(IS_ISNOT_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
    renderText: (item, record, index) => {
      const i = find(propEq(item, "id"))(IS_ISNOT_MAP);
      return i?.name ?? "-";
    },
  },
];

const equipmentManagementMaintenanceExtendColumns = [
  // user-defined code here
];

export const equipmentManagementMaintenanceShowColumnsAtom = atom(
  equipmentManagementMaintenanceShowColumns
);

export const equipmentManagementMaintenanceColumnsAtom = atom([
  ...equipmentManagementMaintenanceShowColumns,
  ...equipmentManagementMaintenanceExtendColumns,
]);

/*export const equipmentManagementMaintenanceColumnsAtom = atom(
  (get) => get(equipmentManagementMaintenanceShowColumnsAtom).concat(get(equipmentManagementMaintenanceExtendColumnsAtom)),
  (get, set, newValue: any) => {
    set(equipmentManagementMaintenanceShowColumnsAtom, newValue.slice(0, newValue.length / 2)); // 更新 ShowColumnsAtom
    set(equipmentManagementMaintenanceExtendColumnsAtom, newValue.slice(newValue.length / 2)); // 更新 ExtendColumnsAtom
  }
)*/

export const equipmentManagementMaintenanceAtoms: CommonAtoms = {
  entity: "EquipmentManagementMaintenance",
  entityCName: "保养设备",
  filter: equipmentManagementMaintenanceFilterAtom,
  Fn: equipmentManagementMaintenanceFnAtom,
  editModal: equipmentManagementMaintenanceEditModalAtom,
  configModal: equipmentManagementMaintenanceConfigModalAtom,
  columns: equipmentManagementMaintenanceColumnsAtom,
};
