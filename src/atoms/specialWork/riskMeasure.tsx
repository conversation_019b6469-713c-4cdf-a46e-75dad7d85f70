import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { Tag, Tooltip } from "@douyinfe/semi-ui";
import { find, propEq } from "ramda";
import { RISK_MEASURE_ACCIDENTTYPE } from "components";

export const riskMeasureConfigModalAtom = atom(false);

export const riskMeasureEditModalAtom = atom({
  id: "",
  show: false,
});

export const riskMeasureMoreModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const riskMeasureDrawerModalAtom = atomWithReset({
  id: "",
  riskLevel: undefined,
  show: false,
});

export enum EvaluationType {
  Easy,
  Clac,
}

type SafetyMeasureEvaluationModalType = {
  id: number | null;
  name?: string;
  show: boolean;
  type: EvaluationType;
};

export const riskMeasureEvaluationModalAtom =
  atomWithReset<SafetyMeasureEvaluationModalType>({
    id: null,
    name: "",
    show: false,
    type: EvaluationType.Easy,
  });

// 查询条件
export const riskMeasureFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
  filter: {},
});

// 查询条件
export const riskMeasureFnAtom = atom({
  refetch: () => {},
});

export const riskMeasureColumnsAtom = atom([
  {
    title: <Tooltip content="ID">ID</Tooltip>,
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  {
    title: <Tooltip content="风险类型">风险类型</Tooltip>,
    dataIndex: "accidentType",
    isShow: true,
    ellipsis: true,
    render: (text: string) => {
      const i = find(propEq(text, "id"))(RISK_MEASURE_ACCIDENTTYPE);
      return (
        <Tag color="grey" type="light" className="min-w-[fit-content]">
          {i?.name}
        </Tag>
      );
    },
    /* render: (item) => <Tooltip content={item?.name}><span className="block truncate">{item?.name}</span></Tooltip>, */
  },
  {
    title: <Tooltip content="安全措施">安全措施</Tooltip>,
    dataIndex: "safetyMeasure",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text ?? ""}>
        <p>{text ?? ""}</p>
      </Tooltip>
    ),
  },
]);
