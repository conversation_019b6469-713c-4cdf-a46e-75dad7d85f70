import { Tooltip } from "@douyinfe/semi-ui";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";

export const safetyMeasureConfigModalAtom = atom(false);

export const safetyMeasureEditModalAtom = atom({
  id: "",
  show: false,
});

// 查询条件
export const safetyMeasureFilterAtom = atomWithReset<SafetyMeasureParams>({
  pageNumber: 1,
  pageSize: 10,
  filter: {},
});

// 查询条件
export const safetyMeasureFnAtom = atom({
  refetch: () => {},
});

export const safetyMeasureColumnsAtom = atom([
  {
    title: <Tooltip content="ID">ID</Tooltip>,
    dataIndex: "id",
    isShow: true,
    fixed: true,
    width: 80,
  },
  {
    title: <Tooltip content="作业类型">作业类型</Tooltip>,
    dataIndex: "category",
    isShow: true,
    ellipsis: true,
    fixed: true,
    render: (item) => (
      <Tooltip content={item?.name}>
        <span className="block truncate">{item?.name}</span>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="安全措施">安全措施</Tooltip>,
    dataIndex: "safetyMeasure",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text ?? ""}>
        <p>{text ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="排序">排序</Tooltip>,
    dataIndex: "number",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text ?? ""}>
        <p>{text ?? ""}</p>
      </Tooltip>
    ),
  },
]);
