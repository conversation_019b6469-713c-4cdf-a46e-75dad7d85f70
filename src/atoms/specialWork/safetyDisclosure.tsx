import { atom } from "jotai";
import { atomWithReset, useResetAtom } from "jotai/utils";
import { Tag, Tooltip } from "@douyinfe/semi-ui";

export const safetyDisclosureConfigModalAtom = atom(false);

export const safetyDisclosureEditModalAtom = atom({
  id: "",
  show: false,
});

// 查询条件
export const safetyDisclosureFilterAtom = atomWithReset<SafetyMeasureParams>({
  pageNumber: 1,
  pageSize: 10,
  filter: {},
});

// 查询条件
export const safetyDisclosureFnAtom = atom({
  refetch: () => {},
});

export const safetyDisclosureColumnsAtom = atom([
  {
    title: <Tooltip content="ID">ID</Tooltip>,
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  {
    title: <Tooltip content="作业类型">作业类型</Tooltip>,
    dataIndex: "category",
    isShow: true,
    ellipsis: true,
    render: (item) => (
      <Tooltip content={item?.name}>
        <span className="block truncate">{item?.name}</span>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="应急措施">应急措施</Tooltip>,
    dataIndex: "emergencyMeasure",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text ?? ""}>
        <p>{text ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="注意事项">注意事项</Tooltip>,
    dataIndex: "precaution",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text ?? ""}>
        <p>{text ?? ""}</p>
      </Tooltip>
    ),
  },
]);
