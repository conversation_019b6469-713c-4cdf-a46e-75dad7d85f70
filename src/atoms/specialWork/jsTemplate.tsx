import { Tooltip } from "@douyinfe/semi-ui";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";

export const jsTemplateConfigModalAtom = atom(false);

export const jsTemplateEditModalAtom = atom({
  id: "",
  show: false,
});

export const inspectionModalAtom = atom({
  id: "",
  show: false,
});

// 查询条件
export const jsTemplateFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
  filter: {},
});

// 查询条件
export const jsTemplateFnAtom = atom({
  refetch: () => {},
});

export const jsTemplateColumnsAtom = atom([
  {
    title: <Tooltip content="ID">ID</Tooltip>,
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  {
    title: <Tooltip content="模板名称">模板名称</Tooltip>,
    dataIndex: "name",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
]);
