import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { CommonAtoms } from "types";

export const specialWorkConfigFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const specialWorkConfigFnAtom = atom({
  refetch: () => {},
});

export const specialWorkConfigEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const specialWorkConfigConfigModalAtom = atom(false);

const specialWorkConfigShowColumns = [
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  // user-defined code here
];

const specialWorkConfigExtendColumns = [
  // user-defined code here
];

export const specialWorkConfigColumnsAtom = atom([
  ...specialWorkConfigShowColumns,
  ...specialWorkConfigExtendColumns,
]);

/*export const specialWorkConfigColumnsAtom = atom(
  (get) => get(specialWorkConfigShowColumnsAtom).concat(get(specialWorkConfigExtendColumnsAtom)),
  (get, set, newValue: any) => {
    set(specialWorkConfigShowColumnsAtom, newValue.slice(0, newValue.length / 2)); // 更新 ShowColumnsAtom
    set(specialWorkConfigExtendColumnsAtom, newValue.slice(newValue.length / 2)); // 更新 ExtendColumnsAtom
  }
)*/

export const specialWorkConfigAtoms: CommonAtoms = {
  entity: "SpecialWorkConfig",
  filter: specialWorkConfigFilterAtom,
  Fn: specialWorkConfigFnAtom,
  editModal: specialWorkConfigEditModalAtom,
  configModal: specialWorkConfigConfigModalAtom,
  columns: specialWorkConfigColumnsAtom,
};
