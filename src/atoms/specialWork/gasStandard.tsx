import { atom } from "jotai";
import { atomWithReset, useResetAtom } from "jotai/utils";
import { Tag, Tooltip } from "@douyinfe/semi-ui";
import { find, propEq } from "ramda";
import { GAS_STANDARD_CATEGORYTYPE, GAS_STANDARD_UNITTYPE } from "components";

export const gasStandardConfigModalAtom = atom(false);

export const gasStandardEditModalAtom = atom({
  id: "",
  show: false,
});

// 查询条件
export const gasStandardFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
  filter: {},
});

// 查询条件
export const gasStandardFnAtom = atom({
  refetch: () => {},
});

export const gasStandardColumnsAtom = atom([
  {
    title: <Tooltip content="ID">ID</Tooltip>,
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  {
    title: <Tooltip content="气体类型">气体类型</Tooltip>,
    dataIndex: "categoryType",
    isShow: true,
    ellipsis: true,
    render: (record) => {
      const item = find(propEq(record, "id"))(GAS_STANDARD_CATEGORYTYPE);
      return <p>{item?.name}</p>;
    },
  },
  {
    title: <Tooltip content="气体名称">气体名称</Tooltip>,
    dataIndex: "name",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text ?? ""}>
        <p>{text ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="最小值">最小值</Tooltip>,
    dataIndex: "minValue",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text ?? ""}>
        <p>{text ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="最大值">最大值</Tooltip>,
    dataIndex: "maxValue",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text ?? ""}>
        <p>{text ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="气体单位类型">气体单位类型</Tooltip>,
    dataIndex: "unitType",
    isShow: true,
    ellipsis: true,
    render: (record) => {
      const item = find(propEq(record, "id"))(GAS_STANDARD_UNITTYPE);
      return <p>{item?.name}</p>;
    },
  },
]);
