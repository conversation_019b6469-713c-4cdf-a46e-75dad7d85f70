import { Image, ImagePreview, Tag, Tooltip } from "@douyinfe/semi-ui";
import { base_url } from "config";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";

export const jsTemplateUserConfigModalAtom = atom(false);

export const jsTemplateUserEditModalAtom = atom({
  id: "",
  show: false,
});

// 查询条件
export const jsTemplateUserFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
  filter: {},
});

// 查询条件
export const jsTemplateUserFnAtom = atom({
  refetch: () => {},
});

export const jsTemplateUserColumnsAtom = atom([
  {
    title: <Tooltip content="ID">ID</Tooltip>,
    dataIndex: "id",
    isShow: true,
    fixed: true,
    width: 80,
  },
  {
    title: <Tooltip content="作业票模板名称">作业票模板名称</Tooltip>,
    dataIndex: "name",
    isShow: true,
    ellipsis: true,
    width: 200,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="作业类型">作业类型</Tooltip>,
    dataIndex: "category",
    isShow: true,
    ellipsis: true,
    render: (item) => (
      <Tooltip content={item?.name}>
        <span className="block truncate">{item?.name}</span>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="图标">图标</Tooltip>,
    dataIndex: "icon",
    isShow: true,
    ellipsis: true,
    render: (record) => {
      if (!record) {
        return null;
      }
      let list;
      try {
        list = JSON.parse(record);
        if (list && !Array.isArray(list) && list.values) {
          // 处理脏数据
          list = list.values;
        }
        if (!Array.isArray(list)) {
          list = [];
        }
      } catch (e) {
        list = [];
      }

      return (
        <ImagePreview>
          {list.map((o) => (
            <Image width={30} height={30} src={`${base_url}${o}`} />
          ))}
        </ImagePreview>
      );
    },
  },
  {
    title: <Tooltip content="验收方式">验收方式</Tooltip>,
    dataIndex: "jobAcceptMode",
    isShow: true,
    ellipsis: true,
    render: (text) =>
      text == 1 ? <Tag color="green">汇签</Tag> : <Tag color="grey">或签</Tag>,
  },
  /* {
    title: <Tooltip content="有效期">有效期</Tooltip>,
    dataIndex: "hourValidCount",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text ?? ""}>
        <p>{text ?? ""}小时</p>
      </Tooltip>
    ),
  }, */
  /* {
    title: (
      <Tooltip content="是否允许不填写气体分析">是否允许不填写气体分析</Tooltip>
    ),
    dataIndex: "allowNoGasAnalysis",
    isShow: true,
    ellipsis: true,
    render: (text) =>
      text == 1 ? <Tag color="green">允许</Tag> : <Tag color="red">不允许</Tag>,
  }, */
  {
    title: <Tooltip content="是否允许无预约">是否允许无预约</Tooltip>,
    dataIndex: "allowNoAppointment",
    isShow: true,
    ellipsis: true,
    render: (text) =>
      text == 1 ? <Tag color="green">允许</Tag> : <Tag color="red">不允许</Tag>,
  },
  {
    title: <Tooltip content="是否已配置模板">是否已配置模板</Tooltip>,
    dataIndex: "hasForm",
    width: 100,
    isShow: true,
    ellipsis: true,
    render: (text, record, index) => {
      const fromText =
        text === 1 ? (
          <>
            {`表单: `}
            <Tag color="green">是</Tag>
          </>
        ) : (
          <>
            {`表单: `}
            <Tag color="red">否</Tag>
          </>
        );
      const processText =
        record.hasProcess === 1 ? (
          <>
            {`审批: `}
            <Tag color="green">是</Tag>
          </>
        ) : (
          <>
            {`审批: `}
            <Tag color="red">否</Tag>
          </>
        );
      const printText =
        record.hasPrint === 1 ? (
          <>
            {`打印: `}
            <Tag color="green">是</Tag>
          </>
        ) : (
          <>
            {`打印: `}
            <Tag color="red">否</Tag>
          </>
        );
      const resText = (
        <>
          {fromText}
          <br />
          {processText}
          <br />
          {printText}
        </>
      );
      return (
        <>
          <Tooltip content={resText}>{resText}</Tooltip>
        </>
      );
    },
  },
  {
    title: <Tooltip content="开票数量">开票数量</Tooltip>,
    dataIndex: "jobSliceCount",
    isShow: true,
    ellipsis: true,
  },
  {
    title: <Tooltip content="状态">状态</Tooltip>,
    dataIndex: "isActive",
    isShow: true,
    ellipsis: true,
    render: (text) =>
      text == 1 ? <Tag color="green">启用</Tag> : <Tag color="red">关闭</Tag>,
  },
]);
