import { Image, ImagePreview, Tag, Tooltip } from "@douyinfe/semi-ui";
import { JOB_TYPE_MAP } from "components";
import { base_url } from "config";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";

export const jobCategoryConfigModalAtom = atom(false);

export const jobCategoryEditModalAtom = atom({
  id: "",
  show: false,
});

// 查询条件
export const jobCategoryFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
  filter: {},
});

// 查询条件
export const jobCategoryFnAtom = atom({
  refetch: () => {},
});

export const jobCategoryColumnsAtom = atom([
  {
    title: <Tooltip content="ID">ID</Tooltip>,
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  {
    title: <Tooltip content="作业类型">作业类型</Tooltip>,
    dataIndex: "name",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="是否特殊作业">是否特殊作业</Tooltip>,
    dataIndex: "isSpecial",
    isShow: true,
    ellipsis: true,
    render: (text) =>
      text == 2 ? <Tag color="grey">否</Tag> : <Tag color="green">是</Tag>,
  },
  {
    title: <Tooltip content="特殊业务类型">特殊业务类型</Tooltip>,
    dataIndex: "jobType",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(JOB_TYPE_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: <Tooltip content="图标">图标</Tooltip>,
    dataIndex: "icon",
    isShow: true,
    ellipsis: true,
    render: (record) => {
      if (!record) {
        return null;
      }
      let list;
      try {
        list = JSON.parse(record);
        if (list && !Array.isArray(list) && list.values) {
          // 处理脏数据
          list = list.values;
        }
        if (!Array.isArray(list)) {
          list = [];
        }
      } catch (e) {
        console.error(e);
        if (!Array.isArray(record)) {
          list = [record];
        } else {
          list = record;
        }
      }
      console.debug("list", list);

      return (
        <ImagePreview>
          {list.map((o) => (
            <Image width={30} height={30} src={`${base_url}${o}`} />
          ))}
        </ImagePreview>
      );
    },
  },
  {
    title: <Tooltip content="排序">排序</Tooltip>,
    dataIndex: "orderNumber",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text ?? ""}>
        <p>{text ?? ""}</p>
      </Tooltip>
    ),
  },
]);
