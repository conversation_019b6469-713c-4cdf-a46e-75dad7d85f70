import { Tag, Tooltip } from "@douyinfe/semi-ui";
import { JOB_APPOINTMENT_ISOVERTIME, JOB_APPOINTMENT_STATUS } from "components";
import dayjs from "dayjs";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";

export const jobAppointmentConfigModalAtom = atom(false);

export const jobAppointmentEditModalAtom = atom({
  id: "",
  show: false,
});

// 查询条件
export const jobAppointmentFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
  filter: {},
});

export const jobAppointmentInfoAtom = atomWithReset({
  id: "",
  show: false,
});

// 查询条件
export const jobAppointmentFnAtom = atom({
  refetch: () => {},
});

export const jobAppointmentColumnsAtom = atom([
  {
    title: <Tooltip content="ID">ID</Tooltip>,
    dataIndex: "id",
    fixed: true,
    isShow: true,
    width: 80,
  },
  {
    title: <Tooltip content="预约编号">预约编号</Tooltip>,
    dataIndex: "code",
    width: 200,
    isShow: true,
    fixed: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="预约名称">预约名称</Tooltip>,
    dataIndex: "name",
    fixed: true,
    isShow: true,
    ellipsis: true,
  },
  {
    title: <Tooltip content="申请人">申请人</Tooltip>,
    dataIndex: "applyPerson",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text?.name ?? ""}>
        <p>{text?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="申请部门">申请部门</Tooltip>,
    dataIndex: "applyDepartment",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text?.name ?? ""}>
        <p>{text?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="申请时间">申请时间</Tooltip>,
    dataIndex: "applyDate",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={dayjs(text).format("YYYY/MM/DD HH:mm")}>
        <p>{dayjs(text).format("YYYY/MM/DD HH:mm")}</p>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="预计开始时间">预计开始时间</Tooltip>,
    dataIndex: "effectivetBeginTime",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={dayjs(text).format("YYYY/MM/DD HH:mm")}>
        <p>{dayjs(text).format("YYYY/MM/DD HH:mm") ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="预计结束时间">预计结束时间</Tooltip>,
    dataIndex: "effectivetEndTime",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={dayjs(text).format("YYYY/MM/DD HH:mm")}>
        <p>{dayjs(text).format("YYYY/MM/DD HH:mm")}</p>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="作业区域">作业区域</Tooltip>,
    dataIndex: "area",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text?.name ?? ""}>
        <p>{text?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="工作内容">工作内容</Tooltip>,
    dataIndex: "content",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text ?? ""}>
        <p className="line-clamp-1">{text ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="预约状态">预约状态</Tooltip>,
    dataIndex: "status",
    isShow: true,
    ellipsis: true,
    render: (text) => {
      const item = find(propEq(text, "id"))(JOB_APPOINTMENT_STATUS);
      return (
        <Tooltip content={text ?? ""}>
          <Tag color={item?.color}>{item?.name ?? ""}</Tag>
        </Tooltip>
      );
    },
  },
  {
    title: <Tooltip content="预约状态">是否过期</Tooltip>,
    dataIndex: "isOvertime",
    isShow: true,
    ellipsis: true,
    render: (text) => {
      const item = find(propEq(text, "id"))(JOB_APPOINTMENT_ISOVERTIME);
      return (
        <Tooltip content={text ?? ""}>
          <Tag color={item?.color}>{item?.name ?? ""}</Tag>
        </Tooltip>
      );
    },
  },
]);
