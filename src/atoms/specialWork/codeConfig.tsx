import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { Tag, Tooltip } from "@douyinfe/semi-ui";
import { CODE_CONFIG_CODETYPE } from "components";
import { find, propEq } from "ramda";

export const codeConfigConfigModalAtom = atom(false);

export const codeConfigEditModalAtom = atom({
  id: "",
  show: false,
});

// 查询条件
export const codeConfigFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
  filter: {},
});

// 查询条件
export const codeConfigFnAtom = atom({
  refetch: () => {},
});

export const codeConfigColumnsAtom = atom([
  {
    title: <Tooltip content="ID">ID</Tooltip>,
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  {
    title: <Tooltip content="作业编码对象类型">对象类型</Tooltip>,
    dataIndex: "codeType",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const i = find(propEq(item, "id"))(CODE_CONFIG_CODETYPE);

      return (
        <Tag color="grey" type="light" className="min-w-[fit-content]">
          {i?.name}
        </Tag>
      );
    },
  },
  {
    title: <Tooltip content="作业类型">作业类型</Tooltip>,
    dataIndex: "category",
    isShow: true,
    ellipsis: true,
    render: (item) => (
      <Tooltip content={item?.name}>
        <span className="block truncate">{item?.name}</span>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="编码示例">编码示例</Tooltip>,
    dataIndex: "example",
    isShow: true,
    ellipsis: true,
  },
  {
    title: <Tooltip content="是否允许删除">是否允许删除</Tooltip>,
    dataIndex: "isFixed",
    isShow: true,
    ellipsis: true,
    render: (text) =>
      text == 2 ? <Tag color="grey">否</Tag> : <Tag color="green">是</Tag>,
  },
]);
