import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import {
  highPlaceOperationLevelOptions,
  liftingOperationLevelOptions,
  operationLevelOptions,
} from "pages/ticket";
import { find, propEq } from "ramda";
import { Tooltip } from "tdesign-react";
import { CommonAtoms } from "types";

export const jobSliceIntervalFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const jobSliceIntervalFnAtom = atom({
  refetch: () => {},
});

export const jobSliceIntervalEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const jobSliceIntervalConfigModalAtom = atom(false);

const jobSliceIntervalShowColumns = [
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  // user-defined code here
  {
    title: "作业类型",
    dataIndex: "category",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "作业级别",
    dataIndex: "level",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      let i;
      if (record.category?.name == "动火作业") {
        i = find(propEq(item ? item : 0, "id"))(operationLevelOptions);
      } else if (record.category?.name == "高处作业") {
        i = find(propEq(item ? item : 0, "id"))(highPlaceOperationLevelOptions);
      } else if (record.category?.name == "吊装作业") {
        i = find(propEq(item ? item : 0, "id"))(liftingOperationLevelOptions);
      } else {
        i = {};
      }

      return <Tooltip content={i?.label}>{i?.label}</Tooltip>;
    },
  },
  {
    title: "有效期(小时)",
    dataIndex: "interval",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "提前推送时间(分钟)",
    dataIndex: "advanceNoticeTime",
    isShow: true,
    ellipsis: true,
  },
];

const jobSliceIntervalExtendColumns = [
  // user-defined code here
];

export const jobSliceIntervalColumnsAtom = atom([
  ...jobSliceIntervalShowColumns,
  ...jobSliceIntervalExtendColumns,
]);

export const jobSliceIntervalAtoms: CommonAtoms = {
  entity: "JobSliceInterval",
  filter: jobSliceIntervalFilterAtom,
  Fn: jobSliceIntervalFnAtom,
  editModal: jobSliceIntervalEditModalAtom,
  configModal: jobSliceIntervalConfigModalAtom,
  columns: jobSliceIntervalColumnsAtom,
};
