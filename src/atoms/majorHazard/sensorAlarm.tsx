import { Tag, Tooltip } from "@douyinfe/semi-ui";
import {
  IS_ISNOT_MAP,
  PERSONNEL_ALARM_STATUS_MAP,
  SENSOR_ALERTPRIORITY_MAP,
} from "components";
import dayjs from "dayjs";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";
import { CommonAtoms } from "types";
import { formatDate } from "utils";

export const sensorAlarmFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const sensorAlarmFnAtom = atom({
  refetch: () => {},
});

export enum SensorAlarmAnalysisType {
  Analysis,
  BatchAnalysis,
}

export const sensorAlarmEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const sensorAlarmCauseAnalysisModalAtom = atomWithReset({
  id: "",
  ids: [],
  reason: "",
  show: false,
  type: SensorAlarmAnalysisType.Analysis,
});

export const sensorAlarmConfigModalAtom = atom(false);

export const sensorAlarmColumnsAtom = atom([
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  // user-defined code here
  {
    title: "监测名称",
    dataIndex: "name",
    isShow: true,
    ellipsis: true,
    width: 180,
  },
  {
    title: "传感器编号",
    dataIndex: "code",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "报警时间",
    dataIndex: "alarmTime",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const content = formatDate(item);
      if (!content) {
        return null;
      }
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
  },
  {
    title: "是否恢复",
    dataIndex: "isNormal",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item ? item : 1, "id"))(IS_ISNOT_MAP);
      return (
        <Tooltip content={i?.name}>
          <Tag color={i?.color} type="light">
            {i?.name}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "恢复时间",
    dataIndex: "normalTime",
    isShow: true,
    ellipsis: true,
    render: (text, record, index) => {
      return (
        <Tooltip content={dayjs(text).format("YYYY-MM-DD HH:mm:ss")}>
          <p>
            {text ? (
              dayjs(text).format("YYYY-MM-DD HH:mm:ss")
            ) : (
              <Tag color="red">报警中</Tag>
            )}
          </p>
        </Tooltip>
      );
    },
  },
  {
    title: "报警优先级",
    dataIndex: "priority",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item ? item : 1, "id"))(SENSOR_ALERTPRIORITY_MAP);
      return (
        <Tooltip content={i.name}>
          <Tag color={i.color} type="light">
            {i.name}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "监测类型",
    dataIndex: "monitorTypeValue",
    isShow: true,
    ellipsis: true,
    render: (r) => <p>{r?.dicValue ?? ""}</p>,
  },
  {
    title: "报警监测值",
    dataIndex: "sampleValue",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "报警类型",
    dataIndex: "alarmType",
    isShow: true,
    ellipsis: true,
    /* render: (item, record, index) => {
      let res = ''
      switch (item) {
        case SENSOR_DATATYPE_MAP[0].id:
          record.smapleValue >= record.highHighAlarm ? res = '高高报' :
            record.sampleValue >= record.highAlarm ? res = '高报' :
              record.sampleValue <= record.lowLowAlarm ? res = '低低报' :
                record.sampleValue <= record.lowAlarm ? res = '低报' :
                  res = '正常'
          break
        case SENSOR_DATATYPE_MAP[1].id:
          const stateRes = record.stateCodeList.filter((ele) => item === ele.state)
          if (stateRes.length > 0) {
            res = stateRes[0].name
          } else {
            res = '未知状态码'
          }
          break
        default:
          res = '类型错误'
      }
      return (
        <p>
          {res}
        </p>
      )
    } */
  },
  {
    title: "报警原因",
    dataIndex: "alarmReasonTypeValue",
    isShow: true,
    ellipsis: true,
    render: (r) => (
      <Tooltip content={r?.dicValue ?? ""}>
        <p className="text-ellipsis overflow-hidden">{r?.dicValue ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "处理人",
    dataIndex: "processPerson",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "处理时间",
    dataIndex: "processTime",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const content = formatDate(item);
      if (!content) {
        return null;
      }
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
  },
  {
    title: "处理措施",
    dataIndex: "alarmMeasureTypeValue",
    isShow: true,
    ellipsis: true,
    render: (r) => <p>{r?.dicValue ?? ""}</p>,
  },
  {
    title: "状态",
    dataIndex: "status",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item ? item : 1, "id"))(PERSONNEL_ALARM_STATUS_MAP);
      return (
        <Tooltip content={i.name}>
          <Tag color={i.color} type="light">
            {i.name}
          </Tag>
        </Tooltip>
      );
    },
  },
]);

export const sensorAlarmAtoms: CommonAtoms = {
  entity: "SensorAlarm",
  filter: sensorAlarmFilterAtom,
  Fn: sensorAlarmFnAtom,
  editModal: sensorAlarmEditModalAtom,
  configModal: sensorAlarmConfigModalAtom,
  columns: sensorAlarmColumnsAtom,
};
