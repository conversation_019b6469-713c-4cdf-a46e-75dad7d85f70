import { Tag, Tooltip } from "@douyinfe/semi-ui";
import {
  PERSONNEL_ALARM_STATUS_MAP,
  PERSONNEL_ALARM_TYPE_MAP,
} from "components";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";
import { CommonAtoms } from "types";
import { formatDate } from "utils";

/* const { data: alarmTypeList } = useQuery({
  queryKey: ['getPersonnelAlarmTypeList'],
  queryFn: () => getPersonnelAlarmTypeList(),
})
const alarmTypeListOptions = useMemo(() => {
  return alarmTypeList?.data ?? []
}, [alarmTypeList]) */

export const personnelAlarmFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const personnelAlarmFnAtom = atom({
  refetch: () => {},
});

export const personnelAlarmEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const personnelAlarmProcessModalAtom = atomWithReset({
  ids: [],
  note: "",
  show: false,
});

export const personnelAlarmConfigModalAtom = atom(false);

export const personnelAlarmColumns = [
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  // user-defined code here
  {
    title: "报警对象",
    dataIndex: "name",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "标签卡MAC",
    dataIndex: "mac",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "告警位置",
    dataIndex: "areaName",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "围栏名称",
    dataIndex: "electronName",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "报警类型",
    dataIndex: "warningType",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item ? item : 1, "id"))(PERSONNEL_ALARM_TYPE_MAP);
      return (
        <Tooltip content={i?.name}>
          <Tag color={i?.color} type="light">
            {i?.name}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "报警开始时间",
    dataIndex: "beginTime",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const content = formatDate(item);
      if (!content) {
        return null;
      }
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
  },
  {
    title: "报警结束时间",
    dataIndex: "endTime",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const content = formatDate(item);
      if (!content) {
        return null;
      }
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
  },
  {
    title: "处理人",
    dataIndex: "confirmName",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "处理意见",
    dataIndex: "confirmOpinion",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "处理时间",
    dataIndex: "confirmTime",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const content = formatDate(item);
      if (!content) {
        return null;
      }
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
  },
  {
    title: "状态",
    dataIndex: "status",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item ? item : 1, "id"))(PERSONNEL_ALARM_STATUS_MAP);
      return <Tooltip content={i?.name}>{i?.name}</Tooltip>;
    },
  },
];
export const personnelAlarmColumnsAtom = atom(personnelAlarmColumns);

export const personnelAlarmAtoms: CommonAtoms = {
  entity: "PersonnelAlarm",
  filter: personnelAlarmFilterAtom,
  Fn: personnelAlarmFnAtom,
  editModal: personnelAlarmEditModalAtom,
  configModal: personnelAlarmConfigModalAtom,
  columns: personnelAlarmColumnsAtom,
};
