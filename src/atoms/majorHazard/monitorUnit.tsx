import { Tag, Tooltip } from "@douyinfe/semi-ui";
import { MONITORUNIT_TYPE_MAP } from "components";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";
import { CommonAtoms } from "types";

export const monitorUnitFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const monitorUnitFnAtom = atom({
  refetch: () => {},
});

export const monitorUnitEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const monitorUnitConfigModalAtom = atom(false);

export const monitorUnitColumnsAtom = atom([
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  // user-defined code here
  {
    title: "监测单元类型",
    dataIndex: "type",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item ? item : 1, "id"))(MONITORUNIT_TYPE_MAP);
      return (
        <Tooltip content={i.name}>
          <Tag color={i.color} type="light">
            {i.name}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "监测单元名称",
    dataIndex: "unit",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "展示分类",
    dataIndex: "displayCategory",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "传感器",
    dataIndex: "sensors",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const sensors = item?.map((sensor: any) => sensor.name).join(", ");
      return (
        <Tooltip content={sensors}>
          <p>{sensors}</p>
        </Tooltip>
      );
    },
  },
  {
    title: "摄像头",
    dataIndex: "monitors",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const monitors = item?.map((monitor: any) => monitor.name).join(", ");
      return (
        <Tooltip content={monitors}>
          <p>{monitors}</p>
        </Tooltip>
      );
    },
  },
  {
    title: "位置",
    dataIndex: "longitude",
    isShow: true,
    ellipsis: true,
  },
]);

export const monitorUnitAtoms: CommonAtoms = {
  entity: "MonitorUnit",
  filter: monitorUnitFilterAtom,
  Fn: monitorUnitFnAtom,
  editModal: monitorUnitEditModalAtom,
  configModal: monitorUnitConfigModalAtom,
  columns: monitorUnitColumnsAtom,
};
