import { MAJORRISKORIGIN_CLASSIFY_ISACTIVE_MAP } from "components";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";
import { CommonAtoms } from "types";

export const displayCategoryFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const displayCategoryFnAtom = atom({
  refetch: () => {},
});

export const displayCategoryEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const displayCategoryConfigModalAtom = atom(false);

export const displayCategoryColumnsAtom = atom([
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  {
    title: "展示分类名称",
    dataIndex: "name",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "是否展示",
    dataIndex: "isActive",
    isShow: true,
    render: (item) => {
      const i = find(propEq(item ? item : 1, "id"))(
        MAJORRISKORIGIN_CLASSIFY_ISACTIVE_MAP,
      );
      return <p>{i?.name ?? ""}</p>;
    },
  },
  {
    title: "排序",
    dataIndex: "number",
    isShow: true,
  },
  {
    title: "监测单元数量",
    dataIndex: "monitorUnitNum",
    isShow: true,
  },
]);

export const displayCategoryAtoms: CommonAtoms = {
  entity: "DisplayCategory",
  filter: displayCategoryFilterAtom,
  Fn: displayCategoryFnAtom,
  editModal: displayCategoryEditModalAtom,
  configModal: displayCategoryConfigModalAtom,
  columns: displayCategoryColumnsAtom,
};
