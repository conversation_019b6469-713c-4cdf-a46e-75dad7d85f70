import { Image, ImagePreview, Tag, Tooltip } from "@douyinfe/semi-ui";
import { ALARM_STATUS_MAP, ALARM_TYPE_MAP } from "components";
import { base_url } from "config";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";
import { CommonAtoms } from "types";
import { formatDate } from "utils";

export const alarmFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const alarmFnAtom = atom({
  refetch: () => {},
});

export const alarmEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const alarmConfigModalAtom = atom(false);

export const alarmColumnsAtom = atom([
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  // user-defined code here
  {
    title: "报警类型",
    dataIndex: "type",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item ? item : 1, "id"))(ALARM_TYPE_MAP);
      return (
        <Tooltip content={i.name}>
          <Tag color={i.color} type="light">
            {i.name}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "报警信息",
    dataIndex: "name",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "报警地点",
    dataIndex: "place",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "报警描述",
    dataIndex: "note",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "报警时间",
    dataIndex: "alarmTime",
    isShow: true,
    ellipsis: true,
    width: 200,
    render: (item) => {
      const content = formatDate(item);
      if (!content) {
        return null;
      }
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
  },
  {
    title: "图片",
    dataIndex: "imageList",
    isShow: true,
    ellipsis: true,
    render: (record) => {
      if (!record) {
        return null;
      }

      return (
        <ImagePreview>
          {record.map((o) => (
            <Image width={30} height={30} src={`${base_url}${o}`} />
          ))}
        </ImagePreview>
      );
    },
  },
  {
    title: "处理人",
    dataIndex: "processPerson",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "处理时间",
    dataIndex: "processTime",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const content = formatDate(item);
      if (!content) {
        return null;
      }
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
  },
  /* {
    title: "原因分析",
    dataIndex: "",
    isShow: true,
    ellipsis: true,
  }, */
  {
    title: "状态",
    dataIndex: "status",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item ? item : 1, "id"))(ALARM_STATUS_MAP);
      return (
        <Tooltip content={i.name}>
          <Tag color={i.color} type="light">
            {i.name}
          </Tag>
        </Tooltip>
      );
    },
  },
]);

export const alarmAtoms: CommonAtoms = {
  entity: "Alarm",
  filter: alarmFilterAtom,
  Fn: alarmFnAtom,
  editModal: alarmEditModalAtom,
  configModal: alarmConfigModalAtom,
  columns: alarmColumnsAtom,
};
