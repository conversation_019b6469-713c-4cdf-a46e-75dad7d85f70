import { STATUS_MAP } from "components";
import { Tag, Tooltip } from "@douyinfe/semi-ui";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";
import { CommonAtoms } from "types";

export const omConfigFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const omConfigFnAtom = atom({
  refetch: () => {},
});

export const omConfigEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const omConfigConfigModalAtom = atom(false);

export const omConfigColumnsAtom = atom([
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  // user-defined code here
]);

export const omConfigAtoms: CommonAtoms = {
  entity: "OmConfig",
  filter: omConfigFilter<PERSON>tom,
  Fn: omConfigFnAtom,
  editModal: omConfigEditModalAtom,
  configModal: omConfigConfigModalAtom,
  columns: omConfigColumnsAtom,
};
