import { Tooltip } from "@douyinfe/semi-ui";
import dayjs from "dayjs";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { CommonAtoms } from "types";

export const dutyInformationFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const dutyInformationFnAtom = atom({
  refetch: () => {},
});

export const dutyInformationEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const dutyInformationConfigModalAtom = atom(false);

export const dutyInformationColumnsAtom = atom([
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  {
    title: "值班领导",
    dataIndex: "liablePerson",
    isShow: true,
    ellipsis: true,
    render: (item) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "部门",
    dataIndex: "liableDepartment",
    isShow: true,
    render: (item) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "手机号",
    dataIndex: "liablePersonMobile",
    isShow: true,
  },
  {
    title: "值班时间",
    // TODO 值班时间
    dataIndex: "beginTime",
    isShow: true,
    render: (item, record, index) => {
      return (
        <p>
          {item ? dayjs(item).format("YYYY-MM-DD HH:mm") : ""} ~{" "}
          {record["endTime"]
            ? dayjs(record["endTime"]).format("YYYY-MM-DD HH:mm")
            : ""}
        </p>
      );
    },
  },
]);

export const dutyInformationAtoms: CommonAtoms = {
  entity: "DutyInformation",
  filter: dutyInformationFilterAtom,
  Fn: dutyInformationFnAtom,
  editModal: dutyInformationEditModalAtom,
  configModal: dutyInformationConfigModalAtom,
  columns: dutyInformationColumnsAtom,
};
