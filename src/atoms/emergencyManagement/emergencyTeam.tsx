import { Tag, Tooltip } from "@douyinfe/semi-ui";
import { EMERGENCYTEAM_LEVEL_MAP } from "components";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";
import { CommonAtoms } from "types";

export const emergencyTeamFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const emergencyTeamFnAtom = atom({
  refetch: () => {},
});

export const emergencyTeamEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const emergencyTeamConfigModalAtom = atom(false);

export const emergencyTeamColumnsAtom = atom([
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  {
    title: "队伍名称",
    dataIndex: "name",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "队伍级别",
    dataIndex: "level",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const i = find(propEq(item ? item : 1, "id"))(EMERGENCYTEAM_LEVEL_MAP);
      return (
        <Tooltip content={i.name}>
          <Tag color={i.color} type="light">
            {i.name}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "队伍负责人",
    dataIndex: "liablePerson",
    isShow: true,
    ellipsis: true,
    render: (item) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "负责人部门",
    dataIndex: "liableDepartment",
    isShow: true,
    ellipsis: true,
    render: (item) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "负责人手机号",
    dataIndex: "liablePersonMobile",
    isShow: true,
    ellipsis: true,
  },
]);

export const emergencyTeamAtoms: CommonAtoms = {
  entity: "EmergencyTeam",
  filter: emergencyTeamFilterAtom,
  Fn: emergencyTeamFnAtom,
  editModal: emergencyTeamEditModalAtom,
  configModal: emergencyTeamConfigModalAtom,
  columns: emergencyTeamColumnsAtom,
};
