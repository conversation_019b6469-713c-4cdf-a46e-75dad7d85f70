import { Tag, Tooltip } from "@douyinfe/semi-ui";
import {
  CHECK_CYCLE_UNIT_MAP,
  CLASSIFY1_MAP,
  CLASSIFY2_MAP,
  DANGER_REPORT_STATUS_MAP,
  TASK_STATUS_MAP,
} from "components";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq, whereEq } from "ramda";
import { CommonAtoms } from "types";

export const bbCheckTaskFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const bbCheckTaskFnAtom = atom({
  refetch: () => {},
});

export const bbCheckTaskEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const bbCheckTaskConfigModalAtom = atom(false);

const bbCheckTaskShowColumns = [
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  // user-defined code here
  {
    title: "排查计划ID",
    dataIndex: "checkPlanId",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "风险对象",
    dataIndex: "riskObject",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "风险单元",
    dataIndex: "riskUnit",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "风险事件",
    dataIndex: "riskEvent",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="一级分类">一级分类</Tooltip>,
    dataIndex: "classify1",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const i = find(propEq(item ? item : 1, "id"))(CLASSIFY1_MAP);
      return (
        <Tag color="grey" type="light" className="min-w-[fit-content]">
          {i.name}
        </Tag>
      );
    },
  },
  {
    title: <Tooltip content="二级分类">二级分类</Tooltip>,
    dataIndex: "classify2",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      if (!item) {
        return (
          <Tag color="grey" type="light" className="min-w-[fit-content]">
            未分类
          </Tag>
        );
      }
      const k = item?.split("-");
      const i = find(
        whereEq({
          pid: parseInt(k[0]),
          id: parseInt(k[1]),
        })
      )(CLASSIFY2_MAP);

      return (
        <Tag color="grey" type="light" className="min-w-[fit-content]">
          {i.name}
        </Tag>
      );
    },
  },
  {
    title: <Tooltip content="三级分类">三级分类</Tooltip>,
    dataIndex: "classify3",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      if (!item) return null;
      return (
        <Tag color="grey" type="light" className="min-w-[fit-content]">
          {item}
        </Tag>
      );
    },
  },
  {
    title: <Tooltip content="排查人">排查人</Tooltip>,
    dataIndex: "informPerson",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      if (!item) return null;
      const value = item.map((o) => o.name).join(",");
      return (
        <Tooltip content={value}>
          <p>{value}</p>
        </Tooltip>
      );
    },
  },
  {
    // title: <Tooltip content="排查周期">排查周期</Tooltip>,
    dataIndex: "checkCycle",
    // isShow: true,
    // ellipsis: true,
    align: "center",
    // }, {
    title: (
      <Tooltip
        content="任务
    排查周期"
      >
        任务排查周期
      </Tooltip>
    ),
    // dataIndex: 'checkCycleUnit',
    isShow: true,
    ellipsis: true,
    render: (text, record) => {
      const i = find(
        propEq(record?.checkCycleUnit ? record?.checkCycleUnit : 1, "id")
      )(CHECK_CYCLE_UNIT_MAP);
      if (record.checkCycle === undefined) return null;
      return (
        <Tag color="grey" type="light" className="min-w-[fit-content]">
          {record.checkCycle}
          {i.id === 3 ? "个" : ""}
          {i.name}
          {/* {record.checkCycle}{i?.name === '月' ? '个' : ''}{i.name} */}
        </Tag>
      );
    },
  },
  {
    title: (
      <Tooltip content="是否是包保责任排查任务">是否是包保责任排查任务</Tooltip>
    ),
    dataIndex: "isBb",
    isShow: true,
    ellipsis: true,
    align: "center",
    render: (item) => {
      if (item == 1) {
        return (
          <Tag color="green" type="light">
            是
          </Tag>
        );
      }
      return (
        <Tag color="red" type="light">
          否
        </Tag>
      );
    },
  },
  {
    title: <Tooltip content="状态">状态</Tooltip>,
    dataIndex: "status",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const i = find(propEq(item ? item : 1, "id"))(TASK_STATUS_MAP);
      return (
        <Tag color="grey" type="light" className="min-w-[fit-content]">
          {i.name}
        </Tag>
      );
    },
  },
  {
    title: <Tooltip content="是否逾期">是否逾期</Tooltip>,
    dataIndex: "isOvertime",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      if (item == 1) {
        return (
          <Tag color="green" type="light">
            是
          </Tag>
        );
      }
      return (
        <Tag color="red" type="light">
          否
        </Tag>
      );
    },
  },
  {
    title: <Tooltip content="上报状态">上报状态</Tooltip>,
    dataIndex: "uploadStatus",
    isShow: false,
    ellipsis: true,
    render: (item) => {
      const i = find(propEq(item, "id"))(DANGER_REPORT_STATUS_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
];

const bbCheckTaskExtendColumns = [
  // user-defined code here
];

export const bbCheckTaskColumnsAtom = atom([
  ...bbCheckTaskShowColumns,
  ...bbCheckTaskExtendColumns,
]);

export const bbCheckTaskAtoms: CommonAtoms = {
  entity: "BbCheckTask",
  filter: bbCheckTaskFilterAtom,
  Fn: bbCheckTaskFnAtom,
  editModal: bbCheckTaskEditModalAtom,
  configModal: bbCheckTaskConfigModalAtom,
  columns: bbCheckTaskColumnsAtom,
};
