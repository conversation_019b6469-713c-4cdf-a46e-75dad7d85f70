import { OVERTIME_OVERTIMENOT_MAP, TASK_STATUS_MAP } from "components";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";
import { Tag, Tooltip } from "tdesign-react";
import { CommonAtoms } from "types";

export const doubleGuardCcTaskItemFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const doubleGuardCcTaskItemFnAtom = atom({
  refetch: () => {},
});

export const doubleGuardCcTaskItemEditModalAtom = atomWithReset({
  id: "",
  show: false,
  type: 0,
});

export const doubleGuardCcTaskItemConfigModalAtom = atom(false);

const doubleGuardCcTaskItemShowColumns = [
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 48,
  },
  // user-defined code here
  {
    title: "检查类别",
    dataIndex: "category.name",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "检查项目",
    dataIndex: "name",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "检查方式",
    dataIndex: "method",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "检查内容",
    dataIndex: "content",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "检查状态",
    dataIndex: "status",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(TASK_STATUS_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "是否逾期",
    dataIndex: "isOvertime",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(OVERTIME_OVERTIMENOT_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
];

const doubleGuardCcTaskItemExtendColumns = [
  // user-defined code here
];

export const doubleGuardCcTaskItemShowColumnsAtom = atom(
  doubleGuardCcTaskItemShowColumns
);

export const doubleGuardCcTaskItemColumnsAtom = atom([
  ...doubleGuardCcTaskItemShowColumns,
  ...doubleGuardCcTaskItemExtendColumns,
]);

/*export const doubleGuardCcTaskItemColumnsAtom = atom(
  (get) => get(doubleGuardCcTaskItemShowColumnsAtom).concat(get(doubleGuardCcTaskItemExtendColumnsAtom)),
  (get, set, newValue: any) => {
    set(doubleGuardCcTaskItemShowColumnsAtom, newValue.slice(0, newValue.length / 2)); // 更新 ShowColumnsAtom
    set(doubleGuardCcTaskItemExtendColumnsAtom, newValue.slice(newValue.length / 2)); // 更新 ExtendColumnsAtom
  }
)*/

export const doubleGuardCcTaskItemAtoms: CommonAtoms = {
  entity: "DoubleGuardCcTaskItem",
  filter: doubleGuardCcTaskItemFilterAtom,
  Fn: doubleGuardCcTaskItemFnAtom,
  editModal: doubleGuardCcTaskItemEditModalAtom,
  configModal: doubleGuardCcTaskItemConfigModalAtom,
  columns: doubleGuardCcTaskItemColumnsAtom,
};
