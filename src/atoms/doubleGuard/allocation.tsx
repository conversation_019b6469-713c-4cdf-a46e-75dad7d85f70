import { Tag, Tooltip } from "@douyinfe/semi-ui";
import {
  CHECK_CYCLE_UNIT_MAP,
  CLASSIFY1_MAP,
  CLASSIFY2_MAP,
  CONTROLTYPE_MAP,
  PLAN_CHECK_TYPE_MAP,
  PLAN_WORKDAYTYPE_MAP,
} from "components";
import dayjs from "dayjs";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq, whereEq } from "ramda";
// allocation
export const allocationConfigModalAtom = atom(false);

export const allocationEditModalAtom = atom({
  item: null,
  show: false,
});

export const checkPlanModalAtom = atomWithReset({
  id: null,
  show: false,
  index: null,
  saveToBatch: false,
});

export const checkPlanBatchModalAtom = atomWithReset({
  items: [],
  show: false,
  plans: [],
});

// 风险事件
export const allocationEvaluationModalAtom = atom({
  id: null,
  show: false,
});

// 查询条件
export const allocationFilterAtom = atomWithReset<AllocationParams>({
  pageNumber: 1,
  pageSize: 10,
  filter: {},
});

// 选中项
export const allocationSelectAtom = atom<string | null>(null);

// 全部数据存储
export const allocationDataAtom = atom<any[]>([]);

// 刷新
export const allocationFnAtom = atom({
  refetch: () => {},
});

export const allocationColumnsAtom = atom([
  {
    title: <Tooltip content="ID">ID</Tooltip>,
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  {
    title: <Tooltip content="关联风险对象">关联风险对象</Tooltip>,
    dataIndex: "riskObject",
    isShow: true,
    ellipsis: true,
    render: (item) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="关联风险单元">关联风险单元</Tooltip>,
    dataIndex: "riskUnit",
    isShow: true,
    ellipsis: true,
    render: (item) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="关联风险事件">关联风险事件</Tooltip>,
    dataIndex: "riskEvent",
    isShow: true,
    ellipsis: true,
    render: (item) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="管控方式">管控方式</Tooltip>,
    dataIndex: "controlType",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const i = find(propEq(item ? item : 1, "id"))(CONTROLTYPE_MAP);
      return <p>{i?.name ?? ""}</p>;
    },
  },
  {
    title: <Tooltip content="一级分类">一级分类</Tooltip>,
    dataIndex: "classify1",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const i = find(propEq(item ? item : 1, "id"))(CLASSIFY1_MAP);
      return (
        <Tooltip content={i?.name}>
          <Tag color="grey" type="light" className="min-w-[fit-content]">
            {i?.name}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: <Tooltip content="二级分类">二级分类</Tooltip>,
    dataIndex: "classify2",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      if (!item) {
        return (
          <Tooltip content={"未分类"}>
            <Tag color="grey" type="light" className="min-w-[fit-content]">
              未分类
            </Tag>
          </Tooltip>
        );
      }
      const k = item?.split("-");
      const i = find(
        whereEq({
          pid: parseInt(k[0]),
          id: parseInt(k[1]),
        }),
      )(CLASSIFY2_MAP);

      return (
        <Tooltip content={i?.name}>
          <Tag color="grey" type="light" className="min-w-[fit-content]">
            {i?.name}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: <Tooltip content="三级分类">三级分类</Tooltip>,
    dataIndex: "classify3",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      if (!item) return null;
      return (
        <Tooltip content={item}>
          <Tag color="grey" type="light" className="min-w-[fit-content]">
            {item}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: <Tooltip content="管控措施">管控措施</Tooltip>,
    dataIndex: "controlMeasure",
    isShow: true,
    ellipsis: true,
    render: (item) => (
      <Tooltip content={item}>
        <p>{item}</p>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="隐患排查内容">隐患排查内容</Tooltip>,
    dataIndex: "troubleShootContent",
    isShow: true,
    ellipsis: true,
    render: (item) => (
      <Tooltip content={item}>
        <p>{item}</p>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="计划个数">计划个数</Tooltip>,
    dataIndex: "checkPlanNum",
    isShow: true,
    ellipsis: true,
    align: "center",
  },
]);

export const allocationTablesAtom = atom([
  {
    title: <Tooltip content="ID">ID</Tooltip>,
    dataIndex: "id",
    isShow: true,
    fixed: true,
    width: 70,
  },
  {
    title: <Tooltip content="管控措施ID">管控措施ID</Tooltip>,
    dataIndex: "riskControlMeasureId",
    align: "center",
    width: 120,
    fixed: true,
    ellipsis: true,
  },
  {
    title: <Tooltip content="风险对象">风险对象</Tooltip>,
    dataIndex: "riskObject",
    isShow: true,
    ellipsis: true,
    align: "center",
    render: (item) => <p>{item?.name}</p>,
  },
  {
    title: <Tooltip content="风险单元">风险单元</Tooltip>,
    dataIndex: "riskUnit",
    isShow: true,
    ellipsis: true,
    align: "center",
    width: 200,
    render: (item) => <p>{item?.name}</p>,
  },
  {
    title: <Tooltip content="风险事件">风险事件</Tooltip>,
    dataIndex: "riskEvent",
    isShow: true,
    ellipsis: true,
    align: "center",
    width: 200,
    render: (item) => <p>{item?.name}</p>,
  },
  {
    title: <Tooltip content="排查人员类型">排查人员类型</Tooltip>,
    dataIndex: "checkType",
    render: (item) => {
      const i = find(propEq(item ? item : 1, "id"))(PLAN_CHECK_TYPE_MAP);
      return (
        <Tag color="grey" type="light" className="min-w-[fit-content]">
          {i.name}
        </Tag>
      );
    },
  },
  {
    title: <Tooltip content="工作人员">工作人员</Tooltip>,
    dataIndex: "person",
    render: (item) => {
      if (!item || !item?.length) {
        return null;
      }
      return (
        <Tooltip content={item.map((o) => o.name).toString()}>
          <div className="flex gap-1">
            {(item ?? []).map((o) => (
              <Tag color="grey" type="light" className="min-w-[fit-content]">
                {o.name}
              </Tag>
            ))}
          </div>
        </Tooltip>
      );
    },
  },
  {
    title: <Tooltip content="工作组">工作组</Tooltip>,
    dataIndex: "group",
    render: (item) => {
      if (!item || !item?.id) {
        return null;
      }
      return (
        <Tag color="grey" type="light" className="min-w-[fit-content]">
          {item.name}
        </Tag>
      );
    },
  },
  {
    // title: <Tooltip content="排查间隔">排查间隔</Tooltip>,
    dataIndex: "checkCycle",
    // isShow: true,
    // ellipsis: true,
    align: "center",
    // },
    // {
    title: <Tooltip content="任务排查周期">任务排查周期</Tooltip>,
    // dataIndex: 'checkCycleUnit',
    isShow: true,
    ellipsis: true,
    render: (text, record) => {
      const i = find(
        propEq(record?.checkCycleUnit ? record?.checkCycleUnit : 1, "id"),
      )(CHECK_CYCLE_UNIT_MAP);
      if (record.checkCycle === undefined) return null;
      return (
        <Tag color="grey" type="light" className="min-w-[fit-content]">
          {record.checkCycle}
          {i.id !== 2 ? "个" : ""}
          {i.name}
        </Tag>
      );
    },
  },
  {
    title: <Tooltip content="持续时间">持续时间</Tooltip>,
    dataIndex: "duration",
    align: "center",
    isShow: true,
    ellipsis: true,
    render: (text, record) => {
      const i = find(
        propEq(record?.durationUnit ? record?.durationUnit : 1, "id"),
      )(CHECK_CYCLE_UNIT_MAP);
      if (record.duration === undefined) return null;
      return (
        <Tag color="grey" type="light" className="min-w-[fit-content]">
          {record.duration}
          {i.id !== 2 ? "个" : ""}
          {i.name}
        </Tag>
      );
    },
  },
  {
    title: <Tooltip content="工作日类型">工作日类型</Tooltip>,
    dataIndex: "workDayType",
    ellipsis: true,
    render: (item) => {
      // if (!item || !item?.id) {
      //   return null
      // }
      const i = find(propEq(item ? item : 1, "id"))(PLAN_WORKDAYTYPE_MAP);
      return (
        <Tag color="grey" type="light" className="min-w-[fit-content]">
          {i?.name}
        </Tag>
      );
    },
  },
  {
    title: <Tooltip content="责任部门">责任部门</Tooltip>,
    dataIndex: "liableDepartment",
    ellipsis: true,
    render: (item) => {
      if (!item || !item?.id) {
        return null;
      }
      return (
        <Tag color="grey" type="light" className="min-w-[fit-content]">
          {item.name}
        </Tag>
      );
    },
  },
  {
    title: <Tooltip content="开始小时">开始小时</Tooltip>,
    dataIndex: "beginHour",
    ellipsis: true,
    render: (hour) => {
      if (!Number.isFinite(hour)) return null;
      if (hour > 0) {
        if (hour < 11) {
          return `早上 ${hour} 点`;
        }
        if (hour < 14) {
          return `中午 ${hour} 点`;
        }
        if (hour < 18) {
          return `下午 ${hour} 点`;
        }
      }
      return `晚上 ${hour} 点`;
    },
  },
  {
    title: <Tooltip content="结束小时">结束小时</Tooltip>,
    dataIndex: "endHour",
    ellipsis: true,
    render: (hour) => {
      if (!Number.isFinite(hour)) return null;
      if (hour > 0) {
        if (hour < 11) {
          return `早上 ${hour} 点`;
        }
        if (hour < 14) {
          return `中午 ${hour} 点`;
        }
        if (hour < 18) {
          return `下午 ${hour} 点`;
        }
      }
      return `晚上 ${hour} 点`;
    },
  },
  {
    title: <Tooltip content="首次生效时间">首次生效时间</Tooltip>,
    dataIndex: "startDatetime",
    ellipsis: true,
    width: 280,
    render: (item) => {
      return <p>{dayjs(item).format("YYYY-MM-DD HH:mm")}</p>;
    },
  },
]);
