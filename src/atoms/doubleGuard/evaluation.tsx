import { atom } from "jotai";
import { atomWithReset, useResetAtom } from "jotai/utils";
import { find, where, __, gt, lt, both, propEq, includes } from "ramda";
import { RISK_LEVEL_EDIT_MAP } from "components";

export const evaluationStepAtom = atomWithReset(0);

export const evaluationValuesAtom = atomWithReset({
  L: 0,
  S: 0,
});

// LS计算结果
export const evaluationLevelAtom = atom((get) => {
  const _l = get(evaluationValuesAtom).L;
  const _s = get(evaluationValuesAtom).S;
  const r = _l * _s;
  const result = RISK_LEVEL_EDIT_MAP.filter((o) => includes(r, o.rangeValue));
  return result?.length
    ? result[0]
    : find(propEq(4, "id"))(RISK_LEVEL_EDIT_MAP);
});

// 最终计算结果
export const evaluationResultAtom = atomWithReset<number>(4);
