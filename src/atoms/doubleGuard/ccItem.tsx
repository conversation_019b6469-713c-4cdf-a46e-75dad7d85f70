import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { CommonAtoms } from "types";

export const doubleGuardCcItemFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const doubleGuardCcItemFnAtom = atom({
  refetch: () => {},
});

export const doubleGuardCcItemEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const doubleGuardCcItemConfigModalAtom = atom(false);

const doubleGuardCcItemShowColumns = [
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 48,
  },
  // user-defined code here
  {
    title: "检查类别",
    dataIndex: "category.name",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "检查项目",
    dataIndex: "name",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "检查方式",
    dataIndex: "method",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "检查内容",
    dataIndex: "content",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "检查依据",
    dataIndex: "basis",
    isShow: true,
    ellipsis: true,
  },
];

const doubleGuardCcItemExtendColumns = [
  // user-defined code here
];

export const doubleGuardCcItemShowColumnsAtom = atom(
  doubleGuardCcItemShowColumns
);

export const doubleGuardCcItemColumnsAtom = atom([
  ...doubleGuardCcItemShowColumns,
  ...doubleGuardCcItemExtendColumns,
]);

/*export const doubleGuardCcItemColumnsAtom = atom(
  (get) => get(doubleGuardCcItemShowColumnsAtom).concat(get(doubleGuardCcItemExtendColumnsAtom)),
  (get, set, newValue: any) => {
    set(doubleGuardCcItemShowColumnsAtom, newValue.slice(0, newValue.length / 2)); // 更新 ShowColumnsAtom
    set(doubleGuardCcItemExtendColumnsAtom, newValue.slice(newValue.length / 2)); // 更新 ExtendColumnsAtom
  }
)*/

export const doubleGuardCcItemAtoms: CommonAtoms = {
  entity: "DoubleGuardCcItem",
  filter: doubleGuardCcItemFilterAtom,
  Fn: doubleGuardCcItemFnAtom,
  editModal: doubleGuardCcItemEditModalAtom,
  configModal: doubleGuardCcItemConfigModalAtom,
  columns: doubleGuardCcItemColumnsAtom,
};
