import { Tag, Tooltip } from "@douyinfe/semi-ui";
import {
  ALLOW_ALLOWNOT_MAP,
  ALLOW_REPORT_STATUS_MAP,
  DANGER_REPORT_STATUS_MAP,
  RISK_LEVEL_MAP,
  UNIT_STATUS_MAP,
} from "components";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";

export const unitConfigModalAtom = atom(false);

export const unitEditModalAtom = atom({
  id: "",
  show: false,
});

export enum UnitModalType {
  Stop,
  History,
}

export const unitModalAtom = atom({
  type: UnitModalType.Stop,
  item: null,
  show: false,
});

export enum CheckMethod {
  None,
  Normal,
  Scan,
  Nfc,
}

// 查询条件
export const unitFilterAtom = atomWithReset<UnitParams>({
  pageNumber: 1,
  pageSize: 10,
  filter: {},
  departmentId: null,
});

export const unitStopFilterAtom = atomWithReset<UnitParams>({
  pageNumber: 1,
  pageSize: 10,
  filter: {},
});

// 查询条件
export const unitFnAtom = atom({
  refetch: () => {},
});

export const unitColumnsAtom = atom([
  {
    title: <Tooltip content="ID">ID</Tooltip>,
    dataIndex: "id",
    isShow: true,
    fixed: true,
    width: 80,
  },
  {
    title: <Tooltip content="单元名称">单元名称</Tooltip>,
    dataIndex: "name",
    isShow: true,
    ellipsis: true,
    fixed: true,
    width: 180,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="关联风险对象">关联风险对象</Tooltip>,
    dataIndex: "riskObject",
    isShow: true,
    ellipsis: true,
    render: (item) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="固有风险等级">固有风险等级</Tooltip>,
    dataIndex: "riskLevel",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const index = find(propEq(item || 5, "id"))(RISK_LEVEL_MAP);
      return (
        <Tooltip content={index.name}>
          <Tag color={index.color} type="light">
            {index.name}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: <Tooltip content="剩余风险等级">剩余风险等级</Tooltip>,
    dataIndex: "remainRiskLevel",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const index = find(propEq(item || 5, "id"))(RISK_LEVEL_MAP);
      return (
        <Tooltip content={index.name}>
          <Tag color={index.color} type="light">
            {index.name}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: <Tooltip content="危害分析">危害分析</Tooltip>,
    dataIndex: "riskAnalysis",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="主要后果">主要后果</Tooltip>,
    dataIndex: "riskMajorResult",
    isShow: true,
    ellipsis: true,
    render: (item) => (
      <Tooltip content={item}>
        <p className="max-w-full truncate">{item}</p>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="责任人">责任人</Tooltip>,
    dataIndex: "liablePerson",
    isShow: true,
    ellipsis: true,
    render: (item) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="责任部门">责任部门</Tooltip>,
    dataIndex: "liableDepartment",
    isShow: true,
    ellipsis: true,
    render: (item) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="状态">状态</Tooltip>,
    dataIndex: "status",
    align: "center",
    isShow: true,
    render: (item) => {
      const index = find(propEq(item || 1, "id"))(UNIT_STATUS_MAP);
      return (
        <Tooltip content={index.name}>
          <Tag color={index.color} type="light">
            {index.name}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: <Tooltip content="是否上报">是否上报</Tooltip>,
    dataIndex: "allowUpload",
    isShow: false,
    ellipsis: true,
    render: (item) => {
      const i = find(propEq(item, "id"))(ALLOW_REPORT_STATUS_MAP);
      return (
        <Tooltip content={i?.name}>
          <Tag color={i?.color} type="light">
            {i?.name}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: <Tooltip content="上报状态">上报状态</Tooltip>,
    dataIndex: "uploadStatus",
    isShow: false,
    ellipsis: true,
    render: (item) => {
      const i = find(propEq(item, "id"))(DANGER_REPORT_STATUS_MAP);
      return (
        <Tooltip content={i?.name}>
          <Tag color={i?.color} type="light">
            {i?.name}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "是否允许批量提交",
    dataIndex: "allowBatchSubmit",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(ALLOW_ALLOWNOT_MAP);
      return (
        <Tooltip content={i.name}>
          <Tag color={i.color} type="light">
            {i.name}
          </Tag>
        </Tooltip>
      );
    },
  },
]);
