import { Tag, Tooltip } from "@douyinfe/semi-ui";
import type { MobileParams } from "api";
import dayjs from "dayjs";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";

export const mobileConfigModalAtom = atom(false);

export const mobileEditModalAtom = atom({
  id: "",
  show: false,
});

// 查询条件
export const mobileFilterAtom = atomWithReset<MobileParams>({
  pageNumber: 1,
  pageSize: 10,
  query: "",
  filter: {},
});

// 查询条件
export const mobileFnAtom = atom({
  refetch: () => {},
});

export const mobileColumnsAtom = atom([
  {
    title: <Tooltip content="ID">ID</Tooltip>,
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  {
    title: <Tooltip content="手机名称">手机名称</Tooltip>,
    dataIndex: "name",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="IMEI码">IMEI码</Tooltip>,
    dataIndex: "imei",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="生产日期">生产日期</Tooltip>,
    dataIndex: "productionDate",
    isShow: true,
    ellipsis: true,
    render: (record) => <p>{dayjs(record).format("YYYY-MM-DD")}</p>,
  },
  {
    title: <Tooltip content="设备编号">设备编号</Tooltip>,
    dataIndex: "equipmentNumber",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="所属部门">所属部门</Tooltip>,
    dataIndex: "department",
    isShow: true,
    ellipsis: true,
    render: (item) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="使用人员列表">使用人员列表</Tooltip>,
    dataIndex: "person",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const textContent = (item ?? []).map((index: any, i: number) =>
        i === 0 ? index.name : `、${index.name}`
      );
      const content = (item ?? []).map((index: any) => (
        <Tag type="light" key={index.id}>
          {index.name}
        </Tag>
      ));
      return <Tooltip content={textContent}>{content}</Tooltip>;
    },
  },
  {
    title: <Tooltip content="本周排查使用次数">本周排查使用次数</Tooltip>,
    dataIndex: "usedNum",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="附注说明">附注说明</Tooltip>,
    dataIndex: "note",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
]);
