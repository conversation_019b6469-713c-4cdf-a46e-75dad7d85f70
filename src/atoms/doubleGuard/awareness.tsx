import { Tooltip } from "@douyinfe/semi-ui";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";

export const awarenessConfigModalAtom = atom(false);

export const awarenessEditModalAtom = atom({
  id: "",
  show: false,
  type: "",
});

// 查询条件
export const filterAtom = atom<AwarenessParams>({
  pageNumber: 1,
  pageSize: 10,
  query: "",
  filter: {},
});

export const awarenessFilterAtom = atomWithReset<AwarenessParams>(filterAtom);

// 查询条件
export const awarenessFnAtom = atom({
  refetch: () => {},
});

export const awarenessColumnsAtom = atom([
  {
    title: <Tooltip content="ID">ID</Tooltip>,
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  {
    title: <Tooltip content="名称">名称</Tooltip>,
    dataIndex: "riskFactorName",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="风险分区">风险分区</Tooltip>,
    dataIndex: "area",
    isShow: true,
    ellipsis: true,
    render: (item) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="应急处置措施">应急处置措施</Tooltip>,
    dataIndex: "emergencyResponseMeasure",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="主要风险控制措施">主要风险控制措施</Tooltip>,
    dataIndex: "riskControlMeasure",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
]);
