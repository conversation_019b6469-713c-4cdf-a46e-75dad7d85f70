import { find, propEq, whereEq, where } from "ramda";
import { atom } from "jotai";
import { ButtonGroup, Button, Tag, Tooltip } from "@douyinfe/semi-ui";
import dayjs from "dayjs";
import {
  EVALUATION_TYPE_MAP,
  CONTROLTYPE_MAP,
  CLASSIFY1_MAP,
  CLASSIFY2_MAP,
} from "components";
import { atomWithReset } from "jotai/utils";

export const checklistConfigModalAtom = atom(false);

export const checklistEditModalAtom = atom({
  item: null,
  show: false,
});

// 风险事件
export const checklistEvaluationModalAtom = atom({
  id: null,
  show: false,
});

// 查询条件
export const checklistFilterAtom = atom({
  pageNumber: 1,
  pageSize: 10,
  filter: {},
});

// 选中项
export const checklistSelectAtom = atomWithReset<string | null>(null);

// 全部数据存储
export const checklistDataAtom = atom<any[]>([]);

// 刷新
export const checklistFnAtom = atom({
  refetch: () => {},
});

export const checklistColumnsAtom = atom([
  {
    title: <Tooltip content="ID">ID</Tooltip>,
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  {
    title: <Tooltip content="关联风险对象">关联风险对象</Tooltip>,
    dataIndex: "riskObject",
    isShow: true,
    ellipsis: true,
    render: (item) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="关联风险单元">关联风险单元</Tooltip>,
    dataIndex: "riskUnit",
    isShow: true,
    ellipsis: true,
    render: (item) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="关联风险事件">关联风险事件</Tooltip>,
    dataIndex: "riskEvent",
    isShow: true,
    ellipsis: true,
    render: (item) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="管控方式">管控方式</Tooltip>,
    dataIndex: "controlType",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const i = find(propEq(item ? item : 1, "id"))(CONTROLTYPE_MAP);
      return <p>{i?.name ?? ""}</p>;
    },
  },
  {
    title: <Tooltip content="一级分类">一级分类</Tooltip>,
    dataIndex: "classify1",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const i = find(propEq(item ? item : 1, "id"))(CLASSIFY1_MAP);
      return (
        <Tag color="grey" type="light" className="min-w-[fit-content]">
          {i.name}
        </Tag>
      );
    },
  },
  {
    title: <Tooltip content="二级分类">二级分类</Tooltip>,
    dataIndex: "classify2",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      if (!item) {
        return (
          <Tag color="grey" type="light" className="min-w-[fit-content]">
            未分类
          </Tag>
        );
      }
      const k = item?.split("-");
      const i = find(
        whereEq({
          pid: parseInt(k[0]),
          id: parseInt(k[1]),
        }),
      )(CLASSIFY2_MAP);

      return (
        <Tag color="grey" type="light" className="min-w-[fit-content]">
          {i.name}
        </Tag>
      );
    },
  },
  {
    title: <Tooltip content="三级分类">三级分类</Tooltip>,
    dataIndex: "classify3",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      if (!item) return null;
      return (
        <Tag color="grey" type="light" className="min-w-[fit-content]">
          {item}
        </Tag>
      );
    },
  },
  {
    title: <Tooltip content="管控措施">管控措施</Tooltip>,
    dataIndex: "controlMeasure",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="隐患排查内容">隐患排查内容</Tooltip>,
    dataIndex: "troubleShootContent",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
]);
