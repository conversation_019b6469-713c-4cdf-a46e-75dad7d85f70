import { Tag, Tooltip } from "@douyinfe/semi-ui";
import { RISK_LEVEL_MAP } from "components";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";

export const identificationConfigModalAtom = atom(false);

export const identificationEditModalAtom = atom({
  id: "",
  show: false,
});

// 查询条件
export const identificationFilterAtom = atom({
  pageNumber: 1,
  pageSize: 10,
  filter: {},
  departmentId: null,
});

export const identificationValueAtom = atomWithReset<string | null>(null);

// 查询条件
export const identificationFnAtom = atom({
  refetch: () => {},
});

export const identificationColumnsAtom = atom([
  {
    title: <Tooltip content="ID">ID</Tooltip>,
    dataIndex: "id",
    isShow: true,
    fixed: true,
    width: 80,
  },
  {
    title: <Tooltip content="单元名称">单元名称</Tooltip>,
    dataIndex: "name",
    isShow: true,
    ellipsis: true,
    fixed: true,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="关联风险对象">关联风险对象</Tooltip>,
    dataIndex: "riskObject",
    isShow: true,
    ellipsis: true,
    render: (item) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="固有风险等级">固有风险等级</Tooltip>,
    dataIndex: "riskLevel",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const index = find(propEq(item || 5, "id"))(RISK_LEVEL_MAP);
      return (
        <Tooltip content={index.name}>
          <Tag color={index.color} type="light">
            {index.name}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: <Tooltip content="剩余风险等级">剩余风险等级</Tooltip>,
    dataIndex: "remainRiskLevel",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const index = find(propEq(item || 5, "id"))(RISK_LEVEL_MAP);
      return (
        <Tooltip content={index.name}>
          <Tag color={index.color} type="light">
            {index.name}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: <Tooltip content="危害分析">危害分析</Tooltip>,
    dataIndex: "riskAnalysis",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="主要后果">主要后果</Tooltip>,
    dataIndex: "riskMajorResult",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="责任人">责任人</Tooltip>,
    dataIndex: "liablePerson",
    isShow: true,
    ellipsis: true,
    render: (item) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="责任部门">责任部门</Tooltip>,
    dataIndex: "liableDepartment",
    isShow: true,
    ellipsis: true,
    render: (item) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
]);
