import { Tag, Tooltip } from "@douyinfe/semi-ui";
import {
  CHECK_CYCLE_UNIT_MAP,
  CLASSIFY1_MAP,
  CLASSIFY2_MAP,
  DANGER_REPORT_STATUS_MAP,
  TASK_CHECK_RESULT_MAP,
  TASK_STATUS_MAP,
} from "components";
import dayjs from "dayjs";
import { atom } from "jotai";
import { find, propEq, whereEq } from "ramda";

export const bbRecordConfigModalAtom = atom(false);

export const bbRecordEditModalAtom = atom({
  item: null,
  show: false,
});

// 风险事件
export const bbRecordEvaluationModalAtom = atom({
  id: null,
  show: false,
});

// 查询条件
export const bbRecordFilterAtom = atom({
  pageNumber: 1,
  pageSize: 10,
  filter: {},
});

// 选中项
export const bbRecordSelectAtom = atom<string | null>(null);

// 全部数据存储
export const bbRecordDataAtom = atom<any[]>([]);

// 刷新
export const bbRecordFnAtom = atom({
  refetch: () => {},
});

export const bbRecordColumnsAtom = atom([
  {
    title: <Tooltip content="ID">ID</Tooltip>,
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  {
    title: <Tooltip content="排查计划id">排查计划id</Tooltip>,
    dataIndex: "checkPlanId",
    isShow: true,
  },
  {
    title: <Tooltip content="关联风险对象">关联风险对象</Tooltip>,
    dataIndex: "riskObject",
    isShow: true,
    ellipsis: true,
    render: (item) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="关联风险单元">关联风险单元</Tooltip>,
    dataIndex: "riskUnit",
    isShow: true,
    ellipsis: true,
    render: (item) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="关联风险事件">关联风险事件</Tooltip>,
    dataIndex: "riskEvent",
    isShow: true,
    ellipsis: true,
    render: (item) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="一级分类">一级分类</Tooltip>,
    dataIndex: "classify1",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const i = find(propEq(item ? item : 1, "id"))(CLASSIFY1_MAP);
      return (
        <Tag color="grey" type="light" className="min-w-[fit-content]">
          {i.name}
        </Tag>
      );
    },
  },
  {
    title: <Tooltip content="二级分类">二级分类</Tooltip>,
    dataIndex: "classify2",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      if (!item) {
        return (
          <Tag color="grey" type="light" className="min-w-[fit-content]">
            未分类
          </Tag>
        );
      }
      const k = item?.split("-");
      const i = find(
        whereEq({
          pid: parseInt(k[0]),
          id: parseInt(k[1]),
        })
      )(CLASSIFY2_MAP);

      return (
        <Tag color="grey" type="light" className="min-w-[fit-content]">
          {i.name}
        </Tag>
      );
    },
  },
  {
    title: <Tooltip content="三级分类">三级分类</Tooltip>,
    dataIndex: "classify3",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      if (!item) return null;
      return (
        <Tag color="grey" type="light" className="min-w-[fit-content]">
          {item}
        </Tag>
      );
    },
  },
  {
    // title: <Tooltip content="排查间隔">排查间隔</Tooltip>,
    dataIndex: "checkCycle",
    // isShow: true,
    // ellipsis: true,
    align: "center",
    // }, {
    title: <Tooltip content="巡检时间周期">巡检时间周期</Tooltip>,
    // dataIndex: 'checkCycleUnit',
    isShow: true,
    ellipsis: true,
    render: (text, record) => {
      const i = find(
        propEq(record?.checkCycleUnit ? record?.checkCycleUnit : 1, "id")
      )(CHECK_CYCLE_UNIT_MAP);
      if (record.checkCycle === undefined) return null;
      return (
        <Tag color="grey" type="light" className="min-w-[fit-content]">
          {record.checkCycle}
          {i.id !== 2 ? "个" : ""}
          {i.name}
        </Tag>
      );
    },
  },
  {
    title: <Tooltip content="持续时间">持续时间</Tooltip>,
    dataIndex: "duration",
    align: "center",
    isShow: true,
    ellipsis: true,
    render: (text, record) => {
      const i = find(
        propEq(record?.durationUnit ? record?.durationUnit : 1, "id")
      )(CHECK_CYCLE_UNIT_MAP);
      if (record.duration === undefined) return null;
      return (
        <Tag color="grey" type="light" className="min-w-[fit-content]">
          {record.duration}
          {i.id !== 2 ? "个" : ""}
          {i.name}
        </Tag>
      );
    },
  },
  {
    title: <Tooltip content="责任部门">责任部门</Tooltip>,
    dataIndex: "liableDepartment",
    isShow: true,
    ellipsis: true,
    render: (item) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: (
      <Tooltip content="是否是包保责任排查任务">是否是包保责任排查任务</Tooltip>
    ),
    dataIndex: "isBb",
    isShow: true,
    ellipsis: true,
    align: "center",
    render: (item) => {
      if (item == 1) {
        return (
          <Tag color="green" type="light">
            是
          </Tag>
        );
      }
      return (
        <Tag color="red" type="light">
          否
        </Tag>
      );
    },
  },
  {
    title: <Tooltip content="状态">状态</Tooltip>,
    dataIndex: "status",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const i = find(propEq(item ? item : 1, "id"))(TASK_STATUS_MAP);
      return (
        <Tag color="grey" type="light" className="min-w-[fit-content]">
          {i.name}
        </Tag>
      );
    },
  },
  {
    title: <Tooltip content="排查人">排查人</Tooltip>,
    dataIndex: "checker",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      if (!item) {
        return null;
      }
      return (
        <Tag color="grey" type="light" className="min-w-[fit-content]">
          {item.name}
        </Tag>
      );
    },
  },
  {
    title: <Tooltip content="排查任务完成时间">排查任务完成时间</Tooltip>,
    dataIndex: "checkTime",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      if (!item) {
        return null;
      }
      const content = dayjs(item).format("YYYY-MM-DD HH:mm");
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
  },
  {
    title: <Tooltip content="排查任务结果">排查任务结果</Tooltip>,
    dataIndex: "checkResult",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item ? item : 1, "id"))(TASK_CHECK_RESULT_MAP);
      return (
        <Tooltip content={i?.name}>
          <Tag color={i?.color} type="light">
            {i?.name}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: <Tooltip content="IMEI">IMEI</Tooltip>,
    dataIndex: "imei",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="隐患ID">隐患ID</Tooltip>,
    dataIndex: "dangerId",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="上报状态">上报状态</Tooltip>,
    dataIndex: "uploadStatus",
    isShow: false,
    ellipsis: true,
    render: (item) => {
      const i = find(propEq(item, "id"))(DANGER_REPORT_STATUS_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
]);
