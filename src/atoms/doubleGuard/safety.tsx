import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { Tag, Tooltip } from "@douyinfe/semi-ui";
import type { EmployeeParams } from "api/basicInfo";
import { RISK_LEVEL_MAP, IS_MAJOR_HAZARD_MAP } from "components";
import { find, propEq } from "ramda";
import dayjs from "dayjs";
export const safetyConfigModalAtom = atom(false);

export const safetyEditModalAtom = atom({
  id: "",
  show: false,
});

// 查询条件
export const safetyFilterAtom = atomWithReset<SafetyParams>({
  pageNumber: 1,
  pageSize: 10,
  query: "",
  filter: {},
});

// 查询条件
export const safetyFnAtom = atom({
  refetch: () => {},
});

export const safetyColumnsAtom = atom([
  {
    title: <Tooltip content="ID">ID</Tooltip>,
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  {
    title: <Tooltip content="承诺人列表">承诺人列表</Tooltip>,
    dataIndex: "commitmentPerson",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      return (
        <div className="flex gap-1">
          {item.map((o) => (
            <Tooltip content={o.name}>
              <Tag color="grey" type="light" className="min-w-[fit-content]">
                {o.name}
              </Tag>
            </Tooltip>
          ))}
        </div>
      );
    },
  },
  {
    title: <Tooltip content="主管领导">主管领导</Tooltip>,
    dataIndex: "leader",
    isShow: true,
    ellipsis: true,
    render: (item) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="承诺日期">承诺日期</Tooltip>,
    dataIndex: "commitmentDate",
    isShow: true,
    ellipsis: true,
    render: (record) => {
      return <p>{dayjs(record).format("YYYY-MM-DD")}</p>;
    },
  },
  {
    title: <Tooltip content="内部联系方式">内部联系方式</Tooltip>,
    dataIndex: "internalCall",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="风险分区">风险分区</Tooltip>,
    dataIndex: "area",
    isShow: true,
    ellipsis: true,
    render: (item) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="承诺内容">承诺内容</Tooltip>,
    dataIndex: "commitmentContent",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
]);
