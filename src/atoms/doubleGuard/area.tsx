import { Tag, Tooltip } from "@douyinfe/semi-ui";
import { AreaParams } from "api";
import { RISK_LEVEL_MAP } from "components";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";
import { formatDateDay } from "utils";

export const areaConfigModalAtom = atom(false);

export const areaEditModalAtom = atom({
  id: "",
  show: false,
});

export const areaMoreModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const areaDrawerModalAtom = atomWithReset({
  id: "",
  riskLevel: undefined,
  show: false,
});

export enum EvaluationType {
  Easy,
  Clac,
}

type AreaEvaluationModalType = {
  id: number | null;
  name?: string;
  show: boolean;
  type: EvaluationType;
};

export const areaEvaluationModalAtom = atomWithReset<AreaEvaluationModalType>({
  id: null,
  name: "",
  show: false,
  type: EvaluationType.Easy,
});

// 查询条件
export const areaFilterAtom = atomWithReset<AreaParams>({
  pageNumber: 1,
  pageSize: 10,
  filter: {},
});

// 查询条件
export const areaFnAtom = atom({
  refetch: () => {},
});

export const areaColumnsAtom = atom([
  {
    title: <Tooltip content="ID">ID</Tooltip>,
    dataIndex: "id",
    isShow: true,
    fixed: true,
    width: 80,
  },
  {
    title: <Tooltip content="分区名称">分区名称</Tooltip>,
    dataIndex: "name",
    isShow: true,
    ellipsis: true,
    fixed: true,
    width: 180,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="是否绘制">是否绘制</Tooltip>,
    dataIndex: "isDrawn",
    isShow: true,
    ellipsis: true,
    render: (item) => (
      <p>
        {item === 1 ? (
          <Tag color="blue" type="light">
            是
          </Tag>
        ) : (
          <Tag color="red" type="light">
            否
          </Tag>
        )}
      </p>
    ),
  },
  {
    title: <Tooltip content="风险等级">风险等级</Tooltip>,
    dataIndex: "riskLevel",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const i = find(propEq(item ? item : 5, "id"))(RISK_LEVEL_MAP);
      return (
        <Tooltip content={i.name}>
          <Tag color={i.color} type="light">
            {i.name}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: <Tooltip content="责任人">责任人</Tooltip>,
    dataIndex: "liablePerson",
    isShow: true,
    ellipsis: true,
    render: (item) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="责任部门">责任部门</Tooltip>,
    dataIndex: "liableDepartment",
    isShow: true,
    ellipsis: true,
    render: (item) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="本次复评时间">本次复评时间</Tooltip>,
    dataIndex: "reviewDatetime",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const content = formatDateDay(item);
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
  },
  {
    title: <Tooltip content="下次复评时间">下次复评时间</Tooltip>,
    dataIndex: "nextReviewDatetime",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const content = formatDateDay(item);
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
  } /* , {
    title: <Tooltip content="备注">备注</Tooltip>,
    dataIndex: 'note',
    isShow: true,
    ellipsis: true,
    render: text => <Tooltip content={text}><span className="block truncate">{text}</span></Tooltip>,
  } */,
]);

export const areaHistoryAtom = atom([
  {
    title: <Tooltip content="ID">ID</Tooltip>,
    dataIndex: "area.id",
    isShow: true,
    fixed: true,
    width: 80,
  },
  {
    title: <Tooltip content="分区名称">分区名称</Tooltip>,
    dataIndex: "area.name",
    isShow: true,
    ellipsis: true,
    fixed: true,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="风险等级">风险等级</Tooltip>,
    dataIndex: "riskLevel",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const i = find(propEq(item ? item : 5, "id"))(RISK_LEVEL_MAP);
      return (
        <Tooltip content={i.name}>
          <Tag color={i.color} type="light">
            {i.name}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: <Tooltip content="本次复评时间">本次复评时间</Tooltip>,
    dataIndex: "reviewDatetime",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const content = formatDateDay(item);
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
  },
  {
    title: <Tooltip content="下次复评时间">下次复评时间</Tooltip>,
    dataIndex: "nextReviewDatetime",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const content = formatDateDay(item);
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
  },
]);
