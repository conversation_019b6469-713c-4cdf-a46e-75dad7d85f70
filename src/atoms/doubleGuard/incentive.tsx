import { Tag, Tooltip } from "@douyinfe/semi-ui";
import { INCENTIVE_TYPE_MAP } from "components";
import dayjs from "dayjs";
import { atom } from "jotai";
import { find, propEq } from "ramda";

export const incentiveConfigModalAtom = atom(false);

export const incentiveEditModalAtom = atom({
  id: "",
  show: false,
});

// 查询条件
export const incentiveFilterAtom = atom({
  pageNumber: 1,
  pageSize: 10,
  filter: {},
});

// 查询条件
export const incentiveFnAtom = atom({
  refetch: () => {},
});

export const incentiveColumnsAtom = atom([
  {
    title: <Tooltip content="ID">ID</Tooltip>,
    dataIndex: "id",
    isShow: true,
    fixed: true,
    width: 80,
  },
  {
    title: <Tooltip content="奖惩个人">奖惩个人</Tooltip>,
    dataIndex: "person",
    isShow: true,
    ellipsis: true,
    render: (item) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="奖惩类型">奖惩类型</Tooltip>,
    dataIndex: "incentiveType",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const index = find(propEq(item || 1, "id"))(INCENTIVE_TYPE_MAP);
      return <Tag type="light">{index.name}</Tag>;
    },
  },
  {
    title: <Tooltip content="奖惩金额">奖惩金额</Tooltip>,
    dataIndex: "amount",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="奖惩依据">奖惩依据</Tooltip>,
    dataIndex: "reason",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="登记人">登记人</Tooltip>,
    dataIndex: "booker",
    isShow: true,
    ellipsis: true,
    render: (item) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="登记日期">登记日期</Tooltip>,
    dataIndex: "bookDate",
    isShow: true,
    ellipsis: true,
    render: (item) => <p>{dayjs(item ?? "").format("YYYY-MM-DD HH:mm")}</p>,
  },
]);
