import { Tag, Tooltip } from "@douyinfe/semi-ui";
import { GC_CHECK_ITEM_RESULT, GC_CHECK_STATUS } from "components";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";
import { CommonAtoms } from "types";

export const doubleGuardGcCheckTaskRecordItemFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const doubleGuardGcCheckTaskRecordItemFnAtom = atom({
  refetch: () => {},
});

export const doubleGuardGcCheckTaskRecordItemEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const doubleGuardGcCheckTaskRecordItemConfigModalAtom = atom(false);

const doubleGuardGcCheckTaskRecordItemShowColumns = [
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 72,
  },
  // user-defined code here
  {
    title: "检查序号",
    dataIndex: "checkName",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "检查内容",
    dataIndex: "checkContent",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "检查依据",
    dataIndex: "checkBasis",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "适用场合",
    dataIndex: "applicablePlace",
    isShow: true,
    ellipsis: true,
    width: 160,
  },
  {
    title: "重大危险源",
    dataIndex: "majorHazard",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "分配人员",
    dataIndex: "checkCandidateList",
    isShow: true,
    ellipsis: true,
    render: (text, record, index) => {
      const content = text
        ? text.map((item, index, array) =>
            index === array.length - 1 ? (
              <span key={index}>{item.name}</span>
            ) : (
              <span key={index}>{item.name},</span>
            )
          )
        : null;
      if (!content) return null;
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
  },
  {
    title: "检查状态",
    dataIndex: "status",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(GC_CHECK_STATUS);
      return (
        <Tooltip content={i?.name}>
          <Tag color={i?.color} type="light">
            {i?.name}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "检查结果",
    dataIndex: "checkResult",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(GC_CHECK_ITEM_RESULT);
      return (
        <Tooltip content={i?.name}>
          <Tag color={i?.color} type="light">
            {i?.name}
          </Tag>
        </Tooltip>
      );
    },
  },
];

const doubleGuardGcCheckTaskRecordItemExtendColumns = [
  // user-defined code here
];

export const doubleGuardGcCheckTaskRecordItemShowColumnsAtom = atom(
  doubleGuardGcCheckTaskRecordItemShowColumns
);

export const doubleGuardGcCheckTaskRecordItemColumnsAtom = atom([
  ...doubleGuardGcCheckTaskRecordItemShowColumns,
  ...doubleGuardGcCheckTaskRecordItemExtendColumns,
]);

/*export const doubleGuardGcCheckTaskRecordItemColumnsAtom = atom(
  (get) => get(doubleGuardGcCheckTaskRecordItemShowColumnsAtom).concat(get(doubleGuardGcCheckTaskRecordItemExtendColumnsAtom)),
  (get, set, newValue: any) => {
    set(doubleGuardGcCheckTaskRecordItemShowColumnsAtom, newValue.slice(0, newValue.length / 2)); // 更新 ShowColumnsAtom
    set(doubleGuardGcCheckTaskRecordItemExtendColumnsAtom, newValue.slice(newValue.length / 2)); // 更新 ExtendColumnsAtom
  }
)*/

export const doubleGuardGcCheckTaskRecordItemAtoms: CommonAtoms = {
  entity: "DoubleGuardGcCheckTaskRecordItem",
  filter: doubleGuardGcCheckTaskRecordItemFilterAtom,
  Fn: doubleGuardGcCheckTaskRecordItemFnAtom,
  editModal: doubleGuardGcCheckTaskRecordItemEditModalAtom,
  configModal: doubleGuardGcCheckTaskRecordItemConfigModalAtom,
  columns: doubleGuardGcCheckTaskRecordItemColumnsAtom,
};

export const doubleGuardGcCheckTaskRecordItemDispatchModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const doubleGuardGcCheckTaskRecordItemAssignModalAtoms = atomWithReset({
  ids: [],
  record: {},
  show: false,
});

export const doubleGuardGcCheckTaskRecordItemExecuteModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const doubleGuardGcCheckTaskRecordItemDangerModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const doubleGuardGcCheckTaskRecordItemDangerItemModalAtom =
  atomWithReset({
    task_id: "",
    record_id: "",
    // item_id: "",
    id: "",
    item: {},
    show: false,
  });
