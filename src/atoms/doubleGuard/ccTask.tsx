import {
  CHECK_STATUS_MAP,
  ISABNORMAL_MAP,
  OVERTIME_OVERTIMENOT_MAP,
} from "components";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";
import { Tag, Tooltip } from "tdesign-react";
import { CommonAtoms } from "types";
import { formatDate } from "utils";

export const doubleGuardCcTaskFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const doubleGuardCcTaskFnAtom = atom({
  refetch: () => {},
});

export const doubleGuardCcTaskEditModalAtom = atomWithReset({
  id: "",
  show: false,
  type: 0, // 0: normal edit; 1: delegate
});

export const doubleGuardCcTaskDetailModalAtom = atomWithReset({
  id: "",
  record: {},
  show: false,
});

export const doubleGuardCcTaskConfigModalAtom = atom(false);

const doubleGuardCcTaskShowColumns = [
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 60,
  },
  // user-defined code here
  {
    title: "计划",
    dataIndex: "plan",
    isShow: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "计划规则ID",
    dataIndex: "planDetailId",
    isShow: true,
  },
  {
    title: "任务候选人",
    dataIndex: "candidateList",
    isShow: true,
    render: (text, record, index) => {
      const content = text
        ? text.map((item, index, array) =>
            index === array.length - 1 ? (
              <span key={index}>{item.name}</span>
            ) : (
              <span key={index}>{item.name},</span>
            )
          )
        : null;
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
  },
  {
    title: "任务开始时间",
    dataIndex: "taskBeginTime",
    isShow: true,
    render: (item) => {
      const content = formatDate(item);
      if (!content) {
        return null;
      }
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
  },
  {
    title: "任务结束时间",
    dataIndex: "taskEndTime",
    isShow: true,
    render: (item) => {
      const content = formatDate(item);
      if (!content) {
        return null;
      }
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
  },
  {
    title: "任务进展",
    dataIndex: "itemTotalNum",
    isShow: true,
    render: (text, record, index) => {
      const content = record?.itemTotalNum ? (
        <span>
          {record?.itemFinishNum}/{record?.itemTotalNum}
        </span>
      ) : null;
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
  },
  {
    title: "任务状态",
    dataIndex: "status",
    isShow: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(CHECK_STATUS_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "是否逾期",
    dataIndex: "isOvertime",
    isShow: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(OVERTIME_OVERTIMENOT_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "是否异常",
    dataIndex: "isAbnormal",
    isShow: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(ISABNORMAL_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
];

const doubleGuardCcTaskExtendColumns = [
  // user-defined code here
];

export const doubleGuardCcTaskShowColumnsAtom = atom(
  doubleGuardCcTaskShowColumns
);

export const doubleGuardCcTaskColumnsAtom = atom([
  ...doubleGuardCcTaskShowColumns,
  ...doubleGuardCcTaskExtendColumns,
]);

/*export const doubleGuardCcTaskColumnsAtom = atom(
  (get) => get(doubleGuardCcTaskShowColumnsAtom).concat(get(doubleGuardCcTaskExtendColumnsAtom)),
  (get, set, newValue: any) => {
    set(doubleGuardCcTaskShowColumnsAtom, newValue.slice(0, newValue.length / 2)); // 更新 ShowColumnsAtom
    set(doubleGuardCcTaskExtendColumnsAtom, newValue.slice(newValue.length / 2)); // 更新 ExtendColumnsAtom
  }
)*/

export const doubleGuardCcTaskAtoms: CommonAtoms = {
  entity: "DoubleGuardCcTask",
  filter: doubleGuardCcTaskFilterAtom,
  Fn: doubleGuardCcTaskFnAtom,
  editModal: doubleGuardCcTaskEditModalAtom,
  configModal: doubleGuardCcTaskConfigModalAtom,
  columns: doubleGuardCcTaskColumnsAtom,
};
