import { Tag, Tooltip } from "@douyinfe/semi-ui";
import type { EventParams } from "api";
import {
  ALLOW_REPORT_STATUS_MAP,
  DANGER_REPORT_STATUS_MAP,
  EVALUATION_TYPE_MAP,
  RISK_LEVEL_MAP,
} from "components";
import dayjs from "dayjs";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";

export const eventConfigModalAtom = atom(false);

export const eventEditModalAtom = atom({
  id: "",
  show: false,
});

// 风险事件
export const eventEvaluationModalAtom = atom({
  id: null,
  show: false,
  tab: 1,
});

// 查询条件
export const eventFilterAtom = atomWithReset<EventParams>({
  pageNumber: 1,
  pageSize: 10,
  filter: {},
  departmentId: null,
});

// 选中项
export const eventSelectAtom = atomWithReset<string | null>(null);

// 全部数据存储
export const eventDataAtom = atom<any[]>([]);

// 刷新
export const eventFnAtom = atom({
  refetch: () => {},
});

export const eventColumnsAtom = atom([
  {
    title: <Tooltip content="ID">ID</Tooltip>,
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  {
    title: <Tooltip content="事件名称">事件名称</Tooltip>,
    dataIndex: "name",
    isShow: true,
    ellipsis: true,
    width: 180,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="关联风险对象">关联风险对象</Tooltip>,
    dataIndex: "riskObject",
    isShow: true,
    ellipsis: true,
    render: (item) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="关联风险单元">关联风险单元</Tooltip>,
    dataIndex: "riskUnit",
    isShow: true,
    ellipsis: true,
    render: (item) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="评估方法">评估方法</Tooltip>,
    dataIndex: "evaluationType",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const index = find(propEq(item || 5, "id"))(EVALUATION_TYPE_MAP);
      return <p>{index?.name ?? ""}</p>;
    },
  },
  {
    title: <Tooltip content="风险等级">风险等级</Tooltip>,
    dataIndex: "riskLevel",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const index = find(propEq(item || 5, "id"))(RISK_LEVEL_MAP);
      return (
        <Tooltip content={index.name}>
          <Tag color={index.color} type="light">
            {index.name}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: <Tooltip content="剩余风险等级">剩余风险等级</Tooltip>,
    dataIndex: "remainRiskLevel",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const index = find(propEq(item || 5, "id"))(RISK_LEVEL_MAP);
      return (
        <Tooltip content={index.name}>
          <Tag color={index.color} type="light">
            {index.name}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: <Tooltip content="上次复评日期">上次复评日期</Tooltip>,
    dataIndex: "reviewDatetime",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      // content = { text? dayjs(text).format('YYYY-MM-DD') : null}
      let content = dayjs(item).format("YYYY-MM-DD") ?? "";
      if (content === "0001-01-01") {
        content = "";
      }
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
  },
  {
    title: <Tooltip content="下次复评日期">下次复评日期</Tooltip>,
    dataIndex: "nextReviewDatetime",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      // content = { text? dayjs(text).format('YYYY-MM-DD') : null}
      let content = dayjs(item).format("YYYY-MM-DD") ?? "";
      if (content === "0001-01-01") {
        content = "";
      }
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
  },
  {
    title: <Tooltip content="责任人">责任人</Tooltip>,
    dataIndex: "liablePerson",
    isShow: true,
    ellipsis: true,
    render: (item) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="责任部门">责任部门</Tooltip>,
    dataIndex: "liableDepartment",
    isShow: true,
    ellipsis: true,
    render: (item) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="是否上报">是否上报</Tooltip>,
    dataIndex: "allowUpload",
    isShow: false,
    ellipsis: true,
    render: (item) => {
      const i = find(propEq(item, "id"))(ALLOW_REPORT_STATUS_MAP);
      return (
        <Tooltip content={i?.name}>
          <Tag color={i?.color} type="light">
            {i?.name}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: <Tooltip content="上报状态">上报状态</Tooltip>,
    dataIndex: "uploadStatus",
    isShow: false,
    ellipsis: true,
    render: (item) => {
      const i = find(propEq(item, "id"))(DANGER_REPORT_STATUS_MAP);
      return (
        <Tooltip content={i?.name}>
          <Tag color={i?.color} type="light">
            {i?.name}
          </Tag>
        </Tooltip>
      );
    },
  },
]);
