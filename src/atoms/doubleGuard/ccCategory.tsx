import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { CommonAtoms } from "types";

export const doubleGuardCcCategoryFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const doubleGuardCcCategoryFnAtom = atom({
  refetch: () => {},
});

export const doubleGuardCcCategoryEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const doubleGuardCcCategoryConfigModalAtom = atom(false);

const doubleGuardCcCategoryShowColumns = [
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 48,
  },
  // user-defined code here
  {
    title: "名称",
    dataIndex: "name",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "备注",
    dataIndex: "note",
    isShow: true,
    ellipsis: true,
  },
];

const doubleGuardCcCategoryExtendColumns = [
  // user-defined code here
];

export const doubleGuardCcCategoryShowColumnsAtom = atom(
  doubleGuardCcCategoryShowColumns
);

export const doubleGuardCcCategoryColumnsAtom = atom([
  ...doubleGuardCcCategoryShowColumns,
  ...doubleGuardCcCategoryExtendColumns,
]);

/*export const doubleGuardCcCategoryColumnsAtom = atom(
  (get) => get(doubleGuardCcCategoryShowColumnsAtom).concat(get(doubleGuardCcCategoryExtendColumnsAtom)),
  (get, set, newValue: any) => {
    set(doubleGuardCcCategoryShowColumnsAtom, newValue.slice(0, newValue.length / 2)); // 更新 ShowColumnsAtom
    set(doubleGuardCcCategoryExtendColumnsAtom, newValue.slice(newValue.length / 2)); // 更新 ExtendColumnsAtom
  }
)*/

export const doubleGuardCcCategoryAtoms: CommonAtoms = {
  entity: "DoubleGuardCcCategory",
  filter: doubleGuardCcCategoryFilterAtom,
  Fn: doubleGuardCcCategoryFnAtom,
  editModal: doubleGuardCcCategoryEditModalAtom,
  configModal: doubleGuardCcCategoryConfigModalAtom,
  columns: doubleGuardCcCategoryColumnsAtom,
};
