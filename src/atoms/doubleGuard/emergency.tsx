import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { Tag, Tooltip } from "@douyinfe/semi-ui";
import type { EmployeeParams } from "api/basicInfo";
import { RISK_LEVEL_MAP, IS_MAJOR_HAZARD_MAP } from "components";
import { find, propEq } from "ramda";

export const emergencyConfigModalAtom = atom(false);

export const emergencyEditModalAtom = atom({
  id: "",
  show: false,
  type: "",
});

// 查询条件
export const emergencyFilterAtom = atomWithReset<EmergencyParams>({
  pageNumber: 1,
  pageSize: 10,
  query: "",
  filter: {},
});

// 查询条件
export const emergencyFnAtom = atom({
  refetch: () => {},
});

export const emergencyColumnsAtom = atom([
  {
    title: <Tooltip content="ID">ID</Tooltip>,
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  {
    title: <Tooltip content="处置卡名称">处置卡名称</Tooltip>,
    dataIndex: "name",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="事故风险">事故风险</Tooltip>,
    dataIndex: "accidentRisk",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="发生场所">发生场所</Tooltip>,
    dataIndex: "accidentPlace",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="事故特征">事故特征</Tooltip>,
    dataIndex: "accidentCharacter",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="负责人">负责人</Tooltip>,
    dataIndex: "liablePerson",
    isShow: true,
    ellipsis: true,
    render: (item) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="内部联系方式">内部联系方式</Tooltip>,
    dataIndex: "internalCall",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="外部联系方式">外部联系方式</Tooltip>,
    dataIndex: "externalCall",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="风险分区">风险分区</Tooltip>,
    dataIndex: "area",
    isShow: true,
    ellipsis: true,
    render: (item) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
]);
