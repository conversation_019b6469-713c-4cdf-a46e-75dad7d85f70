import { Tag, Tooltip } from "@douyinfe/semi-ui";
import { IS_ISNOT_MAP } from "components";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";
import { CommonAtoms } from "types";

export const inspectionStandardFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const inspectionStandardFnAtom = atom({
  refetch: () => {},
});

export const inspectionStandardEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const inspectionStandardConfigModalAtom = atom(false);

const inspectionStandardShowColumns = [
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  // user-defined code here
  {
    title: "巡检标准类别",
    dataIndex: "category",
    isShow: true,
    ellipsis: true,
    width: 160,
  },
  {
    title: "检查内容",
    dataIndex: "content",
    isShow: true,
    ellipsis: true,
    width: 160,
  },
  {
    title: "检查部位",
    dataIndex: "place",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "检查方式",
    dataIndex: "method",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "是否需要填数据",
    dataIndex: "needSample",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item ? item : 1, "id"))(IS_ISNOT_MAP);
      return (
        <Tooltip content={i.name}>
          <Tag color={i.color} type="light">
            {i.name}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "是否需要拍照",
    dataIndex: "needPhoto",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item ? item : 1, "id"))(IS_ISNOT_MAP);
      return (
        <Tooltip content={i.name}>
          <Tag color={i.color} type="light">
            {i.name}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "最小值",
    dataIndex: "minValue",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "最大值",
    dataIndex: "maxValue",
    isShow: true,
    ellipsis: true,
  },
];

const inspectionStandardExtendColumns = [
  // user-defined code here
];

export const inspectionStandardColumnsAtom = atom([
  ...inspectionStandardShowColumns,
  ...inspectionStandardExtendColumns,
]);

export const inspectionStandardPickColumnsAtom = atom([
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "巡检标准ID",
    dataIndex: "standardId",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "巡检标准类别",
    dataIndex: "category",
    isShow: true,
    ellipsis: true,
    width: 120,
  },
  {
    title: "检查内容",
    dataIndex: "content",
    isShow: true,
    ellipsis: true,
    width: 120,
  },
  /* {
    title: "是否需要拍照",
    dataIndex: "needPhoto",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq((item ? item : 1), 'id'))(IS_ISNOT_MAP)
      return (<Tooltip content={i.name}>
        <Tag color={i.color} type="light">
          {i.name}
        </Tag>
      </Tooltip>)
    }
  },
  {
    title: "是否需要填数据",
    dataIndex: "needSample",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq((item ? item : 1), 'id'))(IS_ISNOT_MAP)
      return (<Tooltip content={i.name}>
        <Tag color={i.color} type="light">
          {i.name}
        </Tag>
      </Tooltip>)
    }
  }, */
  {
    title: "最小值",
    dataIndex: "newMinValue",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "最大值",
    dataIndex: "newMaxValue",
    isShow: true,
    ellipsis: true,
  },
]);

export const inspectionStandardPickModalColumnsAtom = atom([
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "巡检标准类别",
    dataIndex: "category",
    isShow: true,
    ellipsis: true,
    width: 120,
  },
  {
    title: "检查内容",
    dataIndex: "content",
    isShow: true,
    ellipsis: true,
    width: 120,
  },
  /* {
    title: "是否需要拍照",
    dataIndex: "needPhoto",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq((item ? item : 1), 'id'))(IS_ISNOT_MAP)
      return (<Tooltip content={i.name}>
        <Tag color={i.color} type="light">
          {i.name}
        </Tag>
      </Tooltip>)
    }
  },
  {
    title: "是否需要填数据",
    dataIndex: "needSample",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq((item ? item : 1), 'id'))(IS_ISNOT_MAP)
      return (<Tooltip content={i.name}>
        <Tag color={i.color} type="light">
          {i.name}
        </Tag>
      </Tooltip>)
    }
  }, */
  {
    title: "最小值",
    dataIndex: "minValue",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "最大值",
    dataIndex: "maxValue",
    isShow: true,
    ellipsis: true,
  },
]);

export const inspectionStandardAtoms: CommonAtoms = {
  entity: "InspectionStandard",
  entityCName: "巡检标准",
  filter: inspectionStandardFilterAtom,
  Fn: inspectionStandardFnAtom,
  editModal: inspectionStandardEditModalAtom,
  configModal: inspectionStandardConfigModalAtom,
  columns: inspectionStandardColumnsAtom,
};
