import { ISABNORMAL_MAP } from "components";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";
import { Tag, Tooltip } from "tdesign-react";
import { CommonAtoms } from "types";
import { formatDate } from "utils";

export const unscheduledTaskRecordFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const unscheduledTaskRecordFnAtom = atom({
  refetch: () => {},
});

export const unscheduledTaskRecordEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const unscheduledTaskRecordConfigModalAtom = atom(false);

export const unscheduledTaskRecordDetailSideAtom = atomWithReset({
  id: "",
  show: false,
});

const unscheduledTaskRecordShowColumns = [
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  // user-defined code here
  {
    title: "巡检人",
    dataIndex: "person",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "巡检时间",
    dataIndex: "processBeginTime",
    width: 200,
    isShow: true,
    ellipsis: true,
    render: (text, record, index) => {
      const processBeginTime = formatDate(text) ? formatDate(text) : "";
      const processEndTime = formatDate(record.processEndTime)
        ? formatDate(record.processEndTime)
        : "";
      const content = processBeginTime + " ~ " + processEndTime;
      return (
        <Tooltip content={content}>
          <p>
            开始:{processBeginTime}
            <br />
            结束:{processEndTime}
          </p>
        </Tooltip>
      );
    },
  },
  {
    title: "巡检点",
    dataIndex: "placeCount",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "巡检结果",
    dataIndex: "isAbnormal",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item ? item : 1, "id"))(ISABNORMAL_MAP);
      return (
        <Tooltip content={i?.name}>
          <Tag color={i?.color} type="light">
            {i?.name}
          </Tag>
        </Tooltip>
      );
    },
  },
];

const unscheduledTaskRecordExtendColumns = [
  // user-defined code here
];

export const unscheduledTaskRecordColumnsAtom = atom([
  ...unscheduledTaskRecordShowColumns,
  ...unscheduledTaskRecordExtendColumns,
]);

/*export const unscheduledTaskRecordColumnsAtom = atom(
  (get) => get(unscheduledTaskRecordShowColumnsAtom).concat(get(unscheduledTaskRecordExtendColumnsAtom)),
  (get, set, newValue: any) => {
    set(unscheduledTaskRecordShowColumnsAtom, newValue.slice(0, newValue.length / 2)); // 更新 ShowColumnsAtom
    set(unscheduledTaskRecordExtendColumnsAtom, newValue.slice(newValue.length / 2)); // 更新 ExtendColumnsAtom
  }
)*/

export const unscheduledTaskRecordAtoms: CommonAtoms = {
  entity: "UnscheduledTaskRecord",
  filter: unscheduledTaskRecordFilterAtom,
  Fn: unscheduledTaskRecordFnAtom,
  editModal: unscheduledTaskRecordEditModalAtom,
  configModal: unscheduledTaskRecordConfigModalAtom,
  columns: unscheduledTaskRecordColumnsAtom,
};
