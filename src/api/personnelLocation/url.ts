import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/personnel_location/url/search

export const getUrlList = async (params) => {
  const base_url = "/personnel_location/url/search";
  const res = await post(base_url, params);
  return res;
};

/* export const getUrl = async (id) => {
  const base_url = `/personnel_location/url/${id}`
  return await get(base_url)
} */

export const getUrl = async () => {
  const base_url = `/personnel_location/url`;
  return await get(base_url);
};

export const createUrl = async (params) => {
  const res = await post("/personnel_location/url", params);
  return res;
};

export const delUrl = async (id: number) => {
  const res = await del(`/personnel_location/url/${id}`);
  return res;
};

export const delUrls = async (ids) => {
  const res = await del(`/personnel_location/url`, ids);
  return res;
};

export const updateUrl = async (params) => {
  const res = await put(`/personnel_location/url/${params.id}`, params?.values);
  return res;
};

export const urlApis: CommonApis = {
  entity: "Url",
  query: getUrlList,
  create: createUrl,
  remove: delUrl,
  removes: delUrls,
  update: updateUrl,
  get: getUrl,
};
