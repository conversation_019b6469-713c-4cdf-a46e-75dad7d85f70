import { get, post } from "api";


export const getDashBasicStat = async () => {
  const base_url = `/major_hazard/basic_stat`;
  return await get(base_url);
};

export const getDashMonitorUnitInfo= async () => {
  const base_url = `/major_hazard/monitor_unit_info`;
  return await get(base_url);
};

export const getDashSensorAlarm = async () => {
  const base_url = `/major_hazard/sensor_alarm`;
  return await get(base_url);
};


// /v1/major_hazard/sensor_alarm_stat
export const getSensorAlarmStatRecords = async (params) => {
  const base_url = `/major_hazard/sensor_alarm_stat`;
  return await post(base_url, params);
};


// /v1/major_hazard/sensor_alarm_info_stat
export const getSensorAlarmInfoStat = async (params) => {
  const base_url = `/major_hazard/sensor_alarm_info_stat`;
  return await post(base_url, params);
};

// /v1/major_hazard/personnel_alarm_stat
export const getPersonnelAlarmStat = async (params) => {
  const base_url = `/major_hazard/personnel_alarm_stat`;
  return await post(base_url, params);
};

// /v1/major_hazard/personnel_alarm_info_stat
export const getPersonnelAlarmInfoStat = async (params) => {
  const base_url = `/major_hazard/personnel_alarm_info_stat`;
  return await post(base_url, params);
};

// /v1/major_hazard/ai_alarm_stat
export const getAIAlarmStat = async (params) => {
  const base_url = `/major_hazard/ai_alarm_stat`;
  return await post(base_url, params);
};

// /v1/major_hazard/ai_alarm_info_stat
export const getAIAlarmInfoStat = async (params) => {
  const base_url = `/major_hazard/ai_alarm_info_stat`;
  return await post(base_url, params);
};