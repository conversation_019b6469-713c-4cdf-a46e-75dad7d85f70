import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/major_hazard/personnel_alarm/search

export const getPersonnelAlarmList = async (params) => {
  const base_url = "/major_hazard/personnel_alarm/search";
  const res = await post(base_url, params);
  return res;
};

export const getPersonnelAlarm = async (id) => {
  const base_url = `/major_hazard/personnel_alarm/${id}`;
  return await get(base_url);
};

export const createPersonnelAlarm = async (params) => {
  const res = await post("/major_hazard/personnel_alarm", params);
  return res;
};

export const delPersonnelAlarm = async (id: number) => {
  const res = await del(`/major_hazard/personnel_alarm/${id}`);
  return res;
};

export const delPersonnelAlarms = async (ids) => {
  const res = await del(`/major_hazard/personnel_alarm`, ids);
  return res;
};

export const updatePersonnelAlarm = async (params) => {
  const res = await put(
    `/major_hazard/personnel_alarm/${params.id}`,
    params?.values,
  );
  return res;
};

export const getPersonnelAlarmTypeList = async () => {
  const base_url = "/major_hazard/personnel_alarm/warning_type";
  const res = await get(base_url);
  return res;
};

export const processPersonnelAlarm = async (params) => {
  const res = await post("/major_hazard/personnel_alarm/process", params);
  return res;
};

export const personnelAlarmApis: CommonApis = {
  entity: "PersonnelAlarm",
  query: getPersonnelAlarmList,
  create: createPersonnelAlarm,
  remove: delPersonnelAlarm,
  removes: delPersonnelAlarms,
  update: updatePersonnelAlarm,
  get: getPersonnelAlarm,
};
