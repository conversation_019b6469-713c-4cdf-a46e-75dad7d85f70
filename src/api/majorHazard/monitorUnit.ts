import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/major_hazard/monitor_unit/search

export const getMonitorUnitList = async (params) => {
  const base_url = "/major_hazard/monitor_unit/search";
  const res = await post(base_url, params);
  return res;
};

export const getMonitorUnit = async (id) => {
  const base_url = `/major_hazard/monitor_unit/${id}`;
  return await get(base_url);
};

export const createMonitorUnit = async (params) => {
  const res = await post("/major_hazard/monitor_unit", params);
  return res;
};

export const delMonitorUnit = async (id: number) => {
  const res = await del(`/major_hazard/monitor_unit/${id}`);
  return res;
};

export const delMonitorUnits = async (ids) => {
  const res = await del(`/major_hazard/monitor_unit`, ids);
  return res;
};

export const updateMonitorUnit = async (params) => {
  const res = await put(
    `/major_hazard/monitor_unit/${params.id}`,
    params?.values,
  );
  return res;
};

export const monitorUnitApis: CommonApis = {
  entity: "MonitorUnit",
  query: getMonitorUnitList,
  create: createMonitorUnit,
  remove: delMonitorUnit,
  removes: delMonitorUnits,
  update: updateMonitorUnit,
  get: getMonitorUnit,
};
