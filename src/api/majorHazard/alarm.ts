import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/major_hazard/alarm/search

export const getAlarmList = async (params) => {
  const base_url = "/major_hazard/alarm/search";
  const res = await post(base_url, params);
  return res;
};

export const getAlarm = async (id) => {
  const base_url = `/major_hazard/alarm/${id}`;
  return await get(base_url);
};

export const createAlarm = async (params) => {
  const res = await post("/major_hazard/alarm", params);
  return res;
};

export const delAlarm = async (id: number) => {
  const res = await del(`/major_hazard/alarm/${id}`);
  return res;
};

export const delAlarms = async (ids) => {
  const res = await del(`/major_hazard/alarm`, ids);
  return res;
};

export const updateAlarm = async (params) => {
  const res = await put(`/major_hazard/alarm/${params.id}`, params?.values);
  return res;
};

export const confirmAlarm = async (id) => {
  const res = await post(`/major_hazard/alarm/${id}/confirm`);
  return res;
};

export const confirmAlarmBatch = async (ids) => {
  const res = await post(`/major_hazard/alarm/confirm`, ids);
  return res;
};

export const refuseAlarm = async (id) => {
  const res = await post(`/major_hazard/alarm/${id}/refuse`);
  return res;
};

export const refuseAlarmBatch = async (ids) => {
  const res = await post(`/major_hazard/alarm/refuse`, ids);
  return res;
};

export const alarmApis: CommonApis = {
  entity: "Alarm",
  query: getAlarmList,
  create: createAlarm,
  remove: delAlarm,
  removes: delAlarms,
  update: updateAlarm,
  get: getAlarm,
};
