import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/major_hazard/sensor/search

export const getSensorListFromEquipment = async (params) => {
  const base_url = "/equipment_management/equipment/sensor";
  const res = await post(base_url, params);
  return res;
};

export const getSensorList = async (params) => {
  const base_url = "/major_hazard/sensor/search";
  const res = await post(base_url, params);
  return res;
};

export const getSensor = async (id) => {
  const base_url = `/major_hazard/sensor/${id}`;
  return await get(base_url);
};

export const createSensor = async (params) => {
  const res = await post("/major_hazard/sensor", params);
  return res;
};

export const delSensor = async (id: number) => {
  const res = await del(`/major_hazard/sensor/${id}`);
  return res;
};

export const delSensors = async (ids) => {
  const res = await del(`/major_hazard/sensor`, ids);
  return res;
};

export const updateSensor = async (params) => {
  const res = await put(`/major_hazard/sensor/${params.id}`, params?.values);
  return res;
};

export const getSensorMeasurement = async () => {
  const base_url = "/major_hazard/sensor/measurement";
  return await get(base_url);
};

export const getSensorField = async (name) => {
  const base_url = `/major_hazard/sensor/measurement/${name}/field`;
  return await get(base_url);
};

export const resumeSensor = async (id) => {
  const base_url = `/major_hazard/sensor/${id}/resume`;
  return await post(base_url);
};

export const batchResumeSensor = async (ids) => {
  const base_url = "/major_hazard/sensor/resume";
  return await post(base_url, ids);
};

export const stopSensor = async (id) => {
  const base_url = `/major_hazard/sensor/${id}/stop`;
  return await post(base_url);
};

export const batchStopSensor = async (ids) => {
  const base_url = "/major_hazard/sensor/stop";
  return await post(base_url, ids);
};

export const sensorApis: CommonApis = {
  entity: "Sensor",
  query: getSensorList,
  create: createSensor,
  remove: delSensor,
  removes: delSensors,
  update: updateSensor,
  get: getSensor,
};
