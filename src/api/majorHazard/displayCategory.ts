import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/major_hazard/display_category/search

export const getDisplayCategoryList = async (params) => {
  const base_url = "/major_hazard/display_category/search";
  const res = await post(base_url, params);
  return res;
};

export const getDisplayCategory = async (id) => {
  const base_url = `/major_hazard/display_category/${id}`;
  return await get(base_url);
};

export const createDisplayCategory = async (params) => {
  const res = await post("/major_hazard/display_category", params);
  return res;
};

export const delDisplayCategory = async (id: number) => {
  const res = await del(`/major_hazard/display_category/${id}`);
  return res;
};

export const delDisplayCategorys = async (ids) => {
  const res = await del(`/major_hazard/contractor`, ids);
  return res;
};

export const updateDisplayCategory = async (params) => {
  const res = await put(
    `/major_hazard/display_category/${params.id}`,
    params?.values,
  );
  return res;
};

export const displayCategoryApis: CommonApis = {
  entity: "DisplayCategory",
  query: getDisplayCategoryList,
  create: createDisplayCategory,
  remove: delDisplayCategory,
  removes: delDisplayCategorys,
  update: updateDisplayCategory,
  get: getDisplayCategory,
};
