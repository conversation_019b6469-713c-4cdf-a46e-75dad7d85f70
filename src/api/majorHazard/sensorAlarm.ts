import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/major_hazard/sensor_alarm/search

export const getSensorAlarmList = async (params) => {
  const base_url = "/major_hazard/sensor_alarm/search";
  const res = await post(base_url, params);
  return res;
};

// 避免与KeyTargets冲突，故改为getSensorAlarm_NORMAL
export const getSensorAlarm_NORMAL = async (id) => {
  const base_url = `/major_hazard/sensor_alarm/${id}`;
  return await get(base_url);
};

export const createSensorAlarm = async (params) => {
  const res = await post("/major_hazard/sensor_alarm", params);
  return res;
};

export const delSensorAlarm = async (id: number) => {
  const res = await del(`/major_hazard/sensor_alarm/${id}`);
  return res;
};

export const delSensorAlarms = async (ids) => {
  const res = await del(`/major_hazard/sensor_alarm`, ids);
  return res;
};

export const updateSensorAlarm = async (params) => {
  const res = await put(
    `/major_hazard/sensor_alarm/${params.id}`,
    params?.values,
  );
  return res;
};

export const causeAnalysisSensorAlarm = async (params) => {
  const res = await post(
    `/major_hazard/sensor_alarm/${params.id}/process`,
    params?.values,
  );
  return res;
};

export const batchCauseAnalysisSensorAlarm = async (params) => {
  const res = await post(`/major_hazard/sensor_alarm/process`, params);
  return res;
};

export const sensorAlarmApis: CommonApis = {
  entity: "SensorAlarm",
  query: getSensorAlarmList,
  create: createSensorAlarm,
  remove: delSensorAlarm,
  removes: delSensorAlarms,
  update: updateSensorAlarm,
  get: getSensorAlarm_NORMAL,
};
