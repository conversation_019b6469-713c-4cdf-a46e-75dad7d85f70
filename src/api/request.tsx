import { Toast } from "@douyinfe/semi-ui";
import { api_url } from "config";
import { last, type } from "ramda";
import { lastRequestTimeKey, loginPageMsgParamName } from "utils/constants";
import {
  AuthDingtalkRoute,
  LoginRoute,
  notNeedLoginRoutes,
} from "utils/routerConstants";
import { handleError } from "../utils";
interface Options {
  method?: "GET" | "POST" | "PUT" | "DELETE";
  body?: object;
}

interface Response {
  code: number;
  message?: string;
  data?: object;
}

export const getHeaders = (isFormData) => {
  const token = localStorage.getItem("token");

  const customHeadKey = localStorage.getItem("customHeadKey");
  const customHeadValue = localStorage.getItem("customHeadValue");

  if (customHeadKey && customHeadValue) {
    localStorage.removeItem("token");
    return {
      [`${customHeadKey}`]: customHeadValue,
      "Content-Type": "application/json",
    };
  }

  if (!token) {
    localStorage.removeItem("token");
    return {
      "Content-Type": "application/json",
    };
  }

  localStorage.removeItem("customHeadKey");
  localStorage.removeItem("customHeadValue");
  if (isFormData) {
    return {
      Authorization: `Bearer ${token}`,
    };
  }

  return {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
  };
};

function formatBody(body: any): object {
  // return body;
  if (type(body) === "Array") return body;

  const formattedBody: { [key: string]: any } = {};
  for (const key in body) {
    switch (type(body[key])) {
      case "String":
        formattedBody[key] = (body[key] as string).trim();
        break;
      case "Object":
        if (body[key]) {
          formattedBody[key] = formatBody(body[key]);
        } else {
          formattedBody[key] = body[key];
        }
        break;
      default:
        formattedBody[key] = body[key];
        break;
    }
  }

  return formattedBody;
}

export const get = (url: string, noAuth) =>
  request(url, {
    noAuth,
    headers: getHeaders(),
  });

export const post = (url: string, body: object, noAuth) =>
  request(url, {
    noAuth,
    method: "POST",
    headers: getHeaders(),
    body: JSON.stringify(formatBody(body)),
  });

export const postFormData = (url: string, body: object, noAuth) =>
  request(url, {
    noAuth,
    method: "POST",
    headers: getHeaders(true),
    body: body,
  });

export const put = (url: string, body: object, noAuth) =>
  request(url, {
    noAuth,
    method: "PUT",
    headers: getHeaders(),
    body: JSON.stringify(formatBody(body)),
  });

export const del = (url: string, body: object, noAuth) =>
  request(url, {
    noAuth,
    method: "DELETE",
    headers: getHeaders(),
    body: JSON.stringify(body),
  });

const onSuccessCallback = [];

const setLastRequestTime = (url: string) => {
  const list = ["platform_config", "renew", "stat"];
  try {
    if (!list.includes(last(url.split("/")))) {
      const lastRequest = {
        key: url,
        time: new Date(),
      };
      window[lastRequestTimeKey] = lastRequest;

      // 触发自定义事件
      const event = new Event("lastRequestTimeUpdated");
      window.dispatchEvent(event);
    }
  } catch (e) {
    console.error(e);
  }
};

export const lastPath = {
  key: "vr-last-path",
  set: (pathname: string) => {
    localStorage.setItem(lastPath.key, pathname);
  },
  get: (): String => {
    const path = localStorage.getItem(lastPath.key) as String;
    return path;
  },
};

async function request(
  url: string,
  options: Options = {},
  isOnPromiseLogin?: boolean,
): Promise<Response> {
  setLastRequestTime(url);

  const response = await fetch(`${api_url}${url}`, options);

  handleError(response);
  const data = await response.json();
  const { code, message, data: result } = data;
  const pathname = window.location.pathname;

  if (
    response?.status === 200 &&
    pathname != LoginRoute &&
    pathname != AuthDingtalkRoute
  ) {
    lastPath.set(pathname);
  }

  if (response?.status === 401) {
    // if (pathname != "/login" && pathname != "/h5_map" && pathname != "/auth") {
    if (!notNeedLoginRoutes.includes(pathname)) {
      window.location.replace(
        `${LoginRoute}?${loginPageMsgParamName}=${message}`,
      );
    }

    /* if (!options.noAuth && !isOnPromiseLogin && history.length > 1) {
      return await new Promise(resolve => {
        let rootEl = document.getElementById('login-form');
        if (!rootEl) {
          rootEl = document.createElement('div');
          rootEl.id = 'login-form';
          document.body.appendChild(rootEl);
          const root = createRoot(rootEl);
          root.render(<QueryClientProvider>
            <Auth>
              <Modal
                visible
                closable={false}
                maskClosable={false}
                title="重新登录"
                header={null}
                footer={null}>
                <LoginPage
                  isPromise
                  className="!w-[auto] !h-[auto]"
                  content={<div>登录状态已过期，重新登录以完成您的操作</div>}
                  onSuccess={async () => {
                    options.headers = getHeaders();
                    const res = await request(url, options, true);
                    resolve(res);
                    let fn;
                    while (fn = onSuccessCallback.shift()) {
                      if (typeof fn === 'function') {
                        fn();
                      }
                    }
                    root.unmount();
                  }} />
              </Modal>
            </Auth>
          </QueryClientProvider>);
        } else {
          onSuccessCallback.push(async () => {
            options.headers = getHeaders();
            resolve(await request(url, options, true));
          });
        }
      });
    } else {
      window.location.replace('/login')
    } */
    // } else if (code < 0 && code != -6 && code != -5) {
  } else if (code < 0) {
    let opts = {
      content: `操作错误: ${code}-${message}`,
      duration: 2,
    };
    Toast.error(opts);
  }

  return { code, message, data: result };
}
