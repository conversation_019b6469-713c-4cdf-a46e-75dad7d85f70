import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/vision_dashboard/style/search

export const getVisionDashboardStyleList = async (params) => {
  const base_url = "/vision_dashboard/style";
  const res = await get(base_url, params);
  return res;
};

export const searchVisionDashboardStyleList = async (params) => {
  const base_url = "/vision_dashboard/style/search";
  const res = await post(base_url, params);
  return res;
};

export const getVisionDashboardStyle = async (id) => {
  const base_url = `/vision_dashboard/style/${id}`;
  return await get(base_url);
};

export const createVisionDashboardStyle = async (params) => {
  const res = await post("/vision_dashboard/style", params);
  return res;
};

export const delVisionDashboardStyle = async (id: number) => {
  const res = await del(`/vision_dashboard/style/${id}`);
  return res;
};

export const delVisionDashboardStyles = async (ids) => {
  const res = await del(`/vision_dashboard/style`, ids);
  return res;
};

export const updateVisionDashboardStyle = async (params) => {
  const res = await put(`/vision_dashboard/style/${params.id}`, params?.values);
  return res;
};

export const visionDashboardStyleApis: CommonApis = {
  entity: "VisionDashboardStyle",
  query: getVisionDashboardStyleList,
  create: createVisionDashboardStyle,
  remove: delVisionDashboardStyle,
  removes: delVisionDashboardStyles,
  update: updateVisionDashboardStyle,
  get: getVisionDashboardStyle,
};
