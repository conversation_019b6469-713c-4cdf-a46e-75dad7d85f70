import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/double_guard/gc_check_task_record/search

export const getDoubleGuardGcCheckTaskRecordList = async (params) => {
  const base_url = "/double_guard/gc_check_task_record/search";
  const res = await post(base_url, params);
  return res;
};

export const getDoubleGuardGcCheckTaskRecord = async (id) => {
  const base_url = `/double_guard/gc_check_task_record/${id}`;
  return await get(base_url);
};

export const createDoubleGuardGcCheckTaskRecord = async (params) => {
  const res = await post("/double_guard/gc_check_task_record", params);
  return res;
};

export const delDoubleGuardGcCheckTaskRecord = async (id: number) => {
  const res = await del(`/double_guard/gc_check_task_record/${id}`);
  return res;
};

export const delDoubleGuardGcCheckTaskRecords = async (ids) => {
  const res = await del(`/double_guard/gc_check_task_record`, ids);
  return res;
};

export const updateDoubleGuardGcCheckTaskRecord = async (params) => {
  const res = await put(
    `/double_guard/gc_check_task_record/${params.id}`,
    params?.values
  );
  return res;
};

export const finishDoubleGuardGcCheckTaskRecord = async (params) => {
  const base_url = `/double_guard/gc_check_task_record/${params.id}/finish`;
  const res = await post(base_url, params.values);
  return res;
};

export const getDoubleGuardGcCheckTaskRecordItemList = async (params) => {
  console.log("getDoubleGuardGcCheckTaskRecordItemList", params);
  const base_url = `/double_guard/gc_check_task_record/${params.id}/check_item`;
  const res = await post(base_url, params); //hack: 与其它search接口兼容，唯一不同：中间有id作为Path参数
  return res;
};

export const normalDoubleGuardGcCheckTaskRecordItem = async (id) => {
  const base_url = `/double_guard/gc_check_item_record/${id}/normal`;
  const res = await post(base_url);
};

export const batchNormalDoubleGuardGcCheckTaskRecordItem = async (ids) => {
  const base_url = `/double_guard/gc_check_item_record/normal`;
  const res = await post(base_url, ids);
};

export const abnormalDoubleGuardGcCheckTaskRecordItem = async (id) => {
  const base_url = `/double_guard/gc_check_item_record/${id}/abnormal`;
  const res = await post(base_url);
};

export const batchAbnormalDoubleGuardGcCheckTaskRecordItem = async (ids) => {
  const base_url = `/double_guard/gc_check_item_record/abnormal`;
  const res = await post(base_url, ids);
};

const dummyDoubleGuardGcCheckTaskRecordItemApi = async (params) => {};

export const disPatchDoubleGuardGcCheckTaskRecordItem = async (params) => {
  const base_url = "/double_guard/gc_check_item_record/dispatch";
  const res = await post(base_url, params);
  return res;
};

export const getDoubleGuardGcCheckTaskRecordItemAbnormalList = async (
  params
) => {
  const base_url = `/double_guard/gc_check_task_record/${params.id}/abnormal_check_item`;
  const res = await post(base_url, params);
  return res;
};

export const dangerDoubleGuardGcCheckTaskRecordItem = async (params) => {
  const base_url = `/double_guard/gc_check_item_record/${params.id}/danger`;
  const res = await post(base_url, params.values);
  return res;
};

export const doubleGuardGcCheckTaskRecordApis: CommonApis = {
  entity: "DoubleGuardGcCheckTaskRecord",
  query: getDoubleGuardGcCheckTaskRecordList,
  create: createDoubleGuardGcCheckTaskRecord,
  remove: delDoubleGuardGcCheckTaskRecord,
  removes: delDoubleGuardGcCheckTaskRecords,
  update: updateDoubleGuardGcCheckTaskRecord,
  get: getDoubleGuardGcCheckTaskRecord,
};

export const doubleGuardGcCheckTaskRecordItemApis: CommonApis = {
  entity: "DoubleGuardGcCheckTaskRecordItem",
  query: getDoubleGuardGcCheckTaskRecordItemList,
  create: dummyDoubleGuardGcCheckTaskRecordItemApi,
  remove: dummyDoubleGuardGcCheckTaskRecordItemApi,
  removes: dummyDoubleGuardGcCheckTaskRecordItemApi,
  update: dummyDoubleGuardGcCheckTaskRecordItemApi,
  get: dummyDoubleGuardGcCheckTaskRecordItemApi,
};

export const doubleGuardGcCheckTaskRecordAbnormalItemApis: CommonApis = {
  entity: "DoubleGuardGcCheckTaskRecordAbnormalItem",
  query: getDoubleGuardGcCheckTaskRecordItemAbnormalList,
  create: dummyDoubleGuardGcCheckTaskRecordItemApi,
  remove: dummyDoubleGuardGcCheckTaskRecordItemApi,
  removes: dummyDoubleGuardGcCheckTaskRecordItemApi,
  update: dummyDoubleGuardGcCheckTaskRecordItemApi,
  get: dummyDoubleGuardGcCheckTaskRecordItemApi,
};
