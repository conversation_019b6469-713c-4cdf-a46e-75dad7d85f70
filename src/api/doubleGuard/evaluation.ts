import { post, get, del, put } from "@api";
import { omit, clone } from "ramda";
import { formatRFC3339 } from "utils";

export type EvaluationParams = {
  filter?: EvaluationFilter;
  pageNumber: number;
  pageSize: number;
  query?: string;
};

export type EvaluationFilter = {
  reviewDatetimeGte?: string;
  reviewDatetimeLTe?: string;
  riskEventId?: number;
  riskLevel?: number;
};

export const getEvaluationList = async (params: EvaluationParams) => {
  const base_url = "/double_guard/risk_event/evaluation/search";
  const res = await post(base_url, params);
  return res;
};

export const getEvaluation = async (id) => {
  const base_url = `/double_guard/risk_event/${id}/evaluation`;
  return await get(base_url);
};

export type CreateParams = {
  control_level: number;
  evaluationType: number;
  lecC: number;
  lecE: number;
  lecL: number;
  lsL: number;
  lsS: number;
  mesE: number;
  mesM: number;
  mesS: number;
  nextReviewDatetime: string;
  riskLevel: number;
  riskScore: number;
};

export type CreateEvaluationParams = {
  id: number;
  values: CreateParams;
};
// 新增
export const createEvaluation = async (params: CreateEvaluationParams) => {
  const res = await put(
    `/double_guard/risk_event/${params.id}/evaluation`,
    params.values,
  );
  return res;
};

export const getHistoryList = async (params: EvaluationParams) => {
  const filter = clone(params?.filter);
  let tmp;
  if (filter?.riskEventId !== undefined) {
    if (!tmp) tmp = {};
    tmp.riskEventId = filter.riskEventId;
  }
  if (filter?.riskLevel !== undefined) {
    if (!tmp) tmp = {};
    tmp.riskLevel = filter.riskLevel;
  }
  if (filter?.areaId !== undefined) {
    if (!tmp) tmp = {};
    tmp.areaId = filter.areaId;
  }
  if (filter?.time !== undefined) {
    if (!tmp) tmp = {};
    if (filter.time[0]) {
      tmp.reviewDatetimeGte = formatRFC3339(filter.time[0]);
    }
    if (filter.time[1]) {
      tmp.reviewDatetimeLTe = formatRFC3339(filter.time[1]);
    }
  }

  const obj = {
    ...params,
    filter: tmp,
  };
  const url = `/double_guard/${obj.uri || "risk_event"}/evaluation/search`;
  if (obj.uri) {
    delete obj.uri;
  }
  const res = await post(url, obj);
  return res;
};

// export const getEvaluation = async (id: string) => {
//   const res = await get(`/double_guard/risk_event/${id}/evaluation`)
//   return res
// }
