import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/double_guard/cc_category/search

export const getDoubleGuardCcCategoryList = async (params) => {
  const base_url = "/double_guard/cc_category/search";
  const res = await post(base_url, params);
  return res;
};

export const getDoubleGuardCcCategory = async (id) => {
  const base_url = `/double_guard/cc_category/${id}`;
  return await get(base_url);
};

export const createDoubleGuardCcCategory = async (params) => {
  const res = await post("/double_guard/cc_category", params);
  return res;
};

export const delDoubleGuardCcCategory = async (id: number) => {
  const res = await del(`/double_guard/cc_category/${id}`);
  return res;
};

export const delDoubleGuardCcCategorys = async (ids) => {
  const res = await del(`/double_guard/cc_category`, ids);
  return res;
};

export const updateDoubleGuardCcCategory = async (params) => {
  const res = await put(
    `/double_guard/cc_category/${params.id}`,
    params?.values,
  );
  return res;
};

export const doubleGuardCcCategoryApis: CommonApis = {
  entity: "DoubleGuardCcCategory",
  query: getDoubleGuardCcCategoryList,
  create: createDoubleGuardCcCategory,
  remove: delDoubleGuardCcCategory,
  removes: delDoubleGuardCcCategorys,
  update: updateDoubleGuardCcCategory,
  get: getDoubleGuardCcCategory,
};
