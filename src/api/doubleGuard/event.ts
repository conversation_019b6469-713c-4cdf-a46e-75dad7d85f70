// https://api.vren-tech.com/v1/double_guard/risk_event/search
import { del, get, post, put } from "@api";

export type EventParams = {
  filter?: EventFilter;
  pageNumber: number;
  pageSize: number;
  query?: string;
};

export type EventFilter = {
  liableDepartmentId: number;
  liablePersonId: number;
  risk_level: number;
  riskObjectId: number;
  riskUnitId: number;
};

export const getEventList = async (params: EventParams) => {
  const base_url = "/double_guard/risk_event/search";
  const res = await post(base_url, params);
  return res;
};

export const getEvent = async (id) => {
  const base_url = `/double_guard/risk_event/${id}`;
  return await get(base_url);
};

// 新增
export const createEvent = async (params) => {
  const res = await post("/double_guard/risk_event", params);
  return res;
};

// 删除单个项目
export const delEvent = async (id: number) => {
  const res = await del(`/double_guard/risk_event/${id}`);
  return res;
};

// 批量删除
export const delEvents = async (ids) => {
  const res = await del(`/double_guard/risk_event`, ids);
  return res;
};

// 修改
export const updateEvent = async (params) => {
  const res = await put(
    `/double_guard/risk_event/${params.id}`,
    params?.values
  );
  return res;
};

export const allowReportRiskEvent = async (ids) => {
  const base_url = `/double_guard/risk_event/allow_upload`;
  const res = await post(base_url, ids);
  return res;
};
