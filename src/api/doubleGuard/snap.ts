import { del, get, post } from "@api";

export type SnapParams = {
  filter?: SnapFilter;
  pageNumber: number;
  pageSize: number;
  query?: string;
};

export type SnapFilter = {
  auditAt: string;
  auditorId: number;
  correctAt: string;
  correctorId: number;
  status: number;
};

export const getSnapList = async (params: SnapParams) => {
  const base_url = "/double_guard/snap/search";
  const res = await post(base_url, params);
  return res;
};

export const getSnap = async (id: string) => {
  const base_url = `/double_guard/snap/${id}`;
  const res = await get(base_url);
  return res;
};

export const delSnap = async (id: string) => {
  const base_url = `/double_guard/snap/${id}`;
  const res = await del(base_url);
  return res;
};

// 随手拍判定正常
export const setSnapNormal = async (id) => {
  const base_url = `/double_guard/snap/${id}/audit/normal`;
  const res = await post(base_url);
  return res;
};
// 随手拍生成限期整改隐患
export const setSnapDeadline = async (params) => {
  const base_url = `/double_guard/snap/${params.id}/audit/deadline_rectification`;
  const res = await post(base_url, params);
  return res;
};

// 随手拍生成即查即改隐患
export const setSnapInstant = async (params) => {
  const base_url = `/double_guard/snap/${params.id}/audit/instant_rectification`;
  const res = await post(base_url, params);
  return res;
};

export const postRedirectSnapAudit = async (params: { id: number }) => {
  const base_url = `/double_guard/snap/${params.id}/audit/delegate`;
  delete params.id;
  const res = await post(base_url, params);
  return res;
};
