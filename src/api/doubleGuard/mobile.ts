import { post, get, del, put } from "@api";

export type MobileParams = {
  filter?: MobileFilter;
  pageNumber: number;
  pageSize: number;
  query?: string;
};

export type MobileFilter = {
  riskEventId?: number;
  riskObjectId: number;
  riskUnitId: number;
};

export const getMobileList = async (params: MobileParams) => {
  const base_url = "/double_guard/mobile/search";
  const res = await post(base_url, params);
  return res;
};

export const getMobile = async (id) => {
  const base_url = `/double_guard/mobile/${id}`;
  return await get(base_url);
};

// 新增
export const createMobile = async (params) => {
  const res = await post("/double_guard/mobile", params);
  return res;
};

// 删除单个项目
export const delMobile = async (id: number) => {
  const res = await del(`/double_guard/mobile/${id}`);
  return res;
};

// 批量删除
export const delMobiles = async (ids) => {
  const res = await del(`/double_guard/mobile`, ids);
  return res;
};

// 修改
export const updateMobile = async (params) => {
  const res = await put(`/double_guard/mobile/${params.id}`, params?.values);
  return res;
};
