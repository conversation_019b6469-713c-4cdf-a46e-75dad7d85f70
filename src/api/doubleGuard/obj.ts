import { del, get, post, put } from "@api";

export type RiskObjectParams = {
  filter?: RiskObjectFilter;
  pageNumber: number;
  pageSize: number;
  query?: string;
};

export type RiskObjectFilter = {
  /**
   * 风险分区id
   */
  areaId?: number;
  /**
   * 是否是重大危险源
   */
  isMajorHazard?: number;
  /**
   * 责任部门id
   */
  liableDepartmentId?: number;
  /**
   * 责任人id
   */
  liablePersonId?: number;
  /**
   * 固有风险等级
   */
  riskLevel?: number;
};

export const getRiskObjectList = async (params: RiskObjectParams) => {
  const base_url = "/double_guard/risk_object/search";
  const res = await post(base_url, params);
  return res;
};

export const getRiskObject = async (id) => {
  const base_url = `/double_guard/risk_object/${id}`;
  return await get(base_url);
};

// 新增
export const createRiskObject = async (params) => {
  const res = await post("/double_guard/risk_object", params);
  return res;
};

// 删除单个项目
export const delRiskObject = async (id: number) => {
  const res = await del(`/double_guard/risk_object/${id}`);
  return res;
};

// 批量删除
export const delRiskObjects = async (ids) => {
  const res = await del(`/double_guard/risk_object`, ids);
  return res;
};

// 修改
export const updateRiskObject = async (params) => {
  const res = await put(
    `/double_guard/risk_object/${params.id}`,
    params?.values
  );
  return res;
};

export const allowReportRiskObject = async (ids) => {
  const base_url = `/double_guard/risk_object/allow_upload`;
  const res = await post(base_url, ids);
  return res;
};

type GetTreeType = {
  withRiskEvent: boolean;
  withRiskUnit: boolean;
  isMajorHazard?: number;
};

// 风险对象/单元/事件浏览组件
export const getTree = async (params: GetTreeType) => {
  const res = await post(`/double_guard/risk_object/tree`, params);
  return res;
};
