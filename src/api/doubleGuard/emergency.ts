import { post, get, del, put } from "@api";

export type EmergencyParams = {
  filter?: RiskAwarenessFilter;
  pageNumber: number;
  pageSize: number;
  query?: string;
};

export type EmergencyFilter = {
  areaId?: number;
};

export const getEmergencyList = async (params: EmergencyParams) => {
  const base_url = "/double_guard/emergency_manage_card/search";
  const res = await post(base_url, params);
  return res;
};

export const getEmergency = async (id) => {
  const base_url = `/double_guard/emergency_manage_card/${id}`;
  return await get(base_url);
};

// 新增
export const createEmergency = async (params) => {
  const res = await post("/double_guard/emergency_manage_card", params);
  return res;
};

// 删除单个项目
export const delEmergency = async (id: number) => {
  const res = await del(`/double_guard/emergency_manage_card/${id}`);
  return res;
};

// 批量删除
export const delEmergencys = async (ids) => {
  const res = await del(`/double_guard/emergency_manage_card`, ids);
  return res;
};

// 修改
export const updateEmergency = async (params) => {
  const res = await put(
    `/double_guard/emergency_manage_card/${params.id}`,
    params?.values,
  );
  return res;
};
