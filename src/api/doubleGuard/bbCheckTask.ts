import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/double_guard/bb_check_task/search

export const getBbCheckTaskList = async (params) => {
  const base_url = "/double_guard/bb_check_task/search";
  const res = await post(base_url, params);
  return res;
};

export const getBbCheckTask = async (id) => {
  const base_url = `/double_guard/bb_check_task/${id}`;
  return await get(base_url);
};

export const createBbCheckTask = async (params) => {
  const res = await post("/double_guard/bb_check_task", params);
  return res;
};

export const delBbCheckTask = async (id: number) => {
  const res = await del(`/double_guard/bb_check_task/${id}`);
  return res;
};

export const delBbCheckTasks = async (ids) => {
  const res = await del(`/double_guard/bb_check_task`, ids);
  return res;
};

export const updateBbCheckTask = async (params) => {
  const res = await put(
    `/double_guard/bb_check_task/${params.id}`,
    params?.values,
  );
  return res;
};

export const bbCheckTaskApis: CommonApis = {
  entity: "BbCheckTask",
  query: getBbCheckTaskList,
  create: createBbCheckTask,
  remove: delBbCheckTask,
  removes: delBbCheckTasks,
  update: updateBbCheckTask,
  get: getBbCheckTask,
};
