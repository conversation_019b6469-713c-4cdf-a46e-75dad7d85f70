import { get } from "api/request";

export const getRiskDistribution = async () => {
  const base_url = "/double_guard/risk_distribution";
  const res = await get(base_url);
  return res;
};


export const getUserStat = async (uid: string) => {
  const base_url = `/double_guard/user/${uid}/stat`;
  const res = await get(base_url);
  return res;
};


export const getCommitmentAnnouncement = async () => {
  const base_url = `/double_guard/commitment_announcement`;
  const res = await get(base_url);
  return res;
};

