import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/double_guard/gc_check_task/search

export const getDoubleGuardGcCheckTaskList = async (params) => {
  const base_url = "/double_guard/gc_check_task/search";
  const res = await post(base_url, params);
  return res;
};

export const getDoubleGuardGcCheckTask = async (id) => {
  const base_url = `/double_guard/gc_check_task/${id}`;
  return await get(base_url);
};

export const createDoubleGuardGcCheckTask = async (params) => {
  const res = await post("/double_guard/gc_check_task", params);
  return res;
};

export const delDoubleGuardGcCheckTask = async (id: number) => {
  const res = await del(`/double_guard/gc_check_task/${id}`);
  return res;
};

export const delDoubleGuardGcCheckTasks = async (ids) => {
  const res = await del(`/double_guard/gc_check_task`, ids);
  return res;
};

export const updateDoubleGuardGcCheckTask = async (params) => {
  const res = await put(
    `/double_guard/gc_check_task/${params.id}`,
    params?.values
  );
  return res;
};

export const dispatchDoubleGuardGcCheckTask = async (params) => {
  const res = await post(
    `/double_guard/gc_check_task/${params?.id}/dispatch`,
    params?.values
  );
  return res;
};

export const reDispatchDoubleGuardGcCheckTask = async (params) => {
  const res = await post(
    `/double_guard/gc_check_task/${params?.id}/redispatch`,
    params?.values
  );
  return res;
};

export const doubleGuardGcCheckTaskApis: CommonApis = {
  entity: "DoubleGuardGcCheckTask",
  query: getDoubleGuardGcCheckTaskList,
  create: createDoubleGuardGcCheckTask,
  remove: delDoubleGuardGcCheckTask,
  removes: delDoubleGuardGcCheckTasks,
  update: updateDoubleGuardGcCheckTask,
  get: getDoubleGuardGcCheckTask,
};
