import { del, get, post, put } from "@api";

export type RiskUnitParams = {
  filter?: RiskUnitFilter;
  pageNumber: number;
  pageSize: number;
  query?: string;
};

export type RiskUnitFilter = {
  /**
   * 责任部门id
   */
  liableDepartmentId: number;
  /**
   * 责任人id
   */
  liablePersonId: number;
  /**
   * 风险等级
   */
  riskLevel: number;
  /**
   * 风险对象id
   */
  riskObjectId: number;
};

export const getRiskUnitList = async (params: RiskUnitParams) => {
  const base_url = "/double_guard/risk_unit/search";
  const res = await post(base_url, params);
  return res;
};

export const getRiskUnit = async (id) => {
  const base_url = `/double_guard/risk_unit/${id}`;
  return await get(base_url);
};

// 新增
export const createRiskUnit = async (params) => {
  const res = await post("/double_guard/risk_unit", params);
  return res;
};

// 删除单个项目
export const delRiskUnit = async (id: number) => {
  const res = await del(`/double_guard/risk_unit/${id}`);
  return res;
};

// 批量删除
export const delRiskUnits = async (ids) => {
  const res = await del(`/double_guard/risk_unit`, ids);
  return res;
};

// 修改
export const updateRiskUnit = async (params) => {
  const res = await put(`/double_guard/risk_unit/${params.id}`, params?.values);
  return res;
};

export const createUnitStop = async (params) => {
  const res = await post(
    `/double_guard/risk_unit/${params.id}/stop`,
    params?.values
  );
  return res;
};

export const getUnitStop = async (id) => {
  const res = await get(`/double_guard/risk_unit/${id}/stop`);
  return res;
};

export const getUnitStopList = async (params) => {
  const res = await post(`/double_guard/risk_unit/stop/search`, params);
  return res;
};

export const getRiskUnitCard = async (id) => {
  const res = await get(`/double_guard/risk_unit/${id}/information_card`);
  return res;
};

export const allowReportRiskUnit = async (ids) => {
  const base_url = `/double_guard/risk_unit/allow_upload`;
  const res = await post(base_url, ids);
  return res;
};
