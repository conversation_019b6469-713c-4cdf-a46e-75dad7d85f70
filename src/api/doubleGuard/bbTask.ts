import { post, get, del, put } from "@api";

export type BbTaskParams = {
  filter?: BbTaskFilter;
  pageNumber: number;
  pageSize: number;
  query?: string;
};

export type BbTaskFilter = {
  bbWorkType?: number;
};

export const getBbTaskList = async (params: BbTaskParams) => {
  const base_url = "/double_guard/bb_task_template/search";
  const res = await post(base_url, params);
  return res;
};

export const getBbTask = async (id) => {
  const base_url = `/double_guard/bb_task_template/${id}`;
  return await get(base_url);
};

// 新增
export const createBbTask = async (params) => {
  const res = await post("/double_guard/bb_task_template", params);
  return res;
};

// 删除单个项目
export const delBbTask = async (id: number) => {
  const res = await del(`/double_guard/bb_task_template/${id}`);
  return res;
};

// 批量删除
export const delBbTasks = async (ids) => {
  const res = await del(`/double_guard/bb_task_template`, ids);
  return res;
};

// 修改
export const updateBbTask = async (params) => {
  const res = await put(
    `/double_guard/bb_task_template/${params.id}`,
    params?.values,
  );
  return res;
};
