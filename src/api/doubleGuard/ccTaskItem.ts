import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/double_guard/cc_task_item/search

export const getDoubleGuardCcTaskItemList = async (params) => {
  const base_url = `/double_guard/cc_task/${params.id}/item`;
  const res = await post(base_url, params.values);
  return res;
};

export const getDoubleGuardCcTaskItem = async (id) => {
  const base_url = `/double_guard/cc_task_item/${id}`;
  return await get(base_url);
};

export const createDoubleGuardCcTaskItem = async (params) => {
  const res = await post("/double_guard/cc_task_item", params);
  return res;
};

export const delDoubleGuardCcTaskItem = async (id: number) => {
  const res = await del(`/double_guard/cc_task_item/${id}`);
  return res;
};

export const delDoubleGuardCcTaskItems = async (ids) => {
  const res = await del(`/double_guard/cc_task_item`, ids);
  return res;
};

export const updateDoubleGuardCcTaskItem = async (params) => {
  const res = await put(
    `/double_guard/cc_task_item/${params.id}`,
    params?.values
  );
  return res;
};

export const delegateDoubleGuardCcTaskItem = async (params) => {
  const res = await post(
    `/double_guard/cc_task/${params.id}/check/delegate`,
    params?.values
  );
  return res;
};

export const normalDoubleGuardCcTaskItem = async (params) => {
  const res = await post(
    `/double_guard/cc_task/${params.taskId}/item/${params.itemId}/check/normal`,
    params?.values
  );
};

export const batchNormalDoubleGuardCcTaskItem = async (params) => {
  const res = await post(
    `/double_guard/cc_task/${params.id}/check/normal`,
    params?.values
  );
  return res;
};

export const deadlineRectificationDoubleGuardCcTaskItem = async (params) => {
  const res = await post(
    `/double_guard/cc_task/${params.taskId}/item/${params.itemId}/check/deadline_rectification`,
    params?.values
  );
  return res;
};

export const instantRectificationDoubleGuardCcTaskItem = async (params) => {
  const res = await post(
    `/double_guard/cc_task/${params.taskId}/item/${params.itemId}/check/instant_rectification`,
    params?.values
  );
  return res;
};

export const doubleGuardCcTaskItemApis: CommonApis = {
  entity: "DoubleGuardCcTaskItem",
  query: getDoubleGuardCcTaskItemList,
  create: createDoubleGuardCcTaskItem,
  remove: delDoubleGuardCcTaskItem,
  removes: delDoubleGuardCcTaskItems,
  update: updateDoubleGuardCcTaskItem,
  get: getDoubleGuardCcTaskItem,
};
