import { post, get, del, put } from "@api";

export type RiskAwarenessParams = {
  filter?: RiskAwarenessFilter;
  pageNumber: number;
  pageSize: number;
  query?: string;
};

export type RiskAwarenessFilter = {
  areaId?: number;
};

export const getRiskAwarenessList = async (params: RiskAwarenessParams) => {
  const base_url = "/double_guard/risk_awareness_card/search";
  const res = await post(base_url, params);
  return res;
};

export const getRiskAwareness = async (id) => {
  const base_url = `/double_guard/risk_awareness_card/${id}`;
  return await get(base_url);
};

// 新增
export const createRiskAwareness = async (params) => {
  const res = await post("/double_guard/risk_awareness_card", params);
  return res;
};

// 删除单个项目
export const delRiskAwareness = async (id: number) => {
  const res = await del(`/double_guard/risk_awareness_card/${id}`);
  return res;
};

// 批量删除
export const delRiskAwarenesss = async (ids) => {
  const res = await del(`/double_guard/risk_awareness_card`, ids);
  return res;
};

// 修改
export const updateRiskAwareness = async (params) => {
  const res = await put(
    `/double_guard/risk_awareness_card/${params.id}`,
    params?.values,
  );
  return res;
};
