import { post, get, del, put } from "@api";

export type AreaParams = {
  filter?: AreaFilter;
  pageNumber: number;
  pageSize: number;
  query?: string;
};

export type AreaFilter = {
  isDrawn: number;
  liablePersonId: number;
  riskLevel: number;
};

export const getAreaList = async (params: AreaParams) => {
  const base_url = "/double_guard/risk_area/search";
  const res = await post(base_url, params);
  return res;
};

export const getArea = async (id) => {
  const base_url = `/double_guard/risk_area/${id}`;
  return await get(base_url);
};

// 新增
export const createArea = async (params) => {
  const res = await post("/double_guard/risk_area", params);
  return res;
};

// 删除单个项目
export const delArea = async (id: number) => {
  const res = await del(`/double_guard/risk_area/${id}`);
  return res;
};

// 批量删除
export const delAreas = async (ids) => {
  const res = await del(`/double_guard/risk_area`, ids);
  return res;
};

// 修改
export const updateArea = async (params) => {
  const res = await put(`/double_guard/risk_area/${params.id}`, params?.values);
  return res;
};

// 新增风险分区评估
export const createAreaEvaluation = async (params) => {
  const res = await put(
    `/double_guard/risk_area/${params.id}/evaluation`,
    params?.values,
  );
  return res;
};

export const getDrawAreas = async () => {
  const res = await get("/double_guard/risk_area/draw");
  return res;
};

export const drawArea = async ({ id, body }) => {
  const res = await put(`/double_guard/risk_area/${id}/draw`, body);
  return res;
};
