import { post, get, del, put } from "@api";

export type BbCheckParams = {
  filter?: BbCheckFilter;
  pageNumber: number;
  pageSize: number;
  query?: string;
};

export type BbCheckFilter = {
  bbWorkType: number;
  riskControlMeasureId: number;
  riskObjectId: number;
  riskUnitId: number;
};

export const getBbCheckList = async (params: BbCheckParams) => {
  const base_url = "/double_guard/bb_check_plan/search";
  const res = await post(base_url, params);
  return res;
};

export const getBbCheck = async (id) => {
  const base_url = `/double_guard/bb_check_plan/${id}`;
  return await get(base_url);
};

// 新增
export const createBbCheck = async (params) => {
  const res = await post("/double_guard/bb_check_plan", params);
  return res;
};

// 删除单个项目
export const delBbCheck = async (id: number) => {
  const res = await del(`/double_guard/bb_check_plan/${id}`);
  return res;
};

// 批量删除
export const delBbChecks = async (ids) => {
  const res = await del(`/double_guard/bb_check_plan`, ids);
  return res;
};

// 修改
export const updateBbCheck = async (params) => {
  const res = await put(
    `/double_guard/bb_check_plan/${params.id}`,
    params?.values,
  );
  return res;
};

export const getBbCheckRecordList = async (params: any) => {
  const base_url = "/double_guard/bb_check_record/search";
  const res = await post(base_url, params);
  return res;
};

export const getBbCheckStat = async () => {
  const base_url = "/double_guard/bb_check_stat";
  const res = await get(base_url);
  return res;
};
