import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/double_guard/cc_plan/search

export const getDoubleGuardCcPlanList = async (params) => {
  const base_url = "/double_guard/cc_plan/search";
  const res = await post(base_url, params);
  return res;
};

export const getDoubleGuardCcPlan = async (id) => {
  const base_url = `/double_guard/cc_plan/${id}`;
  return await get(base_url);
};

export const createDoubleGuardCcPlan = async (params) => {
  const res = await post("/double_guard/cc_plan", params);
  return res;
};

export const delDoubleGuardCcPlan = async (id: number) => {
  const res = await del(`/double_guard/cc_plan/${id}`);
  return res;
};

export const delDoubleGuardCcPlans = async (ids) => {
  const res = await del(`/double_guard/cc_plan`, ids);
  return res;
};

export const updateDoubleGuardCcPlan = async (params) => {
  const res = await put(
    `/double_guard/cc_plan/${params.id}`,
    params?.values,
  );
  return res;
};

export const doubleGuardCcPlanApis: CommonApis = {
  entity: "DoubleGuardCcPlan",
  query: getDoubleGuardCcPlanList,
  create: createDoubleGuardCcPlan,
  remove: delDoubleGuardCcPlan,
  removes: delDoubleGuardCcPlans,
  update: updateDoubleGuardCcPlan,
  get: getDoubleGuardCcPlan,
};
