import { del, post } from "@api";

export interface CheckTaskParameters {
  filter?: CheckTaskFilter;
  pageNumber: number;
  pageSize: number;
  query?: string;
}

export interface CheckTaskFilter {
  classify1: number;
  classify2: number;
  riskObjectId: number;
  riskUnitId: number;
  workTimeEndGt: string;
  workTimeEndLt: string;
}

export const getCheckTaskList = async (parameters: CheckTaskParameters) => {
  const base_url = `/double_guard/check_task/search`;
  const res = await post(base_url, parameters);
  return res;
};

// 删除单个
export const delCheckTask = async (id: number) => {
  const res = await del(`/double_guard/check_task/${id}`);
  return res;
};

// 批量删除
export const delCheckTasks = async (ids) => {
  const res = await del(`/double_guard/check_task`, ids);
  return res;
};
