import { del, get, post } from "@api";

export type DangerParams = {
  filter?: DangerFilter;
  pageNumber: number;
  pageSize: number;
  query?: string;
};

export type DangerFilter = {
  dangerCategory: number;
  dangerType: number;
  manageType: number;
  reportTimeGte: string;
  reportTimeLte: string;
  riskObjectId: number;
  source: number;
};

export interface GetDangerListResult {
  /**
   * 验收候选人列表
   */
  acceptorCands: AcceptorCand[];
  /**
   * 隐患类别
   */
  dangerCategory: number;
  /**
   * 隐患类型
   */
  dangerType: number;
  /**
   * 治理期限
   */
  deadline?: Date;
  evaluateResult: number;
  /**
   * 评估候选人列表
   */
  evalatorCands: EvaluatorCand[];
  /**
   * 隐患记录
   */
  id: number;
  /**
   * 隐患等级
   */
  level: number;
  /**
   * 治理类型
   */
  manageType: number;
  /**
   * 隐患名称
   */
  name?: string;
  /**
   * 整改候选人列表
   */
  rectifierCands: RectifierCand[];
  /**
   * 上报人列表
   */
  reporters?: Reporter[];
  /**
   * 上报时间
   */
  reportTime?: Date;
  /**
   * 风险对象
   */
  riskObject?: RiskObject;
  /**
   * 隐患来源
   */
  source: number;
  /**
   * 隐患状态
   */
  status: number;
  [property: string]: any;
}

export interface AcceptorCand {
  id: number;
  name: string;
  [property: string]: any;
}

export interface EvaluatorCand {
  id: number;
  name: string;
  [property: string]: any;
}

export interface RectifierCand {
  id: number;
  name: string;
  [property: string]: any;
}

export interface Reporter {
  id: number;
  name: string;
  [property: string]: any;
}

export interface RiskObject {
  id: number;
  name: string;
  [property: string]: any;
}

export const getDangerList = async (params: DangerParams) => {
  const base_url = "/double_guard/danger/search";
  const res = await post(base_url, params);
  return res;
};

export const getDanger = async (id) => {
  const base_url = `/double_guard/danger/${id}`;
  return await get(base_url);
};

export const postEvaluate = async (params: any) => {
  const base_url = `/double_guard/danger/${params.id}/evaluate`;
  delete params.id;
  const res = await post(base_url, params);
  return res;
};

export const postRectify = async (params: any) => {
  const base_url = `/double_guard/danger/${params.id}/rectify`;
  delete params.id;
  const res = await post(base_url, params);
  return res;
};

export const postAccept = async (params: any) => {
  const base_url = `/double_guard/danger/${params.id}/accept`;
  delete params.id;
  const res = await post(base_url, params);
  return res;
};

export const postDeadlineRectification = async (params: any) => {
  const base_url = `/double_guard/danger/deadline_rectification`;
  const res = await post(base_url, params);
  return res;
};

export const postInstantRectification = async (params: any) => {
  const base_url = `/double_guard/danger/instant_rectification`;
  const res = await post(base_url, params);
  return res;
};

export const getCheckRecordList = async (params: any) => {
  const base_url = `/double_guard/check_record/search`;
  const res = await post(base_url, params);
  return res;
};

// 删除单个
export const delDanger = async (id: number) => {
  const res = await del(`/double_guard/danger/${id}`);
  return res;
};

// 批量删除
export const delDangers = async (ids) => {
  const res = await del(`/double_guard/danger`, ids);
  return res;
};

export const postRequestDelay = async (params: {
  id: number;
  delayApplyReason: string;
  delayApplyDeadline: string;
}) => {
  const base_url = `/double_guard/danger/${params.id}/rectify/apply_delay`;
  delete params.id;
  const res = await post(base_url, params);
  return res;
};

export const postApproveDelay = async (params: {
  id: number;
  delayApproveResult: number;
}) => {
  const base_url = `/double_guard/danger/${params.id}/rectify/apply_delay/approve`;
  delete params.id;
  const res = await post(base_url, params);
  return res;
};

export const postRedirectDangerEvaluate = async (params: { id: number }) => {
  const base_url = `/double_guard/danger/${params.id}/evaluate/delegate`;
  delete params.id;
  const res = await post(base_url, params);
  return res;
};
export const postRedirectDangerDelayApprover = async (params: {
  id: number;
}) => {
  const base_url = `/double_guard/danger/${params.id}/rectify/apply_delay/approve/delegate`;
  delete params.id;
  const res = await post(base_url, params);
  return res;
};
export const postRedirectDangerRectifier = async (params: { id: number }) => {
  const base_url = `/double_guard/danger/${params.id}/rectify/delegate`;
  delete params.id;
  const res = await post(base_url, params);
  return res;
};
export const postRedirectDangerAcceptor = async (params: { id: number }) => {
  const base_url = `/double_guard/danger/${params.id}/accept/delegate`;
  delete params.id;
  const res = await post(base_url, params);
  return res;
};

export const allowUploadDanger = async (ids) => {
  const base_url = `/double_guard/danger/allow_upload`;
  const res = await post(base_url, ids);
  return res;
};
