import { post, get, del, put } from "@api";

export type CheckPlanParams = {
  filter?: CheckPlanFilter;
  pageNumber: number;
  pageSize: number;
  query?: string;
};

export type CheckPlanFilter = {
  checkCycleUnit: number;
  riskControlMeasureId: number;
  riskObjectId: number;
  riskUnitId: number;
};

export const getCheckPlanList = async (params: CheckPlanParams) => {
  const base_url = "/double_guard/check_plan/search";
  const res = await post(base_url, params);
  return res;
};

export const getCheckPlan = async (id) => {
  const base_url = `/double_guard/check_plan/${id}`;
  return await get(base_url);
};

// 新增
export const createCheckPlan = async (params) => {
  const res = await post("/double_guard/check_plan", params);
  return res;
};

// 批量新增
export const createCheckPlanBatch = async (params) => {
  const res = await post("/double_guard/check_plan_batch", params);
  return res;
};

// 删除单个项目
export const delCheckPlan = async (id: number) => {
  const res = await del(`/double_guard/check_plan/${id}`);
  return res;
};

// 批量删除
export const delCheckPlans = async (ids) => {
  const res = await del(`/double_guard/check_plan`, ids);
  return res;
};

// 修改
export const updateCheckPlan = async (params) => {
  const res = await put(
    `/double_guard/check_plan/${params.id}`,
    params?.values,
  );
  return res;
};
