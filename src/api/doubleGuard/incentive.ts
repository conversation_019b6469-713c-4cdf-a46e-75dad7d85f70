import { post, get, del, put } from "@api";

export type IncentiveParams = {
  filter?: IncentiveFilter;
  pageNumber: number;
  pageSize: number;
  query?: string;
};

export type IncentiveFilter = {
  bookDateGte: string;
  bookDateLte: string;
  departmentId: number;
  incentiveType?: number;
  personId: number;
};

export const getIncentiveList = async (params: IncentiveParams) => {
  const base_url = "/double_guard/incentive_record/search";
  const res = await post(base_url, params);
  return res;
};

export type CreateIncentiveParams = {
  amount: number;
  bookDate: string;
  bookerId: number;
  departmentId: number;
  incentiveType: number;
  objectType: number;
  personId: number;
  reason: string;
};

// 新增
export const createIncentive = async (params: CreateIncentiveParams) => {
  const res = await post("/double_guard/incentive_record", params);
  return res;
};

export const getOperationEffectOld = async () => {
  const res = await get("/double_guard/operation_effect/old");
  return res;
};

export const getOperationEffectNew = async () => {
  const res = await get("/double_guard/operation_effect");
  return res;
};
