import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/double_guard/cc_item/search

export const getDoubleGuardCcItemList = async (params) => {
  const base_url = "/double_guard/cc_item/search";
  const res = await post(base_url, params);
  return res;
};

export const getDoubleGuardCcItem = async (id) => {
  const base_url = `/double_guard/cc_item/${id}`;
  return await get(base_url);
};

export const createDoubleGuardCcItem = async (params) => {
  const res = await post("/double_guard/cc_item", params);
  return res;
};

export const delDoubleGuardCcItem = async (id: number) => {
  const res = await del(`/double_guard/cc_item/${id}`);
  return res;
};

export const delDoubleGuardCcItems = async (ids) => {
  const res = await del(`/double_guard/cc_item`, ids);
  return res;
};

export const updateDoubleGuardCcItem = async (params) => {
  const res = await put(
    `/double_guard/cc_item/${params.id}`,
    params?.values,
  );
  return res;
};

export const doubleGuardCcItemApis: CommonApis = {
  entity: "DoubleGuardCcItem",
  query: getDoubleGuardCcItemList,
  create: createDoubleGuardCcItem,
  remove: delDoubleGuardCcItem,
  removes: delDoubleGuardCcItems,
  update: updateDoubleGuardCcItem,
  get: getDoubleGuardCcItem,
};
