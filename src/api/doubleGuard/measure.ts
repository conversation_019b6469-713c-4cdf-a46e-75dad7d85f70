import { del, get, post, put } from "@api";

export type RiskMeasureParams = {
  filter?: RiskMeasureFilter;
  pageNumber: number;
  pageSize: number;
  query?: string;
};

export type RiskMeasureFilter = {
  riskEventId?: number;
  riskObjectId: number;
  riskUnitId: number;
};

export const getMeasureList = async (params: RiskMeasureParams) => {
  const base_url = "/double_guard/risk_control_measure/search";
  const res = await post(base_url, params);
  return res;
};

export const getMeasure = async (id) => {
  const base_url = `/double_guard/risk_control_measure/${id}`;
  return await get(base_url);
};

// 新增
export const createMeasure = async (params) => {
  const res = await post("/double_guard/risk_control_measure", params);
  return res;
};

// 删除单个项目
export const delMeasure = async (id: number) => {
  const res = await del(`/double_guard/risk_control_measure/${id}`);
  return res;
};

// 批量删除
export const delMeasures = async (ids) => {
  const res = await del(`/double_guard/risk_control_measure`, ids);
  return res;
};

// 修改
export const updateMeasure = async (params) => {
  const res = await put(
    `/double_guard/risk_control_measure/${params.id}`,
    params?.values
  );
  return res;
};

export const allowReportRiskControlMeasure = async (ids) => {
  const base_url = `/double_guard/risk_control_measure/allow_upload`;
  const res = await post(base_url, ids);
  return res;
};
