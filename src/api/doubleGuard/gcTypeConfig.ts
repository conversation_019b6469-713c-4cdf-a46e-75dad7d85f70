import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/double_guard/gc_type_config/search

export const getDoubleGuardGcTypeConfigList = async (params) => {
  const base_url = "/double_guard/gc_type_config/search";
  const res = await post(base_url, params);
  return res;
};

export const getDoubleGuardGcTypeConfig = async (id) => {
  const base_url = `/double_guard/gc_type_config/${id}`;
  return await get(base_url);
};

export const createDoubleGuardGcTypeConfig = async (params) => {
  const res = await post("/double_guard/gc_type_config", params);
  return res;
};

export const delDoubleGuardGcTypeConfig = async (id: number) => {
  const res = await del(`/double_guard/gc_type_config/${id}`);
  return res;
};

export const delDoubleGuardGcTypeConfigs = async (ids) => {
  const res = await del(`/double_guard/gc_type_config`, ids);
  return res;
};

export const updateDoubleGuardGcTypeConfig = async (params) => {
  const res = await put(
    `/double_guard/gc_type_config/${params.id}`,
    params?.values,
  );
  return res;
};

export const doubleGuardGcTypeConfigApis: CommonApis = {
  entity: "DoubleGuardGcTypeConfig",
  query: getDoubleGuardGcTypeConfigList,
  create: createDoubleGuardGcTypeConfig,
  remove: delDoubleGuardGcTypeConfig,
  removes: delDoubleGuardGcTypeConfigs,
  update: updateDoubleGuardGcTypeConfig,
  get: getDoubleGuardGcTypeConfig,
};
