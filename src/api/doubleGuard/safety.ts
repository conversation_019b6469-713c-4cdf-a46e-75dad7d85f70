import { post, get, del, put } from "@api";

export type SafetyParams = {
  filter?: RiskAwarenessFilter;
  pageNumber: number;
  pageSize: number;
  query?: string;
};

export type SafetyFilter = {
  areaId?: number;
};

export const getSafetyList = async (params: SafetyParams) => {
  const base_url = "/double_guard/safety_commitment_card/search";
  const res = await post(base_url, params);
  return res;
};

export const getSafety = async (id) => {
  const base_url = `/double_guard/safety_commitment_card/${id}`;
  return await get(base_url);
};

// 新增
export const createSafety = async (params) => {
  const res = await post("/double_guard/safety_commitment_card", params);
  return res;
};

// 删除单个项目
export const delSafety = async (id: number) => {
  const res = await del(`/double_guard/safety_commitment_card/${id}`);
  return res;
};

// 批量删除
export const delSafetys = async (ids) => {
  const res = await del(`/double_guard/safety_commitment_card`, ids);
  return res;
};

// 修改
export const updateSafety = async (params) => {
  const res = await put(
    `/double_guard/safety_commitment_card/${params.id}`,
    params?.values,
  );
  return res;
};
