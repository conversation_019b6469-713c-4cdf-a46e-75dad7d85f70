import { post, get, del, put } from "@api";

type ParamsType<T> = {
  filter: T;
  pageNumber: number;
  pageSize: number;
  query: string;
  departmentId?: number;
};

type GetRiskIdentificationChecklistParams = {
  liableDepartmentId: number;
  liablePersonId: number;
  riskLevel: number;
  riskObjectId: number;
};

// 风险辨识清单
export const getRiskIdentificationChecklist = async (
  params: ParamsType<GetRiskIdentificationChecklistParams>,
) => {
  const base_url = "/double_guard/risk_identification_checklist/search";
  const res = await post(base_url, params);
  return res;
};

type GetRiskControlChecklistParams = {
  riskEventId?: number;
  riskObjectId: number;
  riskUnitId: number;
};
// 风险管控清单
export const getRiskControlChecklist = async (
  params: ParamsType<GetRiskControlChecklistParams>,
) => {
  const base_url = `/double_guard/risk_control_checklist/search`;
  return await post(base_url, params);
};
