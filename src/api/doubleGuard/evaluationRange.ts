import { post, get, del, put } from "@api";

export type EvaluationRangeParams = {
  filter?: EvaluationRangeFilter;
  pageNumber: number;
  pageSize: number;
  query?: string;
};

export type EvaluationRangeFilter = {
  evaluationType: number;
};

export const getEvaluationRangeList = async (params: EvaluationRangeParams) => {
  const base_url = "/double_guard/evaluation_range/search";
  const res = await post(base_url, params);
  return res;
};

export const getEvaluationRange = async (id) => {
  const base_url = `/double_guard/evaluation_range/${id}`;
  return await get(base_url);
};

// 新增
export const createEvaluationRange = async (params) => {
  const res = await post("/double_guard/evaluation_range", params);
  return res;
};

// 删除
export const delEvaluationRange = async (id: number) => {
  const res = await del(`/double_guard/evaluation_range/${id}`);
  return res;
};

// 修改
export const updateEvaluationRange = async (params) => {
  const res = await put(
    `/double_guard/evaluation_range/${params.id}`,
    params?.values,
  );
  return res;
};
