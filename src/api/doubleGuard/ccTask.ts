import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/double_guard/cc_task/search

export const getDoubleGuardCcTaskList = async (params) => {
  const base_url = "/double_guard/cc_task/search";
  const res = await post(base_url, params);
  return res;
};

export const getDoubleGuardCcTask = async (id) => {
  const base_url = `/double_guard/cc_task/${id}`;
  return await get(base_url);
};

export const createDoubleGuardCcTask = async (params) => {
  const res = await post("/double_guard/cc_task", params);
  return res;
};

export const delDoubleGuardCcTask = async (id: number) => {
  const res = await del(`/double_guard/cc_task/${id}`);
  return res;
};

export const delDoubleGuardCcTasks = async (ids) => {
  const res = await del(`/double_guard/cc_task`, ids);
  return res;
};

export const updateDoubleGuardCcTask = async (params) => {
  const res = await put(
    `/double_guard/cc_task/${params.id}`,
    params?.values,
  );
  return res;
};

export const doubleGuardCcTaskApis: CommonApis = {
  entity: "DoubleGuardCcTask",
  query: getDoubleGuardCcTaskList,
  create: createDoubleGuardCcTask,
  remove: delDoubleGuardCcTask,
  removes: delDoubleGuardCcTasks,
  update: updateDoubleGuardCcTask,
  get: getDoubleGuardCcTask,
};
