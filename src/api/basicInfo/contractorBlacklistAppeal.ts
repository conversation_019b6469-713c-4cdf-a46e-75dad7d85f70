import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/basic_info_management/contractor_blacklist_appeal/search

export const getBasicInfoContractorBlacklistAppealList = async (params) => {
  const base_url = "/basic_info_management/contractor_blacklist_appeal/search";
  const res = await post(base_url, params);
  return res;
};

export const getBasicInfoContractorBlacklistAppeal = async (id) => {
  const base_url = `/basic_info_management/contractor_blacklist_appeal/${id}`;
  return await get(base_url);
};

export const createBasicInfoContractorBlacklistAppeal = async (params) => {
  const res = await post(
    "/basic_info_management/contractor_blacklist_appeal",
    params
  );
  return res;
};

export const delBasicInfoContractorBlacklistAppeal = async (id: number) => {
  const res = await del(
    `/basic_info_management/contractor_blacklist_appeal/${id}`
  );
  return res;
};

export const delBasicInfoContractorBlacklistAppeals = async (ids) => {
  const res = await del(
    `/basic_info_management/contractor_blacklist_appeal`,
    ids
  );
  return res;
};

export const updateBasicInfoContractorBlacklistAppeal = async (params) => {
  const res = await put(
    `/basic_info_management/contractor_blacklist_appeal/${params.id}`,
    params?.values
  );
  return res;
};

export const auditBasicInfoContractorBlacklistAppeal = async (params) => {
  const res = await post(
    `/basic_info_management/contractor_blacklist_appeal/${params.id}/audit`,
    params?.values
  );
  return res;
};

export const auditDelegateBasicInfoContractorBlacklistAppeal = async (
  params
) => {
  const res = await post(
    `/basic_info_management/contractor_blacklist_appeal/${params.id}/audit/delegate`,
    params?.values
  );
  return res;
};

export const basicInfoContractorBlacklistAppealApis: CommonApis = {
  entity: "BasicInfoContractorBlacklistAppeal",
  query: getBasicInfoContractorBlacklistAppealList,
  create: createBasicInfoContractorBlacklistAppeal,
  remove: delBasicInfoContractorBlacklistAppeal,
  removes: delBasicInfoContractorBlacklistAppeals,
  update: updateBasicInfoContractorBlacklistAppeal,
  get: getBasicInfoContractorBlacklistAppeal,
};
