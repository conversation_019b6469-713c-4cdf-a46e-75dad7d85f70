import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/basic_info_management/document_information/search

export const getBasicInfoDocumentInformationList = async (params) => {
  const base_url = "/basic_info_management/document_information/search";
  const res = await post(base_url, params);
  return res;
};

export const getBasicInfoDocumentInformation = async (id) => {
  const base_url = `/basic_info_management/document_information/${id}`;
  return await get(base_url);
};

export const createBasicInfoDocumentInformation = async (params) => {
  const res = await post("/basic_info_management/document_information", params);
  return res;
};

export const delBasicInfoDocumentInformation = async (id: number) => {
  const res = await del(`/basic_info_management/document_information/${id}`);
  return res;
};

export const delBasicInfoDocumentInformations = async (ids) => {
  const res = await del(`/basic_info_management/document_information`, ids);
  return res;
};

export const updateBasicInfoDocumentInformation = async (params) => {
  const res = await put(
    `/basic_info_management/document_information/${params.id}`,
    params?.values,
  );
  return res;
};

export const basicInfoDocumentInformationApis: CommonApis = {
  entity: "BasicInfoDocumentInformation",
  query: getBasicInfoDocumentInformationList,
  create: createBasicInfoDocumentInformation,
  remove: delBasicInfoDocumentInformation,
  removes: delBasicInfoDocumentInformations,
  update: updateBasicInfoDocumentInformation,
  get: getBasicInfoDocumentInformation,
};
