import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/basic_info_management/contractor_certificate/search

export const getContractorCertificateList = async (params) => {
  const base_url = "/basic_info_management/contractor_certificate/search";
  const res = await post(base_url, params);
  return res;
};

export const getContractorCertificate = async (id) => {
  const base_url = `/basic_info_management/contractor_certificate/${id}`;
  return await get(base_url);
};

export const createContractorCertificate = async (params) => {
  const res = await post(
    "/basic_info_management/contractor_certificate",
    params,
  );
  return res;
};

export const delContractorCertificate = async (id: number) => {
  const res = await del(`/basic_info_management/contractor_certificate/${id}`);
  return res;
};

export const delContractorCertificates = async (ids) => {
  const res = await del(`/basic_info_management/contractor`, ids);
  return res;
};

export const updateContractorCertificate = async (params) => {
  const res = await put(
    `/basic_info_management/contractor_certificate/${params.id}`,
    params?.values,
  );
  return res;
};

export const contractorCertificateApis: CommonApis = {
  entity: "ContractorCertificate",
  query: getContractorCertificateList,
  create: createContractorCertificate,
  remove: delContractorCertificate,
  removes: delContractorCertificates,
  update: updateContractorCertificate,
  get: getContractorCertificate,
};
