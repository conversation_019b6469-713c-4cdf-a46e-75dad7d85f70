import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/basic_info_management/warehouse/search

export const getWarehouseList = async (params) => {
  const base_url = "/basic_info_management/warehouse/search";
  const res = await post(base_url, params);
  return res;
};

export const getWarehouse = async (id) => {
  const base_url = `/basic_info_management/warehouse/${id}`;
  return await get(base_url);
};

export const createWarehouse = async (params) => {
  const res = await post("/basic_info_management/warehouse", params);
  return res;
};

export const delWarehouse = async (id: number) => {
  const res = await del(`/basic_info_management/warehouse/${id}`);
  return res;
};

export const delWarehouses = async (ids) => {
  const res = await del(`/basic_info_management/warehouse`, ids);
  return res;
};

export const updateWarehouse = async (params) => {
  const res = await put(
    `/basic_info_management/warehouse/${params.id}`,
    params?.values,
  );
  return res;
};

export const warehouseApis: CommonApis = {
  entity: "Warehouse",
  query: getWarehouseList,
  create: createWarehouse,
  remove: delWarehouse,
  removes: delWarehouses,
  update: updateWarehouse,
  get: getWarehouse,
};
