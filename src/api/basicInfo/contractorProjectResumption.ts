import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/basic_info_management/contractor_project_resumption/search

export const getBasicInfoContractorProjectResumptionList = async (params) => {
  const base_url =
    "/basic_info_management/contractor_project_resumption/search";
  const res = await post(base_url, params);
  return res;
};

export const getBasicInfoContractorProjectResumption = async (id) => {
  const base_url = `/basic_info_management/contractor_project_resumption/${id}`;
  return await get(base_url);
};

export const createBasicInfoContractorProjectResumption = async (params) => {
  const res = await post(
    "/basic_info_management/contractor_project_resumption",
    params
  );
  return res;
};

export const delBasicInfoContractorProjectResumption = async (id: number) => {
  const res = await del(
    `/basic_info_management/contractor_project_resumption/${id}`
  );
  return res;
};

export const delBasicInfoContractorProjectResumptions = async (ids) => {
  const res = await del(
    `/basic_info_management/contractor_project_resumption`,
    ids
  );
  return res;
};

export const updateBasicInfoContractorProjectResumption = async (params) => {
  const res = await put(
    `/basic_info_management/contractor_project_resumption/${params.id}`,
    params?.values
  );
  return res;
};

export const auditBasicInfoContractorProjectResumption = async (params) => {
  const res = await post(
    `/basic_info_management/contractor_project_resumption/${params.id}/audit`,
    params?.values
  );
  return res;
};

export const auditDelegateBasicInfoContractorProjectResumption = async (
  params
) => {
  const res = await post(
    `/basic_info_management/contractor_project_resumption/${params.id}/audit/delegate`,
    params?.values
  );
  return res;
};

export const basicInfoContractorProjectResumptionApis: CommonApis = {
  entity: "BasicInfoContractorProjectResumption",
  query: getBasicInfoContractorProjectResumptionList,
  create: createBasicInfoContractorProjectResumption,
  remove: delBasicInfoContractorProjectResumption,
  removes: delBasicInfoContractorProjectResumptions,
  update: updateBasicInfoContractorProjectResumption,
  get: getBasicInfoContractorProjectResumption,
};
