import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/basic_info_management/contractor_employee_blacklist_appeal/search

export const getBasicInfoContractorEmployeeBlacklistAppealList = async (
  params
) => {
  const base_url =
    "/basic_info_management/contractor_employee_blacklist_appeal/search";
  const res = await post(base_url, params);
  return res;
};

export const getBasicInfoContractorEmployeeBlacklistAppeal = async (id) => {
  const base_url = `/basic_info_management/contractor_employee_blacklist_appeal/${id}`;
  return await get(base_url);
};

export const createBasicInfoContractorEmployeeBlacklistAppeal = async (
  params
) => {
  const res = await post(
    "/basic_info_management/contractor_employee_blacklist_appeal",
    params
  );
  return res;
};

export const delBasicInfoContractorEmployeeBlacklistAppeal = async (
  id: number
) => {
  const res = await del(
    `/basic_info_management/contractor_employee_blacklist_appeal/${id}`
  );
  return res;
};

export const delBasicInfoContractorEmployeeBlacklistAppeals = async (ids) => {
  const res = await del(
    `/basic_info_management/contractor_employee_blacklist_appeal`,
    ids
  );
  return res;
};

export const updateBasicInfoContractorEmployeeBlacklistAppeal = async (
  params
) => {
  const res = await put(
    `/basic_info_management/contractor_employee_blacklist_appeal/${params.id}`,
    params?.values
  );
  return res;
};

export const auditBasicInfoContractorEmployeeBlacklistAppeal = async (
  params
) => {
  const res = await post(
    `/basic_info_management/contractor_employee_blacklist_appeal/${params.id}/audit`,
    params?.values
  );
  return res;
};

export const auditDelegateBasicInfoContractorEmployeeBlacklistAppeal = async (
  params
) => {
  const res = await post(
    `/basic_info_management/contractor_employee_blacklist_appeal/${params.id}/audit/delegate`,
    params?.values
  );
  return res;
};

export const basicInfoContractorEmployeeBlacklistAppealApis: CommonApis = {
  entity: "BasicInfoContractorEmployeeBlacklistAppeal",
  query: getBasicInfoContractorEmployeeBlacklistAppealList,
  create: createBasicInfoContractorEmployeeBlacklistAppeal,
  remove: delBasicInfoContractorEmployeeBlacklistAppeal,
  removes: delBasicInfoContractorEmployeeBlacklistAppeals,
  update: updateBasicInfoContractorEmployeeBlacklistAppeal,
  get: getBasicInfoContractorEmployeeBlacklistAppeal,
};
