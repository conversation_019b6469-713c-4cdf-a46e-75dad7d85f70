import { get, post } from "api";

// /v1/basic_info_management/enterprise_information/search

/* export const getEnterpriseInfoList = async (params) => {
  const base_url = '/basic_info_management/enterprise_information/search'
  const res = await post(base_url, params)
  return res
} */

export const getEnterpriseInfo = async () => {
  const base_url = `/basic_info_management/enterprise_information/current`;
  return await get(base_url);
};

export const createEnterpriseInfo = async (params) => {
  const res = await post(
    "/basic_info_management/enterprise_information",
    params,
  );
  return res;
};

/* export const delEnterpriseInfo = async (id: number) => {
  const res = await del(`/basic_info_management/enterprise_information/${id}`)
  return res
} */

/* export const delEnterpriseInfos = async (ids) => {
  const res = await del(`/basic_info_management/enterprise_information`, ids)
  return res
} */

/* export const updateEnterpriseInfo = async (params) => {
  const res = await put(`/basic_info_management/enterprise_information/${params.id}`, params?.values)
  return res
} */

export const enterpriseInfoApis = {
  entity: "EnterpriseInfo",
  // query: getEnterpriseInfoList,
  create: createEnterpriseInfo,
  // remove: delEnterpriseInfo,
  // removes: delEnterpriseInfos,
  // update: updateEnterpriseInfo,
  get: getEnterpriseInfo,
};
