import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/basic_info_management/warehouse_area/search

export const getWarehouseAreaList = async (params) => {
  const base_url = "/basic_info_management/warehouse_area/search";
  const res = await post(base_url, params);
  return res;
};

export const getWarehouseArea = async (id) => {
  const base_url = `/basic_info_management/warehouse_area/${id}`;
  return await get(base_url);
};

export const createWarehouseArea = async (params) => {
  const res = await post("/basic_info_management/warehouse_area", params);
  return res;
};

export const delWarehouseArea = async (id: number) => {
  const res = await del(`/basic_info_management/warehouse_area/${id}`);
  return res;
};

export const delWarehouseAreas = async (ids) => {
  const res = await del(`/basic_info_management/warehouse_area`, ids);
  return res;
};

export const updateWarehouseArea = async (params) => {
  const res = await put(
    `/basic_info_management/warehouse_area/${params.id}`,
    params?.values,
  );
  return res;
};

export const warehouseAreaApis: CommonApis = {
  entity: "WarehouseArea",
  query: getWarehouseAreaList,
  create: createWarehouseArea,
  remove: delWarehouseArea,
  removes: delWarehouseAreas,
  update: updateWarehouseArea,
  get: getWarehouseArea,
};
