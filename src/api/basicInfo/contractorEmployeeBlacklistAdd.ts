import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/basic_info_management/contractor_employee_blacklist_add/search

export const getBasicInfoContractorEmployeeBlacklistAddList = async (params) => {
  const base_url = "/basic_info_management/contractor_employee_blacklist_add/search";
  const res = await post(base_url, params);
  return res;
};

export const getBasicInfoContractorEmployeeBlacklistAdd = async (id) => {
  const base_url = `/basic_info_management/contractor_employee_blacklist_add/${id}`;
  return await get(base_url);
};

export const createBasicInfoContractorEmployeeBlacklistAdd = async (params) => {
  const res = await post("/basic_info_management/contractor_employee_blacklist_add", params);
  return res;
};

export const delBasicInfoContractorEmployeeBlacklistAdd = async (id: number) => {
  const res = await del(`/basic_info_management/contractor_employee_blacklist_add/${id}`);
  return res;
};

export const delBasicInfoContractorEmployeeBlacklistAdds = async (ids) => {
  const res = await del(`/basic_info_management/contractor_employee_blacklist_add`, ids);
  return res;
};

export const updateBasicInfoContractorEmployeeBlacklistAdd = async (params) => {
  const res = await put(
    `/basic_info_management/contractor_employee_blacklist_add/${params.id}`,
    params?.values,
  );
  return res;
};

export const basicInfoContractorEmployeeBlacklistAddApis: CommonApis = {
  entity: "BasicInfoContractorEmployeeBlacklistAdd",
  query: getBasicInfoContractorEmployeeBlacklistAddList,
  create: createBasicInfoContractorEmployeeBlacklistAdd,
  remove: delBasicInfoContractorEmployeeBlacklistAdd,
  removes: delBasicInfoContractorEmployeeBlacklistAdds,
  update: updateBasicInfoContractorEmployeeBlacklistAdd,
  get: getBasicInfoContractorEmployeeBlacklistAdd,
};
