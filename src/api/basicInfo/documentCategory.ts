import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/basic_info_management/document_category/search

export const getBasicInfoDocumentCategoryList = async (params) => {
  const base_url = "/basic_info_management/document_category/search";
  const res = await post(base_url, params);
  return res;
};

export const getBasicInfoDocumentCategory = async (id) => {
  const base_url = `/basic_info_management/document_category/${id}`;
  return await get(base_url);
};

export const createBasicInfoDocumentCategory = async (params) => {
  const res = await post("/basic_info_management/document_category", params);
  return res;
};

export const delBasicInfoDocumentCategory = async (id: number) => {
  const res = await del(`/basic_info_management/document_category/${id}`);
  return res;
};

export const delBasicInfoDocumentCategorys = async (ids) => {
  const res = await del(`/basic_info_management/document_category`, ids);
  return res;
};

export const updateBasicInfoDocumentCategory = async (params) => {
  const res = await put(
    `/basic_info_management/document_category/${params.id}`,
    params?.values,
  );
  return res;
};

export const basicInfoDocumentCategoryApis: CommonApis = {
  entity: "BasicInfoDocumentCategory",
  query: getBasicInfoDocumentCategoryList,
  create: createBasicInfoDocumentCategory,
  remove: delBasicInfoDocumentCategory,
  removes: delBasicInfoDocumentCategorys,
  update: updateBasicInfoDocumentCategory,
  get: getBasicInfoDocumentCategory,
};
