import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/basic_info_management/announcement/search

export const getAnnouncementList = async (params) => {
  const base_url = "/basic_info_management/announcement/search";
  const res = await post(base_url, params);
  return res;
};

export const getAnnouncement = async (id) => {
  const base_url = `/basic_info_management/announcement/${id}`;
  return await get(base_url);
};

export const createAnnouncement = async (params) => {
  const res = await post("/basic_info_management/announcement", params);
  return res;
};

export const delAnnouncement = async (id: number) => {
  const res = await del(`/basic_info_management/announcement/${id}`);
  return res;
};

export const delAnnouncements = async (ids) => {
  const res = await del(`/basic_info_management/announcement`, ids);
  return res;
};

export const updateAnnouncement = async (params) => {
  const res = await put(
    `/basic_info_management/announcement/${params.id}`,
    params?.values,
  );
  return res;
};

export const announcementApis: CommonApis = {
  entity: "Announcement",
  query: getAnnouncementList,
  create: createAnnouncement,
  remove: delAnnouncement,
  removes: delAnnouncements,
  update: updateAnnouncement,
  get: getAnnouncement,
};
