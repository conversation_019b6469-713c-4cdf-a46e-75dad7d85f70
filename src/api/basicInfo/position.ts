import { post, get, del, put } from "@api";

export type GetPositionParams =
  | {
      query_type?: "list";
    }
  | {
      id?: number;
    }
  | {
      /**
       * 请求的页码页
       */
      pageNumber: number;
      /**
       * 每页的结果数
       */
      pageSize: number;
      /**
       * 职位名称中包含的搜索词
       */
      query?: string;
    };

export const getPositionList = async (params: GetPositionParams) => {
  const base_url = "/basic_info_management/position/search";
  const res = await post(base_url, params);
  return res;
};

export const getPosition = async (id) => {
  const base_url = `/basic_info_management/position/${id}`;
  return await get(base_url);
};

// 新增岗位
export const createPosition = async (params) => {
  const res = await post("/basic_info_management/position", params);
  return res;
};

// 批量新增岗位
export const createPositions = async (params: Array<string>) => {
  const res = await post("/basic_info_management/position/batch", params);
  return res;
};

// 删除单个项目
export const delPosition = async (id: number) => {
  const res = await del(`/basic_info_management/position/${id}`);
  return res;
};

// 批量删除
export const delPositions = async (ids) => {
  const res = await del(`/basic_info_management/position`, ids);
  return res;
};

// 修改岗位
export const updatePosition = async (params) => {
  const res = await put(
    `/basic_info_management/position/${params.id}`,
    params?.values,
  );
  return res;
};
