import { post, get, del, put } from "@api";

export const getDepartmentList = async () => {
  const base_url = "/basic_info_management/department";
  const res = await get(base_url);
  return res;
};

export const getDepartment = async (id) => {
  const base_url = `/basic_info_management/department/${id}`;
  return await get(base_url);
};

// 新增部门
export const createDepartment = async (params) => {
  const res = await post("/basic_info_management/department", params);
  return res;
};

// 删除单个项目
export const delDepartment = async (id: number) => {
  const res = await del(`/basic_info_management/department/${id}`);
  return res;
};

// 批量删除
export const delDepartments = async (ids) => {
  const res = await del(`/basic_info_management/department`, ids);
  return res;
};

// 修改部门
export const updateDepartment = async (params) => {
  const res = await put(
    `/basic_info_management/department/${params.id}`,
    params?.values,
  );
  return res;
};
