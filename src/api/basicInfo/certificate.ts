import { post, get, del, put } from "@api";

// /v1/basic_info_management/employee_certificate/search

export const getCertificateList = async (params) => {
  const base_url = "/basic_info_management/employee_certificate/search";
  const res = await post(base_url, params);
  return res;
};

export const getCertificate = async (id) => {
  const base_url = `/basic_info_management/employee_certificate/${id}`;
  return await get(base_url);
};

export const createCertificate = async (params) => {
  const res = await post("/basic_info_management/employee_certificate", params);
  return res;
};

export const delCertificate = async (id: number) => {
  const res = await del(`/basic_info_management/employee_certificate/${id}`);
  return res;
};

export const delCertificates = async (ids) => {
  const res = await del(`/basic_info_management/employee_certificate`, ids);
  return res;
};

export const updateCertificate = async (params) => {
  const res = await put(
    `/basic_info_management/employee_certificate/${params.id}`,
    params?.values,
  );
  return res;
};
