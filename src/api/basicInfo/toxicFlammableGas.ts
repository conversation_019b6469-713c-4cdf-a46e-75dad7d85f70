import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/basic_info_management/toxic_flammable_gas/search

export const getToxicFlammableGasList = async (params) => {
  const base_url = "/basic_info_management/toxic_flammable_gas/search";
  const res = await post(base_url, params);
  return res;
};

export const getToxicFlammableGas = async (id) => {
  const base_url = `/basic_info_management/toxic_flammable_gas/${id}`;
  return await get(base_url);
};

export const createToxicFlammableGas = async (params) => {
  const res = await post("/basic_info_management/toxic_flammable_gas", params);
  return res;
};

export const delToxicFlammableGas = async (id: number) => {
  const res = await del(`/basic_info_management/toxic_flammable_gas/${id}`);
  return res;
};

export const delToxicFlammableGass = async (ids) => {
  const res = await del(`/basic_info_management/toxic_flammable_gas`, ids);
  return res;
};

export const updateToxicFlammableGas = async (params) => {
  const res = await put(
    `/basic_info_management/toxic_flammable_gas/${params.id}`,
    params?.values,
  );
  return res;
};

export const toxicFlammableGasApis: CommonApis = {
  entity: "ToxicFlammableGas",
  query: getToxicFlammableGasList,
  create: createToxicFlammableGas,
  remove: delToxicFlammableGas,
  removes: delToxicFlammableGass,
  update: updateToxicFlammableGas,
  get: getToxicFlammableGas,
};
