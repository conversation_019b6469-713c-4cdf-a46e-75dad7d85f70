import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/basic_info_management/storage_tank/search

export const getStorageTankList = async (params) => {
  const base_url = "/basic_info_management/storage_tank/search";
  const res = await post(base_url, params);
  return res;
};

export const getStorageTank = async (id) => {
  const base_url = `/basic_info_management/storage_tank/${id}`;
  return await get(base_url);
};

export const createStorageTank = async (params) => {
  const res = await post("/basic_info_management/storage_tank", params);
  return res;
};

export const delStorageTank = async (id: number) => {
  const res = await del(`/basic_info_management/storage_tank/${id}`);
  return res;
};

export const delStorageTanks = async (ids) => {
  const res = await del(`/basic_info_management/storage_tank`, ids);
  return res;
};

export const updateStorageTank = async (params) => {
  const res = await put(
    `/basic_info_management/storage_tank/${params.id}`,
    params?.values,
  );
  return res;
};

export const storageTankApis: CommonApis = {
  entity: "StorageTank",
  query: getStorageTankList,
  create: createStorageTank,
  remove: delStorageTank,
  removes: delStorageTanks,
  update: updateStorageTank,
  get: getStorageTank,
};
