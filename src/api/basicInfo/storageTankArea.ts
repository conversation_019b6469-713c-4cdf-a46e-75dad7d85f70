import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/basic_info_management/storage_tank_area/search

export const getStorageTankAreaList = async (params) => {
  const base_url = "/basic_info_management/storage_tank_area/search";
  const res = await post(base_url, params);
  return res;
};

export const getStorageTankArea = async (id) => {
  const base_url = `/basic_info_management/storage_tank_area/${id}`;
  return await get(base_url);
};

export const createStorageTankArea = async (params) => {
  const res = await post("/basic_info_management/storage_tank_area", params);
  return res;
};

export const delStorageTankArea = async (id: number) => {
  const res = await del(`/basic_info_management/storage_tank_area/${id}`);
  return res;
};

export const delStorageTankAreas = async (ids) => {
  const res = await del(`/basic_info_management/storage_tank_area`, ids);
  return res;
};

export const updateStorageTankArea = async (params) => {
  const res = await put(
    `/basic_info_management/storage_tank_area/${params.id}`,
    params?.values,
  );
  return res;
};

export const storageTankAreaApis: CommonApis = {
  entity: "StorageTankArea",
  query: getStorageTankAreaList,
  create: createStorageTankArea,
  remove: delStorageTankArea,
  removes: delStorageTankAreas,
  update: updateStorageTankArea,
  get: getStorageTankArea,
};
