import { del, get, post, put } from "@api";

export type EmployeeFilter = {
  /**
   * 责任部门id
   */
  departmentId?: number;
  /**
   * 在职状态
   */
  status?: number;
};

export type EmployeeParams = {
  filter?: EmployeeFilter;
  /**
   * 请求页码数，实际请求页码数，第一页对应1，以此类推
   */
  pageNumber: number;
  /**
   * 每页结果数，一次连贯查询该值必须统一
   */
  pageSize: number;
  /**
   * 查询串
   */
  query?: string;
  departmentId?: number | null;
};

export const getEmployeeList = async (params: EmployeeParams) => {
  const base_url = "/basic_info_management/employee/search";
  const res = await post(base_url, params);
  return res;
};

export const getEmployee = async (id) => {
  const base_url = `/basic_info_management/employee/${id}`;
  return await get(base_url);
};

// 新增
export const createEmployee = async (params) => {
  const res = await post("/basic_info_management/employee", params);
  return res;
};

// 删除单个项目
export const delEmployee = async (id: number) => {
  const res = await del(`/basic_info_management/employee/${id}`);
  return res;
};

// 批量删除
export const delEmployees = async (ids) => {
  const res = await del(`/basic_info_management/employee`, ids);
  return res;
};

// 修改
export const updateEmployee = async (params) => {
  const res = await put(
    `/basic_info_management/employee/${params.id}`,
    params?.values,
  );
  return res;
};
