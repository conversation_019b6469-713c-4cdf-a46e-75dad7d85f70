import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/basic_info_management/contractor/search

export const getContractorList = async (params) => {
  const base_url = "/basic_info_management/contractor/search";
  const res = await post(base_url, params);
  return res;
};

export const getContractor = async (id) => {
  const base_url = `/basic_info_management/contractor/${id}`;
  return await get(base_url);
};

export const createContractor = async (params) => {
  const res = await post("/basic_info_management/contractor", params);
  return res;
};

export const delContractor = async (id: number) => {
  const res = await del(`/basic_info_management/contractor/${id}`);
  return res;
};

export const delContractors = async (ids) => {
  const res = await del(`/basic_info_management/contractor`, ids);
  return res;
};

export const updateContractor = async (params) => {
  const res = await put(
    `/basic_info_management/contractor/${params.id}`,
    params?.values
  );
  return res;
};

export const addBlackListContractor = async (params) => {
  const base_url = `/basic_info_management/contractor/${params?.id}/black/add`;
  return await post(base_url, params?.values);
};

export const removeBlackListContractor = async (id) => {
  const base_url = `/basic_info_management/contractor/${id}/black/remove`;
  return await post(base_url);
};

export const getContractorShareInfo = async (id) => {
  const base_url = `/basic_info_management/contractor/${id}/share_info`;
  return await get(base_url);
};

export const updateContractorShareInfo = async (params) => {
  const base_url = `/basic_info_management/contractor/${params.id}/share_info`;
  return await put(base_url, params?.values);
};

export type ContractorCommonApis = CommonApis & {
  addBlackList: (id: any) => Promise<any>;
  removeBlackList: (id: any) => Promise<any>;
};

export const contractorApis: ContractorCommonApis = {
  entity: "contractor",
  query: getContractorList,
  create: createContractor,
  remove: delContractor,
  removes: delContractors,
  update: updateContractor,
  get: getContractor,
  addBlackList: addBlackListContractor,
  removeBlackList: removeBlackListContractor,
};
