import { post, get, del, put } from "@api";

export type CalendarParams = {
  month: number;
  year: number;
};

export const getCalendar = async (params: CalendarParams) => {
  const res = await post("/basic_info_management/calendar/search", params);
  return res;
};

export type CreateParams = {
  month: number;
  restDays: number[];
  workdays: number[];
  year: number;
};

export const createCalendar = async (params: CreateParams) => {
  const res = await put("/basic_info_management/calendar", params);
  return res;
};
