import { del, get, post, put } from "api";

export const getContractorEmployeeCertificateList = async (params) => {
  const base_url =
    "/basic_info_management/contractor_employee_certificate/search";
  const res = await post(base_url, params);
  return res;
};

export const getContractorEmployeeCertificate = async (id) => {
  const base_url = `/basic_info_management/contractor_employee_certificate/${id}`;
  return await get(base_url);
};

export const createContractorEmployeeCertificate = async (params) => {
  const res = await post(
    "/basic_info_management/contractor_employee_certificate",
    params,
  );
  return res;
};

export const delContractorEmployeeCertificate = async (id: number) => {
  const res = await del(
    `/basic_info_management/contractor_employee_certificate/${id}`,
  );
  return res;
};

export const delContractorEmployeeCertificates = async (ids) => {
  const res = await del(
    `/basic_info_management/contractor_employee_certificate`,
    ids,
  );
  return res;
};

export const updateContractorEmployeeCertificate = async (params) => {
  const res = await put(
    `/basic_info_management/contractor_employee_certificate/${params.id}`,
    params?.values,
  );
  return res;
};
