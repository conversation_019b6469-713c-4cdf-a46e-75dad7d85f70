import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/basic_info_management/certificate/search

export const getEnterpriseCertificateList = async (params) => {
  const base_url = "/basic_info_management/certificate/search";
  const res = await post(base_url, params);
  return res;
};

export const getEnterpriseCertificate = async (id) => {
  const base_url = `/basic_info_management/certificate/${id}`;
  return await get(base_url);
};

export const createEnterpriseCertificate = async (params) => {
  const res = await post("/basic_info_management/certificate", params);
  return res;
};

export const delEnterpriseCertificate = async (id: number) => {
  const res = await del(`/basic_info_management/certificate/${id}`);
  return res;
};

export const delEnterpriseCertificates = async (ids) => {
  const res = await del(`/basic_info_management/certificate`, ids);
  return res;
};

export const updateEnterpriseCertificate = async (params) => {
  const res = await put(
    `/basic_info_management/certificate/${params.id}`,
    params?.values,
  );
  return res;
};

export const enterpriseCertificateApis: CommonApis = {
  entity: "EnterpriseCertificate",
  query: getEnterpriseCertificateList,
  create: createEnterpriseCertificate,
  remove: delEnterpriseCertificate,
  removes: delEnterpriseCertificates,
  update: updateEnterpriseCertificate,
  get: getEnterpriseCertificate,
};
