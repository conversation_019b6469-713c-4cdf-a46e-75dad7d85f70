import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/basic_info_management/major_hazard/search

export const getMajorHazardList = async (params) => {
  const base_url = "/basic_info_management/major_hazard/search";
  const res = await post(base_url, params);
  return res;
};

export const getMajorHazard = async (id) => {
  const base_url = `/basic_info_management/major_hazard/${id}`;
  return await get(base_url);
};

export const createMajorHazard = async (params) => {
  const res = await post("/basic_info_management/major_hazard", params);
  return res;
};

export const delMajorHazard = async (id: number) => {
  const res = await del(`/basic_info_management/major_hazard/${id}`);
  return res;
};

export const delMajorHazards = async (ids) => {
  const res = await del(`/basic_info_management/major_hazard`, ids);
  return res;
};

export const updateMajorHazard = async (params) => {
  const res = await put(
    `/basic_info_management/major_hazard/${params.id}`,
    params?.values,
  );
  return res;
};

export const evaluateMajorHazard = async (params) => {
  const res = await post(
    `/basic_info_management/major_hazard/${params.id}/evaluate`,
    params?.values,
  );
  return res;
};

export const getMajorHazardEvaluate = async (id) => {
  const res = await get(`/basic_info_management/major_hazard/${id}/evaluation`);
  return res;
};

export type MajorHazardApis = CommonApis & {
  evaluate: (params: any) => Promise<any>;
  getEvaluate: (id: any) => Promise<any>;
};

export const majorHazardApis: MajorHazardApis = {
  entity: "MajorHazard",
  query: getMajorHazardList,
  create: createMajorHazard,
  remove: delMajorHazard,
  removes: delMajorHazards,
  update: updateMajorHazard,
  get: getMajorHazard,
  evaluate: evaluateMajorHazard,
  getEvaluate: getMajorHazardEvaluate,
};
