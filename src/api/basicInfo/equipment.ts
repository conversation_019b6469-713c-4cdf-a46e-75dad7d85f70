import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/basic_info_management/equipment/search

export const getEquipmentList = async (params) => {
  const base_url = "/basic_info_management/equipment/search";
  const res = await post(base_url, params);
  return res;
};

export const getEquipment = async (id) => {
  const base_url = `/basic_info_management/equipment/${id}`;
  return await get(base_url);
};

export const createEquipment = async (params) => {
  const res = await post("/basic_info_management/equipment", params);
  return res;
};

export const delEquipment = async (id: number) => {
  const res = await del(`/basic_info_management/equipment/${id}`);
  return res;
};

export const delEquipments = async (ids) => {
  const res = await del(`/basic_info_management/equipment`, ids);
  return res;
};

export const updateEquipment = async (params) => {
  const res = await put(
    `/basic_info_management/equipment/${params.id}`,
    params?.values,
  );
  return res;
};

export const equipmentApis: CommonApis = {
  entity: "Equipment",
  query: getEquipmentList,
  create: createEquipment,
  remove: delEquipment,
  removes: delEquipments,
  update: updateEquipment,
  get: getEquipment,
};
