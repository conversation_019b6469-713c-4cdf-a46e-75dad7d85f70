import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/basic_info_management/risk_management/search

export const getRiskManagementList = async (params) => {
  const base_url = "/basic_info_management/risk_management/search";
  const res = await post(base_url, params);
  return res;
};

export const getRiskManagement = async (id) => {
  const base_url = `/basic_info_management/risk_management/${id}`;
  return await get(base_url);
};

export const createRiskManagement = async (params) => {
  const res = await post("/basic_info_management/risk_management", params);
  return res;
};

export const delRiskManagement = async (id: number) => {
  const res = await del(`/basic_info_management/risk_management/${id}`);
  return res;
};

export const delRiskManagements = async (ids) => {
  const res = await del(`/basic_info_management/risk_management`, ids);
  return res;
};

export const updateRiskManagement = async (params) => {
  const res = await put(
    `/basic_info_management/risk_management/${params.id}`,
    params?.values,
  );
  return res;
};

export const approveRiskManagement = async (params) => {
  const res = await post(
    `/basic_info_management/risk_management/${params.id}/approve`,
    params?.values,
  );
  return res;
};

export const riskManagementApis: CommonApis = {
  entity: "RiskManagement",
  query: getRiskManagementList,
  create: createRiskManagement,
  remove: delRiskManagement,
  removes: delRiskManagements,
  update: updateRiskManagement,
  get: getRiskManagement,
};
