import { del, get, post, put } from "api";

export type GroupFilter = {};

export type GroupParams = {
  filter?: GroupFilter;
  pageNumber: number;
  pageSize: number;
  query?: string;
};

export const getGroupList = async (params: GroupParams) => {
  const base_url = "/basic_info_management/group/search";
  const res = await post(base_url, params);
  return res;
};

export const getGroupMemberList = async (params: GroupParams) => {
  const base_url = "/basic_info_management/group/member/search";
  const res = await post(base_url, params);
  return res;
};

export const getGroup = async (id) => {
  const base_url = `/basic_info_management/group/${id}`;
  return await get(base_url);
};

// 新增
export const createGroup = async (params) => {
  const res = await post("/basic_info_management/group", params);
  return res;
};

// 删除单个项目
export const delGroup = async (id: number) => {
  const res = await del(`/basic_info_management/group/${id}`);
  return res;
};

// 批量删除
export const delGroups = async (ids) => {
  const res = await del(`/basic_info_management/group`, ids);
  return res;
};

// 修改
export const updateGroup = async (params) => {
  const res = await put(
    `/basic_info_management/group/${params.id}`,
    params?.values,
  );
  return res;
};
