import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/basic_info_management/chemical/search

export const getChemicalList = async (params) => {
  const base_url = "/basic_info_management/chemical/search";
  const res = await post(base_url, params);
  return res;
};

export const getChemical = async (id) => {
  const base_url = `/basic_info_management/chemical/${id}`;
  return await get(base_url);
};

export const createChemical = async (params) => {
  const res = await post("/basic_info_management/chemical", params);
  return res;
};

export const delChemical = async (id: number) => {
  const res = await del(`/basic_info_management/chemical/${id}`);
  return res;
};

export const delChemicals = async (ids) => {
  const res = await del(`/basic_info_management/chemical`, ids);
  return res;
};

export const updateChemical = async (params) => {
  const res = await put(
    `/basic_info_management/chemical/${params.id}`,
    params?.values,
  );
  return res;
};

export const chemicalApis: CommonApis = {
  entity: "Chemical",
  query: getChemicalList,
  create: createChemical,
  remove: delChemical,
  removes: delChemicals,
  update: updateChemical,
  get: getChemical,
};
