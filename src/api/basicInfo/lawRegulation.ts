import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/basic_info_management/law_regulation/search

export const getLawRegulationList = async (params) => {
  const base_url = "/basic_info_management/law_regulation/search";
  const res = await post(base_url, params);
  return res;
};

export const getLawRegulation = async (id) => {
  const base_url = `/basic_info_management/law_regulation/${id}`;
  return await get(base_url);
};

export const createLawRegulation = async (params) => {
  const res = await post("/basic_info_management/law_regulation", params);
  return res;
};

export const delLawRegulation = async (id: number) => {
  const res = await del(`/basic_info_management/law_regulation/${id}`);
  return res;
};

export const delLawRegulations = async (ids) => {
  const res = await del(`/basic_info_management/law_regulation`, ids);
  return res;
};

export const updateLawRegulation = async (params) => {
  const res = await put(
    `/basic_info_management/law_regulation/${params.id}`,
    params?.values,
  );
  return res;
};

export const lawRegulationApis: CommonApis = {
  entity: "LawRegulation",
  query: getLawRegulationList,
  create: createLawRegulation,
  remove: delLawRegulation,
  removes: delLawRegulations,
  update: updateLawRegulation,
  get: getLawRegulation,
};
