import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/basic_info_management/contractor_project_stop/search

export const getBasicInfoContractorProjectStopList = async (params) => {
  const base_url = "/basic_info_management/contractor_project_stop/search";
  const res = await post(base_url, params);
  return res;
};

export const getBasicInfoContractorProjectStop = async (id) => {
  const base_url = `/basic_info_management/contractor_project_stop/${id}`;
  return await get(base_url);
};

export const createBasicInfoContractorProjectStop = async (params) => {
  const res = await post("/basic_info_management/contractor_project_stop", params);
  return res;
};

export const delBasicInfoContractorProjectStop = async (id: number) => {
  const res = await del(`/basic_info_management/contractor_project_stop/${id}`);
  return res;
};

export const delBasicInfoContractorProjectStops = async (ids) => {
  const res = await del(`/basic_info_management/contractor_project_stop`, ids);
  return res;
};

export const updateBasicInfoContractorProjectStop = async (params) => {
  const res = await put(
    `/basic_info_management/contractor_project_stop/${params.id}`,
    params?.values,
  );
  return res;
};

export const basicInfoContractorProjectStopApis: CommonApis = {
  entity: "BasicInfoContractorProjectStop",
  query: getBasicInfoContractorProjectStopList,
  create: createBasicInfoContractorProjectStop,
  remove: delBasicInfoContractorProjectStop,
  removes: delBasicInfoContractorProjectStops,
  update: updateBasicInfoContractorProjectStop,
  get: getBasicInfoContractorProjectStop,
};
