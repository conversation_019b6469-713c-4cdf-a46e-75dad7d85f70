import { del, get, post, put } from "@api";

// /v1/basic_info_management/dic_item_value/search

export const getDicItemValueList = async (params) => {
  const base_url = "/basic_info_management/dic_item_value/search";
  const res = await post(base_url, params);
  return res;
};

export const getDicItemValue = async (id) => {
  const base_url = `/basic_info_management/dic_item_value/${id}`;
  return await get(base_url);
};

export const createDicItemValue = async (params) => {
  const res = await post("/basic_info_management/dic_item_value", params);
  return res;
};

export const delDicItemValue = async (id: number) => {
  const res = await del(`/basic_info_management/dic_item_value/${id}`);
  return res;
};

export const delDicItemValues = async (ids) => {
  const res = await del(`/basic_info_management/dic_item_value`, ids);
  return res;
};

export const updateDicItemValue = async (params) => {
  const res = await put(
    `/basic_info_management/dic_item_value/${params.id}`,
    params?.values,
  );
  return res;
};

export const getDicItemList = async (params) => {
  const base_url = "/basic_info_management/dic_item/search";
  const res = await post(base_url, params);
  return res;
};

export const getDicItem = async (id) => {
  const base_url = `/basic_info_management/dic_item/${id}`;
  return await get(base_url);
};

export const createDicItem = async (params) => {
  const res = await post("/basic_info_management/dic_item", params);
  return res;
};

export const delDicItem = async (id: number) => {
  const res = await del(`/basic_info_management/dic_item/${id}`);
  return res;
};

export const delDicItems = async (ids) => {
  const res = await del(`/basic_info_management/dic_item`, ids);
  return res;
};

export const updateDicItem = async (params) => {
  const res = await put(
    `/basic_info_management/dic_item/${params.id}`,
    params?.values,
  );
  return res;
};

export const getDicModuleType = async (moduleType: number) => {
  const base_url = `/basic_info_management/dic_item/module/${moduleType}`;
  const res = await get(base_url);
  return res;
};

export const getDicName = async (name: string) => {
  const base_url = `/basic_info_management/dic_item/name/${name}`;
  const res = await get(base_url);
  return res;
};
