import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/basic_info_management/contractor_evaluation/search

export const getBasicInfoContractorEvaluationList = async (params) => {
  const base_url = "/basic_info_management/contractor_evaluation/search";
  const res = await post(base_url, params);
  return res;
};

export const getBasicInfoContractorEvaluation = async (id) => {
  const base_url = `/basic_info_management/contractor_evaluation/${id}`;
  return await get(base_url);
};

export const createBasicInfoContractorEvaluation = async (params) => {
  const res = await post("/basic_info_management/contractor_evaluation", params);
  return res;
};

export const delBasicInfoContractorEvaluation = async (id: number) => {
  const res = await del(`/basic_info_management/contractor_evaluation/${id}`);
  return res;
};

export const delBasicInfoContractorEvaluations = async (ids) => {
  const res = await del(`/basic_info_management/contractor_evaluation`, ids);
  return res;
};

export const updateBasicInfoContractorEvaluation = async (params) => {
  const res = await put(
    `/basic_info_management/contractor_evaluation/${params.id}`,
    params?.values,
  );
  return res;
};

export const basicInfoContractorEvaluationApis: CommonApis = {
  entity: "BasicInfoContractorEvaluation",
  query: getBasicInfoContractorEvaluationList,
  create: createBasicInfoContractorEvaluation,
  remove: delBasicInfoContractorEvaluation,
  removes: delBasicInfoContractorEvaluations,
  update: updateBasicInfoContractorEvaluation,
  get: getBasicInfoContractorEvaluation,
};
