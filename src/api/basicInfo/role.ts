import { post, get, del, put } from "@api";

export const getRoleList = async (params) => {
  const base_url = "/basic_info_management/role/search";
  const res = await post(base_url, params);
  return res;
};

export const getRole = async (id) => {
  const base_url = `/basic_info_management/role/${id}`;
  return await get(base_url);
};

// 新增
export const createRole = async (params) => {
  const res = await post("/basic_info_management/role", params);
  return res;
};

// 删除单个项目
export const delRole = async (id: number) => {
  const res = await del(`/basic_info_management/role/${id}`);
  return res;
};

// 批量删除
export const delRoles = async (ids) => {
  const res = await del(`/basic_info_management/role`, ids);
  return res;
};

// 修改
export const updateRole = async (params) => {
  const res = await put(
    `/basic_info_management/role/${params.id}`,
    params?.values,
  );
  return res;
};

// 角色赋权
export const setRoleAuth = async (params) => {
  const base_url = `/basic_info_management/role/${params.id}/auth`;
  delete params.id;
  const res = await post(base_url, params);
  return res;
};

// 角色添加人员
export const setRoleLink = async (params) => {
  const base_url = `/basic_info_management/role/${params.id}/link`;
  delete params.id;
  const res = await post(base_url, params);
  return res;
};
