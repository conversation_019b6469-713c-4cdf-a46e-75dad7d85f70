import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/basic_info_management/contractor_project/search

export const getContractorProjectList = async (params) => {
  const base_url = "/basic_info_management/contractor_project/search";
  const res = await post(base_url, params);
  return res;
};

export const getContractorProject = async (id) => {
  const base_url = `/basic_info_management/contractor_project/${id}`;
  return await get(base_url);
};

export const createContractorProject = async (params) => {
  const res = await post("/basic_info_management/contractor_project", params);
  return res;
};

export const delContractorProject = async (id: number) => {
  const res = await del(`/basic_info_management/contractor_project/${id}`);
  return res;
};

export const delContractorProjects = async (ids) => {
  const res = await del(`/basic_info_management/contractor`, ids);
  return res;
};

export const updateContractorProject = async (params) => {
  const res = await put(
    `/basic_info_management/contractor_project/${params.id}`,
    params?.values
  );
  return res;
};

export const stopContractorProject = async (params) => {
  const res = await post(
    `/basic_info_management/contractor_project/${params.id}/stop`,
    params?.values
  );
  return res;
};

export const batchStopContractorProject = async (ids: number[]) => {
  const res = await put(`/basic_info_management/contractor_project/stop`, ids);
  return res;
};

export const contractorProjectApis: CommonApis = {
  entity: "ContractorProject",
  query: getContractorProjectList,
  create: createContractorProject,
  remove: delContractorProject,
  removes: delContractorProjects,
  update: updateContractorProject,
  get: getContractorProject,
};
