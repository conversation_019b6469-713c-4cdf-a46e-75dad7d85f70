import { get, post } from "api";

export const getLoginRecordList = async (params: any) => {
  const base_url = "/system/login_record/search";
  const res = await post(base_url, params);
  return res;
};

export const getOperationLogList = async (params: any) => {
  const base_url = "/system/operation_log/search";
  const res = await post(base_url, params);
  return res;
};

export const getLoginRecord = async (id: string) => {
  const base_url = `/system/login_record/${id}`;
  return await get(base_url);
};
