import { post, get, del, put } from "@api";

export const getPermission = async (type) => {
  const base_url = `/system/permission?requestType=${type}`;
  const res = await get(base_url);
  return res;
};

export type EditMenuParams = {
  /**
   * 前端path
   */
  clientPath: string;
  /**
   * 唯一标识符，可由前端根据规则（菜单汉字拼音首字母）生成
   */
  code: string;
  description: string;
  name: string;
  /**
   * 1. web 2. mobile
   */
  requestType: number;
  /**
   * 后端path
   */
  serverPath: string;
  /**
   * 1 基础信息；2. 重大危险源；3. 双重预防
   */
  sysModel: number;
};

export type EditPermissionParams = Omit<EditMenuParams, "sysModel"> & {
  menuId: number;
};
// 新增
export const editMenu = async (params: EditMenuParams) => {
  const res = await post("/system/menu", params);
  return res;
};
// 新增权限
export const editPermission = async (params: EditPermissionParams) => {
  const res = await post("/system/permission", params);
  return res;
};

// 角色赋权
export const editRoleAuth = async (params) => {
  const id = params.id;
  const values = params.values;
  const requestType = params.requestType;
  const res = await post(
    `/basic_info_management/role/${id}/auth?requestType=${requestType}`,
    values,
  );
  return res;
};

export const editRoleLink = async (params) => {
  const id = params.id;
  const values = params.values;

  const res = await post(`/basic_info_management/role/${id}/link`, values);
  return res;
};
