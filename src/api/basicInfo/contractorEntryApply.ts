import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/basic_info_management/contractor_entry_apply/search

export const getBasicInfoContractorEntryApplyList = async (params) => {
  const base_url = "/basic_info_management/contractor_entry_apply/search";
  const res = await post(base_url, params);
  return res;
};

export const getBasicInfoContractorEntryApply = async (id) => {
  const base_url = `/basic_info_management/contractor_entry_apply/${id}`;
  return await get(base_url);
};

export const createBasicInfoContractorEntryApply = async (params) => {
  const res = await post(
    "/basic_info_management/contractor_entry_apply",
    params
  );
  return res;
};

export const delBasicInfoContractorEntryApply = async (id: number) => {
  const res = await del(`/basic_info_management/contractor_entry_apply/${id}`);
  return res;
};

export const delBasicInfoContractorEntryApplys = async (ids) => {
  const res = await del(`/basic_info_management/contractor_entry_apply`, ids);
  return res;
};

export const updateBasicInfoContractorEntryApply = async (params) => {
  const res = await put(
    `/basic_info_management/contractor_entry_apply/${params.id}`,
    params?.values
  );
  return res;
};

export const auditBasicInfoContractorEntryApply = async (params) => {
  const res = await post(
    `/basic_info_management/contractor_entry_apply/${params.id}/audit`,
    params?.values
  );
  return res;
};

export const auditDelegateBasicInfoContractorEntryApply = async (params) => {
  const res = await post(
    `/basic_info_management/contractor_entry_apply/${params.id}/audit/delegate`,
    params?.values
  );
  return res;
};

export const basicInfoContractorEntryApplyApis: CommonApis = {
  entity: "BasicInfoContractorEntryApply",
  query: getBasicInfoContractorEntryApplyList,
  create: createBasicInfoContractorEntryApply,
  remove: delBasicInfoContractorEntryApply,
  removes: delBasicInfoContractorEntryApplys,
  update: updateBasicInfoContractorEntryApply,
  get: getBasicInfoContractorEntryApply,
};
