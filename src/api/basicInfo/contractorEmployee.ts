import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/basic_info_management/contractor_employee/search

export const getContractorEmployeeList = async (params) => {
  const base_url = "/basic_info_management/contractor_employee/search";
  const res = await post(base_url, params);
  return res;
};

export const getContractorEmployee = async (id) => {
  const base_url = `/basic_info_management/contractor_employee/${id}`;
  return await get(base_url);
};

export const createContractorEmployee = async (params) => {
  const res = await post("/basic_info_management/contractor_employee", params);
  return res;
};

export const delContractorEmployee = async (id: number) => {
  const res = await del(`/basic_info_management/contractor_employee/${id}`);
  return res;
};

export const delContractorEmployees = async (ids) => {
  const res = await del(`/basic_info_management/contractor`, ids);
  return res;
};

export const updateContractorEmployee = async (params) => {
  const res = await put(
    `/basic_info_management/contractor_employee/${params.id}`,
    params?.values
  );
  return res;
};

export const addBlackListContractorEmployee = async (params) => {
  const base_url = `/basic_info_management/contractor_employee/${params?.id}/black/add`;
  return await post(base_url, params?.values);
};

export const removeBlackListContractorEmployee = async (id) => {
  const base_url = `/basic_info_management/contractor_employee/${id}/black/remove`;
  return await post(base_url);
};

export type ContractorEmployeeCommonApis = CommonApis & {
  addBlackList: (id: any) => Promise<any>;
  removeBlackList: (id: any) => Promise<any>;
};

export const contractorEmployeeApis: ContractorEmployeeCommonApis = {
  entity: "ContractorEmployee",
  query: getContractorEmployeeList,
  create: createContractorEmployee,
  remove: delContractorEmployee,
  removes: delContractorEmployees,
  update: updateContractorEmployee,
  get: getContractorEmployee,
  addBlackList: addBlackListContractorEmployee,
  removeBlackList: removeBlackListContractorEmployee,
};
