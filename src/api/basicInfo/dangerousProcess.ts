import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/basic_info_management/dangerous_process/search

export const getDangerousProcessList = async (params) => {
  const base_url = "/basic_info_management/dangerous_process/search";
  const res = await post(base_url, params);
  return res;
};

export const getDangerousProcess = async (id) => {
  const base_url = `/basic_info_management/dangerous_process/${id}`;
  return await get(base_url);
};

export const createDangerousProcess = async (params) => {
  const res = await post("/basic_info_management/dangerous_process", params);
  return res;
};

export const delDangerousProcess = async (id: number) => {
  const res = await del(`/basic_info_management/dangerous_process/${id}`);
  return res;
};

export const delDangerousProcesss = async (ids) => {
  const res = await del(`/basic_info_management/dangerous_process`, ids);
  return res;
};

export const updateDangerousProcess = async (params) => {
  const res = await put(
    `/basic_info_management/dangerous_process/${params.id}`,
    params?.values,
  );
  return res;
};

export const dangerousProcessApis: CommonApis = {
  entity: "DangerousProcess",
  query: getDangerousProcessList,
  create: createDangerousProcess,
  remove: delDangerousProcess,
  removes: delDangerousProcesss,
  update: updateDangerousProcess,
  get: getDangerousProcess,
};
