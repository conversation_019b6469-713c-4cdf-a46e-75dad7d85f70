import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/basic_info_management/contractor_config/search

export const getBasicInfoContractorConfigCurrent = async () => {
  const base_url = "/basic_info_management/contractor_config/current";
  const res = await get(base_url);
  return res;
};

export const getBasicInfoContractorConfigList = async (params) => {
  const base_url = "/basic_info_management/contractor_config/search";
  const res = await post(base_url, params);
  return res;
};

export const getBasicInfoContractorConfig = async (id) => {
  const base_url = `/basic_info_management/contractor_config/${id}`;
  return await get(base_url);
};

export const createBasicInfoContractorConfig = async (params) => {
  const res = await post("/basic_info_management/contractor_config", params);
  return res;
};

export const delBasicInfoContractorConfig = async (id: number) => {
  const res = await del(`/basic_info_management/contractor_config/${id}`);
  return res;
};

export const delBasicInfoContractorConfigs = async (ids) => {
  const res = await del(`/basic_info_management/contractor_config`, ids);
  return res;
};

export const updateBasicInfoContractorConfig = async (params) => {
  const res = await put(
    `/basic_info_management/contractor_config/${params.id}`,
    params?.values
  );
  return res;
};

export const basicInfoContractorConfigApis: CommonApis = {
  entity: "BasicInfoContractorConfig",
  query: getBasicInfoContractorConfigList,
  create: createBasicInfoContractorConfig,
  remove: delBasicInfoContractorConfig,
  removes: delBasicInfoContractorConfigs,
  update: updateBasicInfoContractorConfig,
  get: getBasicInfoContractorConfig,
};
