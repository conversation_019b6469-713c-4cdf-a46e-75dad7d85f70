import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/basic_info_management/interlock/search

export const getInterlockList = async (params) => {
  const base_url = "/basic_info_management/interlock/search";
  const res = await post(base_url, params);
  return res;
};

export const getInterlock = async (id) => {
  const base_url = `/basic_info_management/interlock/${id}`;
  return await get(base_url);
};

export const createInterlock = async (params) => {
  const res = await post("/basic_info_management/interlock", params);
  return res;
};

export const delInterlock = async (id: number) => {
  const res = await del(`/basic_info_management/interlock/${id}`);
  return res;
};

export const delInterlocks = async (ids) => {
  const res = await del(`/basic_info_management/interlock`, ids);
  return res;
};

export const updateInterlock = async (params) => {
  const res = await put(
    `/basic_info_management/interlock/${params.id}`,
    params?.values,
  );
  return res;
};

export const interlockApis: CommonApis = {
  entity: "Interlock",
  query: getInterlockList,
  create: createInterlock,
  remove: delInterlock,
  removes: delInterlocks,
  update: updateInterlock,
  get: getInterlock,
};
