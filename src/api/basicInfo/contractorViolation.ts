import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/basic_info_management/contractor_violation/search

export const getBasicInfoContractorViolationList = async (params) => {
  const base_url = "/basic_info_management/contractor_violation/search";
  const res = await post(base_url, params);
  return res;
};

export const getBasicInfoContractorViolation = async (id) => {
  const base_url = `/basic_info_management/contractor_violation/${id}`;
  return await get(base_url);
};

export const createBasicInfoContractorViolation = async (params) => {
  const res = await post("/basic_info_management/contractor_violation", params);
  return res;
};

export const delBasicInfoContractorViolation = async (id: number) => {
  const res = await del(`/basic_info_management/contractor_violation/${id}`);
  return res;
};

export const delBasicInfoContractorViolations = async (ids) => {
  const res = await del(`/basic_info_management/contractor_violation`, ids);
  return res;
};

export const updateBasicInfoContractorViolation = async (params) => {
  const res = await put(
    `/basic_info_management/contractor_violation/${params.id}`,
    params?.values,
  );
  return res;
};

export const basicInfoContractorViolationApis: CommonApis = {
  entity: "BasicInfoContractorViolation",
  query: getBasicInfoContractorViolationList,
  create: createBasicInfoContractorViolation,
  remove: delBasicInfoContractorViolation,
  removes: delBasicInfoContractorViolations,
  update: updateBasicInfoContractorViolation,
  get: getBasicInfoContractorViolation,
};
