import { post, get, del, put } from "@api";

export const getMonitorList = async (params) => {
  const base_url = "/basic_info_management/monitor/search";
  const res = await post(base_url, params);
  return res;
};

export const getMonitor = async (id) => {
  const base_url = `/basic_info_management/monitor/${id}`;
  return await get(base_url);
};

// 新增
export const createMonitor = async (params) => {
  const res = await post("/basic_info_management/monitor", params);
  return res;
};

// 删除单个项目
export const delMonitor = async (id: number) => {
  const res = await del(`/basic_info_management/monitor/${id}`);
  return res;
};

// 批量删除
export const delMonitors = async (ids) => {
  const res = await del(`/basic_info_management/monitor`, ids);
  return res;
};

// 修改
export const updateMonitor = async (params) => {
  const res = await put(
    `/basic_info_management/monitor/${params.id}`,
    params?.values,
  );
  return res;
};
