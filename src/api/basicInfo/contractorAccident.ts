import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/basic_info_management/contractor_accident/search

export const getContractorAccidentList = async (params) => {
  const base_url = "/basic_info_management/contractor_accident/search";
  const res = await post(base_url, params);
  return res;
};

export const getContractorAccident = async (id) => {
  const base_url = `/basic_info_management/contractor_accident/${id}`;
  return await get(base_url);
};

export const createContractorAccident = async (params) => {
  const res = await post("/basic_info_management/contractor_accident", params);
  return res;
};

export const delContractorAccident = async (id: number) => {
  const res = await del(`/basic_info_management/contractor_accident/${id}`);
  return res;
};

export const delContractorAccidents = async (ids) => {
  const res = await del(`/basic_info_management/contractor`, ids);
  return res;
};

export const updateContractorAccident = async (params) => {
  const res = await put(
    `/basic_info_management/contractor_accident/${params.id}`,
    params?.values,
  );
  return res;
};

export const contractorAccidentApis: CommonApis = {
  entity: "ContractorAccident",
  query: getContractorAccidentList,
  create: createContractorAccident,
  remove: delContractorAccident,
  removes: delContractorAccidents,
  update: updateContractorAccident,
  get: getContractorAccident,
};
