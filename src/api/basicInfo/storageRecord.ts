import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/basic_info_management/storage_record/search

export const getStorageRecordList = async (params) => {
  const base_url = "/basic_info_management/storage_record/search";
  const res = await post(base_url, params);
  return res;
};

export const getStorageRecord = async (id) => {
  const base_url = `/basic_info_management/storage_record/${id}`;
  return await get(base_url);
};

export const createStorageRecord = async (params) => {
  const res = await post("/basic_info_management/storage_record", params);
  return res;
};

export const delStorageRecord = async (id: number) => {
  const res = await del(`/basic_info_management/storage_record/${id}`);
  return res;
};

export const delStorageRecords = async (ids) => {
  const res = await del(`/basic_info_management/storage_record`, ids);
  return res;
};

export const updateStorageRecord = async (params) => {
  const res = await put(
    `/basic_info_management/storage_record/${params.id}`,
    params?.values,
  );
  return res;
};

export const storageRecordApis: CommonApis = {
  entity: "StorageRecord",
  query: getStorageRecordList,
  create: createStorageRecord,
  remove: delStorageRecord,
  removes: delStorageRecords,
  update: updateStorageRecord,
  get: getStorageRecord,
};
