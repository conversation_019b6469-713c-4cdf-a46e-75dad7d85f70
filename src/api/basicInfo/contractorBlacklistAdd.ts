import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/basic_info_management/contractor_blacklist_add/search

export const getBasicInfoContractorBlacklistAddList = async (params) => {
  const base_url = "/basic_info_management/contractor_blacklist_add/search";
  const res = await post(base_url, params);
  return res;
};

export const getBasicInfoContractorBlacklistAdd = async (id) => {
  const base_url = `/basic_info_management/contractor_blacklist_add/${id}`;
  return await get(base_url);
};

export const createBasicInfoContractorBlacklistAdd = async (params) => {
  const res = await post("/basic_info_management/contractor_blacklist_add", params);
  return res;
};

export const delBasicInfoContractorBlacklistAdd = async (id: number) => {
  const res = await del(`/basic_info_management/contractor_blacklist_add/${id}`);
  return res;
};

export const delBasicInfoContractorBlacklistAdds = async (ids) => {
  const res = await del(`/basic_info_management/contractor_blacklist_add`, ids);
  return res;
};

export const updateBasicInfoContractorBlacklistAdd = async (params) => {
  const res = await put(
    `/basic_info_management/contractor_blacklist_add/${params.id}`,
    params?.values,
  );
  return res;
};

export const basicInfoContractorBlacklistAddApis: CommonApis = {
  entity: "BasicInfoContractorBlacklistAdd",
  query: getBasicInfoContractorBlacklistAddList,
  create: createBasicInfoContractorBlacklistAdd,
  remove: delBasicInfoContractorBlacklistAdd,
  removes: delBasicInfoContractorBlacklistAdds,
  update: updateBasicInfoContractorBlacklistAdd,
  get: getBasicInfoContractorBlacklistAdd,
};
