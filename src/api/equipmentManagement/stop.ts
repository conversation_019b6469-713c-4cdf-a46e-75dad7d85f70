import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/equipment_management/stop/search

export const getEquipmentManagementStopList = async (params) => {
  const base_url = "/equipment_management/stop/search";
  const res = await post(base_url, params);
  return res;
};

export const getEquipmentManagementStop = async (id) => {
  const base_url = `/equipment_management/stop/${id}`;
  return await get(base_url);
};

export const createEquipmentManagementStop = async (params) => {
  const res = await post("/equipment_management/stop", params);
  return res;
};

export const delEquipmentManagementStop = async (id: number) => {
  const res = await del(`/equipment_management/stop/${id}`);
  return res;
};

export const delEquipmentManagementStops = async (ids) => {
  const res = await del(`/equipment_management/stop`, ids);
  return res;
};

export const updateEquipmentManagementStop = async (params) => {
  const res = await put(
    `/equipment_management/stop/${params.id}`,
    params?.values,
  );
  return res;
};

export const equipmentManagementStopApis: CommonApis = {
  entity: "EquipmentManagementStop",
  query: getEquipmentManagementStopList,
  create: createEquipmentManagementStop,
  remove: delEquipmentManagementStop,
  removes: delEquipmentManagementStops,
  update: updateEquipmentManagementStop,
  get: getEquipmentManagementStop,
};
