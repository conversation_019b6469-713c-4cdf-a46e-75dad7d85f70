import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/equipment_management/scrap/search

export const getEquipmentManagementScrapList = async (params) => {
  const base_url = "/equipment_management/scrap/search";
  const res = await post(base_url, params);
  return res;
};

export const getEquipmentManagementScrap = async (id) => {
  const base_url = `/equipment_management/scrap/${id}`;
  return await get(base_url);
};

export const createEquipmentManagementScrap = async (params) => {
  const res = await post("/equipment_management/scrap", params);
  return res;
};

export const delEquipmentManagementScrap = async (id: number) => {
  const res = await del(`/equipment_management/scrap/${id}`);
  return res;
};

export const delEquipmentManagementScraps = async (ids) => {
  const res = await del(`/equipment_management/scrap`, ids);
  return res;
};

export const updateEquipmentManagementScrap = async (params) => {
  const res = await put(
    `/equipment_management/scrap/${params.id}`,
    params?.values,
  );
  return res;
};

export const equipmentManagementScrapApis: CommonApis = {
  entity: "EquipmentManagementScrap",
  query: getEquipmentManagementScrapList,
  create: createEquipmentManagementScrap,
  remove: delEquipmentManagementScrap,
  removes: delEquipmentManagementScraps,
  update: updateEquipmentManagementScrap,
  get: getEquipmentManagementScrap,
};
