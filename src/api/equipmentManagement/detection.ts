import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/equipment_management/detection/search

export const getEquipmentManagementDetectionList = async (params) => {
  const base_url = "/equipment_management/detection/search";
  const res = await post(base_url, params);
  return res;
};

export const getEquipmentManagementDetection = async (id) => {
  const base_url = `/equipment_management/detection/${id}`;
  return await get(base_url);
};

export const createEquipmentManagementDetection = async (params) => {
  const res = await post("/equipment_management/detection", params);
  return res;
};

export const delEquipmentManagementDetection = async (id: number) => {
  const res = await del(`/equipment_management/detection/${id}`);
  return res;
};

export const delEquipmentManagementDetections = async (ids) => {
  const res = await del(`/equipment_management/detection`, ids);
  return res;
};

export const updateEquipmentManagementDetection = async (params) => {
  const res = await put(
    `/equipment_management/detection/${params.id}`,
    params?.values,
  );
  return res;
};

export const equipmentManagementDetectionApis: CommonApis = {
  entity: "EquipmentManagementDetection",
  query: getEquipmentManagementDetectionList,
  create: createEquipmentManagementDetection,
  remove: delEquipmentManagementDetection,
  removes: delEquipmentManagementDetections,
  update: updateEquipmentManagementDetection,
  get: getEquipmentManagementDetection,
};
