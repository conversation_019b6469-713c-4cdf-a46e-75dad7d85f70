import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/equipment_management/repair/search

export const getEquipmentManagementRepairList = async (params) => {
  const base_url = "/equipment_management/repair/search";
  const res = await post(base_url, params);
  return res;
};

export const getEquipmentManagementRepair = async (id) => {
  const base_url = `/equipment_management/repair/${id}`;
  return await get(base_url);
};

export const createEquipmentManagementRepair = async (params) => {
  const res = await post("/equipment_management/repair", params);
  return res;
};

export const delEquipmentManagementRepair = async (id: number) => {
  const res = await del(`/equipment_management/repair/${id}`);
  return res;
};

export const delEquipmentManagementRepairs = async (ids) => {
  const res = await del(`/equipment_management/repair`, ids);
  return res;
};

export const updateEquipmentManagementRepair = async (params) => {
  const res = await put(
    `/equipment_management/repair/${params.id}`,
    params?.values,
  );
  return res;
};

export const equipmentManagementRepairApis: CommonApis = {
  entity: "EquipmentManagementRepair",
  query: getEquipmentManagementRepairList,
  create: createEquipmentManagementRepair,
  remove: delEquipmentManagementRepair,
  removes: delEquipmentManagementRepairs,
  update: updateEquipmentManagementRepair,
  get: getEquipmentManagementRepair,
};
