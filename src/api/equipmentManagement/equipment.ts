import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/equipment_management/equipment/search

export const getEquipmentManagementEquipmentList = async (params) => {
  const base_url = "/equipment_management/equipment/search";
  const res = await post(base_url, params);
  return res;
};

export const getEquipmentManagementEquipment = async (id) => {
  const base_url = `/equipment_management/equipment/${id}`;
  return await get(base_url);
};

export const createEquipmentManagementEquipment = async (params) => {
  const res = await post("/equipment_management/equipment", params);
  return res;
};

export const delEquipmentManagementEquipment = async (id: number) => {
  const res = await del(`/equipment_management/equipment/${id}`);
  return res;
};

export const delEquipmentManagementEquipments = async (ids) => {
  const res = await del(`/equipment_management/equipment`, ids);
  return res;
};

export const updateEquipmentManagementEquipment = async (params) => {
  const res = await put(
    `/equipment_management/equipment/${params.id}`,
    params?.values
  );
  return res;
};

export const getEquipmentSensorList = async (id) => {
  const base_url = `/equipment_management/equipment/${id}/sensor`;
  return await get(base_url);
};

export const getEquipmentMonitorList = async (id) => {
  const base_url = `/equipment_management/equipment/${id}/monitor`;
  return await get(base_url);
};

export const getEquipmentInspectionList = async (params) => {
  const base_url = `/equipment_management/equipment/${params.id}/inspection`;
  return await post(base_url, params.values);
};

export const equipmentManagementEquipmentApis: CommonApis = {
  entity: "EquipmentManagementEquipment",
  query: getEquipmentManagementEquipmentList,
  create: createEquipmentManagementEquipment,
  remove: delEquipmentManagementEquipment,
  removes: delEquipmentManagementEquipments,
  update: updateEquipmentManagementEquipment,
  get: getEquipmentManagementEquipment,
};
