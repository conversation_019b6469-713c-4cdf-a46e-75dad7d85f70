import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/equipment_management/resume/search

export const getEquipmentManagementResumeList = async (params) => {
  const base_url = "/equipment_management/resume/search";
  const res = await post(base_url, params);
  return res;
};

export const getEquipmentManagementResume = async (id) => {
  const base_url = `/equipment_management/resume/${id}`;
  return await get(base_url);
};

export const createEquipmentManagementResume = async (params) => {
  const res = await post("/equipment_management/resume", params);
  return res;
};

export const delEquipmentManagementResume = async (id: number) => {
  const res = await del(`/equipment_management/resume/${id}`);
  return res;
};

export const delEquipmentManagementResumes = async (ids) => {
  const res = await del(`/equipment_management/resume`, ids);
  return res;
};

export const updateEquipmentManagementResume = async (params) => {
  const res = await put(
    `/equipment_management/resume/${params.id}`,
    params?.values,
  );
  return res;
};

export const equipmentManagementResumeApis: CommonApis = {
  entity: "EquipmentManagementResume",
  query: getEquipmentManagementResumeList,
  create: createEquipmentManagementResume,
  remove: delEquipmentManagementResume,
  removes: delEquipmentManagementResumes,
  update: updateEquipmentManagementResume,
  get: getEquipmentManagementResume,
};
