import { del, get, post, put } from "api";
import { CommonApis } from "types";

// 设备监测API - 基于新的接口规范
export const getEquipmentSensorList = async (params) => {
  const base_url = "/equipment_management/equipment/sensor";
  const res = await post(base_url, params);
  return res;
};

export const getEquipmentSensor = async (id) => {
  const base_url = `/equipment_management/equipment/sensor/${id}`;
  return await get(base_url);
};

export const createEquipmentSensor = async (params) => {
  const res = await post("/equipment_management/equipment/sensor/create", params);
  return res;
};

export const updateEquipmentSensor = async (params) => {
  const res = await put(`/equipment_management/equipment/sensor/${params.id}`, params.values);
  return res;
};

export const delEquipmentSensor = async (id: number) => {
  const res = await del(`/equipment_management/equipment/sensor/${id}`);
  return res;
};

export const delEquipmentSensors = async (ids: number[]) => {
  const res = await del("/equipment_management/equipment/sensor/batch", { ids });
  return res;
};

// 设备监测相关操作API
export const resumeEquipmentSensor = async (id: number) => {
  const res = await put(`/equipment_management/equipment/sensor/${id}/resume`);
  return res;
};

export const stopEquipmentSensor = async (id: number) => {
  const res = await put(`/equipment_management/equipment/sensor/${id}/stop`);
  return res;
};

export const batchResumeEquipmentSensor = async (ids: number[]) => {
  const res = await put("/equipment_management/equipment/sensor/batch/resume", { ids });
  return res;
};

export const batchStopEquipmentSensor = async (ids: number[]) => {
  const res = await put("/equipment_management/equipment/sensor/batch/stop", { ids });
  return res;
};

export const equipmentSensorApis: CommonApis = {
  entity: "EquipmentSensor",
  query: getEquipmentSensorList,
  create: createEquipmentSensor,
  remove: delEquipmentSensor,
  removes: delEquipmentSensors,
  update: updateEquipmentSensor,
  get: getEquipmentSensor,
};
