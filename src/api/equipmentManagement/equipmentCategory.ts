import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/equipment_management/equipment_category/search

export const getEquipmentManagementEquipmentCategoryList = async () => {
  const base_url = "/equipment_management/equipment_category";
  const res = await get(base_url);
  return res;
};

export const getEquipmentManagementEquipmentCategory = async (id) => {
  const base_url = `/equipment_management/equipment_category/${id}`;
  return await get(base_url);
};

export const createEquipmentManagementEquipmentCategory = async (params) => {
  const res = await post("/equipment_management/equipment_category", params);
  return res;
};

export const delEquipmentManagementEquipmentCategory = async (id: number) => {
  const res = await del(`/equipment_management/equipment_category/${id}`);
  return res;
};

export const delEquipmentManagementEquipmentCategorys = async (ids) => {
  const res = await del(`/equipment_management/equipment_category`, ids);
  return res;
};

export const updateEquipmentManagementEquipmentCategory = async (params) => {
  const res = await put(
    `/equipment_management/equipment_category/${params.id}`,
    params?.values
  );
  return res;
};

export const equipmentManagementEquipmentCategoryApis: CommonApis = {
  entity: "EquipmentManagementEquipmentCategory",
  query: getEquipmentManagementEquipmentCategoryList,
  create: createEquipmentManagementEquipmentCategory,
  remove: delEquipmentManagementEquipmentCategory,
  removes: delEquipmentManagementEquipmentCategorys,
  update: updateEquipmentManagementEquipmentCategory,
  get: getEquipmentManagementEquipmentCategory,
};
