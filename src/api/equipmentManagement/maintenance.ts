import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/equipment_management/maintenance/search

export const getEquipmentManagementMaintenanceList = async (params) => {
  const base_url = "/equipment_management/maintenance/search";
  const res = await post(base_url, params);
  return res;
};

export const getEquipmentManagementMaintenance = async (id) => {
  const base_url = `/equipment_management/maintenance/${id}`;
  return await get(base_url);
};

export const createEquipmentManagementMaintenance = async (params) => {
  const res = await post("/equipment_management/maintenance", params);
  return res;
};

export const delEquipmentManagementMaintenance = async (id: number) => {
  const res = await del(`/equipment_management/maintenance/${id}`);
  return res;
};

export const delEquipmentManagementMaintenances = async (ids) => {
  const res = await del(`/equipment_management/maintenance`, ids);
  return res;
};

export const updateEquipmentManagementMaintenance = async (params) => {
  const res = await put(
    `/equipment_management/maintenance/${params.id}`,
    params?.values,
  );
  return res;
};

export const equipmentManagementMaintenanceApis: CommonApis = {
  entity: "EquipmentManagementMaintenance",
  query: getEquipmentManagementMaintenanceList,
  create: createEquipmentManagementMaintenance,
  remove: delEquipmentManagementMaintenance,
  removes: delEquipmentManagementMaintenances,
  update: updateEquipmentManagementMaintenance,
  get: getEquipmentManagementMaintenance,
};
