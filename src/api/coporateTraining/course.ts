import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/coporate_training/course/search

export const getCourseList = async (params) => {
  const base_url = "/coporate_training/course/search";
  const res = await post(base_url, params);
  return res;
};

export const getCourse = async (id) => {
  const base_url = `/coporate_training/course/${id}`;
  return await get(base_url);
};

export const createCourse = async (params) => {
  const res = await post("/coporate_training/course", params);
  return res;
};

export const delCourse = async (id: number) => {
  const res = await del(`/coporate_training/course/${id}`);
  return res;
};

export const delCourses = async (ids) => {
  const res = await del(`/coporate_training/course`, ids);
  return res;
};

export const updateCourse = async (params) => {
  const res = await put(
    `/coporate_training/course/${params.id}`,
    params?.values,
  );
  return res;
};

export const courseApis: CommonApis = {
  entity: "Course",
  query: getCourseList,
  create: createCourse,
  remove: delCourse,
  removes: delCourses,
  update: updateCourse,
  get: getCourse,
};
