import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/coporate_training/people_exam_record/search

export const getCoporateTrainingPeopleExamRecordList = async (params) => {
  const base_url = "/coporate_training/people_exam_record/search";
  const res = await post(base_url, params);
  return res;
};

export const getCoporateTrainingPeopleExamRecord = async (id) => {
  const base_url = `/coporate_training/people_exam_record/${id}`;
  return await get(base_url);
};

export const createCoporateTrainingPeopleExamRecord = async (params) => {
  const res = await post("/coporate_training/people_exam_record", params);
  return res;
};

export const delCoporateTrainingPeopleExamRecord = async (id: number) => {
  const res = await del(`/coporate_training/people_exam_record/${id}`);
  return res;
};

export const delCoporateTrainingPeopleExamRecords = async (ids) => {
  const res = await del(`/coporate_training/people_exam_record`, ids);
  return res;
};

export const updateCoporateTrainingPeopleExamRecord = async (params) => {
  const res = await put(
    `/coporate_training/people_exam_record/${params.id}`,
    params?.values,
  );
  return res;
};

export const coporateTrainingPeopleExamRecordApis: CommonApis = {
  entity: "CoporateTrainingPeopleExamRecord",
  query: getCoporateTrainingPeopleExamRecordList,
  create: createCoporateTrainingPeopleExamRecord,
  remove: delCoporateTrainingPeopleExamRecord,
  removes: delCoporateTrainingPeopleExamRecords,
  update: updateCoporateTrainingPeopleExamRecord,
  get: getCoporateTrainingPeopleExamRecord,
};
