import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/coporate_training/certificate/search

export const getCoporateTrainingCertificateList = async (params) => {
  const base_url = "/coporate_training/certificate/search";
  const res = await post(base_url, params);
  return res;
};

export const getCoporateTrainingCertificate = async (id) => {
  const base_url = `/coporate_training/certificate/${id}`;
  return await get(base_url);
};

export const createCoporateTrainingCertificate = async (params) => {
  const res = await post("/coporate_training/certificate", params);
  return res;
};

export const delCoporateTrainingCertificate = async (id: number) => {
  const res = await del(`/coporate_training/certificate/${id}`);
  return res;
};

export const delCoporateTrainingCertificates = async (ids) => {
  const res = await del(`/coporate_training/certificate`, ids);
  return res;
};

export const updateCoporateTrainingCertificate = async (params) => {
  const res = await put(
    `/coporate_training/certificate/${params.id}`,
    params?.values,
  );
  return res;
};

export const coporateTrainingCertificateApis: CommonApis = {
  entity: "CoporateTrainingCertificate",
  query: getCoporateTrainingCertificateList,
  create: createCoporateTrainingCertificate,
  remove: delCoporateTrainingCertificate,
  removes: delCoporateTrainingCertificates,
  update: updateCoporateTrainingCertificate,
  get: getCoporateTrainingCertificate,
};
