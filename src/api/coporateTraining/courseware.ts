import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/coporate_training/courseware/search

export const getCoursewareList = async (params) => {
  const base_url = "/coporate_training/courseware/search";
  const res = await post(base_url, params);
  return res;
};

export const getCourseware = async (id) => {
  const base_url = `/coporate_training/courseware/${id}`;
  return await get(base_url);
};

export const createCourseware = async (params) => {
  const res = await post("/coporate_training/courseware", params);
  return res;
};

export const delCourseware = async (id: number) => {
  const res = await del(`/coporate_training/courseware/${id}`);
  return res;
};

export const delCoursewares = async (ids) => {
  const res = await del(`/coporate_training/courseware`, ids);
  return res;
};

export const updateCourseware = async (params) => {
  const res = await put(
    `/coporate_training/courseware/${params.id}`,
    params?.values,
  );
  return res;
};

export const approveCourseware = async (id) => {
  const res = await post(`/coporate_training/courseware/${id}/pass`);
  return res;
};

export const batchApproveCourseware = async (ids) => {
  const res = await post(`/coporate_training/courseware/pass`, ids);
  return res;
};

export const rejectCourseware = async (id) => {
  const res = await post(`/coporate_training/courseware/${id}/reject`);
  return res;
};

export const batchRejectCourseware = async (ids) => {
  const res = await post(`/coporate_training/courseware/reject`, ids);
  return res;
};

export const coursewareApis: CommonApis = {
  entity: "Courseware",
  query: getCoursewareList,
  create: createCourseware,
  remove: delCourseware,
  removes: delCoursewares,
  update: updateCourseware,
  get: getCourseware,
};
