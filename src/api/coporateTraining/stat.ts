import { get, post } from "api";


export const getCoporateBasicStat = async () => {
  const base_url = "/coporate_training/basic_stat";
  const res = await get(base_url);
  return res;
};

export const getOngoingPlan = async () => {
  const base_url = "/coporate_training/ongoing_plan";
  const res = await get(base_url);
  return res;
};

export const getPaperStat = async () => {
  const base_url = "/coporate_training/paper_stat";
  const res = await get(base_url);
  return res;
};


export const getPeopleRank = async (params: any) => {
  const base_url = "/coporate_training/people_rank";
  const res = await post(base_url, params);
  return res;
};

export const getCoporateDepartmentStat = async (params: any) => {
  const base_url = "/coporate_training/department_stat";
  const res = await post(base_url, params);
  return res;
};

export const getContractorStat = async (params: any) => {
  const base_url = "/coporate_training/contractor_stat";
  const res = await post(base_url, params);
  return res;
};
