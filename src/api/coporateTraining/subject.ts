import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/coporate_training/subject/search

export const getSubjectList = async () => {
  const base_url = "/coporate_training/subject";
  const res = await get(base_url);
  return res;
};

export const getSubject = async (id) => {
  const base_url = `/coporate_training/subject/${id}`;
  return await get(base_url);
};

export const createSubject = async (params) => {
  const res = await post("/coporate_training/subject", params);
  return res;
};

export const delSubject = async (id: number) => {
  const res = await del(`/coporate_training/subject/${id}`);
  return res;
};

export const delSubjects = async (ids) => {
  const res = await del(`/coporate_training/subject`, ids);
  return res;
};

export const updateSubject = async (params) => {
  const res = await put(
    `/coporate_training/subject/${params.id}`,
    params?.values,
  );
  return res;
};

export const subjectApis: CommonApis = {
  entity: "Subject",
  query: getSubjectList,
  create: createSubject,
  remove: delSubject,
  removes: delSubjects,
  update: updateSubject,
  get: getSubject,
};
