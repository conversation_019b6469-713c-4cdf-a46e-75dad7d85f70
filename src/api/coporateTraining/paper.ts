import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/coporate_training/paper/search

export const getPaperList = async (params) => {
  const base_url = "/coporate_training/paper/search";
  const res = await post(base_url, params);
  return res;
};

export const getPaper = async (id) => {
  const base_url = `/coporate_training/paper/${id}`;
  return await get(base_url);
};

export const createPaper = async (params) => {
  const res = await post("/coporate_training/paper", params);
  return res;
};

export const delPaper = async (id: number) => {
  const res = await del(`/coporate_training/paper/${id}`);
  return res;
};

export const delPapers = async (ids) => {
  const res = await del(`/coporate_training/paper`, ids);
  return res;
};

export const updatePaper = async (params) => {
  const res = await put(
    `/coporate_training/paper/${params.id}`,
    params?.values,
  );
  return res;
};

export const paperApis: CommonApis = {
  entity: "Paper",
  query: getPaperList,
  create: createPaper,
  remove: delPaper,
  removes: delPapers,
  update: updatePaper,
  get: getPaper,
};
