import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/coporate_training/people_study_record/search

export const getCoporateTrainingPeopleStudyRecordList = async (params) => {
  const base_url = "/coporate_training/people_study_record/search";
  const res = await post(base_url, params);
  return res;
};

export const getCoporateTrainingPeopleStudyRecord = async (id) => {
  const base_url = `/coporate_training/people_study_record/${id}`;
  return await get(base_url);
};

export const createCoporateTrainingPeopleStudyRecord = async (params) => {
  const res = await post("/coporate_training/people_study_record", params);
  return res;
};

export const delCoporateTrainingPeopleStudyRecord = async (id: number) => {
  const res = await del(`/coporate_training/people_study_record/${id}`);
  return res;
};

export const delCoporateTrainingPeopleStudyRecords = async (ids) => {
  const res = await del(`/coporate_training/people_study_record`, ids);
  return res;
};

export const updateCoporateTrainingPeopleStudyRecord = async (params) => {
  const res = await put(
    `/coporate_training/people_study_record/${params.id}`,
    params?.values,
  );
  return res;
};

export const coporateTrainingPeopleStudyRecordApis: CommonApis = {
  entity: "CoporateTrainingPeopleStudyRecord",
  query: getCoporateTrainingPeopleStudyRecordList,
  create: createCoporateTrainingPeopleStudyRecord,
  remove: delCoporateTrainingPeopleStudyRecord,
  removes: delCoporateTrainingPeopleStudyRecords,
  update: updateCoporateTrainingPeopleStudyRecord,
  get: getCoporateTrainingPeopleStudyRecord,
};
