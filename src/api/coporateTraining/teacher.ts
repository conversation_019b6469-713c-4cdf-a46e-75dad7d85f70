import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/coporate_training/teacher/search

export const getTeacherList = async (params) => {
  const base_url = "/coporate_training/teacher/search";
  const res = await post(base_url, params);
  return res;
};

export const getTeacher = async (id) => {
  const base_url = `/coporate_training/teacher/${id}`;
  return await get(base_url);
};

export const createTeacher = async (params) => {
  const res = await post("/coporate_training/teacher", params);
  return res;
};

export const delTeacher = async (id: number) => {
  const res = await del(`/coporate_training/teacher/${id}`);
  return res;
};

export const delTeachers = async (ids) => {
  const res = await del(`/coporate_training/teacher`, ids);
  return res;
};

export const updateTeacher = async (params) => {
  const res = await put(
    `/coporate_training/teacher/${params.id}`,
    params?.values,
  );
  return res;
};

export const teacherApis: CommonApis = {
  entity: "Teacher",
  query: getTeacherList,
  create: createTeacher,
  remove: delTeacher,
  removes: delTeachers,
  update: updateTeacher,
  get: getTeacher,
};
