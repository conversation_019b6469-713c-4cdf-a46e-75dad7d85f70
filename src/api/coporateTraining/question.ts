import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/coporate_training/question/search

export const getQuestionList = async (params) => {
  const base_url = "/coporate_training/question/search";
  const res = await post(base_url, params);
  return res;
};

export const getQuestion = async (id) => {
  const base_url = `/coporate_training/question/${id}`;
  return await get(base_url);
};

export const createQuestion = async (params) => {
  const res = await post("/coporate_training/question", params);
  return res;
};

export const delQuestion = async (id: number) => {
  const res = await del(`/coporate_training/question/${id}`);
  return res;
};

export const delQuestions = async (ids) => {
  const res = await del(`/coporate_training/question`, ids);
  return res;
};

export const updateQuestion = async (params) => {
  const res = await put(
    `/coporate_training/question/${params.id}`,
    params?.values,
  );
  return res;
};

export const approveQuestion = async (id) => {
  const res = await post(`/coporate_training/question/${id}/pass`);
  return res;
};

export const batchApproveQuestion = async (ids) => {
  const res = await post(`/coporate_training/question/pass`, ids);
  return res;
};

export const rejectQuestion = async (id) => {
  const res = await post(`/coporate_training/question/${id}/reject`);
  return res;
};

export const batchRejectQuestion = async (ids) => {
  const res = await post(`/coporate_training/question/reject`, ids);
  return res;
};

export const questionApis: CommonApis = {
  entity: "Question",
  query: getQuestionList,
  create: createQuestion,
  remove: delQuestion,
  removes: delQuestions,
  update: updateQuestion,
  get: getQuestion,
};
