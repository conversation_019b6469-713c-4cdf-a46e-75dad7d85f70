import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/coporate_training/people/search

export const getCoporateTrainingPeopleList = async (params) => {
  const base_url = "/coporate_training/people_rank/search";
  const res = await post(base_url, params);
  return res;
};

export const getCoporateTrainingPeople = async (id) => {
  const base_url = `/coporate_training/people_rank/${id}`;
  return await get(base_url);
};

export const createCoporateTrainingPeople = async (params) => {
  const res = await post("/coporate_training/people_rank", params);
  return res;
};

export const delCoporateTrainingPeople = async (id: number) => {
  const res = await del(`/coporate_training/people_rank/${id}`);
  return res;
};

export const delCoporateTrainingPeoples = async (ids) => {
  const res = await del(`/coporate_training/people_rank`, ids);
  return res;
};

export const updateCoporateTrainingPeople = async (params) => {
  const res = await put(
    `/coporate_training/people_rank/${params.id}`,
    params?.values,
  );
  return res;
};

export const coporateTrainingPeopleApis: CommonApis = {
  entity: "CoporateTrainingPeople",
  query: getCoporateTrainingPeopleList,
  create: createCoporateTrainingPeople,
  remove: delCoporateTrainingPeople,
  removes: delCoporateTrainingPeoples,
  update: updateCoporateTrainingPeople,
  get: getCoporateTrainingPeople,
};
