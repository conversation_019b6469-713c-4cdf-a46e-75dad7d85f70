import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/coporate_training/config/search

export const getConfigList = async (params) => {
  const base_url = "/coporate_training/config/search";
  const res = await post(base_url, params);
  return res;
};

export const getCurrentTrainingConfig = async () => {
  const base_url = "/coporate_training/config/current";
  const res = await get(base_url);
  return res;
};

export const getConfig = async (id) => {
  const base_url = `/coporate_training/config/${id}`;
  return await get(base_url);
};

export const createConfig = async (params) => {
  const res = await post("/coporate_training/config", params);
  return res;
};

export const delConfig = async (id: number) => {
  const res = await del(`/coporate_training/config/${id}`);
  return res;
};

export const delConfigs = async (ids) => {
  const res = await del(`/coporate_training/config`, ids);
  return res;
};

export const updateConfig = async (params) => {
  const res = await put(
    `/coporate_training/config/${params.id}`,
    params?.values,
  );
  return res;
};

export const configApis: CommonApis = {
  entity: "Config",
  query: getConfigList,
  create: createConfig,
  remove: delConfig,
  removes: delConfigs,
  update: updateConfig,
  get: getConfig,
};
