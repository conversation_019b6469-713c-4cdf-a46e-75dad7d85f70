import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/coporate_training/record/search

export const getCoporateTrainingRecordList = async (params) => {
  const base_url = "/coporate_training/training_record/search";
  const res = await post(base_url, params);
  return res;
};

export const getCoporateTrainingRecord = async (id) => {
  const base_url = `/coporate_training/training_record/${id}`;
  return await get(base_url);
};

export const createCoporateTrainingRecord = async (params) => {
  const res = await post("/coporate_training/training_record", params);
  return res;
};

export const delCoporateTrainingRecord = async (id: number) => {
  const res = await del(`/coporate_training/training_record/${id}`);
  return res;
};

export const delCoporateTrainingRecords = async (ids) => {
  const res = await del(`/coporate_training/training_record`, ids);
  return res;
};

export const updateCoporateTrainingRecord = async (params) => {
  const res = await put(
    `/coporate_training/training_record/${params.id}`,
    params?.values,
  );
  return res;
};

export const coporateTrainingRecordApis: CommonApis = {
  entity: "CoporateTrainingRecord",
  query: getCoporateTrainingRecordList,
  create: createCoporateTrainingRecord,
  remove: delCoporateTrainingRecord,
  removes: delCoporateTrainingRecords,
  update: updateCoporateTrainingRecord,
  get: getCoporateTrainingRecord,
};
