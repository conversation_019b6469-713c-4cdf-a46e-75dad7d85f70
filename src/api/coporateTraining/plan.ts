import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/coporate_training/plan/search

export const getCoporateTrainingPlanList = async (params) => {
  const base_url = "/coporate_training/plan/search";
  const res = await post(base_url, params);
  return res;
};

export const getCoporateTrainingPlan = async (id) => {
  const base_url = `/coporate_training/plan/${id}`;
  return await get(base_url);
};

export const getCoporateTrainingPlanPeople = async (params) => {
  const base_url = `/coporate_training/plan/${params?.id}/people`;
  return await post(base_url, params?.values);
};

export const getCoporateTrainingPlanDepartment = async (params) => {
  const base_url = `/coporate_training/plan/${params.id}/department`;
  return await get(base_url);
};

export const getCoporateTrainingPlanPosition = async (params) => {
  const base_url = `/coporate_training/plan/${params.id}/position`;
  return await get(base_url);
};

export const LeaveCoporateTrainingPlan = async (params) => {
  const base_url = `/coporate_training/plan/${params.id}/record/${params.recordId}/take_leave`;
  return await post(base_url);
};

export const BackCoporateTrainingPlan = async (params) => {
  const base_url = `/coporate_training/plan/${params.id}/record/${params.recordId}/cancel_leave`;
  return await post(base_url);
};

export const createCoporateTrainingPlan = async (params) => {
  const res = await post("/coporate_training/plan", params);
  return res;
};

export const delCoporateTrainingPlan = async (id: number) => {
  const res = await del(`/coporate_training/plan/${id}`);
  return res;
};

export const delCoporateTrainingPlans = async (ids) => {
  const res = await del(`/coporate_training/plan`, ids);
  return res;
};

export const updateCoporateTrainingPlan = async (params) => {
  const res = await put(`/coporate_training/plan/${params.id}`, params?.values);
  return res;
};

export const coporateTrainingPlanApis: CommonApis = {
  entity: "CoporateTrainingPlan",
  query: getCoporateTrainingPlanList,
  create: createCoporateTrainingPlan,
  remove: delCoporateTrainingPlan,
  removes: delCoporateTrainingPlans,
  update: updateCoporateTrainingPlan,
  get: getCoporateTrainingPlan,
};
