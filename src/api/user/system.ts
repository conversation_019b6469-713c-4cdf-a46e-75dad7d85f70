import { get, post, postFormData } from "api";

export const getCaptcha = async () => {
  const base_url = "/system/captcha";
  const res = await get(base_url, true);
  return res;
};

export type LoginParams = {
  captcha_answer: string;
  captcha_key: string;
  password: string;
  username: string;
};

export const postLogin = async (params: LoginParams) => {
  const base_url = "/system/login";
  const res = await post(base_url, params, true);
  return res;
};

export const getDingTalkAuth = async (params: any) => {
  const base_url = `/third/ding_talk/auth?authCode=${params}`;
  const res = await get(base_url);
  return res;
};

export type ReNewParams = {
  refreshToken: string;
};
export const postReNew = async (params: ReNewParams) => {
  const base_url = "/system/renew";
  const res = await post(base_url, params, true);
  return res;
};

export type ForbidUserParams = {
  uid: number;
};
// 封禁用户
export const forbidUser = async (id: number) => {
  const res = await post(`/system/user/${id}/forbid`);
  return res;
};

export type ResetUserParams = {
  uid: number;
  values?: any;
};
// 重置用户密码
export const resetUser = async (params: ResetUserParams) => {
  const res = await post(`/system/user/${params.uid}/reset`, params.values);
  return res;
};

// 修改用户密码
export const changePassword = async (params) => {
  const res = await post("/system/user/change_password", params);
  return res;
};

// 激活用户
export const activeUser = async (params: ResetUserParams) => {
  const res = await post(`/system/user/${params.uid}/active`, params?.values);
  return res;
};

export const batchActiveUser = async (params) => {
  const res = await post("/system/user/active", params);
  return res;
};

export const fileUpload = async (params) => {
  const res = await post("/system/upload", params);
  return res;
};

export const formDataUpload = async (params) => {
  const formData = new FormData();

  Object.keys(params).forEach((key) => {
    formData.append(key, params[key]);
  });

  const res = await postFormData("/system/upload", formData);
  return res;
};

export const getSystemInfo = async () => {
  const base_url = "/system/info";
  const res = await get(base_url);
  return res;
};
