import { get, post } from "api";
import { CommonApis } from "types";

export const postCoperateTrainingInfo = async (id: number) => {
  const base_url = `/user/${id}/coperate_training/info`;
  const res = await post(base_url, {});
  return res;
};

export const postTrainingRecord = async (id: number, values) => {
  console.log("postTrainingRecord", id, values);
  const base_url = `/user/${id}/coperate_training/training_record`;
  const res = await post(base_url, values);
  return res;
};

export const getTrainingRecord = async (
  id: number,
  trainingRecordId: string
) => {
  const base_url = `/user/${id}/coperate_training/training_record/${trainingRecordId}`;
  const res = await get(base_url);
  return res;
};

export const getTrainingCourseRecord = async (
  id: number,
  trainingCourseRecordId: string
) => {
  const base_url = `/user/${id}/coperate_training/training_course_record/${trainingCourseRecordId}`;
  const res = await get(base_url);
  return res;
};

export const postTrainingCoursewareRecord = async (
  id: number,
  trainingCoursewareRecordId: string,
  studyBeginTime: string,
  studyEndTime: string
) => {
  const base_url = `/user/${id}/coperate_training/training_courseware_record/${trainingCoursewareRecordId}/view`;
  const res = await post(base_url, {
    studyBeginTime: studyBeginTime,
    studyEndTime: studyEndTime,
  });
  return res;
};

//v1/coporate_training/paper/{id}

export const getPaperDemo = async (id: number) => {
  const base_url = `/coporate_training/paper/${id}`;
  const res = await get(base_url);
  return res;
};

export const getPaperRecord = async (
  id: number,
  trainingCourseRecordId: number
) => {
  const base_url = `/user/${id}/coperate_training/training_course_record/${trainingCourseRecordId}/paper_record`;
  const res = await get(base_url);
  return res;
};

export const postPaperRecord = async (params: any) => {
  const base_url = `/user/${params.id}/coperate_training/training_paper_record/${params.trainingPaperRecordId}`;
  const res = await post(base_url, params.values);
  return res;
};

export const postCertificate = async (params: any) => {
  const base_url = `/user/${params.id}/coperate_training/certificate`;
  const res = await post(base_url, params.values);
  return res;
};

export const postStudyRecord = async (params: any) => {
  const base_url = `/user/${params.id}/coperate_training/study_record`;
  const res = await post(base_url, params.values);
  return res;
};

export const postExamRecord = async (params: any) => {
  const base_url = `/user/${params.id}/coperate_training/exam_record`;
  const res = await post(base_url, params.values);
  return res;
};

export const postCourseView = async (params: any) => {
  const base_url = `/user/${params.id}/coperate_training/training_courseware_record/${params.trainingCoursewareRecordId}/view`;
  const res = await post(base_url, params.values);
  return res;
};

export const getViewTime = async (uid: any, tid: any) => {
  const base_url = `/user/${uid}/coperate_training/training_courseware_record/${tid}/view_time`;
  const res = await get(base_url);
  return res;
};

export const getPaperRecordDetail = async (paperRecordId: number) => {
  const base_url = `/coporate_training/exam_record/${paperRecordId}`;
  const res = await get(base_url);
  return res;
};

export const coporateTrainingMyStudyRecordApis: CommonApis = {
  entity: "CoporateTrainingMyStudyRecord",
  query: postStudyRecord,
  create: postStudyRecord,
  update: postStudyRecord,
  remove: postStudyRecord,
  removes: postStudyRecord,
  get: postStudyRecord,
};

export const coporateTrainingMyExamRecordApis: CommonApis = {
  entity: "CoporateTrainingMyExamRecord",
  query: postExamRecord,
  create: postExamRecord,
  update: postExamRecord,
  remove: postExamRecord,
  removes: postExamRecord,
  get: postExamRecord,
};
