import { get, post } from "@api";

export type MessageParams = {
  filter?: MessageFilter;
  pageNumber: number;
  pageSize: number;
  query?: string;
};

export type MessageMaxId = {
  maxId: number;
};

export const getUnreadMsgCount = async (id: number) => {
  const base_url = `/user/${id}/message/stat`;
  const res = await get(base_url);
  return res;
};

export const getMessages = async (id: number, params: MessageParams) => {
  const base_url = `/user/${id}/message/work_message/search`;
  const res = await post(base_url, params);
  return res;
};

export const getMessage = async (id: number) => {
  const base_url = `/message/work_message/${id}`;
  const res = await get(base_url);
  return res;
};

export const readMessages = async (params: number[]) => {
  const base_url = `/message/work_message/read`;
  const res = await post(base_url, params);
  return res;
};

export const readAllMessages = async (params: MessageMaxId) => {
  const base_url = `/message/work_message/read/all`;
  const res = await post(base_url, params);
  return res;
};

export const getAlarmMessages = async (id: number, params: MessageParams) => {
  const base_url = `/user/${id}/message/alarm_message/search`;
  const res = await post(base_url, params);
  return res;
};

export const getAlarmMessage = async (id: number) => {
  const base_url = `/message/alarm_message/${id}`;
  const res = await get(base_url);
  return res;
};

export const readAlarmMessages = async (params: number[]) => {
  const base_url = `/message/alarm_message/read`;
  const res = await post(base_url, params);
  return res;
};

export const readAllAlarmMessages = async (params: MessageMaxId) => {
  const base_url = `/message/alarm_message/read/all`;
  const res = await post(base_url, params);
  return res;
};

export const getNoticeMessages = async (id: number, params: MessageParams) => {
  const base_url = `/user/${id}/message/notice_message/search`;
  const res = await post(base_url, params);
  return res;
};

export const getNoticeMessage = async (id: number) => {
  const base_url = `/message/notice_message/${id}`;
  const res = await get(base_url);
  return res;
};

export const readNoticeMessages = async (params: number[]) => {
  const base_url = `/message/notice_message/read`;
  const res = await post(base_url, params);
  return res;
};

export const readAllNoticeMessages = async (params: MessageMaxId) => {
  const base_url = `/message/notice_message/read/all`;
  const res = await post(base_url, params);
  return res;
};
