import { get } from "api";

export const getBigScreenAnnouncement = async () => {
  const base_url = "/vision_dashboard/information/announcement";
  const res = await get(base_url);
  return res;
};

export const getDepartmentPersonStat = async () => {
  return await get("/vision_dashboard/information/department_person_stat");
};

export const getTrainingStat = async () => {
  return await get("/vision_dashboard/information/training_stat");
};

export const getCertificateStat = async () => {
  return await get("/vision_dashboard/information/people_certificate_stat");
};

export const getBigScreenIntroduction = async () => {
  return await get("/vision_dashboard/information/introduction");
};
