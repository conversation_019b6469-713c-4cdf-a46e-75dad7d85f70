import { get } from "api";

// 人员车辆情况统计
export const getPeopleCarStat = async () => {
  const base_url = "/vision_dashboard/location/people_car_stat";
  const res = await get(base_url);
  return res;
};

// 人员车辆情况_查询承包商员工列表
export const getCurrentContractorEmployee = async () => {
  const base_url = "/vision_dashboard/location/current_contractor_employee";
  const res = await get(base_url);
  return res;
};
// 人员车辆情况_查询访客列表
export const getCurrentVisitor = async () => {
  const base_url = "/vision_dashboard/location/current_visitor";
  const res = await get(base_url);
  return res;
};

// 人员车辆情况_查询员工列表
export const getCurrentEmployee = async () => {
  const base_url = "/vision_dashboard/location/current_employee";
  const res = await get(base_url);
  return res;
};

// 人员车辆情况_查询车辆列表
export const getCurrentCar = async () => {
  const base_url = "/vision_dashboard/location/current_car";
  const res = await get(base_url);
  return res;
};

// 告警监测
export const getAlarmStat = async () => {
  const base_url = "/vision_dashboard/location/alarm_stat";
  const res = await get(base_url);
  return res;
};

//区域统计
export const getAreaStat = async () => {
  const base_url = "/vision_dashboard/location/area_stat";
  const res = await get(base_url);
  return res;
};

//区域统计_获取区域人员列表
export const getAreaPeople = async () => {
  const base_url = "/vision_dashboard/location/area/people";
  const res = await get(base_url);
  return res;
};
// 部门统计
export const getDepartmentStat = async () => {
  const base_url = "/vision_dashboard/location/department_stat";
  const res = await get(base_url);
  return res;
};
