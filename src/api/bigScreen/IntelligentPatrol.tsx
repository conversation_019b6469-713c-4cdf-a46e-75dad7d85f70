import { get, post } from "api";

// 已巡检统计
export const getRecordBasicStat = async (params) => {
  const base_url = "/vision_dashboard/inspection/record_basic_stat";
  const res = await post(base_url, params);
  return res;
};

// 巡检趋势统计
export const getRecordStat = async (params) => {
  const base_url = "/vision_dashboard/inspection/record_stat";
  const res = await post(base_url, params);
  return res;
};

// 巡检数据统计
export const getBasicStat = async () => {
  const base_url = "/vision_dashboard/inspection/basic_stat";
  const res = await get(base_url);
  return res;
};

// 巡检任务
export const getBigScreenTask = async () => {
  const base_url = "/vision_dashboard/inspection/task";
  const res = await get(base_url);
  return res;
};
