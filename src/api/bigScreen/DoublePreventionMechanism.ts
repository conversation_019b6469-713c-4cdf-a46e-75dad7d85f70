import { get, post } from "@api";

export const getDangerCheckRectify = async (params) => {
  const base_url = "/vision_dashboard/double_guard/danger_check_rectify";
  const res = await post(base_url, params);
  return res;
};

export const getRiskDistribution = async () => {
  const base_url = "/vision_dashboard/double_guard/risk_distribution";
  const res = await get(base_url);
  
  return res;
};

export const getDangerRisk = async (params) => {
  const base_url =
    "/vision_dashboard/double_guard/danger_risk_object_distribution";
  const res = await post(base_url, params);
  return res;
};

export const getCommitmentAnnouncement = async (params) => {
  const base_url = "/vision_dashboard/commitment_announcement";
  const res = await get(base_url, params);
  return res;
};

export const getCheckTaskInfo = async (params) => {
  const base_url = "/double_guard/check_task_info";
  const res = await post(base_url, params);
  return res;
};

export const getDangerInfo = async (params) => {
  const base_url = "/double_guard/danger_info";
  const res = await post(base_url, params);
  return res;
};


export const getBbWorkStat  = async (params) => {
  const base_url = "/double_guard/bb_work_stat";
  const res = await post(base_url, params);
  return res;
};

export const getGcWorkStat  = async () => {
  const base_url = "/double_guard/gc_work_stat";
  const res = await post(base_url, {});
  return res;
};

export const getDangerNumStat = async (params) => {
  const base_url = "/double_guard/danger_num_stat";
  const res = await post(base_url, params);
  return res;
};


export const getDangerRectifyRateStat = async (params) => {
  const base_url = "/double_guard/danger_rectify_rate_stat";
  const res = await post(base_url, params);
  return res;
};

