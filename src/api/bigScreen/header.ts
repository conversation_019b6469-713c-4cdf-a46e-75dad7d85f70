import { get } from "@api";

export const getBigScreenMenu = async () => {
  const base_url = "/vision_dashboard/menu";
  const res = await get(base_url);
  return res;
};

export const getSystemOperation = async () => {
  const base_url = "/vision_dashboard/operation";
  const res = await get(base_url);
  return res;
};

export const getModelCustomIconData = async (id: number) => {
  const base_url = `/vision_dashboard/operation/id/${id}`;
  const res = await get(base_url);
  return res;
};

export const getModelIconData = async (id: number) => {
  const base_url = `/vision_dashboard/operation/type/${id}`;
  const res = await get(base_url);
  return res;
};
