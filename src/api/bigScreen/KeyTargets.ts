import { get, post } from "@api";

export const getDangerSource = async () => {
  const base_url = "/vision_dashboard/major_hazard";
  const res = await get(base_url);
  return res;
};

export const getSensorAlarm = async () => {
  const base_url = "/vision_dashboard/major_hazard/sensor_alarm";
  const res = await get(base_url);
  return res;
};

export const getSensorAlarmStat = async (params) => {
  const base_url = "/vision_dashboard/major_hazard/sensor_alarm_stat";
  const res = await post(base_url, params);
  return res;
};

export const getDashboardDutyInformation = async () => {
  const base_url =
    "/vision_dashboard/emergency_management/duty_information/current";
  const res = await get(base_url);
  return res;
};
