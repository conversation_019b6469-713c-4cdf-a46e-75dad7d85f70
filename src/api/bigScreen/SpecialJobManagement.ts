import { get, post } from "@api";

export const getJobTicket = async (params) => {
  const base_url = "/vision_dashboard/special_work/job_category_job_slice";
  const res = await post(base_url, params);
  return res;
};

export const getWorkingArea = async (params) => {
  const base_url = "/vision_dashboard/special_work/area_job_slice";
  const res = await post(base_url, params);
  return res;
};

export const getCurrentJobSlice = async () => {
  const base_url = "/vision_dashboard/special_work/job_slice/current";
  const res = await get(base_url);
  return res;
};
