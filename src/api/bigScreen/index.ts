export * from "./AiAnalyse";
export * from "./BasicInformation";
export * from "./DoublePreventionMechanism";
export * from "./IntelligentPatrol";
export * from "./KeyTargets";
export * from "./PersonnelLocation";
export * from "./SpecialJobManagement";
export * from "./header";
import { del, get, post, put } from "api";

export const geiBigScreenConfig = async () => {
  const base_url = "/vision_dashboard/config";
  const res = await get(base_url);
  return res;
};

export const updateLoginRenewal = async (params) => {
  const base_url = "/system/renew";
  const res = await post(base_url, params);
  return res;
};

export const getCorporateInformation = async () => {
  const base_url = "/vision_dashboard/introduction";
  const res = await get(base_url);
  return res;
};

export const getDashboardNewAlarm = async () => {
  const base_url = "/vision_dashboard/new_alarm";
  const res = await get(base_url);
  return res;
};

export const getDashboardWeather = async () => {
  const base_url = "/vision_dashboard/weather";
  const res = await get(base_url);
  return res;
};

export const set3dConfig = async (params) => {
  const base_url = "/system/om_config/fix_3d";
  const res = await post(base_url, params);
  return res;
};

export const getSensorHistoricalData = async (params) => {
  const base_url = "/major_hazard/sensor/value/history";
  const res = await post(base_url, params);
  return res;
};

export const createBigScreenTheme = async (params) => {
  const base_url = "/vision_dashboard/style";
  const res = await post(base_url, params);
  return res;
};

export const updateBigScreenTheme = async (id, params) => {
  const base_url = `/vision_dashboard/style/${id}`;
  const res = await put(base_url, params);
  return res;
};

export const getBigScreenTheme = async (id) => {
  const base_url = `/vision_dashboard/style/${id}`;
  const res = await get(base_url);
  return res;
};

export const getBigScreenThemeList = async () => {
  const base_url = `/vision_dashboard/style`;
  const res = await get(base_url);
  return res;
};

export const deleteBigScreenTheme = async (id) => {
  const base_url = `/vision_dashboard/style/${id}`;
  const res = await del(base_url);
  return res;
};
