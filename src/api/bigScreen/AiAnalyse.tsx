import { get, post } from "api";

// 报警趋势统计
export const getAiAlarmStat = async (params) => {
  const base_url = "/vision_dashboard/ai/alarm_stat";
  const res = await post(base_url, params);
  return res;
};

// 当前AI检测报警
export const getAiAlarm = async () => {
  const base_url = "/vision_dashboard/ai/alarm";
  const res = await get(base_url);
  return res;
};

// 检测报警信息统计
export const getAlarmInfoStat = async (params) => {
  const base_url = "/vision_dashboard/ai/alarm_info_stat";
  const res = await post(base_url, params);
  return res;
};
// 报警信息趋势统计
export const getAlarmDetailStat = async (params) => {
  const base_url = "/vision_dashboard/ai/alarm_detail_stat";
  const res = await post(base_url, params);
  return res;
};
