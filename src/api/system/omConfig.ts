import { get, post } from "api";
import { CommonApis } from "types";

// /v1/system/om_config/search

export const getPlatformConfig = async () => {
  const base_url = `/system/platform_config`;
  return await get(base_url);
};

export const getOmConfig = async () => {
  const base_url = `/system/om_config/current`;
  return await get(base_url);
};

export const createOmConfig = async (params) => {
  const res = await post("/system/om_config", params);
  return res;
};

export const omConfigApis: CommonApis = {
  entity: "OmConfig",
  // query: getOmConfigList,
  create: createOmConfig,
  // remove: delOmConfig,
  // removes: delOmConfigs,
  // update: updateOmConfig,
  get: getOmConfig,
};
