import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/system/notice_push_config/search

export const getNoticePushConfigList = async (params) => {
  const base_url = "/system/notice_push_config/search";
  const res = await post(base_url, params);
  return res;
};

export const getNoticePushConfig = async (id) => {
  const base_url = `/system/notice_push_config/${id}`;
  return await get(base_url);
};

export const createNoticePushConfig = async (params) => {
  const res = await post("/system/notice_push_config", params);
  return res;
};

export const delNoticePushConfig = async (id: number) => {
  const res = await del(`/system/notice_push_config/${id}`);
  return res;
};

export const delNoticePushConfigs = async (ids) => {
  const res = await del(`/system/notice_push_config`, ids);
  return res;
};

export const updateNoticePushConfig = async (params) => {
  const res = await put(
    `/system/notice_push_config/${params.id}`,
    params?.values,
  );
  return res;
};

export const noticePushConfigApis: CommonApis = {
  entity: "NoticePushConfig",
  query: getNoticePushConfigList,
  create: createNoticePushConfig,
  remove: delNoticePushConfig,
  removes: delNoticePushConfigs,
  update: updateNoticePushConfig,
  get: getNoticePushConfig,
};
