import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/system/alarm_push_config/search

export const getAlarmPushConfigList = async (params) => {
  const base_url = "/system/alarm_push_config/search";
  const res = await post(base_url, params);
  return res;
};

export const getAlarmPushConfig = async (id) => {
  const base_url = `/system/alarm_push_config/${id}`;
  return await get(base_url);
};

export const createAlarmPushConfig = async (params) => {
  const res = await post("/system/alarm_push_config", params);
  return res;
};

export const delAlarmPushConfig = async (id: number) => {
  const res = await del(`/system/alarm_push_config/${id}`);
  return res;
};

export const delAlarmPushConfigs = async (ids) => {
  const res = await del(`/system/alarm_push_config`, ids);
  return res;
};

export const updateAlarmPushConfig = async (params) => {
  const res = await put(
    `/system/alarm_push_config/${params.id}`,
    params?.values,
  );
  return res;
};

export const alarmPushConfigApis: CommonApis = {
  entity: "AlarmPushConfig",
  query: getAlarmPushConfigList,
  create: createAlarmPushConfig,
  remove: delAlarmPushConfig,
  removes: delAlarmPushConfigs,
  update: updateAlarmPushConfig,
  get: getAlarmPushConfig,
};
