import { get, post, put } from "api/";

export const getHiddenSettings = async () => {
  const res = await get("/system/hidden_config/current");
  return res;
};

export const setHiddenSettings = async (params) => {
  const res = await post("/system/hidden_config", params);
  return res;
};

export const getPfLogin = async (params) => {
  const res = await post("/system/pf_login", params);
  return res;
};

export const getThirdLoginSettings = async () => {
  const res = await get("/third/login_config/current");
  return res;
};

export const setThirdLoginSettings = async (params) => {
  const res = await post("/third/login_config", params);
  return res;
};

export const getDoubleGuardHiddenSettings = async () => {
  const res = await get("/double_guard/report_config/current");
  return res;
};

export const setDoubleGuardHiddenSettings = async (params) => {
  const res = await put("/double_guard/report_config", params);
  return res;
};

export const getSpecialWorkHiddenSettings = async () => {
  const res = await get("/special_work/report_config/current");
  return res;
};

export const setSpecialWorkHiddenSettings = async (params) => {
  const res = await put("/special_work/report_config", params);
  return res;
};

export const getMajorHazardReportHiddenSettings = async () => {
  const res = await get("/major_hazard/report_config/current");
  return res;
};

export const setMajorHazardReportHiddenSettings = async (params) => {
  const res = await put("/major_hazard/report_config", params);
  return res;
};

export const getEquipmentReportHiddenSettings = async () => {
  const res = await get("/equipment_management/report_config/current");
  return res;
};

export const setEquipmentReportHiddenSettings = async (params) => {
  const res = await put("/equipment_management/report_config", params);
  return res;
};

export const getApkSettings = async () => {
  const res = await get("/system/apk");
  return res;
};
