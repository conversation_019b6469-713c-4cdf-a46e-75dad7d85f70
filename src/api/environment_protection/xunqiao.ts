import { get } from "../request";

// 已有的类型定义 (EnvironmentProtectionMenuItem, EnvironmentProtectionSubMenuItem)
export interface EnvironmentProtectionSubMenuItem {
  id: number;
  name: string;
  // 可能还有其他字段，根据实际情况添加
}

export interface EnvironmentProtectionMenuItem {
  name: string;
  subItemList: EnvironmentProtectionSubMenuItem[];
  // 可能还有其他字段
}

export const getEnvironmentProtectionMenu = (): Promise<
  ApiResponse<{ menuList: EnvironmentProtectionMenuItem[] }>
> => {
  return get("/environment_protection/menu");
};

// 新增：环保统计数据相关类型
export interface StatPoint {
  statTime: string;
  statValue: number | null;
}

export interface EnvironmentalProtectionStatItem {
  id: number;
  name: string;
  unit: string;
  currentValue: number;
  statList: StatPoint[];
}

export interface EnvironmentalProtectionStatData {
  itemList: EnvironmentalProtectionStatItem[];
}

// 新增：根据ID获取环保统计数据的API函数
export const getEnvironmentProtectionStatById = (
  id: number
): Promise<ApiResponse<EnvironmentalProtectionStatData>> => {
  return get(`/environment_protection/${id}/stat`);
};
