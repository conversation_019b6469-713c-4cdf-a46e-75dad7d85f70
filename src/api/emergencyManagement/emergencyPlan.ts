import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/emergency_management/emergency_plan/search

export const getEmergencyPlanList = async (params) => {
  const base_url = "/emergency_management/emergency_plan/search";
  const res = await post(base_url, params);
  return res;
};

export const getEmergencyPlan = async (id) => {
  const base_url = `/emergency_management/emergency_plan/${id}`;
  return await get(base_url);
};

export const createEmergencyPlan = async (params) => {
  const res = await post("/emergency_management/emergency_plan", params);
  return res;
};

export const delEmergencyPlan = async (id: number) => {
  const res = await del(`/emergency_management/emergency_plan/${id}`);
  return res;
};

export const delEmergencyPlans = async (ids) => {
  const res = await del(`/emergency_management/emergency_plan`, ids);
  return res;
};

export const updateEmergencyPlan = async (params) => {
  const res = await put(
    `/emergency_management/emergency_plan/${params.id}`,
    params?.values,
  );
  return res;
};

export const emergencyPlanApis: CommonApis = {
  entity: "EmergencyPlan",
  query: getEmergencyPlanList,
  create: createEmergencyPlan,
  remove: delEmergencyPlan,
  removes: delEmergencyPlans,
  update: updateEmergencyPlan,
  get: getEmergencyPlan,
};
