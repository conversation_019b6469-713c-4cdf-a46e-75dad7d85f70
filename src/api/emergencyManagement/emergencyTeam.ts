import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/emergency_management/emergency_team/search

export const getEmergencyTeamList = async (params) => {
  const base_url = "/emergency_management/emergency_team/search";
  const res = await post(base_url, params);
  return res;
};

export const getEmergencyTeam = async (id) => {
  const base_url = `/emergency_management/emergency_team/${id}`;
  return await get(base_url);
};

export const createEmergencyTeam = async (params) => {
  const res = await post("/emergency_management/emergency_team", params);
  return res;
};

export const delEmergencyTeam = async (id: number) => {
  const res = await del(`/emergency_management/emergency_team/${id}`);
  return res;
};

export const delEmergencyTeams = async (ids) => {
  const res = await del(`/emergency_management/emergency_team`, ids);
  return res;
};

export const updateEmergencyTeam = async (params) => {
  const res = await put(
    `/emergency_management/emergency_team/${params.id}`,
    params?.values,
  );
  return res;
};

export const emergencyTeamApis: CommonApis = {
  entity: "EmergencyTeam",
  query: getEmergencyTeamList,
  create: createEmergencyTeam,
  remove: delEmergencyTeam,
  removes: delEmergencyTeams,
  update: updateEmergencyTeam,
  get: getEmergencyTeam,
};
