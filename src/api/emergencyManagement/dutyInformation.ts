import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/emergency_management/duty_information/search

export const getDutyInformationList = async (params) => {
  const base_url = "/emergency_management/duty_information/search";
  const res = await post(base_url, params);
  return res;
};

export const getDutyInformation = async (id) => {
  const base_url = `/emergency_management/duty_information/${id}`;
  return await get(base_url);
};

export const createDutyInformation = async (params) => {
  const res = await post("/emergency_management/duty_information", params);
  return res;
};

export const delDutyInformation = async (id: number) => {
  const res = await del(`/emergency_management/duty_information/${id}`);
  return res;
};

export const delDutyInformations = async (ids) => {
  const res = await del(`/emergency_management/duty_information`, ids);
  return res;
};

export const updateDutyInformation = async (params) => {
  const res = await put(
    `/emergency_management/duty_information/${params.id}`,
    params?.values,
  );
  return res;
};

export const dutyInformationApis: CommonApis = {
  entity: "DutyInformation",
  query: getDutyInformationList,
  create: createDutyInformation,
  remove: delDutyInformation,
  removes: delDutyInformations,
  update: updateDutyInformation,
  get: getDutyInformation,
};
