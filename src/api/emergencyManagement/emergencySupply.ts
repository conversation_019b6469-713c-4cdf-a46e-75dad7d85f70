import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/emergency_management/emergency_supply/search

export const getEmergencySupplyList = async (params) => {
  const base_url = "/emergency_management/emergency_supply/search";
  const res = await post(base_url, params);
  return res;
};

export const getEmergencySupply = async (id) => {
  const base_url = `/emergency_management/emergency_supply/${id}`;
  return await get(base_url);
};

export const createEmergencySupply = async (params) => {
  const res = await post("/emergency_management/emergency_supply", params);
  return res;
};

export const delEmergencySupply = async (id: number) => {
  const res = await del(`/emergency_management/emergency_supply/${id}`);
  return res;
};

export const delEmergencySupplys = async (ids) => {
  const res = await del(`/emergency_management/emergency_supply`, ids);
  return res;
};

export const updateEmergencySupply = async (params) => {
  const res = await put(
    `/emergency_management/emergency_supply/${params.id}`,
    params?.values,
  );
  return res;
};

export const emergencySupplyApis: CommonApis = {
  entity: "EmergencySupply",
  query: getEmergencySupplyList,
  create: createEmergencySupply,
  remove: delEmergencySupply,
  removes: delEmergencySupplys,
  update: updateEmergencySupply,
  get: getEmergencySupply,
};
