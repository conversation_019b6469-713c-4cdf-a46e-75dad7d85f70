import { get, post } from "../request"; // 确保 @api 指向项目中的请求工具函数

/**
 * 二级菜单项接口定义
 */
export interface EnergySubMenuItem {
  id: number;
  name: string;
  displayType: number; // 1: 图表页面, 2: 多指标页面, 3: 分组指标页面, 4: 日报表页面
}

/**
 * 一级菜单项接口定义
 */
export interface EnergyMenuItem {
  name: string; // 一级菜单名称
  subItemList: EnergySubMenuItem[];
}

/**
 * API 返回的 data 结构中 menuList 的类型定义
 */
export interface EnergyMenuData {
  menuList: EnergyMenuItem[];
}

/**
 * 动态菜单查询 API 的完整响应类型定义
 */
export interface EnergyMenuApiResponse {
  code: number;
  message: string;
  data: EnergyMenuData;
}

/**
 * @summary 动态菜单查询 (能源管理)
 * @description 获取能源管理的动态菜单。
 * @returns {Promise<EnergyMenuApiResponse>} 包含菜单列表的API响应
 */
export const getEnergyManagementMenu =
  async (): Promise<EnergyMenuApiResponse> => {
    const apiUrl = "/energy_management/menu";
    // API文档中提到的 vdebug header 通常由底层的请求库统一处理或在需要时单独配置。
    // 此处假设 `get` 函数或全局请求配置会处理必要的头部信息。
    const response = await get(apiUrl);
    return response as EnergyMenuApiResponse; // 将响应断言为定义的类型
  };

// 新增：图表页面基础信息接口返回的 data 部分
export interface ChartBasicInfoData {
  unit: string; // 单位，例如 "m³"
  id: number; // 当前页面的ID，与传入的id一致
  sensorId: number; // 传感器ID
  name: string; // 监控对象的名称，例如 "氮气", "蒸汽"
  todayUse: number; // 今日用量
  yestodayUse: number; // 昨日用量
  currentMonthUse: number; // 本月用量
  lastMonthUse: number; // 上月用量
  currentYearUse: number; // 今年用量
  lastYearUse: number; // 去年用量
}

// 新增：图表页面基础信息查询 API 的完整响应类型定义
export interface ChartBasicInfoApiResponse {
  code: number;
  message: string;
  data: ChartBasicInfoData;
}

/**
 * @summary 获取图表页面的基础信息 (能源管理)
 * @description 根据ID获取图表页面的概览统计数据。
 * @param id 页面/监控对象的ID
 * @returns {Promise<ChartBasicInfoApiResponse>} 包含基础信息的API响应
 */
export const getChartBasicInfo = async (
  id: string | number
): Promise<ChartBasicInfoApiResponse> => {
  const apiUrl = `/energy_management/display_stat/${id}/basic`;
  const response = await get(apiUrl);
  return response as ChartBasicInfoApiResponse;
};

// --- 新增：图表页面-时间周期查询 (用能耗趋势图) ---

// 请求体类型
export interface ChartRangeDataRequestBody {
  type: 1 | 2 | 3; // 1: 今日, 2: 本月, 3: 全年
}

// statList 中每个对象的类型
export interface StatItem {
  statTime: string; // 时间点或日期，例如 "00:00", "01-01"
  use: number; // 对应的用量
}

// API 返回的 data 部分的类型
export interface ChartRangeData {
  statList: StatItem[];
}

// 完整的API响应类型
export interface ChartRangeApiResponse {
  code: number;
  message: string;
  data: ChartRangeData;
}

/**
 * @summary 获取图表页面的时间周期统计数据 (能源管理 - 用能耗趋势图)
 * @description 根据ID和时间类型（今日、本月、全年）获取图表的统计数据。
 * @param id 页面/监控对象的ID
 * @param requestBody 请求体，包含 type
 * @returns {Promise<ChartRangeApiResponse>} 包含时间周期统计数据的API响应
 */
export const getChartRangeData = async (
  id: string | number,
  requestBody: ChartRangeDataRequestBody
): Promise<ChartRangeApiResponse> => {
  const apiUrl = `/energy_management/display_stat/${id}/range`;
  const response = await post(apiUrl, requestBody);
  return response as ChartRangeApiResponse;
};

// --- 新增：图表页面-按日查询 (当日用能趋势图) ---

// 请求体类型
export interface ChartDailyDataRequestBody {
  queryDate: string; // RFC3339 格式的日期字符串, e.g., "2023-08-10T00:00:00Z"
}

// API 返回的 data 部分的类型 (复用 ChartRangeData 和 StatItem)
// export interface ChartDailyData {
//   statList: StatItem[]; // statTime 将是小时 "HH:mm", use 是用量
// }
// 实际上，按日查询的响应数据结构与按时间周期查询的 data 结构一致，
// 都是 { statList: StatItem[] }，所以我们可以复用 ChartRangeData 和 ChartRangeApiResponse
// 如果响应结构有细微差别，则需要单独定义 ChartDailyData 和 ChartDailyApiResponse

/**
 * @summary 获取图表页面的按日统计数据 (能源管理 - 当日用能趋势图)
 * @description 根据ID和指定日期获取图表的统计数据。
 * @param id 页面/监控对象的ID
 * @param requestBody 请求体，包含 queryDate
 * @returns {Promise<ChartRangeApiResponse>} 包含按日统计数据的API响应 (复用ChartRangeApiResponse)
 */
export const getChartDailyData = async (
  id: string | number,
  requestBody: ChartDailyDataRequestBody
): Promise<ChartRangeApiResponse> => {
  // 复用 ChartRangeApiResponse 因为 data 结构相同
  const apiUrl = `/energy_management/display_stat/${id}/daily`;
  const response = await post(apiUrl, requestBody);
  return response as ChartRangeApiResponse; // 断言为 ChartRangeApiResponse
};

export const getChartDailyDataRaw = async (
  id: string | number,
  requestBody: ChartDailyDataRequestBody
): Promise<ChartRangeApiResponse> => {
  // 复用 ChartRangeApiResponse 因为 data 结构相同
  const apiUrl = `/energy_management/display_stat/${id}/daily_raw`;
  const response = await post(apiUrl, requestBody);
  return response as ChartRangeApiResponse; // 断言为 ChartRangeApiResponse
};

// --- 结束新增 ---

// 请确保您的 get 和 post 函数以及 ApiResponse 类型已正确导入或定义
// 例如:
// import { get, post, ApiResponse } from "../request"; // 假设您的请求工具在这里

// --- 新增：多指标页面-查询 ---

// 多指标数据项类型
export interface SensorInfo {
  id: number; // 传感器/指标ID
  name: string; // 传感器/指标名称
  unit: string; // 单位
  sampleValue: number; // 采样值
}

// 多指标API响应的 data 部分类型
export interface MultiIndicatorData {
  sensorList: SensorInfo[];
}

// 多指标API的完整响应类型 (如果您的 ApiResponse 是通用的，可以复用)
// export interface MultiIndicatorApiResponse extends ApiResponse<MultiIndicatorData> {}
// 这里我们假设 ApiResponse<T> 是通用的，所以 getMultiIndicatorData 会返回 Promise<ApiResponse<MultiIndicatorData>>

/**
 * @summary 获取多指标页面的统计数据 (能源管理)
 * @description 根据页面ID获取多个指标的最新采样值。
 * @param pageId 多指标页面的ID
 * @returns {Promise<ApiResponse<MultiIndicatorData>>} 包含多指标数据的API响应
 */
export const getMultiIndicatorData = async (
  pageId: string | number
): Promise<ApiResponse<MultiIndicatorData>> => {
  // API URL 不包含 /v1 前缀
  const apiUrl = `/energy_management/display_batch/${pageId}/stat`;
  // 假设 get 函数返回 Promise<ApiResponse<T>>
  const response = await get<ApiResponse<MultiIndicatorData>>(apiUrl);
  return response;
};

// --- 结束新增 ---

// 假设您的 get 函数和 ApiResponse 类型已正确导入或定义
// 例如:
// import { get, ApiResponse } from "../request"; // 调整此路径

// --- 新增：分组指标页面-查询 ---

// 单个传感器/指标信息
export interface GroupedSensorInfo {
  id: number;
  name: string;
  unit: string;
  sampleValue: number;
}

// 单个分组数据
export interface GroupDataItem {
  groupName: string;
  sensorList: GroupedSensorInfo[];
}

// 分组指标API响应的 data 部分类型
export interface GroupedIndicatorData {
  groupList: GroupDataItem[];
}

// 完整API响应类型 (如果您的 ApiResponse 是通用的，可以复用)
// export interface GroupedIndicatorApiResponse extends ApiResponse<GroupedIndicatorData> {}
// 这里我们假设 ApiResponse<T> 是通用的

/**
 * @summary 获取分组指标页面的统计数据 (能源管理)
 * @description 根据页面ID获取多个分组指标的最新采样值。
 * @param pageId 分组指标页面的ID
 * @returns {Promise<ApiResponse<GroupedIndicatorData>>} 包含分组指标数据的API响应
 */
export const getGroupedIndicatorData = async (
  pageId: string | number
): Promise<ApiResponse<GroupedIndicatorData>> => {
  // API URL 不包含 /v1 前缀
  const apiUrl = `/energy_management/display_group/${pageId}/stat`;
  // 假设 get 函数返回 Promise<ApiResponse<T>>
  const response = await get<ApiResponse<GroupedIndicatorData>>(apiUrl);
  return response;
};

// --- 结束新增 ---

// 如果文件末尾还有其他导出或代码，请确保此新增部分不会干扰它们
// 例如:
// export const getDailyReportHeader = async () => { ... }
export const getDailyReportHeader = async () => {
  console.debug("--------------------getDailyReportHeader-----------------");
  const apiUrl = "/energy_management/daily_report/header";
  const response = await get(apiUrl);
  return response;
};

export const getDailyReportData = async (params: any) => {
  const apiUrl = "/energy_management/daily_report/detail";
  const response = await post(apiUrl, params);
  return response;
};
