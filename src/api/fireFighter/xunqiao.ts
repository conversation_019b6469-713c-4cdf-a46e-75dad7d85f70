import { get } from "../request";

// 已有的类型定义 (EnvironmentProtectionMenuItem, EnvironmentProtectionSubMenuItem)
export interface FireProtectionSubMenuItem {
  id: number;
  name: string;
  // 可能还有其他字段，根据实际情况添加
}

export interface FireProtectionMenuItem {
  name: string;
  subItemList: FireProtectionSubMenuItem[];
  // 可能还有其他字段
}

export const getFireProtectionMenu = (): Promise<
  ApiResponse<{ menuList: FireProtectionMenuItem[] }>
> => {
  return get("/fire_protection/menu");
};

// 新增：环保统计数据相关类型
export interface StatPoint {
  statTime: string;
  statValue: number | null;
}

export interface FireProtectionStatItem {
  id: number;
  name: string;
  unit: string;
  currentValue: number;
  statList: StatPoint[];
}

export interface FireProtectionStatData {
  itemList: FireProtectionStatItem[];
}

// 新增：根据ID获取环保统计数据的API函数
export const getFireProtectionStatById = (
  id: number
): Promise<ApiResponse<FireProtectionStatData>> => {
  return get(`/fire_protection/${id}/stat`);
};
