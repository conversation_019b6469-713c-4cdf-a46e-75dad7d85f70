import { post, get, del, put } from "@api";

export const getSafetyMeasureList = async (params) => {
  const base_url = "/special_work/safety_measure/search";
  const res = await post(base_url, params);
  return res;
};

export const getSafetyMeasure = async (id) => {
  const base_url = `/special_work/safety_measure/${id}`;
  return await get(base_url);
};

// 新增
export const createSafetyMeasure = async (params) => {
  const res = await post("/special_work/safety_measure", params);
  return res;
};

// 删除单个项目
export const delSafetyMeasure = async (id: number) => {
  const res = await del(`/special_work/safety_measure/${id}`);
  return res;
};

// 批量删除
export const delSafetyMeasures = async (ids) => {
  const res = await del(`/special_work/safety_measure`, ids);
  return res;
};

// 修改
export const updateSafetyMeasure = async (params) => {
  const res = await put(
    `/special_work/safety_measure/${params.id}`,
    params?.values,
  );
  return res;
};
