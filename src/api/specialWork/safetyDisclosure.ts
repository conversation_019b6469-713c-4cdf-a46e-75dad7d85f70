import { post, get, del, put } from "@api";

export const getSafetyDisclosureList = async (params) => {
  const base_url = "/special_work/safety_disclosure/search";
  const res = await post(base_url, params);
  return res;
};

export const getSafetyDisclosure = async (id) => {
  const base_url = `/special_work/safety_disclosure/${id}`;
  return await get(base_url);
};

// 新增
export const createSafetyDisclosure = async (params) => {
  const res = await post("/special_work/safety_disclosure", params);
  return res;
};

// 删除单个项目
export const delSafetyDisclosure = async (id: number) => {
  const res = await del(`/special_work/safety_disclosure/${id}`);
  return res;
};

// 批量删除
export const delSafetyDisclosures = async (ids) => {
  const res = await del(`/special_work/safety_disclosure`, ids);
  return res;
};

// 修改
export const updateSafetyDisclosure = async (params) => {
  const res = await put(
    `/special_work/safety_disclosure/${params.id}`,
    params?.values,
  );
  return res;
};
