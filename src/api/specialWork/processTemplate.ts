import { post, get, del, put } from "@api";

export const getProcessTemplate = async (id) => {
  const base_url = `/special_work/js_template/${id}/process_template`;
  const res = await get(base_url);
  return res;
};

export const updateProcessTemplate = async (params) => {
  const base_url = `/special_work/js_template/${params.id}/process_template`;

  const res = await put(base_url, params.values);
  return await get(base_url);
};
