import { post, get, del, put } from "@api";

export const getFirstLevel = async () => {
  const base_url = "/special_work/job_category";
  const res = await get(base_url);
  return res;
};

export const getJobCategoryList = async (params) => {
  const base_url = "/special_work/job_category/search";
  const res = await post(base_url, params);
  return res;
};

export const getJobCategory = async (id) => {
  const base_url = `/special_work/job_category/${id}`;
  return await get(base_url);
};

// 新增
export const createJobCategory = async (params) => {
  const res = await post("/special_work/job_category", params);
  return res;
};

// 删除单个项目
export const delJobCategory = async (id: number) => {
  const res = await del(`/special_work/job_category/${id}`);
  return res;
};

// 批量删除
export const delJobCategorys = async (ids) => {
  const res = await del(`/special_work/job_category`, ids);
  return res;
};

// 修改
export const updateJobCategory = async (params) => {
  const res = await put(
    `/special_work/job_category/${params.id}`,
    params?.values,
  );
  return res;
};
