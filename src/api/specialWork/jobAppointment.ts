import { del, get, post, put } from "api";

// 获取作业预约编码
export const getJobAppoimentCode = async () => {
  const base_url = "/special_work/job_appoiment/code";
  const res = await get(base_url);
  return res;
};

// 作业预约审批
export const sendApprove = async (params) => {
  const base_url = `/special_work/job_appointment/${params.id}/approve_process/${params.approveProcessId}/approve`;
  const res = await post(base_url, params.values);
  return res;
};
/* export const sendApprove = async (params) => {
  const base_url = `/special_work/job_appointment/${params.id}/approve`;
  const res = await post(base_url, params.values);
  return res;
}; */

// 查询预约审批
export const getApprove = async (id) => {
  const base_url = `/special_work/job_appointment/${id}/approve`;
  const res = await get(base_url, {});
  return res;
};

export const getJobSlices = async (id) => {
  const base_url = `/special_work/job_appointment/${id}/job_slice`;
  const res = await get(base_url, {});
  return res;
};

export const getJobAppointmentList = async (params) => {
  const base_url = "/special_work/job_appointment/search";
  const res = await post(base_url, params);
  return res;
};

export const getAppointmentProgress = async (id) => {
  const base_url = `/special_work/job_appointment/${id}/progress`;
  const res = await get(base_url);
  return res;
};

export const getJobAppointment = async (id) => {
  const base_url = `/special_work/job_appointment/${id}`;
  return await get(base_url);
};

// 新增
export const createJobAppointment = async (params) => {
  const res = await post("/special_work/job_appointment", params);
  return res;
};

// 删除单个项目
export const delJobAppointment = async (id: number) => {
  const res = await del(`/special_work/job_appointment/${id}`);
  return res;
};

// 批量删除
export const delJobAppointments = async (ids) => {
  const res = await del(`/special_work/job_appointment`, ids);
  return res;
};

// 修改
export const updateJobAppointment = async (params) => {
  const res = await put(
    `/special_work/job_appointment/${params.id}`,
    params?.values,
  );
  return res;
};
