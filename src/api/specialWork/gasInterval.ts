import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/special_work/gas_interval/search

export const getGasIntervalList = async (params) => {
  const base_url = "/special_work/gas_interval/search";
  const res = await post(base_url, params);
  return res;
};

export const getGasInterval = async (id) => {
  const base_url = `/special_work/gas_interval/${id}`;
  return await get(base_url);
};

export const createGasInterval = async (params) => {
  const res = await post("/special_work/gas_interval", params);
  return res;
};

export const delGasInterval = async (id: number) => {
  const res = await del(`/special_work/gas_interval/${id}`);
  return res;
};

export const delGasIntervals = async (ids) => {
  const res = await del(`/special_work/gas_interval`, ids);
  return res;
};

export const updateGasInterval = async (params) => {
  const res = await put(
    `/special_work/gas_interval/${params.id}`,
    params?.values,
  );
  return res;
};

export const gasIntervalApis: CommonApis = {
  entity: "GasInterval",
  query: getGasIntervalList,
  create: createGasInterval,
  remove: delGasInterval,
  removes: delGasIntervals,
  update: updateGasInterval,
  get: getGasInterval,
};
