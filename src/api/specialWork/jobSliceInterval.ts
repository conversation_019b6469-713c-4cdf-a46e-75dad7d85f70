import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/special_work/job_slice_interval/search

export const getJobSliceIntervalList = async (params) => {
  const base_url = "/special_work/job_slice_interval/search";
  const res = await post(base_url, params);
  return res;
};

export const getJobSliceInterval = async (id) => {
  const base_url = `/special_work/job_slice_interval/${id}`;
  return await get(base_url);
};

export const createJobSliceInterval = async (params) => {
  const res = await post("/special_work/job_slice_interval", params);
  return res;
};

export const delJobSliceInterval = async (id: number) => {
  const res = await del(`/special_work/job_slice_interval/${id}`);
  return res;
};

export const delJobSliceIntervals = async (ids) => {
  const res = await del(`/special_work/job_slice_interval`, ids);
  return res;
};

export const updateJobSliceInterval = async (params) => {
  const res = await put(
    `/special_work/job_slice_interval/${params.id}`,
    params?.values,
  );
  return res;
};

export const jobSliceIntervalApis: CommonApis = {
  entity: "JobSliceInterval",
  query: getJobSliceIntervalList,
  create: createJobSliceInterval,
  remove: delJobSliceInterval,
  removes: delJobSliceIntervals,
  update: updateJobSliceInterval,
  get: getJobSliceInterval,
};
