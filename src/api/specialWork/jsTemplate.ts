import { del, get, post, put } from "api";

export const getJsTemplateList = async () => {
  const base_url = "/special_work/js_template";
  const res = await get(base_url);
  return res;
};

export const searchJsTemplateList = async (params) => {
  const base_url = "/special_work/js_template/search";
  const res = await post(base_url, params);
  return res;
};

export const getJsTemplateUser = async (id) => {
  const base_url = `/special_work/js_template/${id}`;
  return await get(base_url);
};

export const getJsTemplate = async (id) => {
  const base_url = `/special_work/js_template/${id}/form_template`;
  return await get(base_url);
};

// 修改作业票模板表单模板
export const updateFormTemplate = async (params) => {
  const res = await put(
    `/special_work/js_template/${params.id}/form_template`,
    params?.values,
  );
  return res;
};
// 修改
export const updateJsTemplate = async (params) => {
  const res = await put(
    `/special_work/js_template/${params.id}`,
    params?.values,
  );
  return res;
};
// 新增
export const createJsTemplate = async (params) => {
  const res = await post("/special_work/js_template", params);
  return res;
};

// 删除单个项目
export const delJsTemplate = async (id: number) => {
  const res = await del(`/special_work/js_template/${id}`);
  return res;
};

// 批量删除
export const delJsTemplates = async (ids) => {
  const res = await del(`/special_work/js_template`, ids);
  return res;
};

export const getPrintTemplate = async (id) => {
  const base_url = `/special_work/js_template/${id}/print_template`;
  return await get(base_url);
};

// 修改作业票模板表单模板
export const updatePrintTemplate = async (params) => {
  const res = await put(
    `/special_work/js_template/${params.id}/print_template`,
    params?.values,
  );
  return res;
};
