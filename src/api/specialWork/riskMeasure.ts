import { post, get, del, put } from "@api";

export const getRiskMeasureList = async (params) => {
  const base_url = "/special_work/risk_measure/search";
  const res = await post(base_url, params);
  return res;
};

export const getRiskMeasure = async (id) => {
  const base_url = `/special_work/risk_measure/${id}`;
  return await get(base_url);
};

// 新增
export const createRiskMeasure = async (params) => {
  const res = await post("/special_work/risk_measure", params);
  return res;
};

// 删除单个项目
export const delRiskMeasure = async (id: number) => {
  const res = await del(`/special_work/risk_measure/${id}`);
  return res;
};

// 批量删除
export const delRiskMeasures = async (ids) => {
  const res = await del(`/special_work/risk_measure`, ids);
  return res;
};

// 修改
export const updateRiskMeasure = async (params) => {
  const res = await put(
    `/special_work/risk_measure/${params.id}`,
    params?.values,
  );
  return res;
};
