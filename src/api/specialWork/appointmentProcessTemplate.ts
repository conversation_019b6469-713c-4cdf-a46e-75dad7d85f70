import { get, put } from "api/request";

export const getAppointmentProcessTemplate = async () => {
  const base_url = `/special_work/appointment_process_template`;
  const res = await get(base_url);
  return res;
};

export const updateAppointmentProcessTemplate = async (params) => {
  const base_url = `/special_work/appointment_process_template`;
  const res = await put(base_url, params.values);
  return res;
};
