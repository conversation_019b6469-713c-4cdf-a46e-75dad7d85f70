import { post, get, del, put } from "@api";

export const getSafetyAnalysisList = async (params) => {
  const base_url = "/special_work/safety_analysis/search";
  const res = await post(base_url, params);
  return res;
};

export const getSafetyAnalysis = async (id) => {
  const base_url = `/special_work/safety_analysis/${id}`;
  return await get(base_url);
};

// 新增
export const createSafetyAnalysis = async (params) => {
  const res = await post("/special_work/safety_analysis", params);
  return res;
};

// 删除单个项目
export const delSafetyAnalysis = async (id: number) => {
  const res = await del(`/special_work/safety_analysis/${id}`);
  return res;
};

// 批量删除
export const delSafetyAnalysiss = async (ids) => {
  const res = await del(`/special_work/safety_analysis`, ids);
  return res;
};

// 修改
export const updateSafetyAnalysis = async (params) => {
  const res = await put(
    `/special_work/safety_analysis/${params.id}`,
    params?.values,
  );
  return res;
};
