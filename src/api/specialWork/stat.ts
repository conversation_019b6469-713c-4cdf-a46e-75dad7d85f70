import { get, post } from "api";

export const getStatCurrentJobSlice = async () => {
  const base_url = "/special_work/current_job_slice";
  const res = await get(base_url);
  return res;
};

//v1/special_work/job_category_job_slice
export const postJobCategoryJobSlice = async (params) => {
  const base_url = "/special_work/job_category_job_slice";
  const res = await post(base_url, params);
  return res;
};

export const postJobSliceRank = async (params) => {
  const base_url = "/special_work/job_slice_rank";
  const res = await post(base_url, params);
  return res;
};


//v1/special_work/job_slice_trend
export const postJobSliceTrend = async (params) => {
  const base_url = "/special_work/job_slice_trend";
  const res = await post(base_url, params);
  return res;
};