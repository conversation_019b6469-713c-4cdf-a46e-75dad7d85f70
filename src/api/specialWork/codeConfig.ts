import { post, get, del, put } from "@api";

export const getCodeConfigList = async (params) => {
  const base_url = "/special_work/code_config/search";
  const res = await post(base_url, params);
  return res;
};

export const getCodeConfig = async (id) => {
  const base_url = `/special_work/code_config/${id}`;
  return await get(base_url);
};

// 新增
export const createCodeConfig = async (params) => {
  const res = await post("/special_work/code_config", params);
  return res;
};

// 删除单个项目
export const delCodeConfig = async (id: number) => {
  const res = await del(`/special_work/code_config/${id}`);
  return res;
};

// 批量删除
export const delCodeConfigs = async (ids) => {
  const res = await del(`/special_work/code_config`, ids);
  return res;
};

// 修改
export const updateCodeConfig = async (params) => {
  const res = await put(
    `/special_work/code_config/${params.id}`,
    params?.values,
  );
  return res;
};
