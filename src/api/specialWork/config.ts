import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/special_work/config/search

export const getSpecialWorkConfigList = async (params) => {
  const base_url = "/special_work/config/search";
  const res = await post(base_url, params);
  return res;
};

export const getSpecialWorkConfig = async () => {
  const base_url = `/special_work/config/current`;
  return await get(base_url);
};

export const createSpecialWorkConfig = async (params) => {
  const res = await post("/special_work/config", params);
  return res;
};

export const delSpecialWorkConfig = async (id: number) => {
  const res = await del(`/special_work/config/${id}`);
  return res;
};

export const delSpecialWorkConfigs = async (ids) => {
  const res = await del(`/special_work/config`, ids);
  return res;
};

export const updateSpecialWorkConfig = async (params) => {
  const res = await put(`/special_work/config/${params.id}`, params?.values);
  return res;
};

export const specialWorkConfigApis: CommonApis = {
  entity: "SpecialWorkConfig",
  query: getSpecialWorkConfigList,
  create: createSpecialWorkConfig,
  remove: delSpecialWorkConfig,
  removes: delSpecialWorkConfigs,
  update: updateSpecialWorkConfig,
  get: getSpecialWorkConfig,
};
