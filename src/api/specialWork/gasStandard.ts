import { post, get, del, put } from "@api";

export const getGasStandardList = async (params) => {
  const base_url = "/special_work/gas_standard/search";
  const res = await post(base_url, params);
  return res;
};

export const getGasStandard = async (id) => {
  const base_url = `/special_work/gas_standard/${id}`;
  return await get(base_url);
};

// 新增
export const createGasStandard = async (params) => {
  const res = await post("/special_work/gas_standard", params);
  return res;
};

// 删除单个项目
export const delGasStandard = async (id: number) => {
  const res = await del(`/special_work/gas_standard/${id}`);
  return res;
};

// 批量删除
export const delGasStandards = async (ids) => {
  const res = await del(`/special_work/gas_standard`, ids);
  return res;
};

// 修改
export const updateGasStandard = async (params) => {
  const res = await put(
    `/special_work/gas_standard/${params.id}`,
    params?.values,
  );
  return res;
};
