import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/intelligent_inspection/inspection_path/search

export const getInspectionPathList = async (params) => {
  const base_url = "/intelligent_inspection/inspection_path/search";
  const res = await post(base_url, params);
  return res;
};

export const getInspectionPath = async (id) => {
  const base_url = `/intelligent_inspection/inspection_path/${id}`;
  return await get(base_url);
};

export const createInspectionPath = async (params) => {
  const res = await post("/intelligent_inspection/inspection_path", params);
  return res;
};

export const delInspectionPath = async (id: number) => {
  const res = await del(`/intelligent_inspection/inspection_path/${id}`);
  return res;
};

export const delInspectionPaths = async (ids) => {
  const res = await del(`/intelligent_inspection/inspection_path`, ids);
  return res;
};

export const updateInspectionPath = async (params) => {
  const res = await put(
    `/intelligent_inspection/inspection_path/${params.id}`,
    params?.values
  );
  return res;
};

export const stopCheckInspectionPath = async (id) => {
  const res = await post(`/intelligent_inspection/inspection_path/${id}/stop`);
  return res;
};

export const resumeCheckInspectionPath = async (id) => {
  const res = await post(
    `/intelligent_inspection/inspection_path/${id}/resume`
  );
  return res;
};

export const batchStopCheckInspectionPath = async (ids) => {
  const res = await post(`/intelligent_inspection/inspection_path/stop`, ids);
  return res;
};

export const batchResumeCheckInspectionPath = async (ids) => {
  const res = await post(`/intelligent_inspection/inspection_path/resume`, ids);
  return res;
};

export const inspectionPathApis: CommonApis = {
  entity: "InspectionPath",
  query: getInspectionPathList,
  create: createInspectionPath,
  remove: delInspectionPath,
  removes: delInspectionPaths,
  update: updateInspectionPath,
  get: getInspectionPath,
};
