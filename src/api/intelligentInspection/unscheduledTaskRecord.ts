import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/intelligent_inspection/unscheduled_task_record/search

export const getUnscheduledTaskRecordList = async (params) => {
  const base_url = "/intelligent_inspection/unscheduled_task_record/search";
  const res = await post(base_url, params);
  return res;
};

export const getUnscheduledTaskRecord = async (id) => {
  const base_url = `/intelligent_inspection/unscheduled_task_record/${id}`;
  return await get(base_url);
};

export const getUnscheduledTask = async (id) => {
  const base_url = `/intelligent_inspection/unscheduled_task/${id}`;
  return await get(base_url);
};

export const createUnscheduledTaskRecord = async (params) => {
  const res = await post(
    "/intelligent_inspection/unscheduled_task_record",
    params
  );
  return res;
};

export const delUnscheduledTaskRecord = async (id: number) => {
  const res = await del(
    `/intelligent_inspection/unscheduled_task_record/${id}`
  );
  return res;
};

export const delUnscheduledTaskRecords = async (ids) => {
  const res = await del(`/intelligent_inspection/unscheduled_task_record`, ids);
  return res;
};

export const updateUnscheduledTaskRecord = async (params) => {
  const res = await put(
    `/intelligent_inspection/unscheduled_task_record/${params.id}`,
    params?.values
  );
  return res;
};

export const unscheduledTaskRecordApis: CommonApis = {
  entity: "UnscheduledTaskRecord",
  query: getUnscheduledTaskRecordList,
  create: createUnscheduledTaskRecord,
  remove: delUnscheduledTaskRecord,
  removes: delUnscheduledTaskRecords,
  update: updateUnscheduledTaskRecord,
  get: getUnscheduledTaskRecord,
};
