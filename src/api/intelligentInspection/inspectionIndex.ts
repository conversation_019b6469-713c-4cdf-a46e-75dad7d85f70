import { get, post } from "api";

export const getIntelligentBasicStat = async () => {
  const base_url = "/intelligent_inspection/basic_stat";
  const res = await get(base_url);
  return res;
};

export const getIntelligentTask = async () => {
  const base_url = "/intelligent_inspection/task";
  const res = await get(base_url);
  return res;
};

export const getAbnormalPlaceStat = async (params:any) => {
  const base_url = "/intelligent_inspection/abnormal_place_stat";
  const res = await post(base_url, params);
  return res;
};

export const getAbnormalAreaStat = async (params:any) => {
  const base_url = "/intelligent_inspection/abnormal_area_stat";
  const res = await post(base_url, params);
  return res;
};

export const getAbnormalPersonStat = async (params:any) => {
  const base_url = "/intelligent_inspection/abnormal_person_stat";
  const res = await post(base_url, params);
  return res;
};


export const getIntelligentInspectionRecordStat = async (params:any) => {
  const base_url = "/intelligent_inspection/record_stat";
  const res = await post(base_url, params);
  return res;
};

