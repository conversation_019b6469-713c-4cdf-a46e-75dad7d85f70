import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/intelligent_inspection/inspection_place/search

export const getInspectionPlaceList = async (params) => {
  const base_url = "/intelligent_inspection/inspection_place/search";
  const res = await post(base_url, params);
  return res;
};

export const getInspectionPlace = async (id) => {
  const base_url = `/intelligent_inspection/inspection_place/${id}`;
  return await get(base_url);
};

export const createInspectionPlace = async (params) => {
  const res = await post("/intelligent_inspection/inspection_place", params);
  return res;
};

export const delInspectionPlace = async (id: number) => {
  const res = await del(`/intelligent_inspection/inspection_place/${id}`);
  return res;
};

export const delInspectionPlaces = async (ids) => {
  const res = await del(`/intelligent_inspection/inspection_place`, ids);
  return res;
};

export const updateInspectionPlace = async (params) => {
  const res = await put(
    `/intelligent_inspection/inspection_place/${params.id}`,
    params?.values
  );
  return res;
};

export const stopCheckInspectionPlace = async (id) => {
  const res = await post(`/intelligent_inspection/inspection_place/${id}/stop`);
  return res;
};

export const resumeCheckInspectionPlace = async (id) => {
  const res = await post(
    `/intelligent_inspection/inspection_place/${id}/resume`
  );
  return res;
};

export const batchStopCheckInspectionPlace = async (ids) => {
  const res = await post(`/intelligent_inspection/inspection_place/stop`, ids);
  return res;
};

export const batchResumeCheckInspectionPlace = async (ids) => {
  const res = await post(
    `/intelligent_inspection/inspection_place/resume`,
    ids
  );
  return res;
};

export const inspectionPlaceApis: CommonApis = {
  entity: "InspectionPlace",
  query: getInspectionPlaceList,
  create: createInspectionPlace,
  remove: delInspectionPlace,
  removes: delInspectionPlaces,
  update: updateInspectionPlace,
  get: getInspectionPlace,
};
