import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/intelligent_inspection/inspection_plan/search

export const getInspectionPlanList = async (params) => {
  const base_url = "/intelligent_inspection/inspection_plan/search";
  const res = await post(base_url, params);
  return res;
};

export const getInspectionPlan = async (id) => {
  const base_url = `/intelligent_inspection/inspection_plan/${id}`;
  return await get(base_url);
};

export const createInspectionPlan = async (params) => {
  const res = await post("/intelligent_inspection/inspection_plan", params);
  return res;
};

export const delInspectionPlan = async (id: number) => {
  const res = await del(`/intelligent_inspection/inspection_plan/${id}`);
  return res;
};

export const delInspectionPlans = async (ids) => {
  const res = await del(`/intelligent_inspection/inspection_plan`, ids);
  return res;
};

export const updateInspectionPlan = async (params) => {
  const res = await put(
    `/intelligent_inspection/inspection_plan/${params.id}`,
    params?.values,
  );
  return res;
};

export const inspectionPlanApis: CommonApis = {
  entity: "InspectionPlan",
  query: getInspectionPlanList,
  create: createInspectionPlan,
  remove: delInspectionPlan,
  removes: delInspectionPlans,
  update: updateInspectionPlan,
  get: getInspectionPlan,
};
