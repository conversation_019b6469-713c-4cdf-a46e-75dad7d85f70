import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/intelligent_inspection/inspection_standard/search

export const getInspectionStandardList = async (params) => {
  const base_url = "/intelligent_inspection/inspection_standard/search";
  const res = await post(base_url, params);
  return res;
};

export const getInspectionStandard = async (id) => {
  const base_url = `/intelligent_inspection/inspection_standard/${id}`;
  return await get(base_url);
};

export const createInspectionStandard = async (params) => {
  const res = await post("/intelligent_inspection/inspection_standard", params);
  return res;
};

export const delInspectionStandard = async (id: number) => {
  const res = await del(`/intelligent_inspection/inspection_standard/${id}`);
  return res;
};

export const delInspectionStandards = async (ids) => {
  const res = await del(`/intelligent_inspection/inspection_standard`, ids);
  return res;
};

export const updateInspectionStandard = async (params) => {
  const res = await put(
    `/intelligent_inspection/inspection_standard/${params.id}`,
    params?.values,
  );
  return res;
};

export const inspectionStandardApis: CommonApis = {
  entity: "InspectionStandard",
  query: getInspectionStandardList,
  create: createInspectionStandard,
  remove: delInspectionStandard,
  removes: delInspectionStandards,
  update: updateInspectionStandard,
  get: getInspectionStandard,
};
