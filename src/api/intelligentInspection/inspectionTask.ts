import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/intelligent_inspection/inspection_task/search

export const getInspectionTaskList = async (params) => {
  const base_url = "/intelligent_inspection/inspection_task/search";
  const res = await post(base_url, params);
  return res;
};

export const getInspectionTask = async (id) => {
  const base_url = `/intelligent_inspection/inspection_task/${id}`;
  return await get(base_url);
};

export const getInspectionTaskProgress = async (id) => {
  const base_url = `/intelligent_inspection/inspection_task/${id}/progress`;
  return await get(base_url);
};

export const createInspectionTask = async (params) => {
  const res = await post("/intelligent_inspection/inspection_task", params);
  return res;
};

export const delInspectionTask = async (id: number) => {
  const res = await del(`/intelligent_inspection/inspection_task/${id}`);
  return res;
};

export const delInspectionTasks = async (ids) => {
  const res = await del(`/intelligent_inspection/inspection_task`, ids);
  return res;
};

export const updateInspectionTask = async (params) => {
  const res = await put(
    `/intelligent_inspection/inspection_task/${params.id}`,
    params?.values
  );
  return res;
};

export const inspectionTaskApis: CommonApis = {
  entity: "InspectionTask",
  query: getInspectionTaskList,
  create: createInspectionTask,
  remove: delInspectionTask,
  removes: delInspectionTasks,
  update: updateInspectionTask,
  get: getInspectionTask,
};
