import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/intelligent_inspection/inspection_task_record/search

export const getInspectionTaskRecordList = async (params) => {
  const base_url = "/intelligent_inspection/inspection_task_record/search";
  const res = await post(base_url, params);
  return res;
};

export const getInspectionTaskRecord = async (id) => {
  const base_url = `/intelligent_inspection/inspection_task_record/${id}`;
  return await get(base_url);
};

export const createInspectionTaskRecord = async (params) => {
  const res = await post(
    "/intelligent_inspection/inspection_task_record",
    params,
  );
  return res;
};

export const delInspectionTaskRecord = async (id: number) => {
  const res = await del(`/intelligent_inspection/inspection_task_record/${id}`);
  return res;
};

export const delInspectionTaskRecords = async (ids) => {
  const res = await del(`/intelligent_inspection/inspection_task_record`, ids);
  return res;
};

export const updateInspectionTaskRecord = async (params) => {
  const res = await put(
    `/intelligent_inspection/inspection_task_record/${params.id}`,
    params?.values,
  );
  return res;
};

export const inspectionTaskRecordApis: CommonApis = {
  entity: "InspectionTaskRecord",
  query: getInspectionTaskRecordList,
  create: createInspectionTaskRecord,
  remove: delInspectionTaskRecord,
  removes: delInspectionTaskRecords,
  update: updateInspectionTaskRecord,
  get: getInspectionTaskRecord,
};
