/* import { <PERSON>Filter, RoleContent, RoleSide } from './content'
import { RoleModal } from './modal'
 */

import { SensorContent, SensorFilter } from "./content";
import { SensorModal } from "./modal/sensorModal";

interface SensorPageProps {
  fieldValidation: any;
  queryApi: any;
}

export function SensorPage({ fieldValidation, queryApi }: SensorPageProps) {
  return (
    <div className="flex flex-col gap-4 ">
      {/* <RoleSide />
      <RoleModal />
      <RoleFilter />
      <RoleContent /> */}
      <SensorFilter />
      <SensorModal fieldValidation={fieldValidation} />
      <SensorContent queryApi={queryApi} />
    </div>
  );
}
