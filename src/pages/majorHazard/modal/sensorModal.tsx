import {
  But<PERSON>,
  Col,
  Form,
  Modal,
  Row,
  Table,
  Toast,
  useFormApi,
} from "@douyinfe/semi-ui";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";

import { IconPlus } from "@douyinfe/semi-icons";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { getMajorHazardList } from "api";
import {
  getSensorField,
  getSensorMeasurement,
  sensorApis,
} from "api/majorHazard";
import {
  sensorAtoms,
  sensorCodeStateModalAtom,
  sensorStateCodeColumnsAtom,
} from "atoms";
import {
  ALLOW_REPORT_STATUS_MAP,
  AreaSearch,
  DepartmentSearch,
  DicSearch,
  IS_ISNOT_DISPLAY_MAP,
  IS_ISNOT_MAP,
  RestSelect,
  SENSOR_ALERTPRIORITY_MAP,
  SENSOR_DATATYPE_MAP,
} from "components";
import { Draft, DraftTrigger, destroyDraft } from "components/Draft";
import { useEquipmentListOptions } from "hooks/useEquipmentList";
import { useAtom } from "jotai";
import { omit } from "ramda";
import { useNavigate } from "react-router-dom";
import { SensorComponentProps } from "types/sensorConfig";
import { filterEditData } from "utils";
import SensorStateCodeModal from "./sensorStateCodeModal";

export const SensorModal = ({ config }: SensorComponentProps = {}) => {
  const gutter = 24;
  const operation = "Edit";

  // 使用配置或默认值
  const modalConfig = config?.modalConfig || {
    newTitle: "新增实时监测信息",
    editTitle: "编辑实时监测信息",
    requiredFields: [],
  };
  const newTitle = modalConfig.newTitle;
  const editTitle = modalConfig.editTitle;
  const ruleMessage = ["此为必填项!"];

  const atoms = config
    ? { ...sensorAtoms, entity: config.entity }
    : sensorAtoms;
  const apis = config?.apis || sensorApis;
  const fieldValidation = config?.fieldValidation || {};

  const entity = atoms.entity;
  const uniqueKey = `${entity}${operation}`;
  const queryClient = useQueryClient();
  const queryKey = "list" + entity;

  const [editModalAtom, setEditModalAtom] = useAtom(atoms.editModal);
  const [fnAtom] = useAtom(atoms.Fn || sensorAtoms.Fn);

  const [sensorStateCodeColumns, setSensorStateCodeColumns] = useAtom(
    sensorStateCodeColumnsAtom
  );
  const [stateCodeModal, setStateCodeModal] = useAtom(sensorCodeStateModalAtom);

  const title = editModalAtom?.id ? editTitle : newTitle;
  const rules = [{ required: true, message: ruleMessage[0] }];

  // 设备ID验证规则
  const equipmentIdRules = (fieldValidation as any).equipmentId?.required
    ? (fieldValidation as any).equipmentId.rules || rules
    : [];

  const getFormApiRef = useRef<any>(null);
  const navigate = useNavigate();

  const [stateCodeListDataSource, setStateCodeListDatasource] = useState<any[]>(
    []
  );

  const UnitComponentUsingFormApi = () => {
    const formApi = useFormApi();
    const formState = formApi.getFormState();

    console.log("formState.values.type", formState.values.type);

    return formState.values.type === SENSOR_DATATYPE_MAP[1].id ? (
      <Form.Input
        label="计量单位"
        field="unit"
        trigger="blur"
        disabled={true}
      />
    ) : (
      <Form.Input
        label="计量单位"
        field="unit"
        trigger="blur"
        // rules={rules}
        disabled={false}
      />
    );
  };

  const { isLoading, data } = useQuery({
    queryKey: [`${entity}-${editModalAtom?.id ?? ""}`],
    queryFn: () => {
      if (editModalAtom?.id) {
        return apis.get(editModalAtom?.id);
      }
    },
    enabled: !!editModalAtom?.id,
  });
  const mutation = useMutation({
    mutationFn: editModalAtom?.id ? apis.update : apis.create,
    onSuccess: async (res) => {
      if (res?.code === 0) {
        let opts = {
          content: `操作成功!`,
          duration: 2,
        };
        Toast.success(opts);
        queryClient.invalidateQueries({ queryKey: [queryKey] });
        fnAtom?.refetch?.();
        destroyDraft(uniqueKey);
        setEditModalAtom({
          id: "",
          show: false,
        });
      }
    },
  });

  //user-defined code here: extra data
  const equipmentListOptions = useEquipmentListOptions();

  const { data: majorzHazardList } = useQuery({
    queryKey: ["getMajorHazardList"],
    queryFn: () =>
      getMajorHazardList({
        pageNumber: 1,
        pageSize: 9999,
      }),
  });
  const majorzHazardOptions = useMemo(() => {
    return (majorzHazardList?.data as any)?.results ?? [];
  }, [majorzHazardList]);

  const { data: databaseTableList } = useQuery({
    queryKey: ["getSensorDatabaseTableList"],
    queryFn: () => getSensorMeasurement(),
  });
  const databaseTableListOptions = useMemo(() => {
    return (databaseTableList?.data as any) ?? [];
  }, [databaseTableList]);

  const DatabaseFieldComponentUsingFormApi = () => {
    const formApi = useFormApi();
    const formState = formApi.getFormState();

    useEffect(() => {
      if (
        formState.values.measurementName &&
        formState.touched?.measurementName
      ) {
        formApi.setValue("fieldName", null);
      }
    }, [formState.values.measurementName, formState.touched?.measurementName]);

    const { data: databaseFieldList } = useQuery({
      queryKey: [
        "getSensorDatabaseFieldList",
        formState.values?.measurementName,
      ],
      queryFn: () => getSensorField(formState.values?.measurementName),
      enabled: !!formState.values.measurementName,
    });
    const databaseFieldListOptions = useMemo(() => {
      return (databaseFieldList?.data as any) ?? [];
    }, [databaseFieldList]);

    return (
      <>
        <Col span={12}>
          <Form.Select
            label="字段名称"
            field="fieldName"
            className="w-full"
            rules={rules}
          >
            {(databaseFieldListOptions as any[]).map((item: any) => (
              <Form.Select.Option key={item} value={item}>
                {item}
              </Form.Select.Option>
            ))}
          </Form.Select>
        </Col>
      </>
    );
  };

  // 自动填充表单
  useEffect(() => {
    if (editModalAtom?.id && data?.data?.id && getFormApiRef.current) {
      const items = omit([], data?.data);
      setStateCodeListDatasource(items?.stateCodeList ?? []);
      getFormApiRef.current.setValues(
        {
          ...filterEditData(items),
          //user-defined code here
          areaId: items?.area?.id === 0 ? null : items?.area?.id,
          majorHazardId:
            items?.majorHazard?.id === 0 ? null : items?.majorHazard?.id,
          sensorBusinessTypeValueId:
            items?.sensorBusinessType?.id === 0
              ? null
              : items?.sensorBusinessType?.id,
        },
        { isOverride: true }
      );
    } else {
      getFormApiRef?.current?.reset?.();
    }
  }, [editModalAtom?.id, data, getFormApiRef]);

  const handleSetFormApi = useCallback(
    (formApi) => {
      getFormApiRef.current = formApi;
    },
    [getFormApiRef]
  );

  const handleClose = useCallback(() => {
    if (mutation.isLoading) {
      return;
    }
    destroyDraft(`${entity}${operation}`);
    setEditModalAtom({
      id: "",
      show: false,
    });
  }, [setEditModalAtom, mutation]);

  const handleOk = () => {
    if (mutation.isLoading) {
      return;
    }
    getFormApiRef.current
      .validate()
      .then((values) => {
        let obj = {
          ...values,
          stateCodeList: stateCodeListDataSource,
          unit: values.type === SENSOR_DATATYPE_MAP[1].id ? "" : values.unit,
          //user-defined code here
        };
        if (editModalAtom?.id) {
          mutation.mutate({
            id: editModalAtom?.id,
            values: obj,
          });
        } else {
          mutation.mutate(obj);
        }
      })
      .catch((errors) => {
        console.log(errors);
        const [formFieldId] = Object.keys(errors || {});
        if (formFieldId) {
          document.getElementById(formFieldId)?.scrollIntoViewIfNeeded?.();
        }
      });
  };

  const handleStateCodeOpenEdit = useCallback(
    (record) => {
      setStateCodeModal({
        name: record?.name,
        show: true,
      });
    },
    [setStateCodeModal]
  );

  const handleStateCodeEdit = (values) => {
    console.log("handleStateCodeEdit", values);
    const res = stateCodeListDataSource.concat(values);
    console.log(res);

    setStateCodeListDatasource(res);
    setStateCodeModal({
      name: "",
      show: false,
    });
  };

  const columns = useMemo(() => {
    const deleteColumn = {
      title: "操作",
      isShow: true,
      dataIndex: "delete",
      align: "center",
      render: (text: any, record: any) => {
        return (
          <Button
            type="danger"
            onClick={() => {
              console.debug("delete", record);
              console.debug("stateCodeListDataSource", stateCodeListDataSource);
              setStateCodeListDatasource(
                stateCodeListDataSource.filter(
                  (item) =>
                    item.code !== record.code || item.state !== record.state
                )
              );
            }}
          >
            删除
          </Button>
        );
      },
    };
    return [...sensorStateCodeColumns, deleteColumn];
  }, [
    sensorStateCodeColumns,
    stateCodeListDataSource,
    setStateCodeListDatasource,
  ]);

  return (
    <>
      <DraftTrigger id={uniqueKey} draftAtom={atoms.editModal} />
      <Modal
        title={title}
        visible={editModalAtom?.show ?? false}
        onCancel={handleClose}
        maskClosable={false}
        keepDOM
        width={800}
        footer={
          <div className="flex gap-2 justify-end">
            <button className="btn rounded btn-sm" onClick={handleClose}>
              取消
            </button>
            {mutation.isLoading ? (
              <button className="btn rounded btn-primary btn-sm btn-disabled">
                <span className="loading loading-spinner loading-xs"></span>
                确定
              </button>
            ) : (
              <button
                className="btn rounded btn-primary btn-sm"
                onClick={handleOk}
              >
                确定
              </button>
            )}
          </div>
        }
        centered
      >
        <SensorStateCodeModal callback={handleStateCodeEdit} />
        <Form
          labelPosition="left"
          labelAlign="right"
          labelWidth={100}
          getFormApi={handleSetFormApi}
        >
          {/* add form items here */}
          {(formState) => (
            <>
              <Row gutter={20}>
                <Col span={12}>
                  <Form.Input
                    label="监测名称"
                    field="name"
                    trigger="blur"
                    rules={rules}
                  />
                </Col>
                <Col span={12}>
                  <Form.Input
                    label="传感器编码"
                    field="code"
                    trigger="blur"
                    rules={rules}
                  />
                </Col>
              </Row>
              <Row gutter={20}>
                <Col span={12}>
                  <Form.Select
                    label="数据类型"
                    field="type"
                    className="w-full"
                    rules={rules}
                  >
                    {SENSOR_DATATYPE_MAP.map((item) => (
                      <Form.Select.Option key={item.id} value={item.id}>
                        {item.name}
                      </Form.Select.Option>
                    ))}
                  </Form.Select>
                </Col>
                <Col span={12}>
                  <DicSearch
                    label="监测类型"
                    field="monitorTypeValueId"
                    placeholder="请选择监测类型"
                    name="monitorType"
                    isRequired
                  />
                </Col>
              </Row>
              <Row gutter={20}>
                <Col span={12}>
                  <Form.Select
                    label="数据表名称"
                    field="measurementName"
                    className="w-full"
                    rules={rules}
                  >
                    {databaseTableListOptions.map((item) => (
                      <Form.Select.Option key={item} value={item}>
                        {item}
                      </Form.Select.Option>
                    ))}
                  </Form.Select>
                </Col>
                <DatabaseFieldComponentUsingFormApi />
              </Row>
              <Row gutter={gutter}>
                <Col span={12}>
                  <Form.Select
                    label="报警优先级"
                    field="priority"
                    className="w-full"
                    rules={rules}
                  >
                    {SENSOR_ALERTPRIORITY_MAP.map((item) => (
                      <Form.Select.Option key={item.id} value={item.id}>
                        {item.name}
                      </Form.Select.Option>
                    ))}
                  </Form.Select>
                </Col>
                <Col span={12}>
                  <Form.Select
                    label="可视化页面是否显示报警"
                    field="isAlarmDisplay"
                    className="w-full"
                    rules={rules}
                  >
                    {IS_ISNOT_DISPLAY_MAP.map((item) => (
                      <Form.Select.Option key={item.id} value={item.id}>
                        {item.name}
                      </Form.Select.Option>
                    ))}
                  </Form.Select>
                </Col>
              </Row>
              <Row gutter={gutter}>
                <Col span={12}>
                  <Form.Select
                    label="是否上报"
                    field="needUpload"
                    className="w-full"
                    rules={rules}
                  >
                    {IS_ISNOT_MAP.map((item) => (
                      <Form.Select.Option key={item.id} value={item.id}>
                        {item.name}
                      </Form.Select.Option>
                    ))}
                  </Form.Select>
                </Col>
                {formState.values.needUpload ===
                  ALLOW_REPORT_STATUS_MAP[0].id && (
                  <Col span={12}>
                    <Form.Input
                      label="上报指标编码"
                      field="reportCode"
                      trigger="blur"
                    />
                  </Col>
                )}
              </Row>
              <Row gutter={20}>
                <Col span={12}>
                  <DicSearch
                    label="业务类型"
                    field="sensorBusinessTypeValueId"
                    placeholder="请选择业务类型"
                    name="sensorBusinessType"
                  />
                </Col>
                <Col span={12}>
                  {/* <Form.Input
                    label="计量单位"
                    field="unit"
                    trigger="blur"
                    rules={
                      formState.values.type === SENSOR_DATATYPE_MAP[1].id
                        ? []
                        : rules
                    }
                    disabled={
                      formState.values.type === SENSOR_DATATYPE_MAP[1].id
                    }
                  /> */}
                  <UnitComponentUsingFormApi />
                </Col>
              </Row>
              {formState.values.type === SENSOR_DATATYPE_MAP[0].id ? (
                <>
                  <Row gutter={20}>
                    <Col span={12}>
                      <Form.InputNumber
                        label="阈值上限"
                        field="highAlarm"
                        trigger="blur"
                      />
                    </Col>
                    <Col span={12}>
                      <Form.InputNumber
                        label="阈值上上限"
                        field="highHighAlarm"
                        trigger="blur"
                      />
                    </Col>
                  </Row>
                  <Row gutter={20}>
                    <Col span={12}>
                      <Form.InputNumber
                        label="阈值下限"
                        field="lowAlarm"
                        trigger="blur"
                      />
                    </Col>
                    <Col span={12}>
                      <Form.InputNumber
                        label="阈值下下限"
                        field="lowLowAlarm"
                        trigger="blur"
                      />
                    </Col>
                  </Row>
                  <Row gutter={20}>
                    <Col span={12}>
                      <Form.InputNumber
                        label="量程上限"
                        field="rangeHigh"
                        trigger="blur"
                      />
                    </Col>
                    <Col span={12}>
                      <Form.InputNumber
                        label="量程下限"
                        field="rangeLow"
                        trigger="blur"
                      />
                    </Col>
                  </Row>
                </>
              ) : null}
              <Row gutter={20}>
                <Col span={12}>
                  <RestSelect
                    label="所属重大危险源"
                    field="majorHazardId"
                    options={majorzHazardOptions}
                    placeholder="请选择所属重大危险源"
                  />
                </Col>
                <Col span={12}>
                  <AreaSearch
                    label="所属区域"
                    field="areaId"
                    placeholder="请选择所属区域"
                  />
                </Col>
              </Row>
              <Row gutter={gutter}>
                <Col span={12}>
                  <DepartmentSearch
                    label="所属部门"
                    field="departmentId"
                    placeholder="请选择所属部门"
                  />
                </Col>
                <Col span={12}>
                  {equipmentIdRules.length > 0 ? (
                    <Form.Select
                      label="所属设备"
                      field="equipmentId"
                      className="w-full"
                      placeholder="请选择所属设备"
                      rules={equipmentIdRules}
                    >
                      {equipmentListOptions.map((item: any) => (
                        <Form.Select.Option key={item.id} value={item.id}>
                          {item.name}
                        </Form.Select.Option>
                      ))}
                    </Form.Select>
                  ) : (
                    <RestSelect
                      label="所属设备"
                      field="equipmentId"
                      options={equipmentListOptions}
                      placeholder="请选择所属设备"
                    />
                  )}
                </Col>
              </Row>
              <Row gutter={gutter}>
                <Col span={12}>
                  <Form.Input
                    label="位号"
                    field="positionNumber"
                    trigger="blur"
                  />
                </Col>
              </Row>
              <Row gutter={20}>
                <Col span={12}>
                  <Form.TextArea label="描述" field="note" trigger="blur" />
                </Col>
              </Row>
              {formState.values.type === SENSOR_DATATYPE_MAP[1].id ? (
                <Row gutter={gutter}>
                  <Col span={24}>
                    <div className="bg-white shadow px-4 h-fit rounded">
                      {/* Operation bar */}
                      <div className="flex py-4 justify-between">
                        <div className="flex gap-4">
                          <button
                            className="btn rounded btn-primary btn-sm"
                            onClick={() => {
                              handleStateCodeOpenEdit({});
                            }}
                          >
                            新增
                            <IconPlus size="small" />
                          </button>
                        </div>
                      </div>
                      <Table
                        title="状态码"
                        rowKey={"name"}
                        columns={columns}
                        dataSource={stateCodeListDataSource}
                        pagination={false}
                      />
                    </div>
                  </Col>
                </Row>
              ) : null}
              {editModalAtom?.id ? null : (
                <Draft id={uniqueKey} draftAtom={atoms.editModal} />
              )}
            </>
          )}
        </Form>
      </Modal>
    </>
  );
};
