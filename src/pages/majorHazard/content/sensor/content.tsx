import { Toast } from "@douyinfe/semi-ui";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import {
  batchResumeSensor,
  batchStopSensor,
  resumeSensor,
  sensorApis,
  stopSensor,
} from "api";
import { sensorAtoms } from "atoms";
import { IS_ISNOT_ACTIVE_MAP, List } from "components";
import { LineChart, LineChartAtom } from "components/chart";
import { useAtom } from "jotai";
import { useCallback } from "react";

interface SensorContentProps {
  queryApi: any;
}
export const SensorContent = ({ queryApi }: SensorContentProps) => {
  const [chartData, setChartData] = useAtom(LineChartAtom);

  const queryClient = useQueryClient();
  const queryKey = "list" + sensorAtoms.entity;

  const _operations = [
    {
      engName: "linechart",
      chnName: "历史波动图",
      func: (record: any) => {
        setChartData({
          show: true,
          data: record,
        });
      },
    },
  ];

  const resumeMutation = useMutation({
    mutationFn: resumeSensor,
    onSuccess: async (res) => {
      if (res?.code === 0) {
        let opts = {
          content: `操作成功!`,
          duration: 2,
        };
        Toast.success(opts);
        queryClient.invalidateQueries({ queryKey: [queryKey] });
        sensorAtoms.Fn?.refetch?.();
        // destroyDraft
        //destroyDraft(uniqueKey);
        // setModal
        /*setEditModalAtom({
          id: '',
          show: false
        })*/
      }
    },
  });
  const handleResume = useCallback(
    (record) => {
      console.log("handleResume", record);
      resumeMutation.mutate(record.id);
    },
    [resumeMutation]
  );

  const stopMutation = useMutation({
    mutationFn: stopSensor,
    onSuccess: async (res) => {
      if (res?.code === 0) {
        let opts = {
          content: `操作成功!`,
          duration: 2,
        };
        Toast.success(opts);
        queryClient.invalidateQueries({ queryKey: [queryKey] });
        sensorAtoms.Fn?.refetch?.();
        // destroyDraft
        //destroyDraft(uniqueKey);
        // setModal
        /*setEditModalAtom({
          id: '',
          show: false
        })*/
      }
    },
  });
  const handleStop = useCallback(
    (record) => {
      console.log("handleStop", record);
      stopMutation.mutate(record.id);
    },
    [stopMutation]
  );

  const toggleIsActive = (record) => {
    return record.isActive === IS_ISNOT_ACTIVE_MAP[0].id
      ? {
          engName: "stop",
          chnName: "停用",
          func: handleStop,
        }
      : record.isActive === IS_ISNOT_ACTIVE_MAP[1].id
        ? {
            engName: "resume",
            chnName: "启用",
            func: handleResume,
          }
        : null;
  };

  const dynamicOperationFuncs = [toggleIsActive];

  const batchResumeMutation = useMutation({
    mutationFn: batchResumeSensor,
    onSuccess: async (res) => {
      if (res?.code === 0) {
        let opts = {
          content: `操作成功!`,
          duration: 2,
        };
        Toast.success(opts);
        queryClient.invalidateQueries({ queryKey: [queryKey] });
        sensorAtoms.Fn?.refetch?.();
        // destroyDraft
        //destroyDraft(uniqueKey);
        // setModal
        /*setEditModalAtom({
          id: '',
          show: false
        })*/
      }
    },
  });
  const handleBatchResume = useCallback(
    (rowKeys, setRowKeys) => {
      console.log("handleBatchResume", rowKeys, setRowKeys);

      batchResumeMutation.mutate(rowKeys);
    },
    [batchResumeMutation]
  );

  const batchStopMutation = useMutation({
    mutationFn: batchStopSensor,
    onSuccess: async (res) => {
      if (res?.code === 0) {
        let opts = {
          content: `操作成功!`,
          duration: 2,
        };
        Toast.success(opts);
        queryClient.invalidateQueries({ queryKey: [queryKey] });
        sensorAtoms.Fn?.refetch?.();
        // destroyDraft
        //destroyDraft(uniqueKey);
        // setModal
        /*setEditModalAtom({
          id: '',
          show: false
        })*/
      }
    },
  });
  const handleBatchStop = useCallback(
    (rowKeys, setRowKeys) => {
      batchStopMutation.mutate(rowKeys);
    },
    [batchStopMutation]
  );

  const batchOperation = [
    {
      engName: "batchResume",
      chnName: "批量启用",
      func: handleBatchResume,
    },
    {
      engName: "batchStop",
      chnName: "批量停用",
      func: handleBatchStop,
    },
  ];

  const importProps = {
    entity: "传感器",
    excelType: "sensor_template",
    downUrl: encodeURI("/static/template/实时监测信息导入模板.xlsx"),
    tip: "请先准备好对应的传感器类型/区域/重大危险源信息，否则导入会失败",
  };

  return (
    <>
      {chartData.show ? <LineChart /> : null}

      <List
        atoms={sensorAtoms}
        apis={sensorApis}
        _operations={_operations}
        dynamicOperationFuncs={dynamicOperationFuncs}
        batchOperation={batchOperation}
        tableProps={{ scroll: { x: 1800 } }}
        importProps={importProps}
        queryApi={queryApi}
      />
    </>
  );
};
