import { IconSearch } from "@douyinfe/semi-icons";
import { Form } from "@douyinfe/semi-ui";
import { useQuery } from "@tanstack/react-query";
import { getSensorList } from "api";
import { sensorAtoms } from "atoms/majorHazard/sensor";
import {
  ALARM_REPORT_STATUS_MAP,
  ALLOW_REPORT_STATUS_MAP,
  AreaSearch,
  DepartmentSearch,
  DicSearch,
  IS_ISNOT_ACTIVE_MAP,
  REPORT_STATUS_MAP,
  RestSelect,
  SENSOR_ALERTPRIORITY_MAP,
} from "components";
import { useFilterSearch } from "hooks";
import { useEquipmentListOptions } from "hooks/useEquipmentList";
import { useMemo } from "react";

export const SensorFilter = () => {
  const atoms = sensorAtoms;
  const [handleSearch, handleReset] = useFilterSearch(atoms.filter);

  const entity = atoms.entity;
  const operation = "List";

  const { data: list } = useQuery({
    queryKey: [`get${entity}${operation}`], //user-defined code here
    queryFn: () => getSensorList, //user-defined code here
  });
  const options = useMemo(() => {
    return list?.data?.results ?? [];
  }, [list]);

  const equipmentListOptions = useEquipmentListOptions();

  return (
    <div className="flex flex-col bg-white shadow rounded relative ">
      <div className="text-gray-900 text-opacity-90 text-base font-semibold leading-snug px-4 py-3 border-b border-gray-900/10 ">
        筛选总览
      </div>

      <div className="p-4 pr-0">
        <Form
          layout="horizontal"
          onSubmit={handleSearch}
          className="grid grid-cols-4 gap-y-4 "
        >
          {/* user-defined code here */}
          <AreaSearch placeholder="请选择所属区域" field="areaId" />
          <DepartmentSearch placeholder="请选择所属部门" field="departmentId" />
          <RestSelect
            options={equipmentListOptions}
            placeholder="请选择所属设备"
            field="equipmentId"
          />
          <Form.Input
            noLabel
            field="query"
            placeholder="请填入监测名称/传感器编号"
            className="w-full"
            suffix={<IconSearch />}
            showClear
          />
          <DicSearch
            placeholder="请选择监测类型"
            field="monitorTypeValueId"
            name="monitorType"
          />
          <DicSearch
            placeholder="请选择业务类型"
            field="sensorBusinessTypeValueId"
            name="sensorBusinessType"
          />

          <Form.Select
            field="priority"
            noLabel
            placeholder="请选择报警优先级"
            className="w-full"
          >
            {SENSOR_ALERTPRIORITY_MAP.map((item) => (
              <Form.Select.Option key={item.id} value={item.id}>
                {item.name}
              </Form.Select.Option>
            ))}
          </Form.Select>
          <Form.Select
            placeholder="请选择运行状态"
            field="isActive"
            noLabel
            className="w-full"
          >
            {IS_ISNOT_ACTIVE_MAP.map((item) => (
              <Form.Select.Option key={item.id} value={item.id}>
                {item.name}
              </Form.Select.Option>
            ))}
          </Form.Select>

          <Form.Select
            placeholder="是否上报"
            field="needUpload"
            noLabel
            className="w-full"
          >
            {ALLOW_REPORT_STATUS_MAP.map((item) => (
              <Form.Select.Option key={item.id} value={item.id}>
                {item.name}
              </Form.Select.Option>
            ))}
          </Form.Select>
          <Form.Select
            placeholder="上报状态"
            field="reportStatus"
            noLabel
            className="w-full"
          >
            {REPORT_STATUS_MAP.map((item) => (
              <Form.Select.Option key={item.id} value={item.id}>
                {item.name}
              </Form.Select.Option>
            ))}
          </Form.Select>
          <Form.Select
            placeholder="报警上报状态"
            field="reportAlarmStatus"
            noLabel
            className="w-full"
          >
            {ALARM_REPORT_STATUS_MAP.map((item) => (
              <Form.Select.Option key={item.id} value={item.id}>
                {item.name}
              </Form.Select.Option>
            ))}
          </Form.Select>

          {/* TODO 实时状态 */}

          <div className="flex gap-2">
            <button className="btn rounded btn-primary btn-sm">
              查&nbsp;&nbsp;询
            </button>
            <button
              className="btn btn-sm rounded"
              type="reset"
              onClick={handleReset}
            >
              重&nbsp;&nbsp;置
            </button>
          </div>
        </Form>
      </div>
    </div>
  );
};
