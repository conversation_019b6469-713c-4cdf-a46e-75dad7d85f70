import { Card, Col, Divider, Form, Row, useFormState } from "@douyinfe/semi-ui";
import { OPEN_NOTOPEN_MAP } from "components";
import { motion } from "framer-motion";

export const useRegionPluginConfigContentList = () => {
  // 优化的云南表单内容组件
  const YunNanConfigContent = ({ readonly = false, ...restProps }) => {
    const formState = useFormState();

    const gutter = 24;
    const rules = [{ required: true, message: "此为必填项" }];

    if (formState?.values.reportType !== 1) return null;

    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        transition={{ duration: 0.3 }}
        className="space-y-4"
      >
        <Card className="hover:shadow-md transition-all border-l-4 border-l-[#12A182]">
          <div className="flex items-center mb-4">
            <span className="text-lg font-medium">上报基础设置</span>
            <Divider layout="vertical" margin="12px" />
            <span className="text-xs text-gray-500">
              配置基本信息和连接参数
            </span>
          </div>
          <Row gutter={gutter}>
            <Col span={12}>
              <Form.Input label="企业编码" field="companyCode" trigger="blur" />
            </Col>
            <Col span={12}>
              <Form.Input
                label="加密算法key变量"
                field="encryptKey"
                trigger="blur"
              />
            </Col>
          </Row>
          <Row gutter={gutter}>
            <Col span={12}>
              <Form.Input
                label="加密算法iv变量"
                field="encryptIv"
                trigger="blur"
              />
            </Col>
            <Col span={12}>
              <Form.Input
                label="上报接入地址"
                field="urlPrefix"
                trigger="blur"
              />
            </Col>
          </Row>
        </Card>

        <Card className="hover:shadow-md transition-all border-l-4 border-l-[#12A182]">
          <div className="flex items-center mb-4">
            <span className="text-lg font-medium">上报接口设置</span>
            <Divider layout="vertical" margin="12px" />
            <span className="text-xs text-gray-500">配置上报接口地址</span>
          </div>
          <Row gutter={gutter}>
            <Col span={12}>
              <Form.Input
                label="作业票上报接口"
                field="ticketUri"
                trigger="blur"
              />
            </Col>
          </Row>
          <Row gutter={gutter}>
            <Col span={12}>
              <Form.Input
                label="作业票表单文件上报接口"
                field="ticketFileUri"
                trigger="blur"
              />
            </Col>
          </Row>
        </Card>

        <Card className="hover:shadow-md transition-all border-l-4 border-l-[#12A182]">
          <div className="flex items-center mb-4">
            <span className="text-lg font-medium">上报开关设置</span>
            <Divider layout="vertical" margin="12px" />
            <span className="text-xs text-gray-500">控制上报功能开关</span>
          </div>
          <Row gutter={gutter}>
            <Col span={12}>
              <Form.RadioGroup
                label="作业票上报是否打开"
                field="ticketIsOn"
                rules={[{ required: true, message: "此为必填项" }]}
              >
                {OPEN_NOTOPEN_MAP.map((item) => (
                  <Form.Radio key={item.id} value={item.id}>
                    {item.name}
                  </Form.Radio>
                ))}
              </Form.RadioGroup>
            </Col>
          </Row>
        </Card>

        <Card className="hover:shadow-md transition-all border-l-4 border-l-[#12A182]">
          <div className="flex items-center mb-4">
            <span className="text-lg font-medium">其他设置</span>
            <Divider layout="vertical" margin="12px" />
            <span className="text-xs text-gray-500">配置截图相关参数</span>
          </div>
          <Row gutter={gutter}>
            <Col span={12}>
              <Form.InputNumber
                label="截图操作超时时间(秒)"
                field="screenshotTimeout"
                trigger="blur"
                rules={rules}
              />
            </Col>
            <Col span={12}>
              <Form.InputNumber
                label="截图文件最小文件大小"
                field="screenshotMinSize"
                trigger="blur"
                rules={rules}
              />
            </Col>
          </Row>
        </Card>
      </motion.div>
    );
  };

  // 优化的江西表单内容组件
  const JiangxiConfigContent = ({ readonly = false, ...restProps }) => {
    const formState = useFormState();

    const gutter = 24;
    const rules = [{ required: true, message: "此为必填项" }];

    if (formState?.values.reportType !== 2) return null;

    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        transition={{ duration: 0.3 }}
        className="space-y-4"
      >
        <Card className="hover:shadow-md transition-all border-l-4 border-l-[#0072E5]">
          <div className="flex items-center mb-4">
            <span className="text-lg font-medium">上报基础设置</span>
            <Divider layout="vertical" margin="12px" />
            <span className="text-xs text-gray-500">
              配置基本信息和连接参数
            </span>
          </div>
          <Row gutter={gutter}>
            <Col span={12}>
              <Form.Input label="企业编码" field="companyCode" trigger="blur" />
            </Col>
            <Col span={12}>
              <Form.Input
                label="上报接入地址"
                field="urlPrefix"
                trigger="blur"
              />
            </Col>
          </Row>
        </Card>

        <Card className="hover:shadow-md transition-all border-l-4 border-l-[#0072E5]">
          <div className="flex items-center mb-4">
            <span className="text-lg font-medium">上报开关设置</span>
            <Divider layout="vertical" margin="12px" />
            <span className="text-xs text-gray-500">控制上报功能开关</span>
          </div>
          <Row gutter={gutter}>
            <Col span={12}>
              <Form.RadioGroup
                label="作业票上报是否打开"
                field="ticketIsOn"
                rules={[{ required: true, message: "此为必填项" }]}
              >
                {OPEN_NOTOPEN_MAP.map((item) => (
                  <Form.Radio key={item.id} value={item.id}>
                    {item.name}
                  </Form.Radio>
                ))}
              </Form.RadioGroup>
            </Col>
            <Col span={12}>
              <Form.RadioGroup
                label="作业报备上报是否打开"
                field="ticketBackupIsOn"
                rules={[{ required: true, message: "此为必填项" }]}
              >
                {OPEN_NOTOPEN_MAP.map((item) => (
                  <Form.Radio key={item.id} value={item.id}>
                    {item.name}
                  </Form.Radio>
                ))}
              </Form.RadioGroup>
            </Col>
          </Row>
          <Row gutter={gutter}>
            <Col span={12}>
              <Form.RadioGroup
                label="作业流程上报是否打开"
                field="ticketProgressIsOn"
                rules={[{ required: true, message: "此为必填项" }]}
              >
                {OPEN_NOTOPEN_MAP.map((item) => (
                  <Form.Radio key={item.id} value={item.id}>
                    {item.name}
                  </Form.Radio>
                ))}
              </Form.RadioGroup>
            </Col>
            <Col span={12}>
              <Form.RadioGroup
                label="作业票截屏上报是否打开"
                field="ticketScreenshotIsOn"
                rules={[{ required: true, message: "此为必填项" }]}
              >
                {OPEN_NOTOPEN_MAP.map((item) => (
                  <Form.Radio key={item.id} value={item.id}>
                    {item.name}
                  </Form.Radio>
                ))}
              </Form.RadioGroup>
            </Col>
          </Row>
        </Card>

        <Card className="hover:shadow-md transition-all border-l-4 border-l-[#12A182]">
          <div className="flex items-center mb-4">
            <span className="text-lg font-medium">其他设置</span>
            <Divider layout="vertical" margin="12px" />
            <span className="text-xs text-gray-500">配置截图相关参数</span>
          </div>
          <Row gutter={gutter}>
            <Col span={12}>
              <Form.InputNumber
                label="截图操作超时时间(秒)"
                field="screenshotTimeout"
                trigger="blur"
                rules={rules}
              />
            </Col>
            <Col span={12}>
              <Form.InputNumber
                label="截图文件最小文件大小"
                field="screenshotMinSize"
                trigger="blur"
                rules={rules}
              />
            </Col>
          </Row>
        </Card>
      </motion.div>
    );
  };

  // 优化的江西-万年凤巢工业区表单内容组件
  const JiangxiWannianFengchaoConfigContent = ({
    readonly = false,
    ...restProps
  }) => {
    const formState = useFormState();

    const gutter = 24;
    const rules = [{ required: true, message: "此为必填项" }];

    if (formState?.values.reportType !== 3) return null;

    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        transition={{ duration: 0.3 }}
        className="space-y-4"
      >
        <Card className="hover:shadow-md transition-all border-l-4 border-l-[#0072E5]">
          <div className="flex items-center mb-4">
            <span className="text-lg font-medium">上报基础设置</span>
            <Divider layout="vertical" margin="12px" />
            <span className="text-xs text-gray-500">
              配置基本信息和连接参数
            </span>
          </div>
          <Row gutter={gutter}>
            <Col span={12}>
              <Form.Input label="企业编码" field="companyCode" trigger="blur" />
            </Col>
            <Col span={12}>
              <Form.Input
                label="上报接入地址"
                field="urlPrefix"
                trigger="blur"
              />
            </Col>
          </Row>
          <Row gutter={gutter}>
            <Col span={12}>
              <Form.Input
                label="企业名称"
                field="companyName"
                trigger="blur"
                rules={rules}
              />
            </Col>
            <Col span={12}>
              <Form.Input
                label="企业信用代码"
                field="socialCreditCode"
                trigger="blur"
                rules={rules}
              />
            </Col>
          </Row>
        </Card>

        <Card className="hover:shadow-md transition-all border-l-4 border-l-[#0072E5]">
          <div className="flex items-center mb-4">
            <span className="text-lg font-medium">上报开关设置</span>
            <Divider layout="vertical" margin="12px" />
            <span className="text-xs text-gray-500">控制上报功能开关</span>
          </div>
          <Row gutter={gutter}>
            <Col span={12}>
              <Form.RadioGroup
                label="作业票上报是否打开"
                field="ticketIsOn"
                rules={[{ required: true, message: "此为必填项" }]}
              >
                {OPEN_NOTOPEN_MAP.map((item) => (
                  <Form.Radio key={item.id} value={item.id}>
                    {item.name}
                  </Form.Radio>
                ))}
              </Form.RadioGroup>
            </Col>
            <Col span={12}>
              <Form.RadioGroup
                label="作业报备上报是否打开"
                field="ticketBackupIsOn"
                rules={[{ required: true, message: "此为必填项" }]}
              >
                {OPEN_NOTOPEN_MAP.map((item) => (
                  <Form.Radio key={item.id} value={item.id}>
                    {item.name}
                  </Form.Radio>
                ))}
              </Form.RadioGroup>
            </Col>
          </Row>
          <Row gutter={gutter}>
            <Col span={12}>
              <Form.RadioGroup
                label="作业流程上报是否打开"
                field="ticketProgressIsOn"
                rules={[{ required: true, message: "此为必填项" }]}
              >
                {OPEN_NOTOPEN_MAP.map((item) => (
                  <Form.Radio key={item.id} value={item.id}>
                    {item.name}
                  </Form.Radio>
                ))}
              </Form.RadioGroup>
            </Col>
            <Col span={12}>
              <Form.RadioGroup
                label="作业票截屏上报是否打开"
                field="ticketScreenshotIsOn"
                rules={[{ required: true, message: "此为必填项" }]}
              >
                {OPEN_NOTOPEN_MAP.map((item) => (
                  <Form.Radio key={item.id} value={item.id}>
                    {item.name}
                  </Form.Radio>
                ))}
              </Form.RadioGroup>
            </Col>
          </Row>
        </Card>

        <Card className="hover:shadow-md transition-all border-l-4 border-l-[#12A182]">
          <div className="flex items-center mb-4">
            <span className="text-lg font-medium">其他设置</span>
            <Divider layout="vertical" margin="12px" />
            <span className="text-xs text-gray-500">配置截图相关参数</span>
          </div>
          <Row gutter={gutter}>
            <Col span={12}>
              <Form.InputNumber
                label="截图操作超时时间(秒)"
                field="screenshotTimeout"
                trigger="blur"
                rules={rules}
              />
            </Col>
            <Col span={12}>
              <Form.InputNumber
                label="截图文件最小文件大小"
                field="screenshotMinSize"
                trigger="blur"
                rules={rules}
              />
            </Col>
          </Row>
        </Card>
      </motion.div>
    );
  };

  return {
    JiangxiConfigContent,
    JiangxiWannianFengchaoConfigContent,
    YunNanConfigContent,
  };
};
