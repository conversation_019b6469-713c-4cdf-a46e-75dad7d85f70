import { useState } from "react";
import AlarmPriorityPie from "./components/AlarmPriorityPie";
import AlarmTrendChart from "./components/AlarmTrendChart";
import AlarmNumStatsTable from "./components/AlarmNumStatsTable";
import BasicStats from "./components/BasicStats";
import AlarmDurationStatsTable from "./components/AlarmDurationStatsTable";
import FilterBar from "./components/FilterBar";
import MonitorTypePie from "./components/MonitorTypePie";

export function AlarmIndexContent() {
  // 统一管理筛选条件
  const [filter, setFilter] = useState<{
    areaId: number | null;
    beginDate: string | null;
    endDate: string | null;
  }>({
    areaId: null,
    beginDate: null,
    endDate: null,
  });

  return (
    <div className="page-root">
      <BasicStats />
      <FilterBar value={filter} onChange={setFilter} />
      <AlarmTrendChart filter={filter} />
      <div className="grid grid-cols-2 gap-x-5">
        <MonitorTypePie filter={filter} />
        <AlarmPriorityPie filter={filter} />
      </div>
      <div className="grid grid-cols-2 gap-x-5">
        <AlarmNumStatsTable filter={filter} />
        <AlarmDurationStatsTable filter={filter} />
      </div>
    </div>
  );
}
