import { getPriorityStat } from "api/alarm/alarmStat";
import {
  <PERSON><PERSON><PERSON>,
  buildPieCenterContent,
  buildPieOption,
} from "components/chart/PieChart";

interface AlarmPriorityPieProps {
  filter: {
    areaId: number | null;
    beginDate: string | null;
    endDate: string | null;
  };
}

export default function AlarmPriorityPie({ filter }: AlarmPriorityPieProps) {
  return (
    <PieChart
      title="报警优先级分布"
      queryKey={["getPriorityStat"]}
      queryFn={getPriorityStat}
      filter={filter}
      optionBuilder={buildPieOption({ nameField: "name", valueField: "num" })}
      centerContent={buildPieCenterContent({
        totalField: "num",
        label: "报警总计",
      })}
      height={300}
    />
  );
}
