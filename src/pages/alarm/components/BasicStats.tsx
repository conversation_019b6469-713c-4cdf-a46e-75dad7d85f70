import { useQuery } from "@tanstack/react-query";
import { getAlarmDashStat } from "api/alarm/alarmStat";

// 首页统计数据类型，结构与接口一致
interface AlarmTopStats {
  sensorNum: number;
  sensorActiveNum: number;
  monitorTypeNum: number;
  alarmReasonNum: number;
  alarmMeasureNum: number;
}

// 渐变色配置，与首页风格一致
const cardBg = [
  "bg-gradient-to-r from-[#44DFDF] to-[#1EA0EA]",
  "bg-gradient-to-r from-[#25AFFE] to-[#AF62C7]",
  "bg-gradient-to-r from-[#F4C258] to-[#F99336]",
  "bg-gradient-to-r from-[#94DD64] to-[#0BCB96]",
  "bg-gradient-to-r from-[#FFB347] to-[#FFCC33]",
];

export default function BasicStats() {
  const { data, isLoading } = useQuery({
    queryKey: ["getAlarmDashStat"],
    queryFn: getAlarmDashStat,
  });

  // 兜底处理，接口返回data为对象
  const d = (data?.data ?? {}) as Partial<AlarmTopStats>;
  const stats = [
    { label: "监测指标数", value: d.sensorNum ?? 0 },
    { label: "运行中监测指标", value: d.sensorActiveNum ?? 0 },
    { label: "指标类型", value: d.monitorTypeNum ?? 0 },
    { label: "报警原因", value: d.alarmReasonNum ?? 0 },
    { label: "报警措施", value: d.alarmMeasureNum ?? 0 },
  ];

  if (isLoading) {
    return (
      <div className="h-40 flex items-center justify-center">
        <span className="text-gray-400">加载中...</span>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-5 gap-5 mb-6">
      {stats.map((item, idx) => (
        <div
          key={item.label}
          className={`flex flex-col justify-evenly overflow-hidden rounded-lg ${cardBg[idx % cardBg.length]} p-6 h-40 relative group cursor-pointer`}
        >
          <div className="flex flex-col text-white justify-evenly">
            {/* 主数据，字体加粗加大 */}
            <p className="font-black text-[34px] group-hover:underline">
              {item.value}
            </p>
            {/* 标题 */}
            <p className="text-lg">{item.label}</p>
          </div>
        </div>
      ))}
    </div>
  );
}
