import { getMonitorTypeStat } from "api/alarm/alarmStat";
import {
  buildPieCenterContent,
  buildPieOption,
  PieC<PERSON>,
} from "components/chart/PieChart";

interface MonitorTypePieProps {
  filter: {
    areaId: number | null;
    beginDate: string | null;
    endDate: string | null;
  };
}

export default function MonitorTypePie({ filter }: MonitorTypePieProps) {
  return (
    <PieChart
      title="监测类型分布"
      queryKey={["getMonitorTypeStat"]}
      queryFn={getMonitorTypeStat}
      filter={filter}
      optionBuilder={buildPieOption({ nameField: "name", valueField: "num" })}
      centerContent={buildPieCenterContent({
        totalField: "num",
        label: "监测类型总计",
      })}
      height={300}
    />
  );
}
