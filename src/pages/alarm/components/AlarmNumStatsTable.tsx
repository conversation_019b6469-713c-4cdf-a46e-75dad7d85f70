import { TableChart } from "components/chart/TableChart";
import {
  getAreaStat,
  getDepartmentStat,
  getDeviceStat,
} from "../../../api/alarm/alarmStat";

interface AlarmNumStatsTableProps {
  filter: {
    areaId: number;
    beginDate: string;
    endDate: string;
    topN?: number;
  };
}

export default function AlarmNumStatsTable({
  filter,
}: AlarmNumStatsTableProps) {
  const tabList = [
    {
      label: "区域统计",
      value: "area",
      queryKey: ["getAreaStat"],
      queryFn: getAreaStat,
      columns: [
        {
          title: "区域名称",
          dataIndex: "area.name",
          align: "left" as const,
        },
        {
          title: "报警数量",
          dataIndex: "num",
          align: "center" as const,
          render: (value: number) => (
            <span className="font-bold text-blue-600">{value}</span>
          ),
        },
      ],
    },
    {
      label: "设备统计",
      value: "device",
      queryKey: ["getDeviceStat"],
      queryFn: getDeviceStat,
      columns: [
        {
          title: "设备名称",
          dataIndex: "equipment.name",
          align: "left" as const,
        },
        {
          title: "报警数量",
          dataIndex: "num",
          align: "center" as const,
          render: (value: number) => (
            <span className="font-bold text-red-600">{value}</span>
          ),
        },
      ],
    },
    {
      label: "部门统计",
      value: "department",
      queryKey: ["getDepartmentStat"],
      queryFn: getDepartmentStat,
      columns: [
        {
          title: "部门名称",
          dataIndex: "department.name",
          align: "left" as const,
        },
        {
          title: "报警数量",
          dataIndex: "num",
          align: "center" as const,
          render: (value: number) => (
            <span className="font-bold text-red-600">{value}</span>
          ),
        },
      ],
    },
  ];

  return (
    <TableChart
      title="报警统计"
      filter={filter}
      tabList={tabList}
      height={400}
      emptyText="暂无报警次数数据"
    />
  );
}
