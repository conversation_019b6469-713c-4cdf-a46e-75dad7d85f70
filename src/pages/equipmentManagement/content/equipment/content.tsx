import { useQueryClient } from "@tanstack/react-query";
import { equipmentManagementEquipmentApis } from "api";
import {
  equipmentManagementDetectionEditModalAtom,
  equipmentManagementEquipmentAtoms,
  equipmentManagementEquipmentDetailSideAtom,
  equipmentManagementMaintenanceEditModalAtom,
  equipmentManagementRepairEditModalAtom,
  equipmentManagementResumeEditModalAtom,
  equipmentManagementScrapEditModalAtom,
  equipmentManagementStopEditModalAtom,
  sensorEditModalAtom,
} from "atoms";
import { ExportProvider, List } from "components";
import { useAtom } from "jotai";
import { FC, useCallback } from "react";

type EquipmentManagementEquipmentContentProps = {
  callback?: any;
  layout?: string;
  referAtom?: any;
  readonly?: boolean;
  filter?: any;
};
export const EquipmentManagementEquipmentContent: FC<
  EquipmentManagementEquipmentContentProps
> = ({
  callback,
  layout,
  referAtom,
  readonly = false,
  filter = {},
  ...restProps
}) => {
  if (layout === "modal") {
    readonly = true;
  }
  const queryClient = useQueryClient();
  const queryKey = "list" + equipmentManagementEquipmentAtoms.entity;

  const [detailSide, setDetailSide] = useAtom(
    equipmentManagementEquipmentDetailSideAtom
  );
  const handleOpenDetailSide = useCallback(
    (record) => {
      setDetailSide({
        id: record.id,
        show: true,
      });
    },
    [setDetailSide]
  );

  const detailOp = {
    engName: "detail",
    chnName: "查看详情",
    func: handleOpenDetailSide,
  };

  const [sensorModal, setSensorModal] = useAtom(sensorEditModalAtom);
  const handleOpenSensorModal = useCallback(
    (record) => {
      setSensorModal({
        id: "",
        show: true,
        equipment: record,
      });
    },
    [setSensorModal]
  );
  const sensorOp = {
    engName: "sensor",
    chnName: "监测",
    func: handleOpenSensorModal,
  };

  const [maitenanceModal, setMaintenanceModal] = useAtom(
    equipmentManagementMaintenanceEditModalAtom
  );
  const handleOpenMaitenanceModal = useCallback(
    (record) => {
      setMaintenanceModal({
        id: "",
        show: true,
        equipment: record,
      });
    },
    [setMaintenanceModal]
  );
  const maintenanceOp = {
    engName: "maintenance",
    chnName: "保养",
    func: handleOpenMaitenanceModal,
  };

  const [detectionModal, setDetectionModal] = useAtom(
    equipmentManagementDetectionEditModalAtom
  );
  const handleOpenDetectionModal = useCallback(
    (record) => {
      setDetectionModal({
        id: "",
        show: true,
        equipment: record,
      });
    },
    [setDetectionModal]
  );
  const detectionOp = {
    engName: "detection",
    chnName: "检测",
    func: handleOpenDetectionModal,
  };

  const [repairModal, setRepairModal] = useAtom(
    equipmentManagementRepairEditModalAtom
  );
  const handleOpenRepairModal = useCallback(
    (record) => {
      setRepairModal({
        id: "",
        show: true,
        equipment: record,
      });
    },
    [setRepairModal]
  );
  const toggleRepairOp = (record) => {
    return record.status !== 2 && record.status !== 4
      ? {
          engName: "repair",
          chnName: "维修",
          func: handleOpenRepairModal,
        }
      : null;
  };

  const [stopModal, setStopModal] = useAtom(
    equipmentManagementStopEditModalAtom
  );
  const handleOpenStopModal = useCallback(
    (record) => {
      setStopModal({
        id: "",
        show: true,
        equipment: record,
      });
    },
    [setStopModal]
  );
  const toggleStopOp = (record) => {
    return record.status !== 2 && record.status !== 3 && record.status !== 4
      ? {
          engName: "stop",
          chnName: "停用",
          func: handleOpenStopModal,
        }
      : null;
  };

  const [resumeModal, setResumeModal] = useAtom(
    equipmentManagementResumeEditModalAtom
  );
  const handleOpenResumeModal = useCallback(
    (record) => {
      setResumeModal({
        id: "",
        show: true,
        equipment: record,
      });
    },
    [setResumeModal]
  );
  const toggleResumeOp = (record) => {
    return record.status !== 3 && record.status !== 4
      ? {
          engName: "resume",
          chnName: "恢复",
          func: handleOpenResumeModal,
        }
      : null;
  };

  const [scrapModal, setScrapModal] = useAtom(
    equipmentManagementScrapEditModalAtom
  );
  const handleOpenScrapModal = useCallback(
    (record) => {
      setScrapModal({
        id: "",
        show: true,
        equipment: record,
      });
    },
    [setScrapModal]
  );
  const toggleScrapOp = (record) => {
    return record.status !== 4
      ? {
          engName: "scrap",
          chnName: "报废",
          func: handleOpenScrapModal,
        }
      : null;
  };

  const importProps = {
    entity: "设备管理信息",
    excelType: "equipmentinfo_template",
    downUrl: encodeURI("/static/template/设备档案信息导入模板.xlsx"),
    tip: "请先准备好对应的设备类型/区域/部门/责任人信息，否则导入会失败",
  };

  return (
    <ExportProvider>
      <List
        atoms={equipmentManagementEquipmentAtoms}
        apis={equipmentManagementEquipmentApis}
        // operations, dynamicOperationFuncs
        _operations={[detailOp, sensorOp, maintenanceOp, detectionOp]}
        dynamicOperationFuncs={[
          toggleRepairOp,
          toggleStopOp,
          toggleResumeOp,
          toggleScrapOp,
        ]}
        importProps={importProps}
        filter={filter}
        callback={callback}
        referAtom={referAtom}
        layout={layout}
        readonly={readonly}
        {...restProps}
      />
    </ExportProvider>
  );
};
