import {
  batchResumeEquipmentSensor,
  batchStopEquipmentSensor,
  equipmentSensorApis,
  resumeEquipmentSensor,
  stopEquipmentSensor,
} from "api/equipmentManagement/equipmentSensor";
import { SensorContent, SensorFilter } from "pages/majorHazard/content";
import { SensorModal } from "pages/majorHazard/modal/sensorModal";
import { SensorConfig } from "types/sensorConfig";

// 设备监测配置
const equipmentSensorConfig: SensorConfig = {
  // API配置
  apis: equipmentSensorApis,

  // 实体名称
  entity: "EquipmentSensor",

  // 原子状态
  atoms: equipmentSensorAtoms,

  // 字段验证配置
  fieldValidation: {
    equipmentId: {
      required: true,
      rules: [{ required: true, message: "请选择所属设备" }],
    },
  },

  // 导入配置
  importProps: {
    entity: "设备监测信息",
    excelType: "equipment_sensor_template",
    downUrl: encodeURI("/static/template/设备监测信息导入模板.xlsx"),
    tip: "请先准备好对应的设备信息/监测类型/区域信息，否则导入会失败",
  },

  // 操作配置
  operations: {
    resumeApi: resumeEquipmentSensor,
    stopApi: stopEquipmentSensor,
    batchResumeApi: batchResumeEquipmentSensor,
    batchStopApi: batchStopEquipmentSensor,
  },

  // 模态框配置
  modalConfig: {
    newTitle: "新增设备监测信息",
    editTitle: "编辑设备监测信息",
    requiredFields: ["equipmentId"],
  },
};

export function EquipmentSensorPage() {
  return (
    <div className="flex flex-col gap-4">
      <SensorFilter />
      <SensorModal config={equipmentSensorConfig} />
      <SensorContent config={equipmentSensorConfig} />
    </div>
  );
}
