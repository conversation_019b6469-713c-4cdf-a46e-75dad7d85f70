/* import { <PERSON><PERSON><PERSON>er, RoleContent, RoleSide } from './content'
import { RoleModal } from './modal'
 */

import {
  getEquipmentInspectionList,
  getEquipmentManagementDetectionList,
  getEquipmentManagementEquipment,
  getEquipmentManagementMaintenanceList,
  getEquipmentManagementRepairList,
  getEquipmentMonitorList,
  getEquipmentSensorList,
} from "api";
import {
  equipmentAtoms,
  equipmentInspectionColumnsAtom,
  equipmentInspectionFilterAtom,
  equipmentManagementEquipmentDetailSideAtom,
} from "atoms";
import {
  DETAIL_STATUS_MAP,
  EQUIPMENT_DETECTION_STATUS_MAP,
  EQUIPMENT_REPAIRTYPE_MAP,
  EQUIPMENT_STATUS_MAP,
  INTELLIGENTINSPECTION_SIGNIN_METHCOD_MAP,
  IS_ISNOT_ACTIVE_MAP,
  IS_ISNOT_MAP,
  ISABNORMAL_MAP,
  JUDGERESULT_MAP,
  MONITOR_TYPE_MAP,
  NEED_ORNOT_MAP,
  SENSOR_ALERTPRIORITY_MAP,
  SENSOR_DATATYPE_MAP,
  SENSOR_STATUS_MAP,
} from "components";
import { RenderSideDetailList } from "components/detail/renderSideTabPaneList";
import { RenderSideTabPaneWithStaticData } from "components/detail/renderSideTabPaneWithStaticData";
import SideDetail from "components/detail/sideDetail";
import { useAtom } from "jotai";
import { SensorModal } from "pages/majorHazard/modal";
import { Divider } from "tdesign-react";
import { DoubleGuardRoutes } from "utils/routerConstants";
import {
  EquipmentManagementEquipmentContent,
  EquipmentManagementEquipmentFilter,
} from "./content";
import { MiscDetail } from "./content/equipment/miscDetail";
import {
  EquipmentManagementDetectionModal,
  EquipmentManagementMaintenanceModal,
  EquipmentManagementRepairModal,
  EquipmentManagementResumeModal,
  EquipmentManagementScrapModal,
  EquipmentManagementStopModal,
} from "./modal";
import { EquipmentManagementEquipmentModal } from "./modal/equipmentModal";

export function EquipmentManagementEquipmentPage({
  readonly = false,
  filter = {},
  ...restProps
}) {
  const [detailSideAtom, setDetailSideAtom] = useAtom(
    equipmentManagementEquipmentDetailSideAtom
  );
  const basicTab = {
    entity: equipmentAtoms.entity,
    entityTitle: "基本信息",
    api: getEquipmentManagementEquipment,
    infoOrList: 1,
    scheme: [
      {
        groupName: "设备信息",
        list: [
          {
            label: "设备名称",
            name: "name",
          },
          {
            label: "设备编号",
            name: "code",
            type: "string",
          },
          {
            label: "设备类型",
            name: "equipmentCategory",
            type: "entity",
          },
          {
            label: "设备状态",
            name: "status",
            type: "enum",
            enumMap: EQUIPMENT_STATUS_MAP,
          },
          {
            label: "设备厂家",
            name: "manufacturer",
          },
          {
            label: "设备型号",
            name: "model",
          },
          {
            label: "介质",
            name: "goods",
          },
          {
            label: "用途",
            name: "usage",
          },
          {
            label: "设备位号",
            name: "positionNumber",
          },
          {
            label: "所属区域",
            name: "area",
            type: "entity",
          },
          {
            label: "使用部门",
            name: "department",
            type: "entity",
          },
          {
            label: "负责人",
            name: "liablePerson",
            type: "entity",
          },
          {
            label: "出厂日期",
            name: "publishDate",
            type: "date",
          },
          {
            label: "投用日期",
            name: "validDate",
            type: "date",
          },
          {
            label: "使用期限",
            name: "expirationMonth",
            type: "number",
          },
          {
            label: "备注",
            name: "note",
          },
          {
            label: "附件列表",
            name: "attachmentList",
            type: "file",
          },
        ],
      },
      {
        groupName: "监测信息",
        list: [
          {
            label: "关联监控列表",
            name: "monitorList",
            type: "array",
          },
          {
            label: "传感器列表",
            name: "sensorList",
            type: "array",
          },
          {
            label: "是否需要检测",
            name: "needDetection",
            type: "enum",
            enumMap: NEED_ORNOT_MAP,
          },
          {
            label: "检测周期(天)",
            name: "detectionDayInterval",
            type: "number",
            unit: "天",
          },
          {
            label: "检测提前通知(天)",
            name: "detectionInformDays",
            type: "number",
            unit: "天",
          },
          {
            label: "是否需要维保",
            name: "needMaintenance",
            type: "enum",
            enumMap: NEED_ORNOT_MAP,
          },
          {
            label: "维保周期(天)",
            name: "maintenanceDayInterval",
            type: "number",
            unit: "天",
          },
          {
            label: "维保提前通知(天)",
            name: "maintenanceInformDays",
            type: "number",
            unit: "天",
          },
        ],
      },
    ],
  };
  const sensorTab = {
    entity: "EquipmentManagementEquipmentSensorList",
    entityTitle: "监控信息",
    api: getEquipmentSensorList,
    params: detailSideAtom?.id,
    infoOrList: 2,
    scheme: [
      {
        table: {
          dataSource: "",
          columns: [
            { label: "监测名称", name: "name" },
            { label: "传感器编号", name: "code" },
            { label: "计量单位", name: "unit" },
            {
              label: "是否上传",
              name: "needUpload",
              type: "enum",
              enumMap: IS_ISNOT_MAP,
            },
            { label: "阈值上限", name: "highAlarm" },
            { label: "阈值上上限", name: "highHighAlarm" },
            { label: "阈值下限", name: "lowAlarm" },
            { label: "阈值下下限", name: "lowLowAlarm" },
            { label: "采样值", name: "sampleValue" },
            { label: "采样时间", name: "sampleTime", type: "datetime" },
            { label: "数据表名", name: "measurementName" },
            { label: "数据字段名", name: "fieldName" },
            {
              label: "运行状态",
              name: "isActive",
              type: "enum",
              enumMap: IS_ISNOT_ACTIVE_MAP,
            },
            {
              label: "状态",
              name: "status",
              type: "enum",
              enumMap: SENSOR_STATUS_MAP,
            },
            {
              label: "报警优先级",
              name: "priority",
              type: "enum",
              enumMap: SENSOR_ALERTPRIORITY_MAP,
            },
            {
              label: "数据类型",
              name: "type",
              type: "enum",
              enumMap: SENSOR_DATATYPE_MAP,
            },
          ],
        },
      },
    ],
  };

  const monitorTab = {
    entity: "EquipmentManagementEquipmentMonitorList",
    entityTitle: "视频信息",
    api: getEquipmentMonitorList,
    params: detailSideAtom?.id,
    infoOrList: 2,
    scheme: [
      {
        table: {
          dataSource: "",
          columns: [
            { label: "名称", name: "name" },
            {
              label: "摄像头类型",
              name: "type",
              type: "enum",
              enumMap: MONITOR_TYPE_MAP,
            },
            { label: "区域信息", name: "area", type: "entity" },
            { label: "视频地址", name: "videoPath", type: "video" },
            {
              label: "是否在一张图呈现",
              name: "isInMap",
              type: "enum",
              enumMap: IS_ISNOT_MAP,
            },
          ],
        },
      },
    ],
  };

  const maintenanceTab = {
    entity: "EquipmentManagementEquipmentMaintenanceList",
    entityTitle: "保养信息",
    api: getEquipmentManagementMaintenanceList,
    params: {
      filter: { equipmentId: detailSideAtom?.id },
    },
    infoOrList: 2,
    scheme: [
      {
        table: {
          dataSource: "results",
          columns: [
            { label: "ID", name: "id" },
            { label: "设备", name: "equipment", type: "entity" },
            { label: "设备类型", name: "equipmentCategory", type: "entity" },
            {
              label: "保养负责人",
              name: "liablePersonList",
              type: "array",
              render: "entity",
            },
            { label: "保养日期", name: "maintenanceTime", type: "date" },
            {
              label: "是否本单位保养",
              name: "isSelfOperation",
              type: "enum",
              enumMap: IS_ISNOT_MAP,
            },
          ],
        },
      },
    ],
  };

  const detectionTab = {
    entity: "EquipmentManagementEquipmentDetectionList",
    entityTitle: "检测信息",
    api: getEquipmentManagementDetectionList,
    params: {
      filter: { equipmentId: detailSideAtom?.id },
    },
    infoOrList: 2,
    scheme: [
      {
        table: {
          dataSource: "results",
          columns: [
            { label: "ID", name: "id" },
            { label: "设备", name: "equipment", type: "entity" },
            { label: "设备类型", name: "equipmentCategory", type: "entity" },
            { label: "检测人", name: "detectionPerson", type: "entity" },
            { label: "检测日期", name: "detectionTime", type: "date" },
            {
              label: "检测状态",
              name: "detectionStatus",
              type: "enum",
              enumMap: EQUIPMENT_DETECTION_STATUS_MAP,
            },
            {
              label: "是否本厂检测",
              name: "isSelfOperation",
              type: "enum",
              enumMap: IS_ISNOT_MAP,
            },
          ],
        },
      },
    ],
  };

  const repairTab = {
    entity: "EquipmentManagementEquipmentRepairList",
    entityTitle: "维修信息",
    api: getEquipmentManagementRepairList,
    params: {
      filter: { equipmentId: detailSideAtom?.id },
    },
    infoOrList: 2,
    scheme: [
      {
        table: {
          dataSource: "results",
          columns: [
            { label: "ID", name: "id" },
            { label: "设备", name: "equipment", type: "entity" },
            { label: "设备类型", name: "equipmentCategory", type: "entity" },
            { label: "负责人", name: "liablePerson", type: "entity" },
            {
              label: "维修类型",
              name: "repairType",
              type: "enum",
              enumMap: EQUIPMENT_REPAIRTYPE_MAP,
            },
            {
              label: "是否本厂维修",
              name: "isSelfOperation",
              type: "enum",
              enumMap: IS_ISNOT_MAP,
            },
            {
              label: "维修开始时间",
              name: "repairBeginTime",
              type: "datetime",
            },
            { label: "维修结束时间", name: "repairEndTime", type: "datetime" },
          ],
        },
      },
    ],
  };

  const basicScheme = [
    {
      groupname: "",
      list: [
        { label: "所在区域", name: "area", type: "entity" },
        { label: "责任人", name: "liablePerson", type: "entity" },
        { label: "经度", name: "longitude" },
        { label: "纬度", name: "latitude" },
        { label: "关联设备", name: "equipments", type: "array" },
        {
          label: "巡检模式",
          name: "checkMethod",
          type: "enum",
          enumMap: INTELLIGENTINSPECTION_SIGNIN_METHCOD_MAP,
        },
        {
          label: "巡检状态",
          name: "status",
          type: "enum",
          enumMap: DETAIL_STATUS_MAP,
        },
        {
          label: "是否异常",
          name: "isAbnormal",
          type: "enum",
          enumMap: ISABNORMAL_MAP,
        },
        // { label: "巡检项", name: "standards", type: "array" },
      ],
    },
  ];

  const standardScheme = [
    {
      table: {
        columns: [
          {
            label: "巡检标准类别",
            name: "category",
            width: 120,
          },
          {
            label: "检查内容",
            name: "content",
            width: 120,
          },
          {
            label: "检查状态",
            name: "status",
            type: "enum",
            enumMap: DETAIL_STATUS_MAP,
            width: 100,
          },
          {
            label: "检查结果",
            name: "judgeResult",
            type: "enum",
            enumMap: JUDGERESULT_MAP,
            render: "link",
            link: DoubleGuardRoutes.DANGER, //"double_guard/danger/danger",
            filterField: "dangerId",
            filterTargetField: "id",
            width: 100,
          },
          {
            label: "检查部位",
            name: "place",
            width: 100,
          },
          {
            label: "检查方式",
            name: "method",
            width: 100,
          },
          {
            label: "负责人",
            name: "liablePerson",
            type: "entity",
            width: 80,
          },
          {
            label: "巡检时间",
            name: "processTime",
            type: "datetime",
            width: 160,
          },
          {
            label: "图片",
            name: "imageList",
            type: "imagelist",
          },
          {
            label: "视频",
            name: "videoList",
            type: "filelist",
          },
          {
            label: "填数",
            name: "sampleValue",
            width: 80,
          },
          {
            label: "备注",
            name: "note",
            width: 120,
          },
        ],
      },
    },
  ];

  const expandedDataCallback = (expandedData: any) => {
    // 计算侧边栏实际宽度
    const sidebarWidth = document.querySelector(".sidebar")?.clientWidth || 220; // 获取实际侧边栏宽度，默认220px

    const expandRowRender = (record: any, index: number) => {
      console.debug("expandedDataCallback", expandedData);
      return (
        <div
          style={{
            position: "relative",
            width: "100%",
            maxWidth: `calc(100vw - ${sidebarWidth}px)`, // 使用动态计算的宽度
            backgroundColor: "#fff",
            padding: 0,
            margin: 0,
            overflow: "hidden", // 添加这个属性防止内容溢出
            borderRadius: "4px", // 添加圆角
            boxShadow: "0 1px 2px rgba(0, 0, 0, 0.05)", // 添加轻微阴影
          }}
        >
          {/* 基本信息部分 */}
          <div
            style={{
              marginBottom: "16px",
              width: "100%",
              backgroundColor: "#fff",
              overflow: "hidden", // 防止内容溢出
            }}
          >
            <RenderSideTabPaneWithStaticData
              entity="IntelligentInspectionTaskRecordPlace"
              entityTitle={expandedData[index]?.name ?? "暂无数据"}
              scheme={basicScheme}
              infoOrList={1}
              dataSource={expandedData[index]}
              // 添加样式覆盖
              style={{ width: "100%", overflow: "hidden" }}
            />
          </div>

          <Divider align="center" style={{ margin: "12px 0" }}>
            <span>巡检记录</span>
          </Divider>

          <div
            style={{
              marginBottom: "16px",
              width: "100%",
              backgroundColor: "#fff",
              overflow: "hidden", // 防止内容溢出
            }}
          >
            <RenderSideTabPaneWithStaticData
              entity="IntelligentInspectionTaskRecordStandard"
              entityTitle={expandedData[index]?.name ?? "暂无数据"}
              scheme={standardScheme}
              infoOrList={2}
              dataSource={expandedData[index]?.standards}
              // 添加样式覆盖
              style={{ width: "100%", overflow: "hidden" }}
            />
          </div>
        </div>
      );
    };
    return expandRowRender;
  };

  const tabPaneList = [
    basicTab,
    sensorTab,
    monitorTab,
    maintenanceTab,
    detectionTab,
    repairTab,
    {
      entity: "EquipmentManagementEquipmentMiscDetail",
      entityTitle: "其他信息",
      child: MiscDetail,
      childProps: {
        detailAtom: equipmentManagementEquipmentDetailSideAtom,
      },
    },
    {
      entity: "EquipmentManagementEquipmentIiPlaceList",
      entityTitle: "点巡检",
      /* child: IiPlaceDetail,
      childProps: {
        detailAtom: equipmentManagementEquipmentDetailSideAtom,
      }, */
      child: RenderSideDetailList,
      childProps: {
        entity: "EquipmentManagementEquipmentIiPlaceDetail",
        columnsAtom: equipmentInspectionColumnsAtom,
        filterAtom: equipmentInspectionFilterAtom,
        queryApi: getEquipmentInspectionList,
        detailAtom: equipmentManagementEquipmentDetailSideAtom,
        expandedDataCallback: expandedDataCallback,
      },
    },
  ];

  return (
    <div className="flex flex-col gap-4 ">
      {/* <RoleSide />
      <RoleModal />
      <RoleFilter />
      <RoleContent /> */}
      <EquipmentManagementEquipmentFilter filter={filter} />
      <EquipmentManagementEquipmentModal />
      <EquipmentManagementMaintenanceModal />
      <EquipmentManagementDetectionModal />
      <EquipmentManagementRepairModal />
      <EquipmentManagementStopModal />
      <EquipmentManagementResumeModal />
      <EquipmentManagementScrapModal />
      <SensorModal
        fieldValidation={{
          equipmentId: {
            required: true,
            rules: [{ required: true, message: "请选择所属设备" }],
          },
        }}
      />
      <EquipmentManagementEquipmentContent
        readonly={readonly}
        filter={filter}
        {...restProps}
      />
      <SideDetail
        entityTitle="设备详情"
        detailAtom={equipmentManagementEquipmentDetailSideAtom}
        tabPaneList={tabPaneList}
      />
    </div>
  );
}
