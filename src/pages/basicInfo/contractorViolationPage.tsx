/* import { <PERSON>Filter, RoleContent, RoleSide } from './content'
import { RoleModal } from './modal'
 */

import {
  BasicInfoContractorViolationContent,
  BasicInfoContractorViolationFilter,
} from "./content";
import { BasicInfoContractorViolationModal } from "./modal/contractorViolationModal";

export function BasicInfoContractorViolationPage({
  readonly = false,
  filter = {},
  ...restProps
}) {
  return (
    <div className="flex flex-col gap-4 ">
      {/* <RoleSide />
      <RoleModal />
      <RoleFilter />
      <RoleContent /> */}
      <BasicInfoContractorViolationFilter filter={filter} />
      <BasicInfoContractorViolationModal />
      <BasicInfoContractorViolationContent
        readonly={readonly}
        filter={filter}
        {...restProps}
      />
    </div>
  );
}
