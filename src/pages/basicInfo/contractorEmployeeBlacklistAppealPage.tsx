/* import { <PERSON><PERSON><PERSON>er, RoleContent, RoleSide } from './content'
import { RoleModal } from './modal'
 */

import {
  BasicInfoContractorEmployeeBlacklistAppealContent,
  BasicInfoContractorEmployeeBlacklistAppealFilter,
} from "./content";
import { BasicInfoContractorEmployeeBlacklistAppealAuditDelegateModal } from "./modal/contractorEmployeeBlacklistAppealAuditDelegateModal";
import { BasicInfoContractorEmployeeBlacklistAppealAuditModal } from "./modal/contractorEmployeeBlacklistAppealAuditModal";
import { BasicInfoContractorEmployeeBlacklistAppealModal } from "./modal/contractorEmployeeBlacklistAppealModal";

export function BasicInfoContractorEmployeeBlacklistAppealPage({
  readonly = false,
  filter = {},
  ...restProps
}) {
  return (
    <div className="flex flex-col gap-4 ">
      {/* <RoleSide />
      <RoleModal />
      <RoleFilter />
      <RoleContent /> */}
      <BasicInfoContractorEmployeeBlacklistAppealFilter filter={filter} />
      <BasicInfoContractorEmployeeBlacklistAppealModal />
      <BasicInfoContractorEmployeeBlacklistAppealAuditModal />
      <BasicInfoContractorEmployeeBlacklistAppealAuditDelegateModal />
      <BasicInfoContractorEmployeeBlacklistAppealContent
        readonly={readonly}
        filter={filter}
        {...restProps}
      />
    </div>
  );
}
