/* import { <PERSON>Filter, RoleContent, RoleSide } from './content'
import { RoleModal } from './modal'
 */

import {
  BasicInfoContractorEntryApplyContent,
  BasicInfoContractorEntryApplyFilter,
} from "./content";
import { BasicInfoContractorEntryApplyAuditDelegateModal } from "./modal/contractorEntryApplyAuditDelegateModal";
import { BasicInfoContractorEntryApplyAuditModal } from "./modal/contractorEntryApplyAuditModal";
import { BasicInfoContractorEntryApplyModal } from "./modal/contractorEntryApplyModal";

export function BasicInfoContractorEntryApplyPage({
  readonly = false,
  filter = {},
  ...restProps
}) {
  return (
    <div className="flex flex-col gap-4 ">
      {/* <RoleSide />
      <RoleModal />
      <RoleFilter />
      <RoleContent /> */}
      <BasicInfoContractorEntryApplyFilter filter={filter} />
      <BasicInfoContractorEntryApplyModal />
      <BasicInfoContractorEntryApplyAuditModal />
      <BasicInfoContractorEntryApplyAuditDelegateModal />
      <BasicInfoContractorEntryApplyContent
        readonly={readonly}
        filter={filter}
        {...restProps}
      />
    </div>
  );
}
