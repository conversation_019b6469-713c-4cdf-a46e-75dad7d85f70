/* import { <PERSON>Filter, RoleContent, RoleSide } from './content'
import { RoleModal } from './modal'
 */

import {
  BasicInfoContractorEvaluationContent,
  BasicInfoContractorEvaluationFilter,
} from "./content";
import { BasicInfoContractorEvaluationModal } from "./modal/contractorEvaluationModal";

export function BasicInfoContractorEvaluationPage({
  readonly = false,
  filter = {},
  ...restProps
}) {
  return (
    <div className="flex flex-col gap-4 ">
      {/* <RoleSide />
      <RoleModal />
      <RoleFilter />
      <RoleContent /> */}
      <BasicInfoContractorEvaluationFilter filter={filter} />
      <BasicInfoContractorEvaluationModal />
      <BasicInfoContractorEvaluationContent
        readonly={readonly}
        filter={filter}
        {...restProps}
      />
    </div>
  );
}
