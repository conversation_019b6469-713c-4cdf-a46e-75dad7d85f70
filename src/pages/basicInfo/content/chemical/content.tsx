import { chemicalApis } from "api";
import { chemicalAtoms } from "atoms";
import { List } from "components";

export const ChemicalContent = () => {
  const importProps = {
    entity: "化学品",
    excelType: "chemical_template",
    downUrl: encodeURI("/static/template/化学品信息导入模板.xlsx"),
    tip: "先确保数据中的储罐/仓库/重大危险源/工艺流程等信息已录入，否则导入会失败",
  };

  return (
    <List atoms={chemicalAtoms} apis={chemicalApis} importProps={importProps} />
  );
};
