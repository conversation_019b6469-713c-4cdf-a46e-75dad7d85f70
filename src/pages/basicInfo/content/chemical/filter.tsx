import { IconSearch } from "@douyinfe/semi-icons";
import { Form } from "@douyinfe/semi-ui";
import { useQuery } from "@tanstack/react-query";
import { getChemicalList } from "api";
import { chemicalAtoms } from "atoms/basicInfo/chemical";
import { CHEMICAL_TYPE_MAP, IS_ISNOT_MAP } from "components";
import { useFilterSearch } from "hooks";
import { useMemo } from "react";

export const ChemicalFilter = () => {
  const atoms = chemicalAtoms;
  const [handleSearch, handleReset] = useFilterSearch(atoms.filter);

  const entity = atoms.entity;
  const operation = "List";

  const { data: list } = useQuery({
    queryKey: [`get${entity}${operation}`], //user-defined code here
    queryFn: getChemicalList, //user-defined code here
  });
  const options = useMemo(() => {
    return list?.data?.results ?? [];
  }, [list]);

  return (
    <div className="flex flex-col bg-white shadow rounded relative ">
      <div className="text-gray-900 text-opacity-90 text-base font-semibold leading-snug px-4 py-3 border-b border-gray-900/10 ">
        筛选总览
      </div>

      <div className="p-4 pr-0">
        <Form
          layout="horizontal"
          onSubmit={handleSearch}
          className="grid grid-cols-4 gap-y-4 "
        >
          {/* user-defined code here */}
          <Form.Input
            noLabel
            field="query"
            placeholder="请填入化学品中文名/别名/编号"
            className="w-full"
            suffix={<IconSearch />}
            showClear
          />
          <Form.Select
            field="type"
            noLabel
            placeholder="化学品类型"
            className="w-full"
          >
            {CHEMICAL_TYPE_MAP.map((item) => (
              <Form.Select.Option key={item.id} value={item.id}>
                {item.name}
              </Form.Select.Option>
            ))}
          </Form.Select>
          <Form.Select
            field="isKeyRegulatory"
            noLabel
            placeholder="是否重点监管危化品"
            className="w-full"
          >
            {IS_ISNOT_MAP.map((item) => (
              <Form.Select.Option key={item.id} value={item.id}>
                {item.name}
              </Form.Select.Option>
            ))}
          </Form.Select>

          <div className="flex gap-2">
            <button className="btn rounded btn-primary btn-sm">
              查&nbsp;&nbsp;询
            </button>
            <button
              className="btn btn-sm rounded"
              type="reset"
              onClick={handleReset}
            >
              重&nbsp;&nbsp;置
            </button>
          </div>
        </Form>
      </div>
    </div>
  );
};
