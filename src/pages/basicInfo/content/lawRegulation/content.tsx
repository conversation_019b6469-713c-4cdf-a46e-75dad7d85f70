import { useQueryClient } from "@tanstack/react-query";
import { lawRegulationApis } from "api";
import { lawRegulationAtoms } from "atoms";
import { ExportProvider, List } from "components";

export const LawRegulationContent = () => {
  const queryClient = useQueryClient();
  const queryKey = "list" + lawRegulationAtoms.entity;

  return (
    <ExportProvider>
      <List atoms={lawRegulationAtoms} apis={lawRegulationApis} />;
    </ExportProvider>
  );
};
