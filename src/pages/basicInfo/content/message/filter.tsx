import { useState, useCallback, useMemo } from "react";
import { Form, Button, Tag } from "@douyinfe/semi-ui";
import { useAtom } from "jotai";
import { IconSearch } from "@douyinfe/semi-icons";
import { useToggle } from "ahooks";
import { unitFilterAtom } from "atoms/doubleGuard";
import { useFilterSearch } from "hooks";
import {
  EmployeeSearch,
  ObjectSearch,
  DepartmentSearch,
  RISK_LEVEL_MAP,
  IS_MAJOR_HAZARD_MAP,
} from "components";

export function MessageFilter() {
  return null;
  // const [state, { toggle }] = useToggle();
  // const [handleSearch, handleReset] = useFilterSearch(unitFilterAtom)

  // return (
  //   <div className="flex flex-col bg-white shadow rounded relative ">
  //     <div className="text-gray-900 text-opacity-90 text-base font-semibold leading-snug px-4 py-3 border-b border-gray-900/10 ">
  //       筛选总览
  //     </div>

  //     <div className="p-4 pr-0">
  //       <Form
  //         layout='horizontal'
  //         onSubmit={handleSearch}
  //         className="grid grid-cols-4 gap-y-4 "
  //       >
  //         <Form.Input noLabel field='query' placeholder='关键字搜索' className="w-full" suffix={<IconSearch />} showClear />
  //         <ObjectSearch field="riskObjectId" placeholder="风险对象" />
  //         <DepartmentSearch field="liableDepartmentId" placeholder="责任部门" />

  //         {
  //           !state ? (
  //             <>
  //               <EmployeeSearch field="liablePersonId" placeholder="责任人" />
  //               <Form.Select noLabel field="riskLevel" placeholder='风险等级' className="w-full">
  //                 {
  //                   RISK_LEVEL_MAP.map((o) => (
  //                     <Form.Select.Option value={o.id}>
  //                       <Tag color={o.color} type="light">
  //                         {o.name}
  //                       </Tag>
  //                     </Form.Select.Option>
  //                   ))
  //                 }
  //               </Form.Select>
  //             </>
  //           ) : null
  //         }

  //         <div className="flex gap-2">
  //           <button className="btn rounded btn-primary btn-sm">
  //             查&nbsp;&nbsp;询
  //           </button>
  //           <button className="btn btn-sm rounded" type="reset" onClick={handleReset}>
  //             重&nbsp;&nbsp;置
  //           </button>
  //         </div>

  //         <button
  //           className="w-10 h-4 bg-white rounded-2xl shadow border absolute bottom-[-6px] left-2/4 ml-[-20px] flex items-center justify-center"
  //           onClick={toggle}
  //         >
  //           {
  //             state ? (
  //               <i className="ri-arrow-down-s-line"></i>
  //             ) : (
  //               <i className="ri-arrow-up-s-line"></i>
  //             )
  //           }
  //         </button>

  //       </Form>
  //     </div>
  //   </div >
  // )
}
