import { IconRefresh } from "@douyinfe/semi-icons";
import { Button, Checkbox, Table, Tooltip } from "@douyinfe/semi-ui";
import { useAuth } from "@reactivers/hooks";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  EntityType,
  MessageStatus,
  messageFilterAtom,
  messageFnAtom,
} from "atoms/basicInfo";
import {
  checkTaskFilterAtom,
  dangerFilterAtom,
  snapFilterAtom,
} from "atoms/doubleGuard";
import { useAtom } from "jotai";
import { uniq } from "ramda";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useNavigate } from "react-router-dom";
import { DoubleGuardRoutes } from "utils/routerConstants";

export function MessageContent({
  queryKey,
  getsApi,
  readApi,
  readAllApi,
  columnsAtom,
}) {
  const queryClient = useQueryClient();
  const navigate = useNavigate();
  const { user } = useAuth();
  const [maxId, setMaxId] = useState(-Infinity);
  const [rows, setRows] = useState<number[]>([]);
  const [_columns] = useAtom(columnsAtom);
  const [messageFilter, setMessageFilter] = useAtom(messageFilterAtom);
  const [messageFn, setMessageFn] = useAtom(messageFnAtom);
  const [, handleCheckTaskFilter] = useAtom(checkTaskFilterAtom);
  const [, handleSnapFilter] = useAtom(snapFilterAtom);
  const [, handleDangerFilter] = useAtom(dangerFilterAtom);

  const { isLoading, isError, error, data, refetch } = useQuery(
    {
      queryKey: [queryKey, messageFilter],
      queryFn: () => {
        return getsApi(user.userInfo?.id, messageFilter);
      },
    },
    [user.userInfo?.id]
  );
  const dataSource = useMemo(() => {
    return data?.data ?? {};
  }, [data]);
  const result = useMemo(() => {
    setMaxId((preState) => {
      let tmpMaxId = -Infinity;
      if (dataSource?.results)
        tmpMaxId = Math.max(...dataSource?.results?.map((obj) => obj.id));

      if (preState) {
        console.log(queryKey, "preState is not null", preState);
        return Math.max(preState, tmpMaxId);
      } else {
        console.log(queryKey, "preState is null", preState);
      }
      return tmpMaxId;
    });

    return dataSource?.results ?? [];
  }, [dataSource]);

  useEffect(() => {
    setMessageFn({
      refetch: refetch,
    });
  }, [refetch]);

  const read = useMutation({
    mutationFn: readApi,
    onSuccess: async (res) => {
      if (res?.code !== 0) {
        return;
      }
      queryClient.invalidateQueries({ queryKey: [queryKey] });
      queryClient.invalidateQueries({ queryKey: ["getUnreadMsgCount"] });
      messageFn?.refetch?.();
    },
  });

  const columns = useMemo(() => {
    return [
      {
        title: <Tooltip content=""></Tooltip>,
        isShow: true,
        fixed: true,
        dataIndex: "selection",
        width: 60,
        render: (text, record, index) => {
          if (record.status === MessageStatus.Read) {
            return null;
          }
          return (
            <Checkbox
              onChange={() =>
                setRows((selected) => {
                  selected = selected.slice();
                  if (selected.includes(record.id)) {
                    selected.splice(selected.indexOf(record.id), 1);
                  } else {
                    selected.push(record.id);
                  }
                  return selected;
                })
              }
              checked={rows.includes(record.id)}
            />
          );
        },
      },
      ..._columns,
      {
        title: <Tooltip content="操作">操作</Tooltip>,
        isShow: true,
        dataIndex: "operate",
        key: "operate",
        align: "center",
        width: 150,
        render: (text, record) => {
          if (record.status === MessageStatus.Unread) {
            return (
              <Button
                onClick={() => {
                  read.mutate([record.id]);
                  setRows([]);
                }}
              >
                已读
              </Button>
            );
          }
          return null;
        },
      },
    ];
  }, [rows]);

  const handleSelectAll = useCallback(() => {
    setRows((_) =>
      uniq([
        ...rows,
        ...result
          .filter((item) => item.status === MessageStatus.Unread)
          .map((item) => item.id),
      ])
    );
  }, [rows, result]);

  const handleBatchRead = useCallback(() => {
    read.mutate(rows);
    setRows([]);
  }, [read, rows, setRows]);

  const readAll = useMutation({
    mutationFn: readAllApi,
    onSuccess: async (res) => {
      if (res?.code !== 0) {
        return;
      }
      queryClient.invalidateQueries({ queryKey: [queryKey] });
      queryClient.invalidateQueries({ queryKey: ["getUnreadMsgCount"] });
      messageFn?.refetch?.();
    },
  });
  const handleAllRead = useCallback(() => {
    if (maxId === -Infinity) {
      return;
    }

    readAll.mutate({ maxId: maxId });
  }, [readAll]);

  const handlePageChange = useCallback(
    (currentPage: number) => {
      setMessageFilter({
        ...messageFilter,
        pageNumber: currentPage,
      });
    },
    [messageFilter, setMessageFilter]
  );

  const handlePageSizeChange = useCallback(
    (pageSize: number) => {
      setMessageFilter({
        ...messageFilter,
        pageSize: pageSize,
      });
    },
    [messageFilter, setMessageFilter]
  );

  return (
    <>
      <div className="bg-white shadow px-4 h-fit rounded">
        <div className="flex py-4 justify-between">
          <div className="flex gap-4">
            {result.some((item) => item.status === MessageStatus.Unread) ? (
              <button onClick={handleSelectAll} type="primary">
                全选
              </button>
            ) : null}
            {rows?.length ? (
              <>
                <span className="flex text-gray-900 text-opacity-60 text-sm justify-center items-center">
                  已选中{rows?.length ?? 0}个
                </span>
                <button
                  onClick={handleBatchRead}
                  className="btn btn-sm rounded"
                >
                  批量已读
                </button>
              </>
            ) : null}
            <button onClick={handleAllRead} className="btn btn-sm rounded">
              全部已读
            </button>
          </div>

          <div className="flex gap">
            <div className="tooltip" data-tip="刷新">
              <button
                className="btn btn-sm btn-ghost rounded no-animation"
                onClick={() => {
                  refetch();
                }}
              >
                <IconRefresh />
              </button>
            </div>
          </div>
        </div>
        <Table
          className="rounded overflow-hidden"
          rowKey="id"
          // scroll={{ x:1200 }}
          columns={columns?.filter?.((o) => o.fixed || o.isShow)}
          dataSource={result}
          loading={isLoading}
          onHeaderRow={(columns, index) => {
            return {
              className: "text-gray-900 text-opacity-90 bg-gray-50",
            };
          }}
          onRow={(record, index) => {
            return {
              onDoubleClick: () => {
                if (record.status === MessageStatus.Unread) {
                  read.mutate([record.id]);
                  setRows([]);
                }
                switch (record.entityType) {
                  case EntityType.CheckTask: {
                    handleCheckTaskFilter((_) => ({
                      ..._,
                      filter: { id: record.entityId },
                    }));
                    return navigate(DoubleGuardRoutes.TASK_LIST);
                  }
                  case EntityType.Snap: {
                    handleSnapFilter((_) => ({
                      ..._,
                      filter: { id: record.entityId },
                    }));
                    return navigate(DoubleGuardRoutes.SNAP);
                  }
                  case EntityType.Danger: {
                    handleDangerFilter((_) => ({
                      ..._,
                      filter: { id: record.entityId },
                    }));
                    return navigate(DoubleGuardRoutes.DANGER);
                  }
                }
              },
            };
          }}
          headerStyle={{ color: "blue" }}
          pagination={{
            showSizeChanger: true,
            popoverPosition: "topRight",
            currentPage: dataSource?.pageNumber ?? 1,
            pageSize: dataSource?.pageSize ?? 10,
            total: dataSource?.totalCount ?? 0,
            onPageChange: handlePageChange,
            onPageSizeChange: handlePageSizeChange,
            pageSizeOpts: [10, 15, 20, 50],
          }}
        />
      </div>
    </>
  );
}
