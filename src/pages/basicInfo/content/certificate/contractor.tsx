import {
  IconDownload,
  IconPlus,
  IconPrint,
  IconRefresh,
  IconSetting,
  IconUpload,
} from "@douyinfe/semi-icons";
import {
  Button,
  ButtonGroup,
  Popconfirm,
  Table,
  Toast,
  Tooltip,
} from "@douyinfe/semi-ui";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  delCertificate,
  delCertificates,
  getContractorEmployeeCertificateList,
} from "api";
import {
  certificateColumnsAtom,
  certificateConfigModalAtom,
  certificateEditModal,
  certificateFilterAtom,
  certificateFnAtom,
} from "atoms/basicInfo";
import { TableConfig } from "components";
import { useAtom } from "jotai";
import { FC, useCallback, useEffect, useMemo, useState } from "react";

type ContractorProps = {
  mode?: "modal";
  cb?: (a: number[], b: any[]) => void;
  queryFn?: () => void;
};

export const Contractor: FC<ContractorProps> = ({ mode, cb }) => {
  const queryClient = useQueryClient();
  const [rows, setRows] = useState<number[]>([]);
  const [certificateFilter, setCertificateFilter] = useAtom(
    certificateFilterAtom,
  );
  const [certificateFn, setCertificateFn] = useAtom(certificateFnAtom);
  const [certificateEdit, setCertificateEdit] = useAtom(certificateEditModal);
  const [configModal, setShow] = useAtom(certificateConfigModalAtom);
  const [_columns, setColumns] = useAtom(certificateColumnsAtom);

  const { isLoading, data, refetch } = useQuery({
    queryKey: ["getContractorEmployeeCertificateList", certificateFilter],
    queryFn: () => getContractorEmployeeCertificateList(certificateFilter),
  });

  const mutation = useMutation({
    mutationFn: delCertificate,
    onSuccess: async (res) => {
      if (res?.code !== 0) {
        return;
      }
      let opts = {
        content: `操作成功!`,
        duration: 2,
      };
      queryClient.invalidateQueries({ queryKey: ["getCertificateList"] });
      Toast.success(opts);
      certificateFn?.refetch?.();
    },
  });

  const removes = useMutation({
    mutationFn: delCertificates,
    onSuccess: async (res) => {
      if (res?.code !== 0) {
        return;
      }
      let opts = {
        content: `操作成功!`,
        duration: 2,
      };
      Toast.success(opts);
      queryClient.invalidateQueries({ queryKey: ["getCertificateList"] });
      certificateFn?.refetch?.();
    },
  });

  useEffect(() => {
    if (data) {
      cb?.(rows, data?.data?.results ?? []);
    }
  }, [rows, data]);

  const dataSource = useMemo(() => data?.data ?? {}, [data?.data]);
  const result = useMemo(() => dataSource?.results ?? [], [dataSource]);

  const handleConfirm = useCallback(
    (record) => {
      mutation.mutate(record.id);
    },
    [mutation],
  );

  const rowSelection = useMemo(
    () => ({
      fixed: true,
      onChange: (selectedRowKeys: any[]) => {
        setRows(selectedRowKeys);
      },
    }),
    [setRows],
  );

  const handleOpenSetting = useCallback(() => {
    setShow(true);
  }, [setShow]);

  const handleOpenEdit = useCallback(
    (id?: string) => {
      setCertificateEdit({
        id: id ?? "",
        show: true,
      });
    },
    [setCertificateEdit],
  );

  const handlePageChange = useCallback(
    (currentPage: number) => {
      setCertificateFilter({
        ...certificateFilter,
        pageNumber: currentPage,
      });
    },
    [certificateFilter, setCertificateFilter],
  );

  const handlePageSizeChange = useCallback(
    (pageSize: number) => {
      setCertificateFilter({
        ...certificateFilter,
        pageSize: pageSize,
      });
    },
    [certificateFilter, setCertificateFilter],
  );

  const handleRemoves = useCallback(() => {
    removes.mutate(rows);
    setRows([]);
  }, [removes, rows, setRows]);

  const columns = useMemo(() => {
    if (mode == "modal") {
      return _columns;
    }
    return [
      ..._columns,
      {
        title: <Tooltip content="操作">操作</Tooltip>,
        isShow: true,
        dataIndex: "operate",
        key: "operate",
        align: "center",
        width: 150,
        render: (text, record) => (
          <div>
            <ButtonGroup aria-label="操作按钮组">
              <Button
                onClick={() => {
                  handleOpenEdit(record.id);
                }}
              >
                编辑
              </Button>
              <Popconfirm
                position="left"
                title="确定是否要删除该项？"
                content="此修改将不可逆"
                okType="danger"
                okButtonProps={{
                  className: "semi-button semi-button-danger semi-button-light",
                }}
                onConfirm={() => {
                  handleConfirm(record);
                }}
              >
                <Button type="danger">删除</Button>
              </Popconfirm>
            </ButtonGroup>
          </div>
        ),
      },
    ];
  }, [_columns, mode]);

  return (
    <>
      <TableConfig
        visible={configModal}
        columns={_columns}
        handleClose={setShow}
        handleSave={setColumns}
      />
      <div className="bg-white shadow px-4 h-fit rounded">
        <div className="flex py-4 justify-between">
          {mode == "modal" ? (
            <div></div>
          ) : (
            <div className="flex gap-4">
              <button
                className="btn rounded btn-primary btn-sm"
                onClick={() => {
                  handleOpenEdit();
                }}
              >
                新增
                <IconPlus size="small" />
              </button>
              <span className="flex text-gray-900 text-opacity-60 text-sm justify-center items-center">
                已选中{rows?.length ?? 0}个
              </span>
              {rows?.length ? (
                <Popconfirm
                  title="确定是否要删除该项？"
                  content="此修改将不可逆"
                  okType="danger"
                  okButtonProps={{
                    className:
                      "semi-button semi-button-danger semi-button-light",
                  }}
                  onConfirm={handleRemoves}
                >
                  <button className="btn btn-sm rounded">批量删除</button>
                </Popconfirm>
              ) : null}
            </div>
          )}

          <div className="flex gap">
            <div className="tooltip" data-tip="刷新">
              <button
                className="btn btn-sm btn-ghost rounded no-animation"
                onClick={() => {
                  refetch();
                }}
              >
                <IconRefresh />
              </button>
            </div>
            <div className="tooltip" data-tip="下载">
              <button className="btn btn-sm btn-ghost rounded no-animation">
                <IconDownload />
              </button>
            </div>
            <div className="tooltip" data-tip="上传">
              <button className="btn btn-sm btn-ghost rounded no-animation">
                <IconUpload />
              </button>
            </div>

            <div className="tooltip" data-tip="打印">
              <button className="btn btn-sm btn-ghost rounded no-animation">
                <IconPrint />
              </button>
            </div>

            <div className="tooltip" data-tip="设置">
              <button
                className="btn btn-sm btn-ghost rounded no-animation"
                onClick={handleOpenSetting}
              >
                <IconSetting />
              </button>
            </div>
          </div>
        </div>
        <Table
          className="rounded overflow-hidden"
          rowKey="id"
          // scroll={{ x:1200 }}
          columns={columns?.filter?.((o) => o.fixed || o.isShow)}
          dataSource={result}
          rowSelection={rowSelection}
          loading={isLoading}
          onHeaderRow={(columns, index) => {
            return {
              className: "text-gray-900 text-opacity-90 bg-gray-50",
            };
          }}
          headerStyle={{ color: "blue" }}
          pagination={{
            showSizeChanger: true,
            popoverPosition: "topRight",
            currentPage: dataSource?.pageNumber ?? 1,
            pageSize: dataSource?.pageSize ?? 10,
            total: dataSource?.totalCount ?? 0,
            onPageChange: handlePageChange,
            onPageSizeChange: handlePageSizeChange,
            pageSizeOpts: [10, 15, 20, 50],
          }}
        />
      </div>
    </>
  );
};
