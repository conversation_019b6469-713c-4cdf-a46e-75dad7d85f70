import {
  IconDownload,
  IconPlus,
  IconPrint,
  IconRefresh,
  IconSetting,
  IconUpload,
} from "@douyinfe/semi-icons";
import {
  Button,
  ButtonGroup,
  Popconfirm,
  Table,
  Toast,
  Tooltip,
} from "@douyinfe/semi-ui";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { delCertificate, delCertificates, getCertificateList } from "api";
import { jobCertificatesReferValues } from "atoms";
import {
  certificateColumnsAtom,
  certificateConfigModalAtom,
  certificateEditModal,
  certificateFilterAtom,
  certificateFnAtom,
  certificatePrintColumnsAtom,
} from "atoms/basicInfo";
import {
  ExportContentType,
  ExportFormat,
  TableConfig,
  UploadTmpl,
  useExport,
} from "components";
import dayjs from "dayjs";
import { useBtnHooks } from "hooks";
import { useAtom, useAtomValue } from "jotai";
import { map, prop, union } from "ramda";
import { FC, useCallback, useEffect, useMemo, useState } from "react";
import { useLoaderData, useLocation } from "react-router-dom";

type CertificateContentProps = {
  mode?: "modal" | "select";
  cb?: (record: number[], type?: 1 | 2) => void;
  initData?: (type: 1 | 2, record: any[]) => void;
  initRows?: any[];
  filter: Object;
  readonly?: boolean;
};

export const CertificateContent: FC<CertificateContentProps> = ({
  mode,
  cb,
  initData,
  initRows,
  filter = {},
  readonly = false,
}) => {
  const loderData = useLoaderData();
  const { pathname } = useLocation();
  const genBtn = useBtnHooks(loderData, pathname);

  const queryClient = useQueryClient();
  const [rowKeys, setRowKeys] = useState<number[]>([]);
  const [rows, setRows] = useState<any[]>([]);
  const [certificateFilter, setCertificateFilter] = useAtom(
    certificateFilterAtom
  );
  const [certificateFn, setCertificateFn] = useAtom(certificateFnAtom);
  const [certificateEdit, setCertificateEdit] = useAtom(certificateEditModal);
  const [configModal, setShow] = useAtom(certificateConfigModalAtom);
  const [_columns, setColumns] = useAtom(certificateColumnsAtom);
  const [printColumns, setPrintColumns] = useAtom(certificatePrintColumnsAtom);
  const jobCertificatesReferValue = useAtomValue(jobCertificatesReferValues);

  const exportContext = useExport();
  const canExport = exportContext && exportContext.exportToFile;

  const { isLoading, data, refetch } = useQuery({
    queryKey: ["getCertificateList", certificateFilter, filter],
    queryFn: () =>
      getCertificateList({
        ...certificateFilter,
        filter: { ...certificateFilter.filter, ...filter },
      }),
  });

  const mutation = useMutation({
    mutationFn: delCertificate,
    onSuccess: async (res) => {
      if (res?.code !== 0) {
        return;
      }
      let opts = {
        content: `操作成功!`,
        duration: 2,
      };
      queryClient.invalidateQueries({ queryKey: ["getCertificateList"] });
      Toast.success(opts);
      certificateFn?.refetch?.();
    },
  });

  const removes = useMutation({
    mutationFn: delCertificates,
    onSuccess: async (res) => {
      if (res?.code !== 0) {
        return;
      }
      let opts = {
        content: `操作成功!`,
        duration: 2,
      };
      Toast.success(opts);
      queryClient.invalidateQueries({ queryKey: ["getCertificateList"] });
      certificateFn?.refetch?.();
    },
  });

  useEffect(() => {
    if (mode === "select" && data?.data) {
      const list = data?.data?.results ?? [];
      const selectKeys: number[] = [];
      list.forEach((o) => {
        if (initRows?.filter?.((i) => i.id === o?.id)?.length) {
          selectKeys.push(o.id);
        }
      });
      setRowKeys(selectKeys);
    }
  }, [initRows, mode, data]);

  useEffect(() => {
    if (data && mode != "select") {
      console.debug("--------cb keys");
      cb?.(rowKeys, 1);
    }
  }, [rowKeys, data]);

  useEffect(() => {
    if (data) {
      initData?.(1, data?.data?.results ?? []);
    }
  }, [data]);

  const dataSource = useMemo(() => data?.data ?? {}, [data?.data]);
  const result = useMemo(() => dataSource?.results ?? [], [dataSource]);

  const handleExport = (
    format: ExportFormat = "excel",
    contentType: ExportContentType = "list"
  ) => {
    exportContext?.exportToFile?.({
      format: format,
      contentType: contentType,
      // data: data,
      apiFn: getCertificateList,
      params: {
        ...certificateFilter,
        filter: { ...filter, ...certificateFilter },
      },
      columns: printColumns,
      entityName: "员工证书",
    });
  };

  const handleConfirm = useCallback(
    (record) => {
      mutation.mutate(record.id);
    },
    [mutation]
  );

  const rowSelection = useMemo(
    () => ({
      fixed: true,
      onChange: (selectedRowKeys: any[], selectedRows: any[]) => {
        setRowKeys(selectedRowKeys);
        // const remainRows = prev.filter((o) => selectedRowKeys.includes(o.id));
        // return union(remainRows, selectedRows);
        setRows((prev) => {
          const remainRows = prev.filter((o) => selectedRowKeys.includes(o.id));
          const selectedRecord = union(remainRows, selectedRows);
          console.debug("--------cb record");
          cb?.(selectedRecord);
          return selectedRecord;
        });
      },
      getCheckboxProps: (record) => ({
        disabled:
          (mode === "modal" || mode === "select") &&
          dayjs(record.expireDate).isBefore(dayjs()),
      }),
      selectedRowKeys: rowKeys?.length
        ? rowKeys
        : map(prop("id"), initRows ?? []),
    }),
    [setRowKeys, mode, initRows, rowKeys]
  );

  const handleOpenSetting = useCallback(() => {
    setShow(true);
  }, [setShow]);

  const handleOpenEdit = useCallback(
    (id?: string) => {
      setCertificateEdit({
        id: id ?? "",
        show: true,
      });
    },
    [setCertificateEdit]
  );

  const handlePageChange = useCallback(
    (currentPage: number) => {
      setCertificateFilter({
        ...certificateFilter,
        pageNumber: currentPage,
      });
    },
    [certificateFilter, setCertificateFilter]
  );

  const handlePageSizeChange = useCallback(
    (pageSize: number) => {
      setCertificateFilter({
        ...certificateFilter,
        pageSize: pageSize,
      });
    },
    [certificateFilter, setCertificateFilter]
  );

  const handleRemoves = useCallback(() => {
    removes.mutate(rowKeys);
    setRowKeys([]);
  }, [removes, rowKeys, setRowKeys]);

  const handleCallback = (record) => {
    // cb?.(record);
  };

  const columns = useMemo(() => {
    if (mode == "modal") {
      return _columns;
    }
    if (mode == "select") {
      return [
        ..._columns,
        /* {
          title: <Tooltip content="操作">操作</Tooltip>,
          isShow: true,
          dataIndex: "operate",
          key: "operate",
          align: "center",
          width: 150,
          render: (text, record) => (
            <ButtonGroup aria-label="操作按钮组">
              <Button
                disabled={dayjs(record.expireDate).isBefore(dayjs())}
                onClick={() => {
                  handleCallback(record);
                }}
              >
                使用该项
              </Button>
            </ButtonGroup>
          ),
        }, */
      ];
    }
    return [
      ..._columns,
      readonly
        ? {}
        : {
            title: <Tooltip content="操作">操作</Tooltip>,
            isShow: true,
            dataIndex: "operate",
            key: "operate",
            align: "center",
            width: 150,
            render: (text, record) => (
              <div>
                <ButtonGroup aria-label="操作按钮组">
                  <Button
                    onClick={() => {
                      handleOpenEdit(record.id);
                    }}
                  >
                    编辑
                  </Button>
                  <Popconfirm
                    position="left"
                    title="确定是否要删除该项？"
                    content="此修改将不可逆"
                    okType="danger"
                    okButtonProps={{
                      className:
                        "semi-button semi-button-danger semi-button-light",
                    }}
                    onConfirm={() => {
                      handleConfirm(record);
                    }}
                  >
                    <Button type="danger">删除</Button>
                  </Popconfirm>
                </ButtonGroup>
              </div>
            ),
          },
    ];
  }, [_columns, mode, handleCallback]);

  return (
    <>
      <TableConfig
        visible={configModal}
        columns={_columns}
        handleClose={setShow}
        handleSave={setColumns}
      />
      <div className="bg-white shadow px-4 h-fit rounded big_screen_table_box">
        <div className="flex py-4 justify-between big_screen_table_filter_operation">
          {mode == "select" || mode == "modal" ? (
            <div></div>
          ) : (
            <div className="flex gap-4">
              <button
                className="btn rounded btn-primary btn-sm"
                onClick={() => {
                  handleOpenEdit();
                }}
              >
                新增
                <IconPlus size="small" />
              </button>
              <span className="flex text-gray-900 text-opacity-60 text-sm justify-center items-center">
                已选中{rowKeys?.length ?? 0}个
              </span>
              {rowKeys?.length ? (
                <Popconfirm
                  title="确定是否要删除该项？"
                  content="此修改将不可逆"
                  okType="danger"
                  okButtonProps={{
                    className:
                      "semi-button semi-button-danger semi-button-light",
                  }}
                  onConfirm={handleRemoves}
                >
                  <button className="btn btn-sm rounded">批量删除</button>
                </Popconfirm>
              ) : null}
            </div>
          )}

          <div className="flex gap">
            <div className="tooltip" data-tip="刷新">
              <button
                className="btn btn-sm btn-ghost rounded no-animation"
                onClick={() => {
                  refetch();
                }}
              >
                <IconRefresh />
              </button>
            </div>
            <div className="tooltip" data-tip="下载">
              <button className="btn btn-sm btn-ghost rounded no-animation">
                <IconDownload />
              </button>
            </div>
            <div className="tooltip" data-tip="上传">
              <button className="btn btn-sm btn-ghost rounded no-animation">
                <IconUpload />
              </button>
            </div>
            {canExport ? (
              <div className="tooltip" data-tip="导出">
                {genBtn(
                  "export",
                  <button
                    className="btn btn-sm btn-ghost rounded no-animation"
                    onClick={() => handleExport()}
                  >
                    <IconDownload />
                  </button>
                )}
              </div>
            ) : null}
            <UploadTmpl
              excelType="employee_certificate_template"
              downUrl={encodeURI("/static/template/人员证书信息导入模板.xlsx")}
              tip="请先确保证书类型和发证机关类型在字典管理那边已经维护好"
            />
            <div className="tooltip" data-tip="打印">
              <button className="btn btn-sm btn-ghost rounded no-animation">
                <IconPrint />
              </button>
            </div>

            <div className="tooltip" data-tip="设置">
              <button
                className="btn btn-sm btn-ghost rounded no-animation"
                onClick={handleOpenSetting}
              >
                <IconSetting />
              </button>
            </div>
          </div>
        </div>
        <Table
          className="rounded overflow-hidden"
          rowKey="id"
          // scroll={{ x:1200 }}
          columns={columns?.filter?.((o) => o.fixed || o.isShow)}
          dataSource={result}
          rowSelection={readonly ? false : rowSelection}
          loading={isLoading}
          onHeaderRow={(columns, index) => {
            return {
              className: "text-gray-900 text-opacity-90 bg-gray-50",
            };
          }}
          headerStyle={{ color: "blue" }}
          pagination={{
            showSizeChanger: true,
            popoverPosition: "topRight",
            currentPage: dataSource?.pageNumber ?? 1,
            pageSize: dataSource?.pageSize ?? 10,
            total: dataSource?.totalCount ?? 0,
            onPageChange: handlePageChange,
            onPageSizeChange: handlePageSizeChange,
            pageSizeOpts: [10, 15, 20, 50],
          }}
        />
      </div>
    </>
  );
};
