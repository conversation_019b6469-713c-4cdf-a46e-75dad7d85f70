import { IconSearch } from "@douyinfe/semi-icons";
import { Form, useFormApi } from "@douyinfe/semi-ui";
import { useToggle } from "ahooks";
import { certificateFilterAtom } from "atoms/basicInfo";
import { DicSearch, EmployeeSearch, VALID_NOTVALID_MAP } from "components";
import { useFilterSearch } from "hooks";

const ComponentUsingFormApi = ({ handleReset }) => {
  const formApi = useFormApi();

  return (
    <div className="flex gap-2">
      <span
        className="btn rounded btn-primary btn-sm"
        onClick={() => {
          formApi.submitForm();
        }}
      >
        查&nbsp;&nbsp;询
      </span>
      <span
        className="btn btn-sm rounded"
        onClick={() => {
          formApi.reset();
          handleReset();
        }}
      >
        重&nbsp;&nbsp;置
      </span>
    </div>
  );
};

export const CertificateFilter = ({ filter, isValidDisabled = false }) => {
  const [state, { toggle }] = useToggle();
  const [handleSearch, handleReset] = useFilterSearch(certificateFilterAtom);

  return (
    <div className="flex flex-col bg-white shadow rounded relative big_screen_table_filter_box">
      <div className="text-gray-900 text-opacity-90 text-base font-semibold leading-snug px-4 py-3 border-b border-gray-900/10 big_screen_table_filter_title">
        筛选总览
      </div>

      <div className="p-4 pr-0">
        <Form
          initValues={filter}
          layout="horizontal"
          onSubmit={handleSearch}
          className="grid grid-cols-4 gap-y-4 "
        >
          <Form.Input
            noLabel
            field="query"
            placeholder="关键字搜索"
            className="w-full"
            suffix={<IconSearch />}
            showClear
          />
          <EmployeeSearch field="employeeId" placeholder="员工" />
          <DicSearch
            field="certificateTypeValueId"
            placeholder="请选择证书类型"
            name="certificateType"
          />

          {state ? (
            <DicSearch
              field="authorityTypeValueId"
              placeholder="请选择发证机关"
              name="authorityType"
            />
          ) : null}
          <Form.DatePicker
            field="expireDateGt"
            noLabel
            placeholder="过期日期下限"
            className="w-full"
            position="bottomRight"
          />
          <Form.DatePicker
            field="expireDateLte"
            noLabel
            placeholder="过期日期上限"
            className="w-full"
            position="bottomRight"
          />

          <Form.Select
            placeholder="是否有效"
            field="isValid"
            noLabel
            className="w-full"
            disabled={isValidDisabled}
          >
            {VALID_NOTVALID_MAP.map((item) => (
              <Form.Select.Option key={item.id} value={item.id}>
                {item.name}
              </Form.Select.Option>
            ))}
          </Form.Select>

          <ComponentUsingFormApi handleReset={handleReset} />
          <span
            className="w-10 h-4 bg-white rounded-2xl shadow border absolute bottom-[-6px] left-2/4 ml-[-20px] flex items-center justify-center cursor-pointer"
            onClick={toggle}
          >
            {state ? (
              <i className="ri-arrow-up-s-line" />
            ) : (
              <i className="ri-arrow-down-s-line" />
            )}
          </span>
        </Form>
      </div>
    </div>
  );
};
