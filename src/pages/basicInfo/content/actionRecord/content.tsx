import { IconRefresh } from "@douyinfe/semi-icons";
import { Table } from "@douyinfe/semi-ui";
import { useQuery } from "@tanstack/react-query";
import { getOperationLogList } from "api";
import {
  actionRecordColumnsAtom,
  actionRecordFilterAtom,
} from "atoms/basicInfo";
import { useAtom } from "jotai";
import { useCallback, useMemo } from "react";

export const ActionRecordContent = () => {
  const [actionRecordFilter, setLoginRecordFilter] = useAtom(
    actionRecordFilterAtom,
  );
  const [_columns, setColumns] = useAtom(actionRecordColumnsAtom);

  const { isLoading, data, refetch } = useQuery({
    queryKey: ["getOperationLogList", actionRecordFilter],
    queryFn: () => getOperationLogList(actionRecordFilter),
  });

  const dataSource = useMemo(() => data?.data ?? {}, [data?.data]);
  const result = useMemo(() => dataSource?.results ?? [], [dataSource]);

  const handlePageChange = useCallback(
    (currentPage: number) => {
      setLoginRecordFilter({
        ...actionRecordFilter,
        pageNumber: currentPage,
      });
    },
    [actionRecordFilter, setLoginRecordFilter],
  );

  const handlePageSizeChange = useCallback(
    (pageSize: number) => {
      setLoginRecordFilter({
        ...actionRecordFilter,
        pageSize: pageSize,
      });
    },
    [actionRecordFilter, setLoginRecordFilter],
  );

  const columns = useMemo(() => {
    return [..._columns];
  }, []);

  return (
    <>
      <div className="bg-white shadow px-4 h-fit rounded">
        <div className="flex py-4 justify-between">
          <div className="flex gap-4"></div>

          <div className="flex gap">
            <div className="tooltip" data-tip="刷新">
              <button
                className="btn btn-sm btn-ghost rounded no-animation"
                onClick={() => {
                  refetch();
                }}
              >
                <IconRefresh />
              </button>
            </div>
          </div>
        </div>
        <Table
          className="rounded overflow-hidden"
          rowKey="id"
          // scroll={{ x:1200 }}
          columns={columns}
          dataSource={result}
          loading={isLoading}
          onHeaderRow={(columns, index) => {
            return {
              className: "text-gray-900 text-opacity-90 bg-gray-50",
            };
          }}
          headerStyle={{ color: "blue" }}
          pagination={{
            showSizeChanger: true,
            popoverPosition: "topRight",
            currentPage: dataSource?.pageNumber ?? 1,
            pageSize: dataSource?.pageSize ?? 10,
            total: dataSource?.totalCount ?? 0,
            onPageChange: handlePageChange,
            onPageSizeChange: handlePageSizeChange,
            pageSizeOpts: [10, 15, 20, 50],
          }}
        />
      </div>
    </>
  );
};
