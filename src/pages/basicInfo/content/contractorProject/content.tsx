import { Toast } from "@douyinfe/semi-ui";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { batchStopContractorProject, contractorProjectApis } from "api";
import {
  contractorProjectAtoms,
  contractorProjectOpenStopModalAtom,
} from "atoms";
import { List } from "components";
import { useAtom } from "jotai";
import { useCallback } from "react";

export const ContractorProjectContent = () => {
  const queryClient = useQueryClient();
  const queryKey = "list" + contractorProjectAtoms.entity;

  const [stopModalAtom, setStopModalAtom] = useAtom(
    contractorProjectOpenStopModalAtom
  );

  const handleOpenStopModal = useCallback(
    (record: any) => {
      setStopModalAtom({
        record: record,
        show: true,
      });
    },
    [setStopModalAtom]
  );

  const toggleStop = (record) => {
    return record.isStop === 2
      ? {
          engName: "stop",
          chnName: "停工",
          func: handleOpenStopModal,
        }
      : null;
  };

  const batchStopMutation = useMutation({
    mutationFn: batchStopContractorProject,
    onSuccess: async (res) => {
      if (res?.code === 0) {
        let opts = {
          content: `操作成功!`,
          duration: 2,
        };
        Toast.success(opts);
        queryClient.invalidateQueries({ queryKey: [queryKey] });
        contractorProjectAtoms.Fn?.refetch?.();
      }
    },
  });

  const handleBatchStop = useCallback(
    (recordIdList: any) => {
      batchStopMutation.mutate(recordIdList);
    },
    [batchStopMutation]
  );

  const batchToggleStop = {
    engName: "batchStop",
    chnName: "批量停工",
    func: handleBatchStop,
  };

  return (
    <List
      atoms={contractorProjectAtoms}
      apis={contractorProjectApis}
      dynamicOperationFuncs={[toggleStop]}
      // batchOperation={[batchToggleStop]}
    />
  );
};
