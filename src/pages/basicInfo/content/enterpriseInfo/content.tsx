import { useQueryClient } from "@tanstack/react-query";
import { enterpriseInfoApis } from "api";
import { enterpriseInfoAtoms } from "atoms";
import { List } from "components";

export const EnterpriseInfoContent = ({ readonly = false, ...restProps }) => {
  const queryClient = useQueryClient();
  const queryKey = "list" + enterpriseInfoAtoms.entity;

  return (
    <List
      atoms={enterpriseInfoAtoms}
      apis={enterpriseInfoApis}
      readonly={readonly}
      {...restProps}
    />
  );
};
