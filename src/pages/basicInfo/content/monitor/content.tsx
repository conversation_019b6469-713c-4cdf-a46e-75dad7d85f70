import {
  IconDownload,
  IconPlus,
  IconPrint,
  IconRefresh,
  IconSetting,
  IconUpload,
} from "@douyinfe/semi-icons";
import {
  Button,
  ButtonGroup,
  Popconfirm,
  Table,
  Toast,
  Tooltip,
} from "@douyinfe/semi-ui";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { delMonitor, delMonitors, getMonitorList } from "api";
import {
  monitorColumnsAtom,
  monitorConfigModalAtom,
  monitorEditModal,
  monitorFilterAtom,
  monitorFnAtom,
} from "atoms/basicInfo";
import { TableConfig, UploadTmpl } from "components/index";
import { useBtnHooks } from "hooks";
import { useAtom } from "jotai";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useLoaderData, useLocation } from "react-router-dom";

export const MonitorContent = () => {
  const queryClient = useQueryClient();
  const loderData = useLoaderData();
  const { pathname } = useLocation();
  const genBtn = useBtnHooks(loderData, pathname);
  const [rows, setRows] = useState<number[]>([]);
  const [monitorFilter, setMonitorFilter] = useAtom(monitorFilterAtom);
  const [monitorFn, setMonitorFn] = useAtom(monitorFnAtom);
  const [monitorEdit, setMonitorEdit] = useAtom(monitorEditModal);
  const [configModal, setShow] = useAtom(monitorConfigModalAtom);
  const [_columns, setColumns] = useAtom(monitorColumnsAtom);

  const { isLoading, isError, error, data, refetch } = useQuery({
    queryKey: ["getMonitorList", monitorFilter],
    queryFn: () => getMonitorList(monitorFilter),
  });

  const mutation = useMutation({
    mutationFn: delMonitor,
    onSuccess: async (res) => {
      if (res?.code !== 0) {
        return;
      }
      let opts = {
        content: `操作成功!`,
        duration: 2,
      };
      queryClient.invalidateQueries({ queryKey: ["getMonitorList"] });
      Toast.success(opts);
      monitorFn?.refetch?.();
    },
  });

  const removes = useMutation({
    mutationFn: delMonitors,
    onSuccess: async (res) => {
      if (res?.code !== 0) {
        return;
      }
      let opts = {
        content: `操作成功!`,
        duration: 2,
      };
      Toast.success(opts);
      queryClient.invalidateQueries({ queryKey: ["getMonitorList"] });
      monitorFn?.refetch?.();
    },
  });

  useEffect(() => {
    setMonitorFn({
      refetch: refetch,
    });
  }, [refetch]);

  const dataSource = useMemo(() => data?.data ?? {}, [data?.data]);
  const result = useMemo(() => dataSource?.results ?? [], [dataSource]);

  const handleConfirm = useCallback(
    (record) => {
      mutation.mutate(record.id);
    },
    [mutation],
  );

  const rowSelection = useMemo(
    () => ({
      fixed: true,
      onChange: (selectedRowKeys) => {
        setRows(selectedRowKeys);
      },
    }),
    [setRows],
  );

  const handleOpenSetting = useCallback(() => {
    setShow(true);
  }, [setShow]);

  const handleOpenEdit = useCallback(
    (id?: string) => {
      setMonitorEdit({
        id: id ?? "",
        show: true,
      });
    },
    [setMonitorEdit],
  );

  const handlePageChange = useCallback(
    (currentPage: number) => {
      setMonitorFilter({
        ...monitorFilter,
        pageNumber: currentPage,
      });
    },
    [monitorFilter, setMonitorFilter],
  );

  const handlePageSizeChange = useCallback(
    (pageSize: number) => {
      setMonitorFilter({
        ...monitorFilter,
        pageSize: pageSize,
      });
    },
    [monitorFilter, setMonitorFilter],
  );

  const handleRemoves = useCallback(() => {
    removes.mutate(rows);
    setRows([]);
  }, [removes, rows, setRows]);

  const columns = useMemo(() => {
    return [
      ..._columns,
      {
        title: <Tooltip content="操作">操作</Tooltip>,
        isShow: true,
        dataIndex: "operate",
        key: "operate",
        align: "center",
        width: 150,
        render: (text, record) => (
          <div>
            <ButtonGroup aria-label="操作按钮组">
              {genBtn(
                "edit",
                <Button
                  onClick={() => {
                    handleOpenEdit(record.id);
                  }}
                >
                  编辑
                </Button>,
              )}
              {genBtn(
                "remove",
                <Popconfirm
                  position="bottomRight"
                  title="确定是否要删除该项？"
                  content="此修改将不可逆"
                  okType="danger"
                  okButtonProps={{
                    className:
                      "semi-button semi-button-danger semi-button-light",
                  }}
                  onConfirm={() => {
                    handleConfirm(record);
                  }}
                >
                  <Button type="danger">删除</Button>
                </Popconfirm>,
              )}
            </ButtonGroup>
          </div>
        ),
      },
    ];
  }, [_columns, genBtn]);

  return (
    <>
      <TableConfig
        visible={configModal}
        columns={_columns}
        handleClose={setShow}
        handleSave={setColumns}
      />
      <div className="bg-white shadow px-4 h-fit rounded">
        <div className="flex py-4 justify-between">
          <div className="flex gap-4">
            {genBtn(
              "create",
              <button
                className="btn rounded btn-primary btn-sm"
                onClick={() => {
                  handleOpenEdit();
                }}
              >
                新增
                <IconPlus size="small" />
              </button>,
            )}

            {genBtn(
              "removes",
              <>
                {rows?.length ? (
                  <>
                    <span className="flex text-gray-900 text-opacity-60 text-sm justify-center items-center">
                      已选中{rows?.length ?? 0}个
                    </span>
                    <Popconfirm
                      title="确定是否要删除该项？"
                      content="此修改将不可逆"
                      okType="danger"
                      okButtonProps={{
                        className:
                          "semi-button semi-button-danger semi-button-light",
                      }}
                      onConfirm={handleRemoves}
                    >
                      <button className="btn btn-sm rounded">批量删除</button>
                    </Popconfirm>
                  </>
                ) : null}
              </>,
            )}
          </div>

          <div className="flex gap">
            <div className="tooltip" data-tip="刷新">
              <button
                className="btn btn-sm btn-ghost rounded no-animation"
                onClick={() => {
                  refetch();
                }}
              >
                <IconRefresh />
              </button>
            </div>
            <UploadTmpl
              excelType="monitor_template"
              downUrl={encodeURI("/static/template/视频监控信息导入模板.xlsx")}
              tip="请先准备好对应的区域信息，否则导入会失败"
            />
            <div className="tooltip" data-tip="下载">
              <button className="btn btn-sm btn-ghost rounded no-animation">
                <IconDownload />
              </button>
            </div>
            <div className="tooltip" data-tip="上传">
              <button className="btn btn-sm btn-ghost rounded no-animation">
                <IconUpload />
              </button>
            </div>

            <div className="tooltip" data-tip="打印">
              <button className="btn btn-sm btn-ghost rounded no-animation">
                <IconPrint />
              </button>
            </div>

            <div className="tooltip" data-tip="设置">
              <button
                className="btn btn-sm btn-ghost rounded no-animation"
                onClick={handleOpenSetting}
              >
                <IconSetting />
              </button>
            </div>
          </div>
        </div>
        {/* <Video
          streamUrl='wss://video.vren-tech.com/stream/8e8bd71d-fb93-4052-b637-e03e19f33e3d_2/channel/0/mse?uuid=8e8bd71d-fb93-4052-b637-e03e19f33e3d_2&channel=0'
          autoplay
        /> */}
        <Table
          className="rounded overflow-hidden"
          rowKey="id"
          // scroll={{ x:1200 }}
          columns={columns?.filter?.((o) => o.fixed || o.isShow)}
          dataSource={result}
          rowSelection={rowSelection}
          loading={isLoading}
          onHeaderRow={(columns, index) => {
            return {
              className: "text-gray-900 text-opacity-90 bg-gray-50",
            };
          }}
          headerStyle={{ color: "blue" }}
          pagination={{
            showSizeChanger: true,
            popoverPosition: "topRight",
            currentPage: dataSource?.pageNumber ?? 1,
            pageSize: dataSource?.pageSize ?? 10,
            total: dataSource?.totalCount ?? 0,
            onPageChange: handlePageChange,
            onPageSizeChange: handlePageSizeChange,
            pageSizeOpts: [10, 15, 20, 50],
          }}
        />
      </div>
    </>
  );
};
