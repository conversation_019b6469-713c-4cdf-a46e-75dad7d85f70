import { useQueryClient } from "@tanstack/react-query";
import { basicInfoContractorProjectResumptionApis } from "api";
import {
  basicInfoContractorProjectResumptionAtoms,
  basicInfoContractorProjectResumptionAuditDelegateModalAtom,
  basicInfoContractorProjectResumptionAuditModalAtom,
} from "atoms";
import { ExportProvider, List } from "components";
import { useAtom } from "jotai";
import { FC, useCallback } from "react";
import { getUserInfo } from "utils";

type BasicInfoContractorProjectResumptionContentProps = {
  callback?: any;
  layout?: string;
  referAtom?: any;
  readonly?: boolean;
  filter?: any;
};
export const BasicInfoContractorProjectResumptionContent: FC<
  BasicInfoContractorProjectResumptionContentProps
> = ({
  callback,
  layout,
  referAtom,
  readonly = false,
  filter = {},
  ...restProps
}) => {
  if (layout === "modal") {
    readonly = true;
  }
  const { id: userId } = getUserInfo();
  const queryClient = useQueryClient();
  const queryKey = "list" + basicInfoContractorProjectResumptionAtoms.entity;

  const [auditModalVisible, setAuditModalVisible] = useAtom(
    basicInfoContractorProjectResumptionAuditModalAtom
  );

  const handleOpenAuditModal = useCallback(
    (record: any) => {
      setAuditModalVisible({
        record,
        show: true,
      });
    },
    [setAuditModalVisible]
  );

  const toggleAudit = (record: any) => {
    return record?.status === 1 &&
      record.auditorList.some((item: any) => item.id === userId)
      ? {
          engName: "audit",
          chnName: "审核",
          func: handleOpenAuditModal,
        }
      : null;
  };

  const [auditDelegateModalVisible, setAuditDelegateModalVisible] = useAtom(
    basicInfoContractorProjectResumptionAuditDelegateModalAtom
  );

  const handleOpenAuditDelegateModal = useCallback(
    (record: any) => {
      setAuditDelegateModalVisible({
        record,
        show: true,
      });
    },
    [setAuditDelegateModalVisible]
  );

  const toggleAuditDelegate = (record: any) => {
    return record?.status === 1 &&
      record.auditorList.some((item: any) => item.id === userId)
      ? {
          engName: "auditDelegate",
          chnName: "审核代办",
          func: handleOpenAuditDelegateModal,
        }
      : null;
  };

  return (
    <ExportProvider>
      <List
        atoms={basicInfoContractorProjectResumptionAtoms}
        apis={basicInfoContractorProjectResumptionApis}
        // operations, dynamicOperationFuncs
        filter={filter}
        callback={callback}
        referAtom={referAtom}
        layout={layout}
        readonly={readonly}
        cud={0b101}
        dynamicOperationFuncs={[toggleAudit, toggleAuditDelegate]}
        {...restProps}
      />
    </ExportProvider>
  );
};
