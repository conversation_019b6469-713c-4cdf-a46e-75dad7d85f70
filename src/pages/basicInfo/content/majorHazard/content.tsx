import { majorHazardApis } from "api";
import { majorHazardAtoms } from "atoms";
import { List } from "components";
import { useAtom } from "jotai";
import { useCallback } from "react";

export const MajorHazardContent = () => {
  const [evaluateModal, setEvaluateModal] = useAtom(
    majorHazardAtoms.evaluateModal,
  );

  const handleOpenEvaluate = useCallback(
    (record) => {
      console.log("handleEvaluate", record);

      setEvaluateModal({
        id: record.id,
        show: true,
      });
    },
    [setEvaluateModal],
  );

  return (
    <List
      atoms={majorHazardAtoms}
      apis={majorHazardApis}
      _operations={[
        {
          engName: "evaluate",
          chnName: "等级评定",
          func: handleOpenEvaluate,
        },
      ]}
    />
  );
};
