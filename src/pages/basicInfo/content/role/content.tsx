import {
  IconDownload,
  IconPlus,
  IconPrint,
  IconRefresh,
  IconSetting,
  IconUpload,
} from "@douyinfe/semi-icons";
import {
  Button,
  ButtonGroup,
  Dropdown,
  Popconfirm,
  Table,
  Toast,
  Tooltip,
} from "@douyinfe/semi-ui";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { delRole, delRoles, getRoleList } from "api";
import {
  roleColumnsAtom,
  roleConfigModalAtom,
  roleEditModal,
  roleFilterAtom,
  roleFnAtom,
  roleSettingAtom,
} from "atoms/basicInfo";
import { TableConfig } from "components";
import { useBtnHooks } from "hooks";
import { useAtom } from "jotai";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useLoaderData, useLocation } from "react-router-dom";

export const RoleContent = () => {
  const queryClient = useQueryClient();
  const loderData = useLoaderData();
  const { pathname } = useLocation();
  const genBtn = useBtnHooks(loderData, pathname);
  const [rows, setRows] = useState<number[]>([]);
  const [roleFilter, setRoleFilter] = useAtom(roleFilterAtom);
  const [roleFn, setRoleFn] = useAtom(roleFnAtom);
  const [roleEdit, setRoleEdit] = useAtom(roleEditModal);
  const [configModal, setShow] = useAtom(roleConfigModalAtom);
  const [_columns, setColumns] = useAtom(roleColumnsAtom);
  const [info, setInfo] = useAtom(roleSettingAtom);

  const { isLoading, data, refetch } = useQuery({
    queryKey: ["getRoleList", roleFilter],
    queryFn: () => getRoleList(roleFilter),
  });

  const mutation = useMutation({
    mutationFn: delRole,
    onSuccess: async (res) => {
      if (res?.code !== 0) {
        return;
      }
      let opts = {
        content: `操作成功!`,
        duration: 2,
      };
      queryClient.invalidateQueries({ queryKey: ["getRoleList"] });
      Toast.success(opts);
      roleFn?.refetch?.();
    },
  });

  const removes = useMutation({
    mutationFn: delRoles,
    onSuccess: async (res) => {
      if (res?.code !== 0) {
        return;
      }
      let opts = {
        content: `操作成功!`,
        duration: 2,
      };
      Toast.success(opts);
      queryClient.invalidateQueries({ queryKey: ["getRoleList"] });
      roleFn?.refetch?.();
    },
  });

  useEffect(() => {
    setRoleFn({
      refetch: refetch,
    });
  }, [refetch]);

  const dataSource = useMemo(() => data?.data ?? {}, [data?.data]);
  const result = useMemo(() => dataSource?.results ?? [], [dataSource]);

  const handleConfirm = useCallback(
    (record) => {
      mutation.mutate(record.id);
    },
    [mutation],
  );

  const rowSelection = useMemo(
    () => ({
      fixed: true,
      onChange: (selectedRowKeys) => {
        setRows(selectedRowKeys);
      },
    }),
    [setRows],
  );

  const handleOpenSetting = useCallback(() => {
    setShow(true);
  }, [setShow]);

  const handleOpenSettingRole = useCallback(
    (id?: string) => {
      setInfo({
        id: id ?? "",
        show: true,
      });
    },
    [setInfo],
  );

  const handleOpenEdit = useCallback(
    (id?: string) => {
      setRoleEdit({
        id: id ?? "",
        show: true,
      });
    },
    [setRoleEdit],
  );

  const handlePageChange = useCallback(
    (currentPage: number) => {
      setRoleFilter({
        ...roleFilter,
        pageNumber: currentPage,
      });
    },
    [roleFilter, setRoleFilter],
  );

  const handlePageSizeChange = useCallback(
    (pageSize: number) => {
      setRoleFilter({
        ...roleFilter,
        pageSize: pageSize,
      });
    },
    [roleFilter, setRoleFilter],
  );

  const handleRemoves = useCallback(() => {
    removes.mutate(rows);
    setRows([]);
  }, [removes, rows, setRows]);

  const columns = useMemo(() => {
    return [
      ..._columns,
      {
        title: <Tooltip content="操作">操作</Tooltip>,
        isShow: true,
        dataIndex: "operate",
        key: "operate",
        align: "center",
        width: 220,
        render: (text, record) => (
          <div>
            <ButtonGroup aria-label="操作按钮组">
              <Dropdown
                trigger={"hover"}
                position={"bottomLeft"}
                render={
                  <Dropdown.Menu>
                    {genBtn(
                      "action1",
                      <Dropdown.Item
                        onClick={() => {
                          handleOpenSettingRole(record.id);
                        }}
                      >
                        设置权限
                      </Dropdown.Item>,
                    )}
                    {genBtn(
                      "edit",
                      <Dropdown.Item
                        onClick={() => {
                          handleOpenEdit(record.id);
                        }}
                      >
                        编辑
                      </Dropdown.Item>,
                    )}
                  </Dropdown.Menu>
                }
              >
                <Button>操作</Button>
              </Dropdown>
              {genBtn(
                "remove",
                <Popconfirm
                  role="bottomRight"
                  title="确定是否要删除该项？"
                  content="此修改将不可逆"
                  okType="danger"
                  okButtonProps={{
                    className:
                      "semi-button semi-button-danger semi-button-light",
                  }}
                  onConfirm={() => {
                    handleConfirm(record);
                  }}
                >
                  <Button type="danger">删除</Button>
                </Popconfirm>,
              )}
            </ButtonGroup>
          </div>
        ),
      },
    ];
  }, [_columns, genBtn]);

  return (
    <>
      <TableConfig
        visible={configModal}
        columns={_columns}
        handleClose={setShow}
        handleSave={setColumns}
      />
      <div className="bg-white shadow px-4 h-fit rounded">
        <div className="flex py-4 justify-between">
          <div className="flex gap-4">
            {genBtn(
              "create",
              <button
                className="btn rounded btn-primary btn-sm"
                onClick={() => {
                  handleOpenEdit();
                }}
              >
                新增
                <IconPlus size="small" />
              </button>,
            )}
            {genBtn(
              "removes",
              <>
                <span className="flex text-gray-900 text-opacity-60 text-sm justify-center items-center">
                  已选中{rows?.length ?? 0}个
                </span>
                {rows?.length ? (
                  <Popconfirm
                    title="确定是否要删除该项？"
                    content="此修改将不可逆"
                    okType="danger"
                    okButtonProps={{
                      className:
                        "semi-button semi-button-danger semi-button-light",
                    }}
                    onConfirm={handleRemoves}
                  >
                    <button className="btn btn-sm rounded">批量删除</button>
                  </Popconfirm>
                ) : null}
              </>,
            )}
          </div>

          <div className="flex gap">
            <div className="tooltip" data-tip="刷新">
              <button
                className="btn btn-sm btn-ghost rounded no-animation"
                onClick={() => {
                  refetch();
                }}
              >
                <IconRefresh />
              </button>
            </div>
            <div className="tooltip" data-tip="下载">
              <button className="btn btn-sm btn-ghost rounded no-animation">
                <IconDownload />
              </button>
            </div>
            <div className="tooltip" data-tip="上传">
              <button className="btn btn-sm btn-ghost rounded no-animation">
                <IconUpload />
              </button>
            </div>

            <div className="tooltip" data-tip="打印">
              <button className="btn btn-sm btn-ghost rounded no-animation">
                <IconPrint />
              </button>
            </div>

            <div className="tooltip" data-tip="设置">
              <button
                className="btn btn-sm btn-ghost rounded no-animation"
                onClick={handleOpenSetting}
              >
                <IconSetting />
              </button>
            </div>
          </div>
        </div>
        <Table
          className="rounded overflow-hidden"
          rowKey="id"
          // scroll={{ x:1200 }}
          columns={columns?.filter?.((o) => o.fixed || o.isShow)}
          dataSource={result}
          rowSelection={rowSelection}
          loading={isLoading}
          onHeaderRow={(columns, index) => {
            return {
              className: "text-gray-900 text-opacity-90 bg-gray-50",
            };
          }}
          headerStyle={{ color: "blue" }}
          pagination={{
            showSizeChanger: true,
            popoverRole: "topRight",
            currentPage: dataSource?.pageNumber ?? 1,
            pageSize: dataSource?.pageSize ?? 10,
            total: dataSource?.totalCount ?? 0,
            onPageChange: handlePageChange,
            onPageSizeChange: handlePageSizeChange,
            pageSizeOpts: [10, 15, 20, 50],
          }}
        />
      </div>
    </>
  );
};
