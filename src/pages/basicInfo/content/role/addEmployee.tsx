import { Banner, Form, Modal, Toast, useFormApi } from "@douyinfe/semi-ui";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { editRoleLink } from "api/basicInfo";
import { EmployeeSearch } from "components";
import { useCallback, useEffect, useRef } from "react";

const ComponentUsingFormApi = ({ users }) => {
  const formApi = useFormApi();

  useEffect(() => {
    if (users?.length && formApi) {
      const tmp = [];
      users.forEach((o) => {
        tmp.push(o.id);
      });
      formApi.setValues({
        users: tmp,
      });
    }
  }, [formApi, users]);

  return null;
};

export const AddEmployee = ({ id, users, handleClose, show }) => {
  const getFormApiRef = useRef<any>(null);
  const queryClient = useQueryClient();
  const mutation = useMutation({
    mutationFn: editRoleLink,
    onSuccess: async (res) => {
      if (res?.code === 0) {
        let opts = {
          content: `操作成功!`,
          duration: 2,
        };
        Toast.success(opts);
        queryClient.invalidateQueries(["getRole"]);
        handleClose?.();
      }
    },
  });

  const handleSetFormApi = useCallback(
    (formApi: any) => {
      getFormApiRef.current = formApi;
    },
    [getFormApiRef]
  );

  const handleOk = () => {
    if (mutation.isLoading) {
      return;
    }
    getFormApiRef.current
      .validate()
      .then((values) => {
        mutation.mutate({
          id: id,
          values: values,
        });
      })
      .catch((errors) => {
        console.log(errors);
        const [formFieldId] = Object.keys(errors || {});
        if (formFieldId) {
          document.getElementById(formFieldId)?.scrollIntoViewIfNeeded?.();
        }
      });
  };

  return (
    <>
      <Modal
        title="设置成员"
        visible={show}
        onCancel={handleClose}
        maskClosable
        keepDOM
        width={800}
        footer={
          <div className="flex gap-2 justify-end">
            <button className="btn rounded btn-sm" onClick={handleClose}>
              取消
            </button>
            {mutation.isLoading ? (
              <button className="btn rounded btn-primary btn-sm btn-disabled">
                <span className="loading loading-spinner loading-xs"></span>
                确定
              </button>
            ) : (
              <button
                className="btn rounded btn-primary btn-sm"
                onClick={handleOk}
              >
                确定
              </button>
            )}
          </div>
        }
        centered
      >
        <Banner
          type="warning"
          description="请注意：用户只能属于一个角色，选择后会覆盖之前的设置，仔细辨别每个用户后面的角色是否为空"
        ></Banner>
        <Form
          labelPosition="left"
          labelAlign="right"
          getFormApi={handleSetFormApi}
        >
          <ComponentUsingFormApi users={users} />
          <EmployeeSearch
            field="users"
            placeholder="添加角色所属成员"
            multiple
          />
          <button className="btn rounded btn-primary btn-sm" onClick={handleOk}>
            保存
          </button>
        </Form>
      </Modal>
    </>
  );
};
