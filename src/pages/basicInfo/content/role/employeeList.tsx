import React, { useState, useMemo, useEffect, useCallback } from "react";
import {
  Dropdown,
  Table,
  ButtonGroup,
  Button,
  Popconfirm,
  Toast,
  Tooltip,
  Tag,
} from "@douyinfe/semi-ui";
import { IconPlus } from "@douyinfe/semi-icons";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { editRoleLink } from "api/basicInfo";
import { AddEmployee } from "./addEmployee";
import { omit, pick } from "ramda";

const _columns = [
  {
    title: <Tooltip content="ID">ID</Tooltip>,
    dataIndex: "id",
    isShow: true,
    fixed: true,
    width: 80,
  },
  {
    title: <Tooltip content="姓名">姓名</Tooltip>,
    dataIndex: "name",
    isShow: true,
    ellipsis: true,
    fixed: true,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="性别">性别</Tooltip>,
    dataIndex: "gender",
    isShow: true,
    ellipsis: true,
    render: (item) => (
      <p>
        {item === 1 ? (
          <Tag color="blue" type="light">
            男
          </Tag>
        ) : (
          <Tag color="red" type="light">
            女
          </Tag>
        )}
      </p>
    ),
  },
  {
    title: <Tooltip content="部门">部门</Tooltip>,
    dataIndex: "department",
    isShow: true,
    ellipsis: true,
    render: (item) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="岗位">岗位</Tooltip>,
    dataIndex: "position",
    isShow: true,
    ellipsis: true,
    render: (item) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="工号">工号</Tooltip>,
    dataIndex: "employeeId",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
];

export const EmployeeList = ({ users, id }) => {
  const queryClient = useQueryClient();
  const [addModal, setAddModal] = useState<boolean>(false);

  const mutation = useMutation({
    mutationFn: editRoleLink,
    onSuccess: async (res) => {
      queryClient.invalidateQueries(["getRole"]);
    },
  });

  const result = useMemo(() => users ?? [], [users]);

  const handleConfirm = useCallback(
    (record) => {
      const tmp = [];
      (result ?? []).forEach((o) => {
        if (o.id != record.id) {
          tmp.push(o.id);
        }
      });

      mutation.mutate({
        id: id,
        values: {
          users: tmp,
        },
      });
    },
    [mutation],
  );

  const handleOpenEdit = useCallback(() => {
    setAddModal(!addModal);
  }, [setAddModal, addModal]);

  const columns = useMemo(() => {
    return _columns;
  }, [_columns]);

  return (
    <>
      <AddEmployee
        id={id}
        users={users}
        handleClose={handleOpenEdit}
        show={addModal}
      />

      <div className="bg-white shadow px-4 h-fit rounded">
        <div className="flex py-4 justify-between">
          <div className="flex gap-4">
            <button
              className="btn rounded btn-primary btn-sm"
              onClick={handleOpenEdit}
            >
              新增
              <IconPlus size="small" />
            </button>
          </div>
          <div className="flex gap"></div>
        </div>

        <Table
          className="rounded overflow-hidden"
          rowKey="id"
          // scroll={{ x:1200 }}
          columns={columns}
          dataSource={result}
          onHeaderRow={(columns, index) => {
            return {
              className: "text-gray-900 text-opacity-90 bg-gray-50",
            };
          }}
          headerStyle={{ color: "blue" }}
          pagination={false}
        />
      </div>
    </>
  );
};
