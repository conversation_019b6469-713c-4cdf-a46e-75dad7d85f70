import { Checkbox, Collapse, Switch, Toast } from "@douyinfe/semi-ui";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { editRoleAuth, getPermission } from "api";
import { roleSetting<PERSON>tom } from "atoms/basicInfo";
import { RoleKeyCode } from "config";
import { useMenuHooks } from "hooks";
import { useAtom } from "jotai";
import { useResetAtom } from "jotai/utils";
import { clone, find, findIndex, groupBy, omit, propEq, remove } from "ramda";
import { useEffect, useMemo, useState } from "react";
import { AppRouterName } from "../permission/appRole";

export function SettingRole({ requestType, permission }) {
  const queryClient = useQueryClient();
  const [info, setInfo] = useAtom(roleSettingAtom);
  const reset = useResetAtom(roleSettingAtom);
  const [role, setRole] = useState([]);
  const { MainMenu } = useMenuHooks();

  const mutation = useMutation({
    mutationFn: editRoleAuth,
    onSuccess: async (res) => {
      const options = {
        content: `操作成功!`,
        duration: 2,
      };
      Toast.success(options);
    },
  });

  const { data } = useQuery({
    queryKey: ["getPermission", requestType],
    queryFn: async () => getPermission(requestType),
  });

  // 需要把接口数据存入state中给switch做判断
  useEffect(() => {
    const dataSource = data?.data ?? [];

    if (!permission || !dataSource?.length) {
      return;
    }

    const _p = JSON.parse(permission);

    const state = [];
    _p.forEach((o: any) => {
      let object = {};
      const menu = find(propEq(o?.[`${RoleKeyCode}`], "code"))(dataSource);
      object = omit(["permissions"])(menu);
      const perList: any[] = [];

      o?.permissions?.forEach?.((p: any) => {
        const json = JSON.parse(p || "{}");

        // TODO: permission is null
        /* console.log(
          menu?.permissions,
          "menu?.permissionsmenu?.permissionsmenu?.permissionsmenu?.permissions",
        );
        console.log(menu); */
        const item = find(propEq(json.id, "id"))(menu?.permissions ?? []);
        perList.push(item);
      });
      object.permissions = perList;
      state.push(object);
    });
    setRole(state);
  }, [permission, data?.data]);

  const results = useMemo(() => {
    const list = data?.data ?? [];
    const group = groupBy((item) => {
      const { sysModel = 1 } = item as any;
      // AppRouterName
      let name = {} as any;
      if (requestType === 1) {
        name = find(propEq(sysModel, "bindApiKey"))(MainMenu[0].items);
      } else {
        name = {
          text: AppRouterName[`${sysModel}`],
        };
        // name = find(propEq(sysModel, 'bindApiKey'))(MainMenu[0].items)
      }

      return name?.text;
    })(list);
    return group;
  }, [data, MainMenu, requestType]);

  const update = (copy) => {
    const send = [];
    setRole(copy);

    for (const o of copy) {
      const pid = [];
      o?.permissions?.forEach?.((p) => {
        pid.push(JSON.stringify(p));
      });
      if (o?.code) {
        send.push({
          [`${RoleKeyCode}`]: o?.code,
          permissions: pid,
        });
      }
    }
    mutation.mutate({
      id: info.id,
      requestType,
      values: send,
    });
  };

  // 开启菜单但是不开启权限
  // TODO: 当误操作时，右边的全部开启即使选中也会呈现为disable状态
  const handleCheckMenu = (checked: boolean, menu: any) => {
    let copy = clone(role);

    const omitMenu = omit(["permissions"], menu);
    if (checked) {
      copy.push(omitMenu);
    } else {
      const index = findIndex(propEq(menu.id, "id"))(copy);
      copy = remove(index, 1, copy);
    }
    update(copy);
    // setRole(copy)
  };
  // 全部开启
  const handleCheckMenuAll = (checked: boolean, menu: any) => {
    let copy = clone(role);
    if (checked) {
      const dataSource = data?.data ?? [];
      const m = find(propEq(menu.id, "id"))(dataSource);

      copy = copy.filter((c) => c.id != m.id);
      copy.push(m);
    } else {
      const index = findIndex(propEq(menu.id, "id"))(copy);
      copy = remove(index, 1, copy);
    }
    update(copy);
    // setRole(copy)
  };

  // 设置按钮权限
  const handleCheckButton = (checked: boolean, p: any, menu: any) => {
    const copy = clone(role);
    const hasMenu = findIndex(propEq(menu.id, "id"))(copy);

    if (checked) {
      // 选中权限，但是菜单没开启
      if (hasMenu < 0) {
        const omit_menu = omit(["permissions"], menu);
        const m = {
          ...omit_menu,
          permissions: [p],
        };
        copy.push(m);
      } else {
        // 选中权限，并且菜单已经开启
        if (!copy[hasMenu]?.permissions?.length) {
          copy[hasMenu].permissions = [];
        }
        copy[hasMenu].permissions.push(p);
      }
    } else {
      const menuIndex = findIndex(propEq(menu.id, "id"))(copy);
      copy[menuIndex].permissions = copy[menuIndex].permissions?.filter?.(
        (o) => o.id != p.id,
      );
    }
    update(copy);

    // setRole(copy)
  };

  // 检查菜单是否开启
  const hasCheckMenu = (m): boolean =>
    Boolean(find(propEq(m.code, "code"))(role)?.id);

  // 检查是否全部开启
  const hasCheckAll = (m): boolean => {
    const menu = find(propEq(m.code, "code"))(role);

    if (
      menu?.id &&
      (m?.permissions ?? [])?.length === menu.permissions?.length
    ) {
      return true;
    }

    // “菜单开启” 判断按钮开关
    if (hasCheckMenu(m)) {
      return true;
    }

    return false;
  };

  // 检查该权限按钮是否开启
  const hasCheckButton = (p): boolean => {
    if (role.length === 0) {
      return false;
    }

    const menu = find(propEq(p.menuId, "id"))(role);

    if (!menu) {
      return false;
    }
    if (!menu.permissions?.length) {
      return false;
    }
    const has = find(propEq(p.id, "id"))(menu.permissions);
    if (!has?.id) {
      return false;
    }
    return true;
  };

  // 检查模块下菜单/功能是否全部开启
  const hasOpenModAll = (list: any) => {
    const allItem = list.filter((menu: any) => hasCheckAll(menu));
    if (allItem.length && allItem.length === list.length) {
      return true;
    }
    return false;
  };

  const handleCheckModAll = (checked: boolean, list: any) => {
    // checked, results[k]
    let copy = clone(role);
    const allItem = list.forEach((menu: any) => {
      if (checked) {
        const dataSource = data?.data ?? [];
        const m = find(propEq(menu.id, "id"))(dataSource);

        copy = copy.filter((c) => c.id != m.id);
        copy.push(m);
      } else {
        const index = findIndex(propEq(menu.id, "id"))(copy);
        copy = remove(index, 1, copy);
      }
    });

    update(copy);
  };

  return (
    <Collapse className="w-full" keepDOM defaultActiveKey={[0, 1]}>
      {Object.keys(results).map((k, index) => (
        <Collapse.Panel header={k} itemKey={index}>
          <div className="grid grid-cols-5 font-bold text-black [&_div]:p-2 border-y divide-x border-x bg-gray-100">
            <div>{requestType == 1 ? "菜单名称" : "功能名称"}</div>
            <div>{requestType == 1 ? "开启菜单" : "开启功能"}</div>
            {requestType == 1 ? (
              <>
                <div className="text-center col-span-2">操作</div>
                <div className="flex justify-center items-center gap-4">
                  <span>全部开启 </span>
                  <Switch
                    defaultChecked={false}
                    checked={hasOpenModAll(results[k])}
                    onChange={(checked) => {
                      handleCheckModAll(checked, results[k]);
                    }}
                  />
                </div>
              </>
            ) : null}
          </div>
          {(results[k] ?? [])
            .sort((a, b) => a?.sort - b?.sort)
            .map((menu, index_) => (
              <div className="flex border-b border-x ml-0 flex-col">
                <div className="flex flex-col divide-y">
                  <div className="grid grid-cols-5 text-black  items-center divide-x">
                    <div className="min-h-[68px] items-center flex p-2">
                      {menu.name}
                    </div>
                    <div className="text-center flex min-h-[68px] h-full items-center p-2">
                      <Checkbox
                        defaultChecked={false}
                        checked={hasCheckMenu(menu)}
                        onChange={(e) => {
                          handleCheckMenu(e.target.checked, menu);
                        }}
                      >
                        {requestType == 1 ? "菜单开启" : "功能开启"}
                      </Checkbox>
                    </div>
                    {requestType == 1 ? (
                      <>
                        <div className="w-full flex flex-wrap min-h-[68px] items-center col-span-2 h-full p-2 gap-2">
                          {(menu?.permissions ?? [])
                            .sort((a, b) => a.name.localeCompare(b.name, "zh"))
                            .map((p) => (
                              <Checkbox
                                checked={hasCheckButton(p)}
                                onChange={(e) => {
                                  handleCheckButton(e.target.checked, p, menu);
                                }}
                              >
                                {p.name}
                              </Checkbox>
                            ))}
                        </div>
                        <div className="w-full flex flex-wrap min-h-[68px] items-center justify-center h-full p-2">
                          <Switch
                            defaultChecked={false}
                            checked={hasCheckAll(menu)}
                            onChange={(checked) => {
                              handleCheckMenuAll(checked, menu);
                            }}
                          />
                        </div>
                      </>
                    ) : null}
                  </div>
                </div>
              </div>
            ))}
        </Collapse.Panel>
      ))}
    </Collapse>
  );
}
