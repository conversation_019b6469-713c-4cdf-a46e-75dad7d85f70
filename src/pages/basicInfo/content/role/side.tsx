import { SideSheet, Tab<PERSON>ane, Tabs, Typography } from "@douyinfe/semi-ui";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useAtom } from "jotai";
import { useResetAtom } from "jotai/utils";
import { useMemo } from "react";

import { getRole } from "api";
import { roleSettingAtom } from "atoms/basicInfo";
import { SettingRole } from "./settingRole";

import { EmployeeList } from "./employeeList";

export const RoleSide = () => {
  const queryClient = useQueryClient();
  const [info, setInfo] = useAtom(roleSettingAtom);
  const reset = useResetAtom(roleSettingAtom);

  const { data } = useQuery({
    queryKey: ["getRole", info?.id],
    queryFn: () => {
      return getRole(info?.id);
    },
    enabled: Boolean(info?.id),
  });

  const dataSource = useMemo(() => {
    return data?.data ?? {};
  }, [data]);

  return (
    <SideSheet
      title={<Typography.Title heading={4}>详细信息</Typography.Title>}
      headerStyle={{ borderBottom: "1px solid var(--semi-color-border)" }}
      bodyStyle={{ borderBottom: "1px solid var(--semi-color-border)" }}
      visible={info?.show}
      closeIcon={null}
      onCancel={() => {
        reset();
      }}
      size="large"
    >
      <div className="flex flex-col gap-2 mt-2">
        <Typography.Title heading={3}>{data?.data?.name}</Typography.Title>
        <p>{data?.data?.description}</p>
      </div>

      <Tabs type="line">
        <TabPane tab="PC操作权限" itemKey="1">
          <SettingRole requestType={1} permission={dataSource?.permission} />
        </TabPane>
        <TabPane tab="APP操作权限" itemKey="2">
          <SettingRole
            requestType={2}
            permission={dataSource?.mobilePermission}
          />
        </TabPane>
        <TabPane tab="设置成员" itemKey="3">
          <EmployeeList id={info?.id} users={dataSource?.users} />
        </TabPane>
      </Tabs>
    </SideSheet>
  );
};
