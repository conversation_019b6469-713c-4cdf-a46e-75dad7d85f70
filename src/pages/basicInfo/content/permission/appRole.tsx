import { <PERSON>lapse, Spin } from "@douyinfe/semi-ui";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  EditPermissionParams,
  editMenu,
  editPermission,
  getPermission,
} from "api";
import { equals, find, omit, propEq } from "ramda";
import { useMemo } from "react";

export const AppRouterName = {
  1: "隐患排查治理",
  2: "作业管理",
  // 3: '双重预防',
  3: "智能巡检",
  4: "报警管理",
  5: "教育培训",
  6: "承包商",
};

export const AppRole = () => {
  const queryClient = useQueryClient();

  const routers = {
    1: [
      {
        clientPath: "",
        code: "scyf_yhsb_scyfyhsb",
        description: "",
        name: "隐患上报",
        permissions: null,
        requestType: 2,
        serverPath: "",
        sysModel: 1,
        sort: 1,
      },
      {
        clientPath: "",
        code: "scyf_yhpc_scyfyhpc",
        description: "",
        name: "隐患排查",
        permissions: null,
        requestType: 2,
        serverPath: "",
        sysModel: 1,
        sort: 2,
      },
      {
        clientPath: "",
        code: "scyf_yhzl_scyfyhzl",
        description: "",
        name: "隐患治理",
        permissions: null,
        requestType: 2,
        serverPath: "",
        sysModel: 1,
        sort: 3,
      },
      {
        clientPath: "",
        code: "scyf_sspsh_scyfsspsh", // menu code
        description: "",
        name: "随手拍审核",
        permissions: null,
        requestType: 2, // 1: pc 2: app
        serverPath: "",
        sysModel: 1, // duplicate with key
        sort: 4, //sort
      },
      {
        clientPath: "",
        code: "scyf_ssp_scyfssp",
        description: "",
        name: "随手拍",
        permissions: null,
        requestType: 2,
        serverPath: "",
        sysModel: 1,
        sort: 5,
      },
      {
        clientPath: "",
        code: "scyf_sspsh_scyfszfzx",
        description: "",
        name: "政府专项",
        permissions: null,
        requestType: 2,
        serverPath: "",
        sysModel: 1,
        sort: 6,
      },
      {
        clientPath: "",
        code: "scyf_scyf_scyfqyzx",
        description: "",
        name: "企业自查",
        permissions: null,
        requestType: 2,
        serverPath: "",
        sysModel: 1,
        sort: 7,
      },
    ],
    2: [
      {
        clientPath: "",
        code: "tszy_zyyy_tszyzyyy",
        description: "",
        name: "作业预约",
        permissions: null,
        requestType: 2,
        serverPath: "",
        sysModel: 2,
        sort: 5,
      },
      {
        clientPath: "",
        code: "tszy_yysp_tszyyysp",
        description: "",
        name: "预约审批",
        permissions: null,
        requestType: 2,
        serverPath: "",
        sysModel: 2,
        sort: 1,
      },
      {
        clientPath: "",
        code: "tszy_zysq_tszyzysq",
        description: "",
        name: "作业票",
        permissions: null,
        requestType: 2,
        serverPath: "",
        sysModel: 2,
        sort: 6,
      },
      {
        clientPath: "",
        code: "tszy_zyxk_tszzyxk",
        description: "",
        name: "作业许可",
        permissions: null,
        requestType: 2,
        serverPath: "",
        sysModel: 2,
        sort: 2,
      },
      {
        clientPath: "",
        code: "tszy_xcgl_tszyxcgl",
        description: "",
        name: "现场管理",
        permissions: null,
        requestType: 2,
        serverPath: "",
        sysModel: 2,
        sort: 3,
      },
      {
        clientPath: "",
        code: "tszy_zycx_tszzycx",
        description: "",
        name: "作业查询",
        permissions: null,
        requestType: 2,
        serverPath: "",
        sysModel: 2,
        sort: 4,
      },
    ],

    4: [
      {
        clientPath: "",
        code: "bjgl_bjlb_bjglbjlb",
        description: "",
        name: "报警列表",
        permissions: null,
        requestType: 2,
        serverPath: "",
        sysModel: 4,
        sort: 1,
      },
    ],
    3: [
      {
        clientPath: "",
        code: "znxj_znxj_znxjxjrw",
        description: "",
        name: "巡检任务",
        permissions: null,
        requestType: 2,
        serverPath: "",
        sysModel: 3,
        sort: 1,
      },
      {
        clientPath: "",
        code: "znxj_znxj_znxjfjhxj",
        description: "",
        name: "非计划巡检",
        permissions: null,
        requestType: 2,
        serverPath: "",
        sysModel: 3,
        sort: 2,
      },
    ],
    5: [
      {
        clientPath: "",
        code: "jypx_jypx_jypxwdpx",
        description: "",
        name: "教育培训",
        permissions: null,
        requestType: 2,
        serverPath: "",
        sysModel: 5,
        sort: 1,
      },
    ],
    6: [
      {
        clientPath: "",
        code: "cbsh_cbsh_cbshrcsq",
        description: "",
        name: "入厂申请",
        permissions: null,
        requestType: 2,
        serverPath: "",
        sysModel: 6,
        sort: 1,
      },
      {
        clientPath: "",
        code: "cbsh_cbsh_cbshrcsp",
        description: "",
        name: "入厂审批",
        permissions: null,
        requestType: 2,
        serverPath: "",
        sysModel: 6,
        sort: 2,
      },
      {
        clientPath: "",
        code: "cbsh_cbsh_cbshxmfg",
        description: "",
        name: "项目复工",
        permissions: null,
        requestType: 2,
        serverPath: "",
        sysModel: 6,
        sort: 3,
      },
      {
        clientPath: "",
        code: "cbsh_cbsh_cbshfgsp",
        description: "",
        name: "项目复工审批",
        permissions: null,
        requestType: 2,
        serverPath: "",
        sysModel: 6,
        sort: 4,
      },
      {
        clientPath: "",
        code: "cbsh_cbsh_cbshcbshmd",
        description: "",
        name: "承包商黑名单",
        permissions: null,
        requestType: 2,
        serverPath: "",
        sysModel: 6,
        sort: 5,
      },
      {
        clientPath: "",
        code: "cbsh_cbsh_cbshcbshmdsp",
        description: "",
        name: "承包商黑名单审批",
        permissions: null,
        requestType: 2,
        serverPath: "",
        sysModel: 6,
        sort: 6,
      },
      {
        clientPath: "",
        code: "cbsh_cbsh_cbshcbsyghmd",
        description: "",
        name: "承包商员工黑名单",
        permissions: null,
        requestType: 2,
        serverPath: "",
        sysModel: 6,
        sort: 7,
      },
      {
        clientPath: "",
        code: "cbsh_cbsh_cbshcbsyghmdsp",
        description: "",
        name: "承包商员工黑名单审批",
        permissions: null,
        requestType: 2,
        serverPath: "",
        sysModel: 6,
        sort: 8,
      },
      {
        clientPath: "",
        code: "cbsh_cbsh_cbshwgdj",
        description: "",
        name: "违规登记",
        permissions: null,
        requestType: 2,
        serverPath: "",
        sysModel: 6,
        sort: 9,
      },
      {
        clientPath: "",
        code: "cbsh_cbsh_cbshxmtgdj",
        description: "",
        name: "项目停工",
        permissions: null,
        requestType: 2,
        serverPath: "",
        sysModel: 6,
        sort: 10,
      },
      {
        clientPath: "",
        code: "cbsh_cbsh_cbshqylh",
        description: "",
        name: "企业拉黑",
        permissions: null,
        requestType: 2,
        serverPath: "",
        sysModel: 6,
        sort: 11,
      },
      {
        clientPath: "",
        code: "cbsh_cbsh_cbshrylh",
        description: "",
        name: "人员拉黑",
        permissions: null,
        requestType: 2,
        serverPath: "",
        sysModel: 6,
        sort: 12,
      },
    ],
  };

  const mutationMenu = useMutation({
    mutationFn: editMenu,
    onSuccess: async (res) => {
      // queryClient.invalidateQueries(['getPermission', 1])
      console.log(res, "res-----");
    },
  });

  const mutationPermission = useMutation({
    mutationFn: editPermission,
    onSuccess: async (res) => {
      console.log(res, "res-----");
    },
  });

  const { data } = useQuery({
    queryKey: ["getPermission", 2],
    queryFn: () => {
      return getPermission(2);
    },
  });

  const createSystemMenu = useMemo(() => {
    //return routers['1'];

    return [
      ...routers["1"],
      ...routers["2"],
      ...routers["3"],
      ...routers["4"],
      ...routers["5"],
      ...routers["6"],
    ];
  }, [routers]);

  // 检查菜单下权限
  const checkPermissions = () => {
    const serverMenus = data?.data ?? [];
    serverMenus.forEach((menu) => {
      if (menu?.id) {
        const clientMenuItem = find(propEq(menu.code, "code"))(
          createSystemMenu
        );
        if (clientMenuItem?.permissions?.length) {
          (clientMenuItem?.permissions ?? []).forEach((clientPermission) => {
            // 获取服务端权限
            const item = find(propEq(clientPermission.code, "code"))(
              menu?.permissions ?? []
            );
            const serverItem = omit(["menuId", "serverPath", "id"], item);
            const clientItem = omit(["menuId"], clientPermission);
            if (!equals(serverItem, clientItem)) {
              const newPermission = {
                ...clientItem,
                menuId: menu.id,
              } as EditPermissionParams;
              mutationPermission.mutate(newPermission, {
                onSuccess: (data, variables, context) => {
                  queryClient.invalidateQueries(["getPermission", 2]);
                  console.log("更新成功");
                },
              });
            }
          });
        }
      }
    });
  };

  const handleCheck = () => {
    createSystemMenu.forEach((menu) => {
      const item = find(propEq(menu.code, "code"))(data?.data ?? []);
      const serverItem = omit(
        ["id", "permissions", "serverPath", "description"],
        item
      );
      const clientItem = omit(
        ["permissions", "serverPath", "description"],
        menu
      );

      if (!item?.code || !equals(serverItem, clientItem)) {
        mutationMenu.mutate(omit(["permissions"], menu), {
          onSuccess: (data, variables, context) => {
            queryClient.invalidateQueries(["getPermission", 2]);
            checkPermissions();
          },
        });
      }
    });
    checkPermissions();
  };

  return (
    <div className="bg-white shadow px-4 h-fit rounded py-4">
      <button
        className="btn"
        onClick={handleCheck}
        disabled={mutationPermission.isLoading || mutationMenu.isLoading}
      >
        {mutationPermission.isLoading || mutationMenu.isLoading ? (
          <Spin size="small" />
        ) : null}
        自检权限
      </button>
      <div className="flex py-4 justify-between">
        <Collapse accordion className="w-full">
          {Object.keys(routers).map((k, index) => (
            <Collapse.Panel
              header={AppRouterName[k]}
              key={index}
              itemKey={`${index}`}
            >
              <div className="overflow-x-auto mb-4">
                <table className="table">
                  <thead>
                    <tr>
                      <th>ID</th>
                      <th>模块名称</th>
                      <th>菜单路径</th>
                      <th className="max-w-xs">按钮权限</th>
                      <th>编码</th>
                      <th>备注</th>
                    </tr>
                  </thead>
                  <tbody>
                    {(data?.data ?? [])
                      .filter((o) => o.sysModel == k)
                      ?.map?.((sub, subIndex) => (
                        <tr
                          className={subIndex % 2 == 0 ? "bg-base-200" : ""}
                          key={sub.key}
                        >
                          <th>{sub?.id}</th>
                          <td>{sub.name}</td>
                          <td>{sub?.clientPath}</td>
                          <td className="flex gap-x-2 max-w-xs flex-wrap">
                            {sub?.permissions?.map?.((p, i) => (
                              <button className="text-blue-500 flex" key={i}>
                                {p?.name}
                              </button>
                            ))}
                          </td>
                          <td>{sub?.code}</td>
                          <td>{sub?.description}</td>
                        </tr>
                      ))}
                  </tbody>
                </table>
              </div>
            </Collapse.Panel>
          ))}
        </Collapse>
      </div>
    </div>
  );
};
