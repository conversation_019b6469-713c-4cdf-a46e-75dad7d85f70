import { Collapse, Spin } from "@douyinfe/semi-ui";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  EditPermissionParams,
  editMenu,
  editPermission,
  getPermission,
} from "api";
import { useMenuHooks } from "hooks";
import { equals, find, omit, propEq } from "ramda";
import { useMemo } from "react";

const menuSort = [
  {
    name: "部门管理",
    sort: 101,
  },
  {
    name: "岗位管理",
    sort: 102,
  },
  {
    name: "员工管理",
    sort: 103,
  },
  {
    name: "角色管理",
    sort: 104,
  },
  {
    name: "登录记录",
    sort: 105,
  },
  {
    name: "工作组",
    sort: 106,
  },
  {
    name: "公司工作日历",
    sort: 107,
  },
  {
    name: "关于系统",
    sort: 108,
  },
  {
    name: "风险分区管理",
    sort: 201,
  },
  {
    name: "风险分析对象",
    sort: 202,
  },
  {
    name: "风险分析单元",
    sort: 203,
  },
  {
    name: "风险分析事件",
    sort: 204,
  },
  {
    name: "风险管控措施",
    sort: 205,
  },
  {
    name: "风险应知卡",
    sort: 206,
  },
  {
    name: "应急处置卡",
    sort: 207,
  },
  {
    name: "安全承诺卡",
    sort: 208,
  },
  {
    name: "风险辨识清单",
    sort: 209,
  },
  {
    name: "风险管控清单",
    sort: 210,
  },
  {
    name: "包保责任模板",
    sort: 211,
  },
  {
    name: "隐患排查计划",
    sort: 212,
  },
  {
    name: "执行记录",
    sort: 213,
  },
  {
    name: "履职统计",
    sort: 214,
  },
  {
    name: "IMEI管理",
    sort: 215,
  },
  {
    name: "排查计划",
    sort: 216,
  },
  {
    name: "排查任务",
    sort: 217,
  },
  {
    name: "排查记录",
    sort: 218,
  },
  {
    name: "隐患治理",
    sort: 219,
  },
  {
    name: "额外奖惩管理",
    sort: 220,
  },
  {
    name: "机制运行效果(旧)",
    sort: 221,
  },
  {
    name: "机制运行效果(新)",
    sort: 222,
  },
];

export const PcRole = () => {
  const queryClient = useQueryClient();
  const { MainMenu } = useMenuHooks();

  const mutationMenu = useMutation({
    mutationFn: editMenu,
    onSuccess: async (res) => {
      // queryClient.invalidateQueries(['getPermission', 1])
      console.log(res, "res-----");
    },
  });

  const mutationPermission = useMutation({
    mutationFn: editPermission,
    onSuccess: async (res) => {
      console.log(res, "res-----");
    },
  });

  const { data } = useQuery({
    queryKey: ["getPermission", 1],
    queryFn: () => {
      return getPermission(1);
    },
  });

  // 拼凑数据生成菜单配置
  const createSystemMenu = useMemo(() => {
    const tmpl = [];
    (MainMenu[0].items ?? []).forEach((item) => {
      item.module.forEach((o) => {
        if (o?.level === 1) {
          const s = find(propEq(o?.text, "name"))(menuSort);
          tmpl.push({
            sort: s?.sort ?? 0,
            code: o?.itemKey,
            name: o?.text,
            description: "",
            requestType: 1,
            clientPath: o?.itemKey,
            sysModel: parseInt(item.bindApiKey),
            serverPath: null,
            permissions: o?.permissions ?? [],
          });
        } else {
          (o?.items ?? []).forEach((i) => {
            if (i?.level === 1) {
              const s = find(propEq(i?.text, "name"))(menuSort);
              tmpl.push({
                sort: s?.sort ?? 0,
                code: i.itemKey,
                name: i?.text,
                description: "",
                requestType: 1,
                clientPath: i?.itemKey,
                sysModel: parseInt(item.bindApiKey),
                serverPath: null,
                permissions: i?.permissions ?? [""],
              });
            }
          });
        }
      });
    });

    return tmpl;
  }, [MainMenu]);

  // 检查菜单下权限
  const checkPermissions = () => {
    const serverMenus = data?.data ?? [];

    serverMenus.forEach((menu) => {
      if (menu?.id) {
        const clientMenuItem = find(propEq(menu.code, "code"))(
          createSystemMenu,
        );
        if (clientMenuItem?.permissions?.length) {
          (clientMenuItem?.permissions ?? []).forEach((clientPermission) => {
            // 获取服务端权限
            const item = find(propEq(clientPermission.code, "code"))(
              menu?.permissions ?? [],
            );
            console.debug(
              menu?.permissions,
              "menu?.permissions",
              clientPermission.code,
            );

            const serverItem = omit(["menuId", "serverPath", "id"], item);
            const clientItem = omit(["menuId"], clientPermission);

            if (!equals(serverItem, clientItem)) {
              const newPermission = {
                ...clientItem,
                menuId: menu.id,
              } as EditPermissionParams;

              mutationPermission.mutate(newPermission, {
                onSuccess: (data, variables, context) => {
                  queryClient.invalidateQueries(["getPermission", 1]);
                  console.log("更新成功");
                },
              });
            }
          });
        }
      }
    });
  };

  const handleCheck = async () => {
    createSystemMenu.forEach((menu) => {
      const item = find(propEq(menu.code, "code"))(data?.data ?? []);

      /*
      理论上，应该用ClientPath做code，现在导致的问题是如果name修改，code跟着修改，导致无法找到对应的menu，其实ClientPath没有变

      处理方法：
      1. 尽量不要修改已有菜单的name -> code
      2. 临时方案：重新设置权限看是否正常；如果不正常，truncate sys_menu和sys_permission表，自检，重新设置权限(理论上，老权限还存在)
      3. 终极方案：在这里通过ClientPath找到之前的菜单，然后增加删除逻辑：删除再增加
      const item2 = find(propEq(menu.code, 'clientPath'))(data?.data ?? []) 
      */
      const serverItem = omit(
        ["id", "permissions", "serverPath", "description"],
        item,
      );
      const clientItem = omit(
        ["permissions", "serverPath", "description"],
        menu,
      );
      // clientPath / code / name / requestType / sort / sysModel

      // 服务端没有 || 服务端和客户端不一致
      if (!item?.code || !equals(serverItem, clientItem)) {
        mutationMenu.mutate(omit(["permissions"], menu), {
          onSuccess: (data, variables, context) => {
            queryClient.invalidateQueries(["getPermission", 1]);
            checkPermissions();
          },
        });
      } else {
        console.log("无需更新");
      }
    });
    checkPermissions();
  };

  return (
    <div className="bg-white shadow px-4 h-fit rounded py-4">
      <button
        className="btn"
        onClick={handleCheck}
        disabled={mutationPermission.isLoading || mutationMenu.isLoading}
      >
        {mutationPermission.isLoading || mutationMenu.isLoading ? (
          <Spin size="small" />
        ) : null}
        自检权限
      </button>
      <div className="flex py-4 justify-between">
        <Collapse accordion className="w-full">
          {MainMenu[0].items.map((item: any, index: number) => (
            <Collapse.Panel header={item.text} key={index} itemKey={`${index}`}>
              <div className="overflow-x-auto mb-4">
                <table className="table">
                  <thead>
                    <tr>
                      <th>ID</th>
                      <th>模块名称</th>
                      <th>菜单路径</th>
                      <th className="max-w-xs">按钮权限</th>
                      <th>编码</th>
                      <th>备注</th>
                    </tr>
                  </thead>
                  <tbody>
                    {(data?.data ?? [])
                      .filter((o) => o.sysModel == item.bindApiKey)
                      ?.map?.((sub, subIndex) => (
                        <tr
                          className={subIndex % 2 == 0 ? "bg-base-200" : ""}
                          key={sub.key}
                        >
                          <th>{sub?.id}</th>
                          <td>{sub.name}</td>
                          <td>{sub?.clientPath}</td>
                          <td className="flex gap-x-2 max-w-xs flex-wrap">
                            {sub?.permissions?.map?.((p, i) => (
                              <button className="text-blue-500 flex" key={i}>
                                {p?.name}
                              </button>
                            ))}
                          </td>
                          <td>{sub?.code}</td>
                          <td>{sub?.description}</td>
                        </tr>
                      ))}
                  </tbody>
                </table>
              </div>
            </Collapse.Panel>
          ))}
        </Collapse>
      </div>
    </div>
  );
};
