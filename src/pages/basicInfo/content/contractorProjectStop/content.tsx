import { useQueryClient } from "@tanstack/react-query";
import { basicInfoContractorProjectStopApis } from "api";
import { basicInfoContractorProjectStopAtoms } from "atoms";
import { ExportProvider, List } from "components";
import { FC } from "react";

type BasicInfoContractorProjectStopContentProps = {
  callback?: any;
  layout?: string;
  referAtom?: any;
  readonly?: boolean;
  filter?: any;
};
export const BasicInfoContractorProjectStopContent: FC<
  BasicInfoContractorProjectStopContentProps
> = ({
  callback,
  layout,
  referAtom,
  readonly = false,
  filter = {},
  ...restProps
}) => {
  if (layout === "modal") {
    readonly = true;
  }
  const queryClient = useQueryClient();
  const queryKey = "list" + basicInfoContractorProjectStopAtoms.entity;

  return (
    <ExportProvider>
      <List
        atoms={basicInfoContractorProjectStopAtoms}
        apis={basicInfoContractorProjectStopApis}
        // operations, dynamicOperationFuncs
        filter={filter}
        callback={callback}
        referAtom={referAtom}
        layout={layout}
        readonly={readonly}
        cud={0b101}
        {...restProps}
      />
    </ExportProvider>
  );
};
