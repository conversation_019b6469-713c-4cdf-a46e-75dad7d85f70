import { useQueryClient } from "@tanstack/react-query";
import { basicInfoContractorEntryApplyApis } from "api";
import {
  basicInfoContractorEntryApplyAtoms,
  basicInfoContractorEntryApplyAuditDelegateModalAtom,
  basicInfoContractorEntryApplyAuditModalAtom,
} from "atoms";
import { ExportProvider, List } from "components";
import { useAtom } from "jotai";
import { FC, useCallback } from "react";
import { getUserInfo } from "utils";

type BasicInfoContractorEntryApplyContentProps = {
  callback?: any;
  layout?: string;
  referAtom?: any;
  readonly?: boolean;
  filter?: any;
};
export const BasicInfoContractorEntryApplyContent: FC<
  BasicInfoContractorEntryApplyContentProps
> = ({
  callback,
  layout,
  referAtom,
  readonly = false,
  filter = {},
  ...restProps
}) => {
  if (layout === "modal") {
    readonly = true;
  }
  const { id: userId } = getUserInfo();
  const queryClient = useQueryClient();
  const queryKey = "list" + basicInfoContractorEntryApplyAtoms.entity;

  const [auditModalAtom, setAuditModalAtom] = useAtom(
    basicInfoContractorEntryApplyAuditModalAtom
  );
  const [auditDelegateModalAtom, setAuditDelegateModalAtom] = useAtom(
    basicInfoContractorEntryApplyAuditDelegateModalAtom
  );

  const handleOpenAuditModal = useCallback(
    (record: any) => {
      setAuditModalAtom({
        record,
        show: true,
      });
    },
    [setAuditModalAtom]
  );

  const toggleAudit = (record: any) => {
    return record?.status === 1 &&
      record.auditorList.some((item: any) => item.id === userId)
      ? {
          engName: "audit",
          chnName: "审核",
          func: handleOpenAuditModal,
        }
      : null;
  };

  const handleOpenAuditDelegateModal = useCallback(
    (record: any) => {
      setAuditDelegateModalAtom({
        record,
        show: true,
      });
    },
    [setAuditDelegateModalAtom]
  );

  const toggleAuditDelegate = (record: any) => {
    return record?.status === 1 &&
      record.auditorList.some((item: any) => item.id === userId)
      ? {
          engName: "auditDelegate",
          chnName: "审核代办",
          func: handleOpenAuditDelegateModal,
        }
      : null;
  };

  return (
    <ExportProvider>
      <List
        atoms={basicInfoContractorEntryApplyAtoms}
        apis={basicInfoContractorEntryApplyApis}
        // operations, dynamicOperationFuncs
        dynamicOperationFuncs={[toggleAudit, toggleAuditDelegate]}
        filter={filter}
        callback={callback}
        referAtom={referAtom}
        layout={layout}
        readonly={readonly}
        cud={0b101}
        {...restProps}
      />
    </ExportProvider>
  );
};
