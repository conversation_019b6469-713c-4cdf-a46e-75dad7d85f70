import { useQueryClient } from "@tanstack/react-query";
import { basicInfoContractorBlacklistAddApis } from "api";
import { basicInfoContractorBlacklistAddAtoms } from "atoms";
import { ExportProvider, List } from "components";
import { FC } from "react";

type BasicInfoContractorBlacklistAddContentProps = {
  callback?: any;
  layout?: string;
  referAtom?: any;
  readonly?: boolean;
  filter?: any;
};
export const BasicInfoContractorBlacklistAddContent: FC<
  BasicInfoContractorBlacklistAddContentProps
> = ({
  callback,
  layout,
  referAtom,
  readonly = false,
  filter = {},
  ...restProps
}) => {
  if (layout === "modal") {
    readonly = true;
  }
  const queryClient = useQueryClient();
  const queryKey = "list" + basicInfoContractorBlacklistAddAtoms.entity;

  return (
    <ExportProvider>
      <List
        atoms={basicInfoContractorBlacklistAddAtoms}
        apis={basicInfoContractorBlacklistAddApis}
        // operations, dynamicOperationFuncs
        filter={filter}
        callback={callback}
        referAtom={referAtom}
        layout={layout}
        readonly={readonly}
        cud={0b101}
        {...restProps}
      />
    </ExportProvider>
  );
};
