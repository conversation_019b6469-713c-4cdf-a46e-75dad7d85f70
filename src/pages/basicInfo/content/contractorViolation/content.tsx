import { useQueryClient } from "@tanstack/react-query";
import { basicInfoContractorViolationApis } from "api";
import { basicInfoContractorViolationAtoms } from "atoms";
import { ExportProvider, List } from "components";
import React, { FC } from "react";

type BasicInfoContractorViolationContentProps = {
  callback?: any;
  layout?: string;
  referAtom?: any;
  readonly?: boolean;
  filter?: any;
};
export const BasicInfoContractorViolationContent: FC<
  BasicInfoContractorViolationContentProps
> = ({
  callback,
  layout,
  referAtom,
  readonly = false,
  filter = {},
  ...restProps
}) => {
  if (layout === "modal") {
    readonly = true;
  }
  const queryClient = useQueryClient();
  const queryKey = "list" + basicInfoContractorViolationAtoms.entity;

  return (
    <ExportProvider>
      <List
        atoms={basicInfoContractorViolationAtoms}
        apis={basicInfoContractorViolationApis}
        // operations, dynamicOperationFuncs
        filter={filter}
        callback={callback}
        referAtom={referAtom}
        layout={layout}
        readonly={readonly}
        {...restProps}
      />
    </ExportProvider>
  );
};
