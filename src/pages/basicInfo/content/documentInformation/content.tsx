import { useQueryClient } from "@tanstack/react-query";
import { basicInfoDocumentInformationApis } from "api";
import { basicInfoDocumentInformationAtoms } from "atoms";
import { ExportProvider, List } from "components";
import { FC } from "react";

type BasicInfoDocumentInformationContentProps = {
  callback?: any;
  layout?: string;
  referAtom?: any;
  readonly?: boolean;
  filter?: any;
};
export const BasicInfoDocumentInformationContent: FC<
  BasicInfoDocumentInformationContentProps
> = ({
  callback,
  layout,
  referAtom,
  readonly = false,
  filter = {},
  ...restProps
}) => {
  if (layout === "modal") {
    readonly = true;
  }
  const queryClient = useQueryClient();
  const queryKey = "list" + basicInfoDocumentInformationAtoms.entity;

  return (
    <ExportProvider>
      <List
        atoms={basicInfoDocumentInformationAtoms}
        apis={basicInfoDocumentInformationApis}
        // operations, dynamicOperationFuncs
        filter={filter}
        callback={callback}
        referAtom={referAtom}
        layout={layout}
        readonly={readonly}
        // leftSelectAtom={basicInfoDocumentInformationWithCategorySelectAtom}
        // lefstSelectKey="category"
        {...restProps}
      />
    </ExportProvider>
  );
};
