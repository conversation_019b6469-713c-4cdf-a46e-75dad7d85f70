import { Tree } from "@douyinfe/semi-ui";
import { useQuery } from "@tanstack/react-query";
import { getBasicInfoDocumentCategoryList } from "api";
import {
  basicInfoDocumentCategoryDataAtom,
  basicInfoDocumentCategoryRootName,
  basicInfoDocumentInformationFilterAtom,
  basicInfoDocumentInformationFnAtom,
  basicInfoDocumentInformationWithCategorySelectAtom,
} from "atoms";
import { convert } from "components";
import { useAtom } from "jotai";
import { clone, find, propEq } from "ramda";
import { useCallback, useEffect, useMemo, useState } from "react";
import { listPageSizeWithoutPaging } from "utils";

export const BasicInfoDocumentInformationLeft = () => {
  // user-defined start
  const [documentCategoryData, setDocumentCategoryData] = useAtom(
    basicInfoDocumentCategoryDataAtom
  );
  const [value, setValue] = useAtom(
    basicInfoDocumentInformationWithCategorySelectAtom
  );
  const [filter, setFilter] = useAtom(basicInfoDocumentInformationFilterAtom);
  const [documentInformationFn, setDocumentInformationFn] = useAtom(
    basicInfoDocumentInformationFnAtom
  );
  // user-defined end

  const [expandedKeys, setExpandedKeys] = useState([]);

  // user-defined start
  const { isLoading, isError, error, data, refetch } = useQuery({
    queryKey: ["getDocumentCategoryList"],
    queryFn: getBasicInfoDocumentCategoryList({
      pageNumber: 1,
      pageSize: listPageSizeWithoutPaging,
    }),
  });
  // user-defined end

  useEffect(() => {
    setDocumentInformationFn({
      // user-defined
      refetch: refetch,
    });
  }, [refetch]);

  useEffect(() => {
    if (data?.data?.results?.length) {
      setDocumentCategoryData(data?.data.results); // user-defined
    }
  }, [data]);

  // 转换函数
  function convert_deprecated(data) {
    const result = [];
    const map = {};
    const hasIds = [];

    function traverse(items, parentKey) {
      (items ?? []).forEach((_item) => {
        let item = _item;
        if (typeof item === "number") {
          item = find(propEq(item, "id"))(data.data.results);
        }

        const key = parentKey ? `${parentKey}-${item.id}` : String(item.id);

        const treeItem = {
          label: item.name,
          value: item.id,
          key,
          children: [],
        };

        map[key] = treeItem;

        if (item.children) {
          traverse(item.children, key);
        }
        if (hasIds.includes(item.id)) {
          return;
        }
        if (parentKey) {
          map[parentKey].children.push(treeItem);
        } else {
          result.push(treeItem);
        }
        hasIds.push(item.id);
      });
    }

    traverse(data.data.results, null);
    return result;
  }

  const treeData = useMemo(() => {
    if (data?.data?.results.length) {
      return [
        {
          label: "全部",
          value: null,
          key: "all",
        },
        {
          label: basicInfoDocumentCategoryRootName, // user-defined
          value: 0,
          key: "0",
          children: convert(data),
        },
      ];
    }
    return [];
  }, [data]);

  useEffect(() => {
    const keys = [];
    function traverse(items) {
      (items ?? []).forEach((item) => {
        keys.push(item.key);
        if (item.children) {
          traverse(item.children);
        }
      });
    }
    traverse(treeData);
    setExpandedKeys(keys);
  }, [treeData]);

  const handleSelect = useCallback((selectedKey, s, selectedNode) => {
    console.debug("debug: ", selectedKey, selectedNode);
    console.debug("filter:", filter);
    /* if (filter.filter.categoryId === selectedNode.value) {
      setValue(undefined);
      setFilter((_) => {
        _ = clone(_);
        delete _.filter.categoryId;
        return _;
      });
      return;
    } */
    setValue(selectedNode.value);
    setFilter((_) => {
      _ = clone(_);
      delete _.filter.categoryId; // user-defined
      if (selectedNode.value !== null) {
        console.debug("setFilter selectedNode.value", selectedNode.value);
        _.filter.categoryId = selectedNode.value; // user-defined
      }
      console.debug("setFilter _", _);
      return _;
    });
  }, []);

  return (
    <div className="w-fit bg-white shadow px h-fit max-h-full rounded grow-0">
      <Tree
        treeData={treeData}
        filterTreeNode
        expandAll
        expandedKeys={expandedKeys}
        value={value}
        onSelect={handleSelect}
        onExpand={(expandedKeys) => {
          setExpandedKeys(expandedKeys);
        }}
        onSearch={(inputValue, filteredExpandedKeys) => {
          setExpandedKeys([...filteredExpandedKeys]);
        }}
      />
    </div>
  );
};
