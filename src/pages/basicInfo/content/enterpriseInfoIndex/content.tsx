import { IconChevronLeft, IconChevronRight } from "@douyinfe/semi-icons";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { getCompanyInformation, getNumStat, getOvertimeStat } from "api";
import { certificateFilterAtom, contractorCertificateAtoms, contractorEmployeeCertificateFilterAtom, enterpriseCertificateFilterAtom, equipmentFilterAtom } from "atoms";
import { TdMap } from "components";
import { base_url } from "config";
import { useAtom } from "jotai";
import { useMemo, useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import Slider from "react-slick";
import "slick-carousel/slick/slick-theme.css";
import "slick-carousel/slick/slick.css";
import { formatRFC3339 } from "utils";
import { BasicRoutes } from "utils/routerConstants";





export const EnterpriseInfoIndexContent = ({
  readonly = false,
  ...restProps
}) => {
  const queryClient = useQueryClient();
  const [img, setImg] = useState("");
  const navigate = useNavigate()
  const [certificateFilter, setCertificateFilter] = useAtom(certificateFilterAtom)
  const [enterpriseCertificate, setEnterpriseCertificate] = useAtom(enterpriseCertificateFilterAtom)
  const [contractorCertificate, setContractorCertificate] = useAtom(contractorCertificateAtoms.filter)
  const [
    contractorEmployeeCertificateFilter,
    setContractorEmployeeCertificateFilter
  ] = useAtom(contractorEmployeeCertificateFilterAtom)
  const [
    equipmentFilter,
    setEquipmentFilter
  ] = useAtom(equipmentFilterAtom)

  const { data: intro } = useQuery({
    queryKey: ["getCompanyInformation"],
    queryFn: getCompanyInformation,
  });
  const { data: overtimeStat } = useQuery({
    queryKey: ["getOvertimeStat"],
    queryFn: getOvertimeStat,
  });
  const { data: numStat } = useQuery({
    queryKey: ["getNumStat"],
    queryFn: getNumStat,
  });

  const introData = useMemo(() => {
    return intro?.data;
  }, [intro, setImg]);


  const numStatData = useMemo(() => {
    return numStat?.data ?? {};
  }, [numStat]);

  const overtimeStatData = useMemo(() => {
    return overtimeStat?.data ?? {};
  }, [overtimeStat]);

  const SampleNextArrow = (props) => {
    const { onClick } = props;
    return (
      <div
        className="absolute top-[50%] mt-[-10px] right-[-40px] cursor-pointer "
        onClick={onClick}
      >
        <IconChevronRight size="extra-large" className="text-[#666]" />

      </div>
    );
  }

  const SamplePrevArrow = (props) => {
    const { onClick } = props;
    return (
      <div
        className="absolute top-[50%] mt-[-10px] left-[-40px] cursor-pointer"
        onClick={onClick}
      >
        <IconChevronLeft size="extra-large" className="text-[#666]" />
      </div>
    );
  }


  const settings = {
    dots: false,
    infinite: !true,
    speed: 500,
    autoplay: true,
    slidesToShow: 1,
    slidesToScroll: 1,
    nextArrow: <SampleNextArrow />,
    prevArrow: <SamplePrevArrow />
  };

  const iconNumMap = useMemo(() => {
    return [
      {
        value: overtimeStatData?.employeeCertificateOvertimeNum ?? 0,
        name: "人员证书到期",
        color: "#D2FFFC",
        link: BasicRoutes.EMPLOYEE_CERTIFICATE, // "/employee_certificate",
      },
      {
        value: overtimeStatData?.companyCertificateOvertimeNum ?? 0,
        name: "企业证书到期",
        color: "#E0F4FE",
        link: BasicRoutes.CERTIFICATE // "/certificate",
      },
      {
        value: overtimeStatData?.equipmentOvertimeNum ?? 0,
        name: "设备设施到期",
        color: "#F4E7FF",
        link: BasicRoutes.EQUIPMENT, // "/equipment",
      },
      {
        value: overtimeStatData?.contractorCertificateOvertimeNum ?? 0,
        name: "承包商资质证到期",
        color: "#FEEFE7",
        link: BasicRoutes.CONTRACTOR_CERTIFICATE, // "/contractor_certificate",
      },
      {
        value: overtimeStatData?.contractorEmployeeCertificateOvertimeNum ?? 0,
        name: "承包商人员证书到期",
        color: "#DAF7DE",
        link: BasicRoutes.CONTRACTOR_EMPLOYEE_CERTIFICATE, // "/contractor_employee_certificate",
      },
    ];
  }, [
    overtimeStatData,
  ]);

  const handleLink = (o: any) => {
    if (o.link === BasicRoutes.EMPLOYEE_CERTIFICATE) {
      setCertificateFilter({
        ...(certificateFilter),
        filter: {
          expireDateLte: formatRFC3339(new Date()),
        }
      })
    }
    if (o.link === BasicRoutes.CERTIFICATE) {
      setEnterpriseCertificate({
        ...(enterpriseCertificate),
        filter: {
          expireDateLte: formatRFC3339(new Date()),
        }
      })
    }
    if (o.link === BasicRoutes.EQUIPMENT) {
      setEquipmentFilter({
        ...(equipmentFilter),
        filter: {
          expireDateLte: formatRFC3339(new Date()),
        }
      })
    }
    if (o.link === BasicRoutes.CONTRACTOR_CERTIFICATE) {
      setContractorCertificate({
        ...(contractorCertificate),
        filter: {
          expireDateLte: formatRFC3339(new Date()),
        }
      })
    }

    if (o.link === BasicRoutes.CONTRACTOR_EMPLOYEE_CERTIFICATE) {
      setContractorEmployeeCertificateFilter({
        ...(contractorEmployeeCertificateFilter),
        filter: {
          expireDateLte: formatRFC3339(new Date()),
        }
      })
    }


    return navigate(o.link)
  }

  return (
    <div className="flex flex-col w-full gap-y-5 ">
      <div className="grid grid-cols-4 gap-5">
        <Link to={BasicRoutes.EMPLOYEE} className="flex justify-between overflow-hidden rounded-lg bg-gradient-to-r from-[#44DFDF] to-[#1EA0EA] p-6 h-40 relative group">
          <div className="flex flex-col text-white justify-evenly">
            <p className="text-lg">企业人数</p>
            <p className="font-black text-[34px] group-hover:underline">
              {numStatData?.employeeTotalNum}人
            </p>
            <div className="flex justify-between rounded-full bg-white/70 text-[#666] text-sm px-4">
              <span>{numStatData?.employeeGrowth}%</span>
              <span>环比上周</span>
            </div>
          </div>
          <div className="flex items-center justify-center absolute right-0 top-[4px]">
            <img src="/images/enterprise/card1.png" alt="" />
          </div>
        </Link>

        <Link to={BasicRoutes.CONTRACTOR} className="flex justify-between overflow-hidden rounded-lg bg-gradient-to-r from-[#25AFFE] to-[#AF62C7] p-6 h-40 relative group">

          <div className="flex flex-col text-white justify-evenly">
            <p className="text-lg">承包商数</p>
            <p className="font-black text-[34px] group-hover:underline">
              {numStatData?.contractorTotalNum ?? 0} 家
            </p>
            <div className="flex justify-between rounded-full bg-white/70 text-[#666] text-sm px-4">
              <span>{numStatData?.contractorGrowth}%</span>
              <span>环比上周</span>
            </div>
          </div>
          <div className="flex items-center justify-center absolute right-0 top-[4px]">
            <img src="/images/enterprise/card2.png" alt="" />
          </div>
        </Link>


        <Link to={BasicRoutes.CONTRACTOR_EMPLOYEE} className="flex justify-between overflow-hidden rounded-lg bg-gradient-to-r from-[#F4C258] to-[#F99336] p-6 h-40 relative group">

          <div className="flex flex-col text-white justify-evenly">
            <p className="text-lg">承包商人员</p>
            <p className="font-black text-[34px] group-hover:underline">
              {numStatData?.contractorEmployeeTotalNum ?? 0} 人
            </p>
            <div className="flex justify-between rounded-full bg-white/70 text-[#666] text-sm px-4">
              <span>{numStatData?.contractorEmployeeGrowth}%</span>
              <span>环比上周</span>
            </div>
          </div>
          <div className="flex items-center justify-center absolute right-0 top-[4px]">
            <img src="/images/enterprise/card3.png" alt="" />
          </div>
        </Link>

        <Link to={BasicRoutes.EQUIPMENT} className="flex justify-between overflow-hidden rounded-lg bg-gradient-to-r from-[#94DD64] to-[#0BCB96] p-6 h-40 relative group ">

          <div className="flex flex-col text-white justify-evenly">
            <p className="text-lg">设备设施</p>
            <p className="font-black text-[34px] group-hover:underline">
              {numStatData?.equipmentTotalNum} 台
            </p>
            <div className="flex justify-between rounded-full bg-white/70 text-[#666] text-sm px-4">
              <span>{numStatData.equipmentGrowth}%</span>
              <span>环比上周</span>
            </div>
          </div>
          <div className="flex items-center justify-center absolute right-0 top-[4px]">
            <img src="/images/enterprise/card4.png" alt="" />
          </div>
        </Link>

      </div>

      <div className="flex justify-between pr-6 bg-white border border-[#EDEDEE] rounded-lg py-[30px]  divide-x">
        {iconNumMap.map((o, i) => (
          <div
            className={[
              'text-sm text-[#60B7FF] cursor-pointer flex items-center',
              o.link ? 'group' : ''
            ].join(' ')}
            onClick={() => {
              handleLink(o)
            }}
          >
            <div className="flex gap-x-6 items-center pl-6 pr-1" key={i}>
              <div
                className={`w-[60px] h-[60px] rounded-lg flex items-center justify-center`}
                style={{
                  background: o.color,
                }}
              >
                <img
                  src={`/images/enterprise/icon${i + 1}.png`}
                  alt=""
                  className="w-10"
                />
              </div>
              <div className="flex flex-col gap-2">
                <p
                  className={[
                    'text-[28px] text-[#333] font-black',
                    o.link ? 'group-hover:underline hover:text-[#60B7FF]  hover:!decoration-[#60B7FF] group-hover:decoration-[#333]' : ''
                  ].join(' ')}
                >
                  {o.value}
                </p>
                <p className="text-base text-[#666]">{o.name}</p>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="grid grid-cols-2 gap-x-5">
        <div className="flex flex-col bg-white border border-[#EDEDEE] rounded-lg p-6 gap-y-5 relative">
          <div className="flex items-center gap-x-[10px] border-b pb-5">
            <div className="bg-[#60B7FF] w-1 h-[22px]"></div>
            <span className="text-[18px] font-bold">企业信息</span>
          </div>


          <div className="w-full px-[60px] relative ">
            <Slider {...settings}>
              {introData?.imageList?.map((o, i) => (
                <div key={i} className="w-full h-[300px] ">
                  <div className="w-full h-full flex items-center justify-center">
                    <img src={`${base_url}${o}`} className="w-full " alt="" />
                  </div>
                </div>
              ))}
            </Slider>

          </div>

          <div className="flex flex-col gap-[10px]">
            <div className="flex gap-[10px] items-center">
              <div className="bg-[#60B7FF] w-[10px] h-[10px] rounded-full"></div>
              <span className="text-sm font-bold">企业简介</span>
            </div>
            <div className="text-sm leading-[24px] font-normal text-[#333]">
              {introData?.abstraction ?? ""}
            </div>
          </div>
        </div>

        <div className="flex flex-col bg-white border border-[#EDEDEE] rounded-lg p-6 divide-y gap-y-5">
          <div className="flex items-center gap-x-[10px] border-b pb-5">
            <div className="bg-[#60B7FF] w-1 h-[22px]"></div>
            <span className="text-[18px] font-bold">企业卫星图</span>
          </div>

          <div className="flex flex-col gap-[10px] h-full min-h-[498px]">
            <TdMap
              position={{
                lat: introData?.latitude,
                lng: introData?.longitude,
              }}
              marker={{
                lat: introData?.latitude,
                lng: introData?.longitude,
              }}
              tools={["marker"]}
              readonly
              mode="pc"
            />
          </div>
        </div>
      </div>
    </div>
  );
};
