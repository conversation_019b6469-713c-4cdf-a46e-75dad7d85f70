import { useQueryClient } from "@tanstack/react-query";
import { basicInfoContractorEvaluationApis } from "api";
import { basicInfoContractorEvaluationAtoms } from "atoms";
import { ExportProvider, List } from "components";
import React, { FC } from "react";

type BasicInfoContractorEvaluationContentProps = {
  callback?: any;
  layout?: string;
  referAtom?: any;
  readonly?: boolean;
  filter?: any;
};
export const BasicInfoContractorEvaluationContent: FC<
  BasicInfoContractorEvaluationContentProps
> = ({
  callback,
  layout,
  referAtom,
  readonly = false,
  filter = {},
  ...restProps
}) => {
  if (layout === "modal") {
    readonly = true;
  }
  const queryClient = useQueryClient();
  const queryKey = "list" + basicInfoContractorEvaluationAtoms.entity;

  return (
    <ExportProvider>
      <List
        atoms={basicInfoContractorEvaluationAtoms}
        apis={basicInfoContractorEvaluationApis}
        // operations, dynamicOperationFuncs
        filter={filter}
        callback={callback}
        referAtom={referAtom}
        layout={layout}
        readonly={readonly}
        {...restProps}
      />
    </ExportProvider>
  );
};
