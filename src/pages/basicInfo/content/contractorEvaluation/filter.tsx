import { Form } from "@douyinfe/semi-ui";
import { useQuery } from "@tanstack/react-query";
import { getBasicInfoContractorEvaluationList, getContractorList } from "api";
import { basicInfoContractorEvaluationAtoms } from "atoms/basicInfo/contractorEvaluation";
import {
  AUDIT_RESULT_MAP,
  CONTRACTOR_EVALUATION_PHASE_MAP,
  RestSelect,
} from "components";
import { useFilterSearch } from "hooks";
import { useResetAtom } from "jotai/utils";
import { useEffect, useMemo } from "react";
import { listPageSizeWithoutPaging } from "utils";

export const BasicInfoContractorEvaluationFilter = ({ filter }) => {
  const atoms = basicInfoContractorEvaluationAtoms;
  const resetfilterAtom = useResetAtom(atoms.filter);
  const [handleSearch, handleReset] = useFilterSearch(atoms.filter);

  const entity = atoms.entity;
  const operation = "List";

  // reset filter when unmount
  useEffect(() => {
    return () => {
      resetfilterAtom();
    };
  }, []);

  const { data: list } = useQuery({
    queryKey: [`get${entity}${operation}`], //user-defined code here
    queryFn: () => getBasicInfoContractorEvaluationList, //user-defined code here
  });
  const options = useMemo(() => {
    return list?.data?.results ?? [];
  }, [list]);

  const { data: contractorList } = useQuery({
    queryKey: ["getContractorList"],
    queryFn: () =>
      getContractorList({
        pageNumber: 1,
        pageSize: listPageSizeWithoutPaging,
      }),
  });
  const contractorListOptions = useMemo(() => {
    return contractorList?.data?.results ?? [];
  }, [contractorList]);

  return (
    <div className="flex flex-col bg-white shadow rounded relative ">
      <div className="text-gray-900 text-opacity-90 text-base font-semibold leading-snug px-4 py-3 border-b border-gray-900/10 ">
        筛选总览
      </div>

      <div className="p-4 pr-0">
        <Form
          initValues={filter}
          layout="horizontal"
          onSubmit={handleSearch}
          className="grid grid-cols-4 gap-y-4 "
        >
          {/* user-defined code here */}
          {/* initValue={filter?.type}
          disabled */}
          <RestSelect
            options={contractorListOptions}
            placeholder="请选择承包商"
            field="contractorId"
            noLabel
          />
          <Form.DatePicker
            field="evaluationTime"
            type="dateTimeRange"
            noLabel
            placeholder={["评估时间上限", "评估时间下限"]}
            className="w-full"
            position="bottomRight"
          />
          <Form.Select
            placeholder="请选择评估结果"
            field="evaluationResult"
            noLabel
            className="w-full"
          >
            {AUDIT_RESULT_MAP.map((item) => (
              <Form.Select.Option key={item.id} value={item.id}>
                {item.name}
              </Form.Select.Option>
            ))}
          </Form.Select>
          <Form.Select
            placeholder="请选择评估阶段"
            field="phase"
            noLabel
            className="w-full"
          >
            {CONTRACTOR_EVALUATION_PHASE_MAP.map((item) => (
              <Form.Select.Option key={item.id} value={item.id}>
                {item.name}
              </Form.Select.Option>
            ))}
          </Form.Select>

          <div className="flex gap-2">
            <button className="btn rounded btn-primary btn-sm">
              查&nbsp;&nbsp;询
            </button>
            <button
              className="btn btn-sm rounded"
              type="reset"
              onClick={handleReset}
            >
              重&nbsp;&nbsp;置
            </button>
          </div>
        </Form>
      </div>
    </div>
  );
};
