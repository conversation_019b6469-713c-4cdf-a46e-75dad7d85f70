import { Toast } from "@douyinfe/semi-ui";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { contractorEmployeeApis } from "api";
import {
  contractorEmployeeAtoms,
  contractorEmployeeBlackModalAtom,
} from "atoms";
import { BIM_CONTRACTOR_ISBLACK_MAP, List } from "components";
import { useAtom } from "jotai";
import { useCallback } from "react";

export const ContractorEmployeeContent = () => {
  const queryClient = useQueryClient();
  const queryKey = "list" + contractorEmployeeAtoms.entity;

  const [contractorEmployeeBlackModal, setContractorEmployeeBlackModal] =
    useAtom(contractorEmployeeBlackModalAtom);

  const importProps = {
    entity: "承包商人员",
    excelType: "contractor_employee_template",
    downUrl: encodeURI("/static/template/承包商人员信息导入模板.xlsx"),
    tip: "请先准备好对应的承包商信息，否则导入会失败",
  };

  const handleOpenBlackModal = useCallback(
    (record) => {
      console.log("handleOpenBlackModal", record);
      setContractorEmployeeBlackModal({
        employee: record,
        show: true,
      });
    },
    [setContractorEmployeeBlackModal]
  );

  const removeBlackMutation = useMutation({
    mutationFn: contractorEmployeeApis.removeBlackList,
    onSuccess: async (res) => {
      if (res?.code === 0) {
        let opts = {
          content: `操作成功!`,
          duration: 2,
        };
        queryClient.invalidateQueries({ queryKey: [queryKey] });
        Toast.success(opts);
        contractorEmployeeAtoms.Fn?.refetch?.();
      }
    },
  });
  const handleRemoveBlack = useCallback(
    (record) => {
      console.log("handleRemoveBlack", record);
      removeBlackMutation.mutate(record.id);
    },
    [removeBlackMutation]
  );

  const _dynamicOperationFuncs = [];
  const toggleBlacklist = (record) => {
    return record.isBlack === BIM_CONTRACTOR_ISBLACK_MAP[0].id
      ? {
          engName: "removeBlacklist",
          chnName: "移除黑名单",
          func: handleRemoveBlack,
        }
      : {
          engName: "addBlacklist",
          chnName: "加入黑名单",
          func: handleOpenBlackModal,
        };
  };

  return (
    <List
      atoms={contractorEmployeeAtoms}
      apis={contractorEmployeeApis}
      dynamicOperationFuncs={[toggleBlacklist]}
      importProps={importProps}
    />
  );
};
