import { useQueryClient } from "@tanstack/react-query";
import { contractorAccidentApis } from "api";
import { contractorAccidentAtoms } from "atoms";
import { List } from "components";

export const ContractorAccidentContent = () => {
  const queryClient = useQueryClient();
  const queryKey = "list" + contractorAccidentAtoms.entity;

  return <List atoms={contractorAccidentAtoms} apis={contractorAccidentApis} />;
};
