import { useQuery } from "@tanstack/react-query";
import { Echart } from "pages/bigScreen/component/Chart";
import { useMemo, useState } from "react";
import { Link } from "react-router-dom";

export const Right = ({
  queryKey,
  queryFn,
  title,
  link
}) => {
  const [type, setType] = useState<1 | 2 | 3 | 4>(1);
  const { data, isLoading } = useQuery({
    queryKey: [queryKey, type],
    queryFn: () => {
      return queryFn({
        type: type
      })
    },
  });

  const dataSource = useMemo(() => {
    return data?.data ?? [];
  }, [data]);

  const handleActiveTab = (val: number) => {
    if (!isLoading) {
      setType(val)
    }
  }

  const option = useMemo(() => {

    if (dataSource?.details?.length) {
      const list = [];
      const total = []
      dataSource.details.forEach((o) => {
        total.push(o?.count);
        list.push({
          value: o?.num ?? 0,
          name: `${o.name}  ${o?.num}`,

        })
      })

      return {
        title: {
          show: false,
          text: `报警总计 ${dataSource?.totalNum ?? 0} 个`,
          left: '30%',
          bottom: '0%'
        },

        tooltip: {
          trigger: 'item'
        },
        legend: {
          orient: 'vertical',
          left: '65%',
          top: 'center'
        },

        series: [
          {
            center: ['30%', '50%'],
            name: '',
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: false,
              formatter(params) {
                return `{name|${params.name}}\n{percent|${params.value + "个"}}`;
              },
              rich: {
                name: {
                  // color: "#ccc",
                  fontSize: 12,
                  padding: [0, 0],
                  align: "center",
                },
                percent: {
                  align: "center",
                  fontSize: 14,
                  padding: [0, 0],
                },
              },
              color: "#FFF",
              opacity: 1,
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 40,
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: list
          }
        ]
      }
    }
    return {}
  }, [dataSource])



  return (
    <div className="w-5/120 w-full flex-1 py-4 px-5">
      <div className="flex justify-between">
        <div className="flex gap-2 items-center">
          <div className="bg-[#60B7FF] rounded-full w-[10px] h-[10px]">
          </div>
          <p>{title}</p>
        </div>
        <div className="flex items-center justify-between gap-x-4">
          <div className="border border-[#60B7FF] text-[#60B7FF] text-sm cursor-pointer overflow-hidden rounded-md flex">
            <div
              className={`leading-none px-[14px] py-[8px] border-r border-[#60B7FF] ${type === 1 ? 'text-white bg-[#60B7FF]' : ''}`}
              onClick={() => { handleActiveTab(1) }}
            >
              本周
            </div>
            <div
              className={`leading-none px-[14px] py-[8px] border-r border-[#60B7FF] ${type === 2 ? 'text-white bg-[#60B7FF]' : ''}`}
              onClick={() => { handleActiveTab(2) }}
            >
              本月
            </div>
            <div
              className={`leading-none px-[14px] py-[8px] ${type === 3 ? 'text-white bg-[#60B7FF]' : ''}`}
              onClick={() => { handleActiveTab(3) }}
            >
              本年
            </div>
          </div>
          <Link to={link} className="text-sm flex text-[#60B7FF] ">查看更多&gt;&gt;</Link>
        </div>
      </div>
      <div className="w-full h-[300px] relative">
        <div className="w-full h-[300px] z-0">
          <Echart option={option} />
        </div>
        <div className="absolute w-[125px] flex flex-col justify-center items-center z-10 top-[120px] left-[20%]">
          <p className="text-base text-[#666]">报警总计</p>
          <p className="text-[30px] text-[#333] font-black">{dataSource?.totalNum}</p>
        </div>
      </div>
    </div>
  );
};
