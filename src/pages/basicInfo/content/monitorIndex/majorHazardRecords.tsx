import { useQuery } from "@tanstack/react-query";
import {
  getAIAlarmInfoStat,
  getAIAlarmStat,
  getPersonnelAlarmInfoStat,
  getPersonnelAlarmStat,
  getSensorAlarmInfoStat,
  getSensorAlarmStatRecords,
} from "api";
import * as echarts from "echarts";
import { useMemo, useState } from "react";
import { AIRecognitionRoutes, MajorHazardRoutes } from "utils/routerConstants";
import { Left } from "./recordLeft";
import { Right } from "./recordRight";

export const MajorHazardRecords = () => {
  const [type, setType] = useState<1 | 2 | 3 | 4>(1);

  const { data, isLoading } = useQuery({
    queryKey: ["getSensorAlarmStatRecords"],
    queryFn: () => {
      return getSensorAlarmStatRecords({
        type: 1,
      });
    },
  });

  const dataSource = useMemo(() => {
    return data?.data ?? [];
  }, [data]);

  const handleActiveTab = (val: number) => {
    if (!isLoading) {
      setType(val);
    }
  };

  const option = useMemo(() => {
    if (!dataSource?.length) {
      return {};
    }
    const xAxisData = dataSource.map((item) => {
      return `${item.month}-${item.day}`;
    });

    return {
      legend: {
        right: "4%",
        top: "0%",
      },
      grid: {
        left: "0%",
        right: "0%",
        bottom: "0%",
        top: "8%",
        containLabel: true,
      },
      tooltip: {
        trigger: "axis",
      },
      xAxis: {
        type: "category",
        boundaryGap: false,
        data: xAxisData,
      },
      yAxis: {
        type: "value",
      },
      series: [
        {
          data: dataSource.map((item) => item.num),
          type: "line",
          showSymbol: false,
          lineStyle: {
            color: "#60B7FF",
          },
          smooth: true,
          areaStyle: {
            opacity: 1,
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 1,
                color: "#fff",
              },
              {
                offset: 0,
                color: "#60B7FF",
              },
            ]),
          },
        },
      ],
    };
  }, [dataSource]);

  return (
    <div className="flex flex-col w-full gap-y-5">
      <div className="flex flex-col bg-white border border-[#EDEDEE] rounded-lg p-6 divide-y gap-y-5">
        <div className="flex justify-between">
          <div className="flex items-center gap-x-[10px]">
            <div className="bg-[#60B7FF] w-1 h-[22px]"></div>
            <span className="text-[18px] font-bold">报警趋势</span>
          </div>
        </div>

        <div className="flex flex-col py-6 w-full gap-y-6 ">
          <div
            className="flex flex-col bg-white rounded-lg  w-full"
            style={{
              boxShadow: "0 0 10px 2px rgba(0,0,0,0.05)",
            }}
          >
            <div className="flex w-full divide-x">
              <Left
                queryKey="getSensorAlarmStatRecords"
                queryFn={getSensorAlarmStatRecords}
                title="实时监测报警"
              />
              <Right
                queryKey="getSensorAlarmInfoStat"
                queryFn={getSensorAlarmInfoStat}
                title="实时监测报警统计"
                link={MajorHazardRoutes.SENSOR_ALARM}
              />
            </div>
          </div>
          <div
            className="flex flex-col bg-white rounded-lg  w-full"
            style={{
              boxShadow: "0 0 10px 2px rgba(0,0,0,0.05)",
            }}
          >
            <div className="flex w-full divide-x">
              <Left
                queryKey="getPersonnelAlarmStat"
                queryFn={getPersonnelAlarmStat}
                title="人员定位报警"
              />
              <Right
                queryKey="getPersonnelAlarmInfoStat"
                queryFn={getPersonnelAlarmInfoStat}
                title="人员定位报警统计"
                link={MajorHazardRoutes.PERSONNEL_ALARM}
              />
            </div>
          </div>

          <div
            className="flex flex-col bg-white rounded-lg  w-full"
            style={{
              boxShadow: "0 0 10px 2px rgba(0,0,0,0.05)",
            }}
          >
            <div className="flex w-full divide-x">
              <Left
                queryKey="getAIAlarmStat"
                queryFn={getAIAlarmStat}
                title="AI监测报警"
              />
              <Right
                queryKey="getAIAlarmInfoStat"
                queryFn={getAIAlarmInfoStat}
                title="AI监测报警统计"
                link={AIRecognitionRoutes.VIDEO_AI}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
