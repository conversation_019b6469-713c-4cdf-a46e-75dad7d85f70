import { Tag } from "@douyinfe/semi-ui";
import { useQuery } from "@tanstack/react-query";
import { getDashSensorAlarm } from "api";
import { SENSOR_ALERTPRIORITY_MAP } from "components";
import { find, propEq, range } from "ramda";
import { useMemo } from "react";

export const SensorAlarm = () => {
  const { data } = useQuery({
    queryKey: ["getDashSensorAlarm"],
    queryFn: getDashSensorAlarm,
  });

  const dataSource = useMemo(() => {
    return (data?.data?.length ?? 0) === 0 ? range(0, 10) : data?.data;
  }, [data]);

  const StatusBtn = (id: number) => {
    const item = find(propEq(id, "id"))(SENSOR_ALERTPRIORITY_MAP);
    return (
      <Tag color={item?.color ?? ""} size="large">
        {item?.name ?? "--"}
      </Tag>
    );
  };

  return (
    <div className="flex flex-col bg-white border border-[#EDEDEE] rounded-lg p-6 divide-y gap-y-5">
      <div className="flex items-center gap-x-[10px]">
        <div className="bg-[#60B7FF] w-1 h-[22px]"></div>
        <span className="text-[18px] font-bold">实时监测报警</span>
      </div>

      <div className="flex pt-6 h-full">
        <div className="flex flex-col w-full h-full overflow-y-auto">
          <div className="bg-[#F1F3F6] text-xs text-[#333] font-semibold h-10 items-center px-5 w-full flex">
            <div className="w-2/12">监测名称</div>
            <div className="w-3/12">持续时长</div>
            <div className="w-3/12">报警类型</div>
            <div className="w-2/12">当前值</div>
            <div className="w-2/12 flex items-center justify-center">
              报警优先级
            </div>
          </div>

          <div className="flex flex-col w-full h-full overflow-y-auto max-h-[400px]">
            {dataSource.map((o, index) => (
              <div
                className="bg-white text-sm text-[#666] h-10 items-center px-5 w-full flex"
                style={{
                  background: index % 2 !== 0 ? "#F4FAFF" : "",
                }}
                key={index}
              >
                <div className="w-2/12">{o?.name ?? "-"}</div>
                <div className="w-3/12">{o?.duration ?? "-"}</div>
                <div className="w-3/12">{o?.priorityName ?? "-"}</div>
                <div className="w-2/12 text-[#60B7FF]">
                  {o?.alarmValue ?? "-"}
                </div>
                <div className="w-2/12 flex items-center justify-center">
                  {o?.priority > 0 ? StatusBtn(o?.priority) : "-"}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};
