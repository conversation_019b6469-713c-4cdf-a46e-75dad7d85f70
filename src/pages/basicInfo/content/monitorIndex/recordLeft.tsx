import { useQuery } from "@tanstack/react-query";
import * as echarts from "echarts";
import { Echart } from "pages/bigScreen/component/Chart";
import { useMemo, useState } from "react";

export const Left = ({
  queryKey,
  queryFn,
  title

}) => {
  const [type, setType] = useState<1 | 2 | 3 | 4>(1);
  const { data, isLoading } = useQuery({
    queryKey: [queryKey, type],
    queryFn: () => {
      return queryFn({
        type: type
      })
    },
  });

  const dataSource = useMemo(() => {
    return data?.data ?? [];
  }, [data]);

  const handleActiveTab = (val: number) => {
    if (!isLoading) {
      setType(val)
    }
  }


  const option = useMemo(() => {
    if (!dataSource?.length) {
      return {}
    }
    const xAxisData = dataSource.map((item) => {
      return `${item.month}-${item.day}`;
    });

    return {
      legend: {
        right: '4%',
        top: '0%'
      },
      grid: {
        left: '0%',
        right: '0%',
        bottom: '0%',
        top: '8%',
        containLabel: true
      },
      tooltip: {
        trigger: 'axis'
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: xAxisData
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          data: dataSource.map((item) => item.num),
          type: 'line',
          showSymbol: false,
          lineStyle: {
            color: '#60B7FF'
          },
          smooth: true,
          areaStyle: {
            opacity: 1,
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 1,
                color: '#fff'
              },
              {
                offset: 0,
                color: '#60B7FF'
              }
            ])
          }
        }
      ]
    };
  }, [dataSource])



  return (
    <div className="w-7/12 py-4 px-5">
      <div className="flex justify-between">
        <div className="flex gap-2 items-center">
          <div className="bg-[#60B7FF] rounded-full w-[10px] h-[10px]">
          </div>
          <p>{title}</p>
        </div>
        <div className="flex flex-col  gap-2">
          <div className="w-full border border-[#60B7FF] text-[#60B7FF] text-sm cursor-pointer overflow-hidden rounded-md flex">
            <div
              className={`leading-none px-[14px] py-[8px] border-r border-[#60B7FF] ${type === 1 ? 'text-white bg-[#60B7FF]' : ''}`}
              onClick={() => { handleActiveTab(1) }}
            >
              近7天
            </div>
            <div
              className={`leading-none px-[14px] py-[8px] border-r border-[#60B7FF] ${type === 2 ? 'text-white bg-[#60B7FF]' : ''}`}
              onClick={() => { handleActiveTab(2) }}
            >
              近30天
            </div>
            <div
              className={`leading-none px-[14px] py-[8px]  ${type === 3 ? 'text-white bg-[#60B7FF]' : ''}`}
              onClick={() => { handleActiveTab(3) }}
            >
              近12月
            </div>

          </div>

        </div>
      </div>
      <div className="w-full h-[300px]">
        <Echart option={option} />
      </div>
    </div>
  );
};
