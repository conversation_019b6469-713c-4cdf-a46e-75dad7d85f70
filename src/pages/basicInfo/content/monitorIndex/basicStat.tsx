import { useQuery } from "@tanstack/react-query";
import { getDashBasicStat } from "api";
import { chemicalAtoms } from "atoms";
import { useAtom } from "jotai";
import { useMemo } from "react";
import { Link, useNavigate } from "react-router-dom";
import { BasicRoutes, MajorHazardRoutes } from "utils/routerConstants";


export const BasicStat = () => {
  const [filter, setFilter] = useAtom(chemicalAtoms.filter)
  const navigate = useNavigate();
  const { data } = useQuery({
    queryKey: ["getBasicStat"],
    queryFn: getDashBasicStat,
  });

  const dataSource = useMemo(() => {
    return data?.data ?? {};
  }, [data]);


  return (
    <div className="grid grid-cols-4 gap-5">
      <Link to={BasicRoutes.MAJOR_HAZARD} className="flex justify-between overflow-hidden rounded-lg bg-gradient-to-r from-[#44DFDF] to-[#1EA0EA] p-6 h-40 relative group">
        <div className="flex flex-col text-white justify-evenly">
          <p className="font-black text-[34px] group-hover:underline">
            {dataSource?.majorHazardNum}
          </p>
          <p className="text-lg">重大危险源</p>
        </div>
        <div className="flex items-center justify-center absolute top-[4px] right-0">
          <img src="/images/monitor/card1.png" alt="" />
        </div>
      </Link>

      <Link to={BasicRoutes.DANGEROUS_PROCESS} className="flex justify-between overflow-hidden rounded-lg bg-gradient-to-r from-[#25AFFE] to-[#AF62C7] p-6 h-40 relative group">
        <div className="flex flex-col text-white justify-evenly">
          <p className="font-black text-[34px] group-hover:underline">
            {dataSource?.keyRegulatoryProcessNum}
          </p>
          <p className="text-lg">重点监管工艺</p>
        </div>
        <div className="flex items-center justify-center absolute top-[4px] right-0">
          <img src="/images/monitor/card2.png" alt="" />
        </div>
      </Link>

      <div
        className="flex justify-between overflow-hidden rounded-lg bg-gradient-to-r from-[#F4C258] to-[#F99336] p-6 px-4 h-40 relative group cursor-pointer"
        onClick={() => {
          setFilter({
            ...filter,
            filter: {
              isKeyRegulatory: 1
            }
          })
          navigate(BasicRoutes.CHEMICAL)
        }}
      >
        <div className="flex flex-col text-white justify-evenly">
          <p className="font-black text-[34px] group-hover:underline">
            {dataSource?.keyRegulatoryChemicalNum}
          </p>
          <p className="text-lg">重点监管危化品</p>
        </div>
        <div className="flex items-center justify-center absolute top-[4px] right-0">
          <img src="/images/monitor/card3.png" alt="" />
        </div>
      </div>

      <Link to={MajorHazardRoutes.MONITOR} className="flex justify-between overflow-hidden rounded-lg bg-gradient-to-r from-[#94DD64] to-[#0BCB96] p-6 h-40 relative group">
        <div className="flex flex-col text-white justify-evenly">
          <p className="font-black text-[34px] group-hover:underline">
            {dataSource?.monitorNum}
          </p>
          <p className="text-lg">视频监控</p>
        </div>
        <div className="flex items-center justify-center absolute top-[4px] right-0">
          <img src="/images/monitor/card4.png" alt="" />
        </div>
      </Link>
    </div>
  );
};
