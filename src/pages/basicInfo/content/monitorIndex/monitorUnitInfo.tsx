import { useQuery } from "@tanstack/react-query";
import { getDashMonitorUnitInfo } from "api";
import { monitorUnitAtoms } from "atoms";
import { useAtom } from "jotai";
import { useMemo } from "react";
import { useNavigate } from "react-router-dom";
import { MajorHazardRoutes } from "utils/routerConstants";

export const MonitorUnitInfo = () => {
  const navigate = useNavigate();
  const [filters, setFilters] = useAtom(monitorUnitAtoms.filter);
  const { data } = useQuery({
    queryKey: ["getDashMonitorUnitInfo"],
    queryFn: getDashMonitorUnitInfo,
  });

  const dataSource = useMemo(() => {
    return data?.data ?? {};
  }, [data]);

  return (
    <div className="flex flex-col bg-white border border-[#EDEDEE] rounded-lg p-6 gap-y-5">
      <div className="flex items-center gap-x-[10px] border-b pb-5">
        <div className="bg-[#60B7FF] w-1 h-[22px]"></div>
        <span className="text-[18px] font-bold">监测点信息</span>
      </div>

      <div className="flex gap-x-6 items-center">
        <div className="bg-[#F3F5F8] rounded-lg flex w-full px-6 items-center justify-between h-[120px]">
          <div className="flex flex-col">
            <p className="text-2xl text-[#333] font-black">
              {dataSource?.totalNum ?? 0}
            </p>
            <p className="text-sm text-[#666]">监测点总数</p>
          </div>
          <img src="/images/monitor/icon1.png" alt="" />
        </div>
        <div className="h-5/6 border-r"></div>
        <div className="bg-[#F3F5F8] rounded-lg flex w-full px-6 items-center justify-between  h-[120px]">
          <div className="flex flex-col">
            <p className="text-2xl text-[#FF4B4D] font-black">
              {dataSource?.alarmNum ?? 0}
            </p>
            <p className="text-sm text-[#666]">报警中监测点数</p>
          </div>
          <img src="/images/monitor/icon2.png" alt="" />
        </div>
      </div>

      <div className="w-11/12 border-b mx-auto"></div>

      <div className="flex gap-x-[10px] items-center ">
        <div
          onClick={() => {
            setFilters({
              ...filters,
              filter: { type: 2 },
            });
            navigate(MajorHazardRoutes.MONITOR_UNIT);
          }}
          className="bg-[#F3F5F8] rounded-lg flex w-full pl-5 pr-[6px] items-center justify-between h-[120px] cursor-pointer group"
        >
          <div className="flex flex-col">
            <p className="text-2xl text-[#333] font-black group-hover:underline">
              <span className="text-[#FF4B4D]">
                {dataSource?.storageTankAlarmNum ?? 0}
              </span>
              /{dataSource?.storageTankTotalNum ?? 0}
            </p>
            <p className="text-sm text-[#666]">储罐监测点</p>
          </div>
          <img src="/images/monitor/icon3.png" className="w-[50px]" alt="" />
        </div>
        <div className="h-5/6 border-r"></div>

        <div
          className="bg-[#F3F5F8] rounded-lg flex w-full pl-5 pr-[6px] items-center justify-between h-[120px] cursor-pointer group"
          onClick={() => {
            setFilters({
              ...filters,
              filter: { type: 1 },
            });
            navigate(MajorHazardRoutes.MONITOR_UNIT);
          }}
        >
          <div className="flex flex-col">
            <p className="text-2xl text-[#333] font-black group-hover:underline">
              <span className="text-[#FF4B4D]">
                {dataSource?.productionUnitAlarmNum ?? 0}
              </span>
              /{dataSource?.productionUnitTotalNum ?? 0}
            </p>

            <p className="text-sm text-[#666]">装置监测点</p>
          </div>
          <img src="/images/monitor/icon4.png" className="w-[50px]" alt="" />
        </div>
        <div className="h-5/6 border-r"></div>
        <div
          className="bg-[#F3F5F8] rounded-lg flex w-full pl-5 pr-[6px] items-center justify-between h-[120px] cursor-pointer group"
          onClick={() => {
            setFilters({
              ...filters,
              filter: { type: 3 },
            });
            navigate(MajorHazardRoutes.MONITOR_UNIT);
          }}
        >
          <div className="flex flex-col">
            <p className="text-2xl text-[#333] font-black group-hover:underline">
              <span className="text-[#FF4B4D]">
                {dataSource?.warehouseAlarmNum ?? 0}
              </span>
              /{dataSource?.warehouseTotalNum ?? 0}
            </p>
            <p className="text-sm text-[#666]">仓库监测点</p>
          </div>
          <img src="/images/monitor/icon5.png" className="w-[50px]" alt="" />
        </div>
      </div>

      <div className="w-full grid grid-cols-3 gap-x-[44px] px-3">
        <div className="w-full border-b mx-auto"></div>
        <div className="w-full border-b mx-auto"></div>
        <div className="w-full border-b mx-auto"></div>
      </div>

      <div className="flex gap-x-[10px] items-center">
        <div
          className="bg-[#F3F5F8] rounded-lg flex w-full pl-5 pr-[6px] items-center justify-between h-[120px] cursor-pointer group"
          onClick={() => {
            setFilters({
              ...filters,
              filter: { type: 4 },
            });
            navigate(MajorHazardRoutes.MONITOR_UNIT);
          }}
        >
          <div className="flex flex-col">
            <p className="text-2xl text-[#333] font-black group-hover:underline">
              <span className="text-[#FF4B4D]">
                {dataSource?.dangerousProcessAlarmNum ?? 0}
              </span>
              /{dataSource?.dangerousProcessTotalNum ?? 0}
            </p>
            <p className="text-sm text-[#666]">工艺监测点</p>
          </div>
          <img src="/images/monitor/icon6.png" className="w-[50px]" alt="" />
        </div>
        <div className="h-5/6 border-r"></div>
        <div
          className="bg-[#F3F5F8] rounded-lg flex w-full pl-5 pr-[6px] items-center justify-between h-[120px] cursor-pointer group"
          onClick={() => {
            setFilters({
              ...filters,
              filter: { type: 5 },
            });
            navigate(MajorHazardRoutes.MONITOR_UNIT);
          }}
        >
          <div className="flex flex-col">
            <p className="text-2xl text-[#333] font-black group-hover:underline">
              <span className="text-[#FF4B4D]">
                {dataSource?.toxicFlammableGasAlarmNum ?? 0}
              </span>
              /{dataSource?.toxicFlammableGasTotalNum ?? 0}
            </p>
            <p className="text-sm text-[#666]">气体监测点</p>
          </div>
          <img src="/images/monitor/icon7.png" className="w-[50px]" alt="" />
        </div>
        <div className="h-5/6 border-r"></div>
        <div
          onClick={() => {
            setFilters({
              ...filters,
              filter: { type: 6 },
            });
            navigate(MajorHazardRoutes.MONITOR_UNIT);
          }}
          className="bg-[#F3F5F8] rounded-lg flex w-full pl-5 pr-[6px] items-center justify-between h-[120px] cursor-pointer group"
        >
          <div className="flex flex-col">
            <p className="text-2xl text-[#333] font-black group-hover:underline">
              <span className="text-[#FF4B4D]">
                {dataSource?.equipmentAlarmNum ?? 0}
              </span>
              /{dataSource?.equipmentTotalNum ?? 0}
            </p>
            <p className="text-sm text-[#666]">设备监测点</p>
          </div>
          <img src="/images/monitor/icon8.png" className="w-[50px]" alt="" />
        </div>
      </div>
    </div>
  );
};
