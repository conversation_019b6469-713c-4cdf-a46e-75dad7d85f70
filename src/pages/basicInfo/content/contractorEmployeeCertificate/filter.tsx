import { IconSearch } from "@douyinfe/semi-icons";
import { Form, useFormApi } from "@douyinfe/semi-ui";
import { useQuery } from "@tanstack/react-query";
import { useToggle } from "ahooks";
import { getContractorEmployeeList } from "api";
import { contractorEmployeeCertificateFilterAtom } from "atoms/basicInfo";
import { ContractorSearch, DicSearch, VALID_NOTVALID_MAP } from "components";
import { useFilterSearch } from "hooks";
import { useMemo } from "react";

const ComponentUsingFormApi = ({ handleReset }) => {
  const formApi = useFormApi();

  return (
    <div className="flex gap-2">
      <span
        className="btn rounded btn-primary btn-sm"
        onClick={() => {
          formApi.submitForm();
        }}
      >
        查&nbsp;&nbsp;询
      </span>
      <span
        className="btn btn-sm rounded"
        onClick={() => {
          formApi.reset();
          handleReset();
        }}
      >
        重&nbsp;&nbsp;置
      </span>
    </div>
  );
};

export const ContractorEmployeeCertificateFilter = ({
  filter,
  isValidDisabled = false,
}) => {
  const [state, { toggle }] = useToggle();
  const [handleSearch, handleReset] = useFilterSearch(
    contractorEmployeeCertificateFilterAtom
  );
  const { data: list } = useQuery({
    queryKey: ["getContractorEmployeeList"],
    queryFn: getContractorEmployeeList,
  });
  const options = useMemo(() => {
    return list?.data?.results ?? [];
  }, [list]);

  return (
    <div className="flex flex-col bg-white shadow rounded relative big_screen_table_filter_box">
      <div className="text-gray-900 text-opacity-90 text-base font-semibold leading-snug px-4 py-3 border-b border-gray-900/10 big_screen_table_filter_title">
        筛选总览
      </div>

      <div className="p-4 pr-0">
        <Form
          initValues={filter}
          layout="horizontal"
          onSubmit={handleSearch}
          className="grid grid-cols-4 gap-y-4 "
        >
          <Form.Input
            noLabel
            field="query"
            placeholder="请输入姓名/证书名称/证书编号"
            className="w-full"
            suffix={<IconSearch />}
            showClear
          />
          <ContractorSearch
            field="contractorId"
            placeholder="请选择承包商"
            noLabel
          />

          <DicSearch
            field="certificateTypeValueId"
            placeholder="请选择证书类型"
            name="certificateType"
          />

          {state ? (
            <DicSearch
              field="authorityTypeValueId"
              placeholder="请选择发证机关"
              name="authorityType"
            />
          ) : null}
          <Form.DatePicker
            field="expireDateGt"
            noLabel
            placeholder="过期日期下限"
            className="w-full"
            position="bottomRight"
          />
          <Form.DatePicker
            field="expireDateLte"
            noLabel
            placeholder="过期日期上限"
            className="w-full"
            position="bottomRight"
          />

          <Form.Select
            placeholder="是否有效"
            field="isValid"
            noLabel
            className="w-full"
            disabled={isValidDisabled}
          >
            {VALID_NOTVALID_MAP.map((item) => (
              <Form.Select.Option key={item.id} value={item.id}>
                {item.name}
              </Form.Select.Option>
            ))}
          </Form.Select>

          <ComponentUsingFormApi handleReset={handleReset} />
          <span
            className="w-10 h-4 bg-white rounded-2xl shadow border absolute bottom-[-6px] left-2/4 ml-[-20px] flex items-center justify-center"
            onClick={toggle}
          >
            {state ? (
              <i className="ri-arrow-up-s-line" />
            ) : (
              <i className="ri-arrow-down-s-line" />
            )}
          </span>
        </Form>
      </div>
    </div>
  );
};
