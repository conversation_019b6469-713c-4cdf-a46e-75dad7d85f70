import { Col, Form, Row, Toast } from "@douyinfe/semi-ui";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  createBasicInfoContractorConfig,
  getBasicInfoContractorConfigCurrent,
} from "api";
import { basicInfoContractorConfigAtoms } from "atoms";
import { EmployeeSearch } from "components";
import { omit } from "ramda";
import { FC, useEffect, useRef } from "react";
import { filterEditData } from "utils";

type BasicInfoContractorConfigContentProps = {
  callback?: any;
  layout?: string;
  referAtom?: any;
  readonly?: boolean;
  filter?: any;
};
export const BasicInfoContractorConfigContent: FC<
  BasicInfoContractorConfigContentProps
> = ({
  callback,
  layout,
  referAtom,
  readonly = false,
  filter = {},
  ...restProps
}) => {
  const gutter = 24;
  const rules = [{ required: true, message: "此为必填项" }];

  const queryClient = useQueryClient();
  const queryKey = "list" + basicInfoContractorConfigAtoms.entity;
  const formRef = useRef<any>(null);

  const { isLoading, data } = useQuery({
    queryKey: [queryKey],
    queryFn: () => getBasicInfoContractorConfigCurrent(),
  });

  useEffect(() => {
    console.debug("data", data);
    console.debug("formRef", formRef.current?.formApi);
    if (data && formRef.current?.formApi) {
      const items = omit([], data?.data);
      console.debug(items, "itemsitemsitems");

      formRef.current?.formApi.setValues(
        {
          ...filterEditData(items),
        },
        { isOverride: true }
      );
    } else {
      formRef.current?.frormApi?.reset?.();
    }
  }, [data, formRef.current?.formApi]);

  const updateMutation = useMutation({
    mutationFn: createBasicInfoContractorConfig,
    onSuccess: async (res) => {
      if (res?.code === 0) {
        let opts = {
          content: `操作成功!`,
          duration: 2,
        };
        Toast.success(opts);
        queryClient.invalidateQueries({ queryKey: [queryKey] });
        basicInfoContractorConfigAtoms.Fn?.refetch?.();
      }
    },
  });

  const handleSubmit = (values) => {
    console.log(values);
    const params = {
      blacklistAuditCandidateIdList: values.blacklistAuditCandidateIdList,
      employeeBlacklistAuditCandidateIdList:
        values.employeeBlacklistAuditCandidateIdList,
      resumptionAuditCandidateIdList: values.resumptionAuditCandidateIdList,
      entryAuditCandidateIdList: values.entryAuditCandidateIdList,
    };
    updateMutation.mutate(params);
    // updateMutation.mutate(formRef.current?.formApi.getValues());
  };

  return (
    <div className="bg-white shadow p-4 h-fit rounded">
      <Form
        labelPosition="left"
        labelAlign="right"
        labelWidth={160}
        ref={formRef}
        onSubmit={(values) => handleSubmit(values)}
        autoScrollToError
      >
        {/* add form items here */}
        {({ formState }) => (
          <>
            {/* <FormDebugComponentUsingFormState /> */}
            <Row gutter={gutter}>
              <Col span={24}>
                <Form.Label>承包商黑名单审核人列表</Form.Label>
                <EmployeeSearch
                  field="blacklistAuditCandidateIdList"
                  placeholder="请选择承包商黑名单审核人列表"
                  multiple
                />
              </Col>
            </Row>
            <Row gutter={gutter}>
              <Col span={24}>
                <Form.Label>承包商员工黑名单审核人列表</Form.Label>
                <EmployeeSearch
                  field="employeeBlacklistAuditCandidateIdList"
                  placeholder="请选择承包商员工黑名单审核人列表"
                  multiple
                />
              </Col>
            </Row>
            <Row gutter={gutter}>
              <Col span={24}>
                <Form.Label>项目复工审核人列表</Form.Label>
                <EmployeeSearch
                  field="resumptionAuditCandidateIdList"
                  placeholder="请选择项目复工审核人列表"
                  multiple
                />
              </Col>
            </Row>
            <Row gutter={gutter}>
              <Col span={24}>
                <Form.Label>入场申请审核人列表</Form.Label>
                <EmployeeSearch
                  field="entryAuditCandidateIdList"
                  placeholder="请选择入场申请审核人列表"
                  multiple
                />
              </Col>
            </Row>
            <div className="flex gap-2 justify-end">
              <button className="btn btn-primary btn-sm rounded w-[150px]">
                提交
              </button>
            </div>
          </>
        )}
      </Form>
    </div>
  );
};
