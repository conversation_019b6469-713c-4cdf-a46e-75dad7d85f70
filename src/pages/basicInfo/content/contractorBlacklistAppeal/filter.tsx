import { Form } from "@douyinfe/semi-ui";
import { useQuery } from "@tanstack/react-query";
import {
  getBasicInfoContractorBlacklistAppealList,
  getContractorList,
} from "api";
import { basicInfoContractorBlacklistAppealAtoms } from "atoms/basicInfo/contractorBlacklistAppeal";
import { AUDIT_STATUS_MAP, RestSelect } from "components";
import { useFilterSearch } from "hooks";
import { useResetAtom } from "jotai/utils";
import { useEffect, useMemo } from "react";
import { listPageSizeWithoutPaging } from "utils";

export const BasicInfoContractorBlacklistAppealFilter = ({ filter }) => {
  const atoms = basicInfoContractorBlacklistAppealAtoms;
  const resetfilterAtom = useResetAtom(atoms.filter);
  const [handleSearch, handleReset] = useFilterSearch(atoms.filter);

  const entity = atoms.entity;
  const operation = "List";

  // reset filter when unmount
  useEffect(() => {
    return () => {
      resetfilterAtom();
    };
  }, []);

  const { data: list } = useQuery({
    queryKey: [`get${entity}${operation}`], //user-defined code here
    queryFn: () =>
      getBasicInfoContractorBlacklistAppealList({
        page: 1,
        pageSize: listPageSizeWithoutPaging,
      }), //user-defined code here
  });
  const options = useMemo(() => {
    return list?.data?.results ?? [];
  }, [list]);

  const { data: contractorList } = useQuery({
    queryKey: ["getContractorList"],
    queryFn: () =>
      getContractorList({
        page: 1,
        pageSize: listPageSizeWithoutPaging,
      }),
  });

  const contractorOptions = useMemo(() => {
    return contractorList?.data?.results ?? [];
  }, [contractorList]);

  return (
    <div className="flex flex-col bg-white shadow rounded relative ">
      <div className="text-gray-900 text-opacity-90 text-base font-semibold leading-snug px-4 py-3 border-b border-gray-900/10 ">
        筛选总览
      </div>

      <div className="p-4 pr-0">
        <Form
          initValues={filter}
          layout="horizontal"
          onSubmit={handleSearch}
          className="grid grid-cols-4 gap-y-4 "
        >
          {/* user-defined code here */}
          {/* initValue={filter?.type}
          disabled */}

          <RestSelect
            options={contractorOptions}
            placeholder="请选择承包商"
            field="contractorId"
            noLabel
          />
          <Form.Select
            placeholder="请选择状态"
            field="status"
            noLabel
            className="w-full"
          >
            {AUDIT_STATUS_MAP.map((item) => (
              <Form.Select.Option key={item.id} value={item.id}>
                {item.name}
              </Form.Select.Option>
            ))}
          </Form.Select>

          <div className="flex gap-2">
            <button className="btn rounded btn-primary btn-sm">
              查&nbsp;&nbsp;询
            </button>
            <button
              className="btn btn-sm rounded"
              type="reset"
              onClick={handleReset}
            >
              重&nbsp;&nbsp;置
            </button>
          </div>
        </Form>
      </div>
    </div>
  );
};
