import { useQueryClient } from "@tanstack/react-query";
import { basicInfoContractorBlacklistAppealApis } from "api";
import {
  basicInfoContractorBlacklistAppealAtoms,
  basicInfoContractorBlacklistAppealAuditDelegateModalAtom,
  basicInfoContractorBlacklistAppealAuditModalAtom,
} from "atoms";
import { ExportProvider, List } from "components";
import { useAtom } from "jotai";
import { FC, useCallback } from "react";
import { getUserInfo } from "utils";

type BasicInfoContractorBlacklistAppealContentProps = {
  callback?: any;
  layout?: string;
  referAtom?: any;
  readonly?: boolean;
  filter?: any;
};
export const BasicInfoContractorBlacklistAppealContent: FC<
  BasicInfoContractorBlacklistAppealContentProps
> = ({
  callback,
  layout,
  referAtom,
  readonly = false,
  filter = {},
  ...restProps
}) => {
  if (layout === "modal") {
    readonly = true;
  }
  const { id: userId } = getUserInfo();
  const queryClient = useQueryClient();
  const queryKey = "list" + basicInfoContractorBlacklistAppealAtoms.entity;

  const [auditModalAtom, setAuditModalAtom] = useAtom(
    basicInfoContractorBlacklistAppealAuditModalAtom
  );

  const handleOpenAuditModal = useCallback(
    (record: any) => {
      setAuditModalAtom({
        record,
        show: true,
      });
    },
    [setAuditModalAtom]
  );

  const toggleAudit = (record: any) => {
    return record?.status === 1 &&
      record.auditorList.some((item: any) => item.id === userId)
      ? {
          engName: "audit",
          chnName: "审核",
          func: handleOpenAuditModal,
        }
      : null;
  };

  const [auditDelegateModalAtom, setAuditDelegateModalAtom] = useAtom(
    basicInfoContractorBlacklistAppealAuditDelegateModalAtom
  );

  const handleOpenAuditDelegateModal = useCallback(
    (record: any) => {
      setAuditDelegateModalAtom({
        record,
        show: true,
      });
    },
    [setAuditDelegateModalAtom]
  );

  const toggleAuditDelegate = (record: any) => {
    return record?.status === 1 &&
      record.auditorList.some((item: any) => item.id === userId)
      ? {
          engName: "auditDelegate",
          chnName: "审核代办",
          func: handleOpenAuditDelegateModal,
        }
      : null;
  };
  return (
    <ExportProvider>
      <List
        atoms={basicInfoContractorBlacklistAppealAtoms}
        apis={basicInfoContractorBlacklistAppealApis}
        // operations, dynamicOperationFuncs
        dynamicOperationFuncs={[toggleAudit, toggleAuditDelegate]}
        filter={filter}
        callback={callback}
        referAtom={referAtom}
        layout={layout}
        readonly={readonly}
        cud={0b101}
        {...restProps}
      />
    </ExportProvider>
  );
};
