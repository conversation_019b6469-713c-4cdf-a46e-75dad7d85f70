import { useQueryClient } from "@tanstack/react-query";
import { announcementApis } from "api";
import { announcementAtoms } from "atoms";
import { List } from "components";

export const AnnouncementContent = ({
  callback,
  layout,
  referAtom,
  readonly = false,
  filter = {},
  ...restProps
}) => {
  if (layout === "modal") {
    readonly = true;
  }
  const queryClient = useQueryClient();
  const queryKey = "list" + announcementAtoms.entity;

  return (
    <List
      atoms={announcementAtoms}
      apis={announcementApis}
      // operations, dynamicOperationFuncs
      filter={filter}
      callback={callback}
      referAtom={referAtom}
      layout={layout}
      readonly={readonly}
      {...restProps}
    />
  );
};
