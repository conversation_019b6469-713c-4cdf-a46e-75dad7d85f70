import { IconSearch } from "@douyinfe/semi-icons";
import { Form } from "@douyinfe/semi-ui";
import { useQuery } from "@tanstack/react-query";
import { getEnterpriseCertificateList } from "api";
import { enterpriseCertificateAtoms } from "atoms/basicInfo/enterpriseCertificate";
import { OVERTIME_STATUS_MAP } from "components";
import { useFilterSearch } from "hooks";
import { useMemo } from "react";

export const EnterpriseCertificateFilter = () => {
  const atoms = enterpriseCertificateAtoms;
  const [handleSearch, handleReset] = useFilterSearch(atoms.filter);

  const entity = atoms.entity;
  const operation = "List";

  const { data: list } = useQuery({
    queryKey: [`get${entity}${operation}`], //user-defined code here
    queryFn: getEnterpriseCertificateList, //user-defined code here
  });
  const options = useMemo(() => {
    return list?.data?.results ?? [];
  }, [list]);

  return (
    <div className="flex flex-col bg-white shadow rounded relative big_screen_table_filter_box">
      <div className="text-gray-900 text-opacity-90 text-base font-semibold big_screen_table_filter_title leading-snug px-4 py-3 border-b border-gray-900/10 ">
        筛选总览
      </div>

      <div className="p-4 pr-0">
        <Form
          layout="horizontal"
          onSubmit={handleSearch}
          className="grid grid-cols-4 gap-y-4 "
        >
          <Form.Input
            noLabel
            field="query"
            placeholder="请填入证书名称/编号/类型"
            className="w-full"
            suffix={<IconSearch />}
            showClear
          />
          <Form.Select
            field="isValid"
            noLabel
            placeholder="是否过期"
            className="w-full"
          >
            {OVERTIME_STATUS_MAP.map((o) => (
              <Form.Select.Option value={parseInt(o?.id) ?? 1} key={o?.id ?? 1}>
                {o.name}
              </Form.Select.Option>
            ))}
          </Form.Select>
          <Form.DatePicker
            field="expireDateGt"
            noLabel
            placeholder="过期日期下限"
            className="w-full"
            position="bottomRight"
          />
          <Form.DatePicker
            field="expireDateLte"
            noLabel
            placeholder="过期日期上限"
            className="w-full"
            position="bottomRight"
          />

          <div className="flex gap-2">
            <button className="btn rounded btn-primary btn-sm">
              查&nbsp;&nbsp;询
            </button>
            <button
              className="btn btn-sm rounded"
              type="reset"
              onClick={handleReset}
            >
              重&nbsp;&nbsp;置
            </button>
          </div>
        </Form>
      </div>
    </div>
  );
};
