import { useQueryClient } from "@tanstack/react-query";
import { enterpriseCertificateApis } from "api";
import { enterpriseCertificateAtoms } from "atoms";
import { ExportProvider, List } from "components";

export const EnterpriseCertificateContent = () => {
  const queryClient = useQueryClient();
  const queryKey = "list" + enterpriseCertificateAtoms.entity;

  return (
    <ExportProvider>
      <List
        atoms={enterpriseCertificateAtoms}
        apis={enterpriseCertificateApis}
      />
    </ExportProvider>
  );
};
