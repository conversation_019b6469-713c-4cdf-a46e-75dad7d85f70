import { useQueryClient } from "@tanstack/react-query";
import { basicInfoDocumentCategoryApis } from "api";
import { basicInfoDocumentCategoryAtoms } from "atoms";
import { List } from "components";
import React, { FC } from "react";

type BasicInfoDocumentCategoryContentProps = {
  callback?: any;
  layout?: string;
  referAtom?: any;
  readonly?: boolean;
  filter?: any;
};
export const BasicInfoDocumentCategoryContent: FC<
  BasicInfoDocumentCategoryContentProps
> = ({
  callback,
  layout,
  referAtom,
  readonly = false,
  filter = {},
  ...restProps
}) => {
  if (layout === "modal") {
    readonly = true;
  }
  const queryClient = useQueryClient();
  const queryKey = "list" + basicInfoDocumentCategoryAtoms.entity;

  return (
    <List
      atoms={basicInfoDocumentCategoryAtoms}
      apis={basicInfoDocumentCategoryApis}
      // operations, dynamicOperationFuncs
      filter={filter}
      callback={callback}
      referAtom={referAtom}
      layout={layout}
      readonly={readonly}
      {...restProps}
    />
  );
};
