import { useQueryClient } from "@tanstack/react-query";
import { contractorCertificateApis } from "api";
import { contractorCertificateAtoms } from "atoms";
import { List } from "components";

export const ContractorCertificateContent = () => {
  const queryClient = useQueryClient();
  const queryKey = "list" + contractorCertificateAtoms.entity;

  return (
    <List atoms={contractorCertificateAtoms} apis={contractorCertificateApis} />
  );
};
