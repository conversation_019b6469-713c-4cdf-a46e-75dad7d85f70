import { IconSearch } from "@douyinfe/semi-icons";
import { Form } from "@douyinfe/semi-ui";
import { contractorCertificateAtoms } from "atoms/basicInfo";
import { ContractorSearch } from "components";
import { useFilterSearch } from "hooks";

export const ContractorCertificateFilter = () => {
  const atoms = contractorCertificateAtoms;
  const [handleSearch, handleReset] = useFilterSearch(atoms.filter);

  const entity = atoms.entity;
  const operation = "List";

  return (
    <div className="flex flex-col bg-white shadow rounded relative big_screen_table_filter_box">
      <div className="text-gray-900 text-opacity-90 text-base font-semibold leading-snug px-4 py-3 border-b border-gray-900/10 big_screen_table_filter_title">
        筛选总览
      </div>

      <div className="p-4 pr-0">
        <Form
          layout="horizontal"
          onSubmit={handleSearch}
          className="grid grid-cols-4 gap-y-4 "
        >
          <Form.Input
            noLabel
            field="query"
            placeholder="请填入资质证书编号/名称"
            className="w-full"
            suffix={<IconSearch />}
            showClear
          />
          <ContractorSearch
            field="contractorId"
            placeholder="请选择承包商"
            noLabel
          />
          <Form.DatePicker
            field="expireDateGt"
            noLabel
            placeholder="过期日期下限"
            className="w-full"
            position="bottomRight"
          />
          <Form.DatePicker
            field="expireDateLte"
            noLabel
            placeholder="过期日期上限"
            className="w-full"
            position="bottomRight"
          />

          <div className="flex gap-2">
            <button className="btn rounded btn-primary btn-sm">
              查&nbsp;&nbsp;询
            </button>
            <button
              className="btn btn-sm rounded"
              type="reset"
              onClick={handleReset}
            >
              重&nbsp;&nbsp;置
            </button>
          </div>
        </Form>
      </div>
    </div>
  );
};
