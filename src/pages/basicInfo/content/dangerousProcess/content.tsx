import { dangerousProcessApis } from "api";
import { dangerousProcessAtoms } from "atoms";
import { List } from "components";

export const DangerousProcessContent = () => {
  const importProps = {
    entity: "危险化工工艺",
    excelType: "dangerous_process_template",
    downUrl: encodeURI("/static/template/危险化工工艺信息导入模板.xlsx"),
    tip: "",
  };

  return (
    <List
      atoms={dangerousProcessAtoms}
      apis={dangerousProcessApis}
      importProps={importProps}
    />
  );
};
