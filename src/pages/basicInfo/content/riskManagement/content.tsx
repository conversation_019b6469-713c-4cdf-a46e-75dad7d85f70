import { useQueryClient } from "@tanstack/react-query";
import { riskManagementApis } from "api";
import { riskManagementApproveModalAtom, riskManagementAtoms } from "atoms";
import { List, RIKSMANAGEMENT_STATUS_MAP } from "components";
import { useAtom } from "jotai";
import { useCallback } from "react";
import { getUserInfo } from "utils";

export const RiskManagementContent = ({
  callback,
  layout,
  referAtom,
  readonly = false,
  ...restProps
}) => {
  /* const { getItem, removeItem } = useLocalStorage(userInfoName)
  const id = getItem()?.userInfo.id
  const employeeId = getItem()?.userInfo.employeeId
  const userName = getItem()?.username */

  if (layout === "modal") {
    readonly = true;
  }

  const { id } = getUserInfo();

  const queryClient = useQueryClient();
  const queryKey = "list" + riskManagementAtoms.entity;

  const [approveModalAtom, setApproveModalAtom] = useAtom(
    riskManagementApproveModalAtom
  );

  const handleOpenApprove = useCallback(
    (record) => {
      console.log("handleOpenApprove", record);

      setApproveModalAtom({
        id: record.id ?? "",
        show: true,
      });
    },
    [setApproveModalAtom]
  );

  const toggleApprove = (record) => {
    const approvalCandidates = record.approvalCandidates.filter(
      (item) => item.id === id
    );
    if (approvalCandidates.length === 0) {
      return null;
    }
    return record.status === RIKSMANAGEMENT_STATUS_MAP[0].id
      ? {
          engName: "approve",
          chnName: "审批",
          func: handleOpenApprove,
        }
      : null;
  };

  return (
    <List
      atoms={riskManagementAtoms}
      apis={riskManagementApis}
      dynamicOperationFuncs={[toggleApprove]}
      callback={callback}
      referAtom={referAtom}
      layout={layout}
      readonly={readonly}
      {...restProps}
    />
  );
};
