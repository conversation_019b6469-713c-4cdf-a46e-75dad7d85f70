import { Toast } from "@douyinfe/semi-ui";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { contractorApis } from "api";
import {
  contractorAtoms,
  contractorBlackModalAtom,
  contractorDetailSideAtom,
  contractorShareModalAtom,
} from "atoms/basicInfo/contractor";
import { IS_ISNOT_MAP, List } from "components";
import { useAtom } from "jotai";
import { useCallback } from "react";

export const ContractorContent = () => {
  const queryClient = useQueryClient();
  const queryKey = "list" + contractorAtoms.entity;

  const [contractorBlackModal, setContractorBlackModal] = useAtom(
    contractorBlackModalAtom
  );
  const [contractorShareModal, setContractorShareModal] = useAtom(
    contractorShareModalAtom
  );
  const [contractorDetailSide, setContractorDetailSide] = useAtom(
    contractorDetailSideAtom
  );

  const importProps = {
    entity: "承包商",
    excelType: "contractor_template",
    downUrl: encodeURI("/static/template/承包商信息导入模板.xlsx"),
    tip: "请先准备好对应的承包商类型信息，否则导入会失败",
  };

  //handler sample for testing
  const handleVoid = useCallback((record) => {
    console.log("handleVoid", record);
  }, []);

  const handleOpenBlack = useCallback(
    (record) => {
      console.log("handleOpenBlack", record);
      setContractorBlackModal({
        contractor: record,
        show: true,
      });
    },
    [setContractorBlackModal]
  );

  const removeBlackMutation = useMutation({
    mutationFn: contractorApis.removeBlackList,
    onSuccess: async (res) => {
      if (res?.code === 0) {
        let opts = {
          content: `操作成功!`,
          duration: 2,
        };
        Toast.success(opts);
        queryClient.invalidateQueries({ queryKey: [queryKey] });
        contractorAtoms.Fn?.refetch?.();
      }
    },
  });

  const handleRemoveBlack = useCallback(
    (record) => {
      console.log("handleRemoveBlack", record);
      removeBlackMutation.mutate(record.id);
    },
    [removeBlackMutation]
  );

  const handleOpenShare = useCallback(
    (record) => {
      console.log("handleOpenShare", record);
      setContractorShareModal({
        contractor: record,
        show: true,
      });
    },
    [setContractorShareModal]
  );

  const handleOpenDetail = useCallback(
    (record) => {
      console.log("handleOpenDetail", record);
      setContractorDetailSide({
        id: record.id,
        show: true,
      });
    },
    [setContractorDetailSide]
  );

  const _operations = [];
  const _dynamicOperationFuncs = [];

  const detail = {
    engName: "detail",
    chnName: "查看详情",
    func: handleOpenDetail,
  };

  const share = {
    engName: "share",
    chnName: "邀请完善信息",
    func: handleOpenShare,
  };
  _operations.push(detail);
  _operations.push(share);

  /* const toggleBlacklist = {
    engName: ['toggleBlacklist'],
    chnName: '加入/移除黑名单',
    func: handleBlack,
  } */
  const toggleBlacklist = (record) => {
    return record.isBlack === IS_ISNOT_MAP[0].id
      ? {
          engName: "removeBlacklist",
          chnName: "移除黑名单",
          func: handleRemoveBlack,
        }
      : {
          engName: "addBlacklist",
          chnName: "加入黑名单",
          func: handleOpenBlack,
        };
  };

  return (
    <List
      atoms={contractorAtoms}
      apis={contractorApis}
      _operations={_operations}
      dynamicOperationFuncs={[toggleBlacklist]}
      importProps={importProps}
    />
  );
};
