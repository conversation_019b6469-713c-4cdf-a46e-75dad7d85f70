import { IconSearch } from "@douyinfe/semi-icons";
import { Form } from "@douyinfe/semi-ui";
import { contractorAtoms } from "atoms/basicInfo";
import {
  BIM_CONTRACTOR_ISBLACK_MAP,
  BIM_CONTRACTOR_TYPE_MAP,
} from "components";
import { useFilterSearch } from "hooks";

export const ContractorFilter = () => {
  const atoms = contractorAtoms;
  const [handleSearch, handleReset] = useFilterSearch(atoms.filter);

  const entity = atoms.entity;
  const operation = "List";

  return (
    <div className="flex flex-col bg-white shadow rounded relative ">
      <div className="text-gray-900 text-opacity-90 text-base font-semibold leading-snug px-4 py-3 border-b border-gray-900/10 ">
        筛选总览
      </div>

      <div className="p-4 pr-0">
        <Form
          layout="horizontal"
          onSubmit={handleSearch}
          className="grid grid-cols-4 gap-y-4 "
        >
          <Form.Input
            noLabel
            field="query"
            placeholder="公司名称"
            className="w-full"
            suffix={<IconSearch />}
            showClear
          />

          <Form.Select
            field="contractorType"
            noLabel
            placeholder="承包商类型"
            className="w-full"
          >
            {BIM_CONTRACTOR_TYPE_MAP.map((item) => (
              <Form.Select.Option key={item.id} value={item.id}>
                {item.name}
              </Form.Select.Option>
            ))}
          </Form.Select>

          <Form.Select
            field="isBlack"
            noLabel
            placeholder="是否黑名单"
            className="w-full"
          >
            {BIM_CONTRACTOR_ISBLACK_MAP.map((item) => (
              <Form.Select.Option key={item.id} value={item.id}>
                {item.name}
              </Form.Select.Option>
            ))}
          </Form.Select>
          <div className="flex gap-2">
            <button className="btn rounded btn-primary btn-sm">
              查&nbsp;&nbsp;询
            </button>
            <button
              className="btn btn-sm rounded"
              type="reset"
              onClick={handleReset}
            >
              重&nbsp;&nbsp;置
            </button>
          </div>
        </Form>
      </div>
    </div>
  );
};
