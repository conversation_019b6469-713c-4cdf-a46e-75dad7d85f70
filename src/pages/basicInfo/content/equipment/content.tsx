import { useQueryClient } from "@tanstack/react-query";
import { equipmentApis } from "api";
import { equipmentAtoms } from "atoms";
import { List } from "components";

export const EquipmentContent = () => {
  const queryClient = useQueryClient();
  const queryKey = "list" + equipmentAtoms.entity;

  const importProps = {
    engtity: "设备",
    excelType: "equipment_template",
    downUrl: encodeURI("/static/template/设备信息导入模板.xlsx"),
    tip: "请先准备好对应的设备类型/区域/部门/责任人信息，否则导入会失败",
  };

  return (
    <List
      atoms={equipmentAtoms}
      apis={equipmentApis}
      importProps={importProps}
    />
  );
};
