import { IconSearch } from "@douyinfe/semi-icons";
import { Form } from "@douyinfe/semi-ui";
import { useQuery } from "@tanstack/react-query";
import {
  equipmentManagementEquipmentCategoryApis,
  getEquipmentList,
} from "api";
import {
  equipmentManagementEquipmentCategoryAtoms,
  equipmentManagementEquipmentCategoryDataAtom,
} from "atoms";
import { equipmentAtoms } from "atoms/basicInfo/equipment";
import {
  ALLOW_REPORT_STATUS_MAP,
  EQUIPMENT_STATUS_MAP,
  REPORT_STATUS_MAP,
} from "components";
import { TreeSearch } from "components/tree/search";
import { useFilterSearch } from "hooks";
import { useMemo } from "react";

export const EquipmentFilter = () => {
  const atoms = equipmentAtoms;
  const [handleSearch, handleReset] = useFilterSearch(atoms.filter);

  const entity = atoms.entity;
  const operation = "List";

  const { data: list } = useQuery({
    queryKey: [`get${entity}${operation}`], //user-defined code here
    queryFn: getEquipmentList, //user-defined code here
  });
  const options = useMemo(() => {
    return list?.data?.results ?? [];
  }, [list]);

  return (
    <div className="flex flex-col bg-white shadow rounded relative ">
      <div className="text-gray-900 text-opacity-90 text-base font-semibold leading-snug px-4 py-3 border-b border-gray-900/10 ">
        筛选总览
      </div>

      <div className="p-4 pr-0">
        <Form
          layout="horizontal"
          onSubmit={handleSearch}
          className="grid grid-cols-4 gap-y-4 "
        >
          {/* user-defined code here */}
          <Form.Input
            noLabel
            field="query"
            placeholder="请填入设备名称/编号"
            className="w-full"
            suffix={<IconSearch />}
            showClear
          />
          {/* <DicSearch
            field="equipmentTypeValueId"
            placeholder="设备类型"
            name="equipmentType"
          /> */}
          <TreeSearch
            placeholder="设备类型"
            field="equipmentCategoryId"
            atoms={equipmentManagementEquipmentCategoryAtoms}
            dataAtom={equipmentManagementEquipmentCategoryDataAtom}
            apis={equipmentManagementEquipmentCategoryApis}
          />
          <Form.Select
            field="status"
            noLabel
            placeholder="设备状态"
            className="w-full"
          >
            {EQUIPMENT_STATUS_MAP.map((item) => (
              <Form.Select.Option key={item.id} value={item.id}>
                {item.name}
              </Form.Select.Option>
            ))}
          </Form.Select>
          <Form.DatePicker
            field="expireDateGt"
            noLabel
            placeholder="过期日期下限"
            className="w-full"
            position="bottomRight"
          />
          <Form.DatePicker
            field="expireDateLte"
            noLabel
            placeholder="过期日期上限"
            className="w-full"
            position="bottomRight"
          />
          <Form.Select
            placeholder="是否上报"
            field="needReport"
            noLabel
            className="w-full"
          >
            {ALLOW_REPORT_STATUS_MAP.map((item) => (
              <Form.Select.Option key={item.id} value={item.id}>
                {item.name}
              </Form.Select.Option>
            ))}
          </Form.Select>
          <Form.Select
            placeholder="上报状态"
            field="reportStatus"
            noLabel
            className="w-full"
          >
            {REPORT_STATUS_MAP.map((item) => (
              <Form.Select.Option key={item.id} value={item.id}>
                {item.name}
              </Form.Select.Option>
            ))}
          </Form.Select>

          <div className="flex gap-2">
            <button className="btn rounded btn-primary btn-sm">
              查&nbsp;&nbsp;询
            </button>
            <button
              className="btn btn-sm rounded"
              type="reset"
              onClick={handleReset}
            >
              重&nbsp;&nbsp;置
            </button>
          </div>
        </Form>
      </div>
    </div>
  );
};
