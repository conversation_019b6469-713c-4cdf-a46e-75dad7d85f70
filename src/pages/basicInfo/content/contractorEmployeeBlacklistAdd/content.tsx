import { useQueryClient } from "@tanstack/react-query";
import { basicInfoContractorEmployeeBlacklistAddApis } from "api";
import { basicInfoContractorEmployeeBlacklistAddAtoms } from "atoms";
import { ExportProvider, List } from "components";
import { FC } from "react";

type BasicInfoContractorEmployeeBlacklistAddContentProps = {
  callback?: any;
  layout?: string;
  referAtom?: any;
  readonly?: boolean;
  filter?: any;
};
export const BasicInfoContractorEmployeeBlacklistAddContent: FC<
  BasicInfoContractorEmployeeBlacklistAddContentProps
> = ({
  callback,
  layout,
  referAtom,
  readonly = false,
  filter = {},
  ...restProps
}) => {
  if (layout === "modal") {
    readonly = true;
  }
  const queryClient = useQueryClient();
  const queryKey = "list" + basicInfoContractorEmployeeBlacklistAddAtoms.entity;

  return (
    <ExportProvider>
      <List
        atoms={basicInfoContractorEmployeeBlacklistAddAtoms}
        apis={basicInfoContractorEmployeeBlacklistAddApis}
        // operations, dynamicOperationFuncs
        filter={filter}
        callback={callback}
        referAtom={referAtom}
        layout={layout}
        readonly={readonly}
        cud={0b101}
        {...restProps}
      />
    </ExportProvider>
  );
};
