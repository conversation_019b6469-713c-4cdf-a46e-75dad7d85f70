import { productionUnitApis } from "api";
import { productionUnitAtoms } from "atoms";
import { List } from "components";

export const ProductionUnitContent = () => {
  const importProps = {
    entity: "生产装置",
    excelType: "production_unit_template",
    downUrl: encodeURI("/static/template/生产装置信息导入模板.xlsx"),
    tip: "请先维护好重大危险源和危险工艺信息，否则导入会失败",
  };

  return (
    <List
      atoms={productionUnitAtoms}
      apis={productionUnitApis}
      importProps={importProps}
    />
  );
};
