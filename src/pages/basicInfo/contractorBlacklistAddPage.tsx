/* import { <PERSON><PERSON><PERSON><PERSON>, RoleContent, RoleSide } from './content'
import { RoleModal } from './modal'
 */

import {
  BasicInfoContractorBlacklistAddContent,
  BasicInfoContractorBlacklistAddFilter,
} from "./content";
import { BasicInfoContractorBlacklistAddModal } from "./modal/contractorBlacklistAddModal";

export function BasicInfoContractorBlacklistAddPage({
  readonly = false,
  filter = {},
  ...restProps
}) {
  return (
    <div className="flex flex-col gap-4 ">
      {/* <RoleSide />
      <RoleModal />
      <RoleFilter />
      <RoleContent /> */}
      <BasicInfoContractorBlacklistAddFilter filter={filter} />
      <BasicInfoContractorBlacklistAddModal />
      <BasicInfoContractorBlacklistAddContent
        readonly={readonly}
        filter={filter}
        {...restProps}
      />
    </div>
  );
}
