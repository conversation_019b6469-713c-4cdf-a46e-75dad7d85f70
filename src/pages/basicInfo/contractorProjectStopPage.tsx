/* import { <PERSON><PERSON><PERSON>er, RoleContent, RoleSide } from './content'
import { RoleModal } from './modal'
 */

import {
  BasicInfoContractorProjectStopContent,
  BasicInfoContractorProjectStopFilter,
} from "./content";
import { BasicInfoContractorProjectStopModal } from "./modal/contractorProjectStopModal";

export function BasicInfoContractorProjectStopPage({
  readonly = false,
  filter = {},
  ...restProps
}) {
  return (
    <div className="flex flex-col gap-4 ">
      {/* <RoleSide />
      <RoleModal />
      <RoleFilter />
      <RoleContent /> */}
      <BasicInfoContractorProjectStopFilter filter={filter} />
      <BasicInfoContractorProjectStopModal />
      <BasicInfoContractorProjectStopContent
        readonly={readonly}
        filter={filter}
        {...restProps}
      />
    </div>
  );
}
