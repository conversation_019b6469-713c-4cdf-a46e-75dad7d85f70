/* import { <PERSON><PERSON><PERSON>er, RoleContent, RoleSide } from './content'
import { RoleModal } from './modal'
 */

import { BasicInfoContractorConfigContent } from "./content";

export function BasicInfoContractorConfigPage({
  readonly = false,
  filter = {},
  ...restProps
}) {
  return (
    <div className="flex flex-col gap-4 ">
      {/* <RoleSide />
      <RoleModal />
      <RoleFilter />
      <RoleContent /> */}
      {/* <BasicInfoContractorConfigFilter filter={filter} /> */}
      {/* <BasicInfoContractorConfigModal /> */}
      <BasicInfoContractorConfigContent
        readonly={readonly}
        filter={filter}
        {...restProps}
      />
    </div>
  );
}
