/* import { <PERSON><PERSON><PERSON>er, RoleContent, RoleSide } from './content'
import { RoleModal } from './modal'
 */

import {
  BasicInfoContractorProjectResumptionContent,
  BasicInfoContractorProjectResumptionFilter,
} from "./content";
import { BasicInfoContractorProjectResumptionAuditDelegateModal } from "./modal/contractorProjectResumptionAuditDelegateModal";
import { BasicInfoContractorProjectResumptionAuditModal } from "./modal/contractorProjectResumptionAuditModal";
import { BasicInfoContractorProjectResumptionModal } from "./modal/contractorProjectResumptionModal";

export function BasicInfoContractorProjectResumptionPage({
  readonly = false,
  filter = {},
  ...restProps
}) {
  return (
    <div className="flex flex-col gap-4 ">
      {/* <RoleSide />
      <RoleModal />
      <RoleFilter />
      <RoleContent /> */}
      <BasicInfoContractorProjectResumptionFilter filter={filter} />
      <BasicInfoContractorProjectResumptionModal />
      <BasicInfoContractorProjectResumptionAuditModal />
      <BasicInfoContractorProjectResumptionAuditDelegateModal />
      <BasicInfoContractorProjectResumptionContent
        readonly={readonly}
        filter={filter}
        {...restProps}
      />
    </div>
  );
}
