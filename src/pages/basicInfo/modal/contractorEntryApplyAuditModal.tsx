import {
  Col,
  Form,
  Modal,
  Row,
  TextArea,
  Toast,
  useFormState,
} from "@douyinfe/semi-ui";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import {
  auditBasicInfoContractorEntryApply,
  basicInfoContractorEntryApplyApis,
} from "api/basicInfo";
import {
  basicInfoContractorEntryApplyAtoms,
  basicInfoContractorEntryApplyAuditModalAtom,
} from "atoms";
import { AUDIT_RESULT_MAP } from "components";
import { Draft, DraftTrigger, destroyDraft } from "components/Draft";
import { useAtom } from "jotai";
import { useCallback, useRef } from "react";
import { useNavigate } from "react-router-dom";

const FormDebugComponentUsingFormState = () => {
  const formState = useFormState();
  return (
    <>
      <TextArea rows={8} value={JSON.stringify(formState.values, null, 2)} />
    </>
  );
};

export const BasicInfoContractorEntryApplyAuditModal = () => {
  const entityCname = "入厂申请审批";
  const title = entityCname;
  const requiredRule = { required: true, message: "此为必填项" };
  const gutter = 24;

  const atoms = basicInfoContractorEntryApplyAtoms;
  const apis = basicInfoContractorEntryApplyApis;

  const entity = atoms.entity;
  const uniqueKey = `${entity}`;

  const [auditModalAtom, setAuditModalAtom] = useAtom(
    basicInfoContractorEntryApplyAuditModalAtom
  );
  const [fnAtom] = useAtom(atoms.Fn);

  const rules = [requiredRule];

  const queryClient = useQueryClient();
  const queryKey = "list" + entity;

  const getFormApiRef = useRef<any>(null);
  const navigate = useNavigate();

  const handleSetFormApi = useCallback(
    (formApi) => {
      getFormApiRef.current = formApi;
    },
    [getFormApiRef]
  );

  const mutation = useMutation({
    mutationFn: auditBasicInfoContractorEntryApply,
    onSuccess: async (res) => {
      if (res?.code === 0) {
        let opts = {
          content: `操作成功!`,
          duration: 2,
        };
        Toast.success(opts);
        queryClient.invalidateQueries({ queryKey: [queryKey] });
        fnAtom?.refetch?.();
        // getFormApiRef.current?.reset?.();
        getFormApiRef.current?.setValues?.({}, { isOverride: true });
        destroyDraft(uniqueKey);
        setAuditModalAtom({
          record: {},
          show: false,
        });
      }
    },
  });

  //user-defined code here: extra data

  const handleClose = useCallback(() => {
    if (mutation.isLoading) {
      return;
    }
    destroyDraft(`${uniqueKey}`);
    setAuditModalAtom({
      record: {},
      show: false,
    });
  }, [setAuditModalAtom, mutation]);

  const handleOk = () => {
    if (mutation.isLoading) {
      return;
    }
    getFormApiRef.current
      .validate()
      .then((values) => {
        let obj = {
          ...values,
          //user-defined code here
        };
        if (auditModalAtom?.record?.id) {
          mutation.mutate({
            id: auditModalAtom?.record?.id,
            values: obj,
          });
        } else {
          mutation.mutate(obj);
        }
      })
      .catch((errors) => {
        console.log(errors);
        const [formFieldId] = Object.keys(errors || {});
        if (formFieldId) {
          document.getElementById(formFieldId)?.scrollIntoViewIfNeeded?.();
        }
      });
  };

  return (
    <>
      <DraftTrigger id={uniqueKey} draftAtom={atoms.editModal} />
      <Modal
        title={title}
        visible={auditModalAtom?.show ?? false}
        onCancel={handleClose}
        maskClosable={false}
        keepDOM
        width={800}
        footer={
          <div className="flex gap-2 justify-end">
            <button className="btn rounded btn-sm" onClick={handleClose}>
              取消
            </button>
            {mutation.isLoading ? (
              <button className="btn rounded btn-primary btn-sm btn-disabled">
                <span className="loading loading-spinner loading-xs"></span>
                确定
              </button>
            ) : (
              <button
                className="btn rounded btn-primary btn-sm"
                onClick={handleOk}
              >
                确定
              </button>
            )}
          </div>
        }
        centered
      >
        <Form
          labelPosition="left"
          labelAlign="right"
          labelWidth={100}
          getFormApi={handleSetFormApi}
        >
          {({ formState }) => (
            <>
              {/* <FormDebugComponentUsingFormState /> */}
              <Row gutter={gutter}>
                <Col span={12}>
                  <Form.Select
                    label="审核结果"
                    field="auditResult"
                    className="w-full"
                    rules={rules}
                  >
                    {AUDIT_RESULT_MAP.map((item) => (
                      <Form.Select.Option key={item.id} value={item.id}>
                        {item.name}
                      </Form.Select.Option>
                    ))}
                  </Form.Select>
                </Col>
              </Row>
              <Row gutter={gutter}>
                <Col span={24}>
                  <Form.TextArea
                    label="审核意见"
                    field="auditComment"
                    className="w-full"
                    rules={formState.values?.auditResult === 2 ? rules : []}
                  />
                </Col>
              </Row>
              {/* add form items here */}
              {auditModalAtom?.record?.id ? null : (
                <Draft id={uniqueKey} draftAtom={atoms.editModal} />
              )}
            </>
          )}
        </Form>
      </Modal>
    </>
  );
};
