import { Col, Form, Modal, Row, Toast } from "@douyinfe/semi-ui";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { contractorEmployeeApis } from "api";
import {
  contractorEmployeeAtoms,
  contractorEmployeeBlackModalAtom,
} from "atoms";
import { useAtom } from "jotai";
import { omit } from "ramda";
import { useCallback, useEffect, useRef } from "react";
import data from "tdesign-icons-react/lib/components/data";
import { filterEditData } from "utils";

export const ContractorEmployeeBlackModal = () => {
  const gutter = 24;
  const rules = [{ required: true, message: "此为必填项" }];

  const queryClient = useQueryClient();
  const queryKey = "list" + contractorEmployeeAtoms.entity;

  const getFormApiRef = useRef<any>(null);

  const [contractorEmployeeBlackModal, setContractorEmployeeBlackModal] =
    useAtom(contractorEmployeeBlackModalAtom);
  const fnAtom = useAtom(contractorEmployeeAtoms?.Fn);

  const name = contractorEmployeeBlackModal.employee?.name ?? "";

  const addBlackMutation = useMutation({
    mutationFn: contractorEmployeeApis.addBlackList,
    onSuccess: async (res) => {
      if (res?.code === 0) {
        let opts = {
          content: `操作成功!`,
          duration: 2,
        };
        queryClient.invalidateQueries({ queryKey: [queryKey] });
        Toast.success(opts);
        fnAtom?.refetch?.();
      }
    },
  });
  /* const handleAddBlack = useCallback(
    (record) => {
      console.log("handleAddBlack", record);
      addBlackMutation.mutate(record.id);
    },
    [addBlackMutation]
  ); */

  const handleSetFormApi = useCallback(
    (formApi) => {
      getFormApiRef.current = formApi;
    },
    [getFormApiRef]
  );

  useEffect(() => {
    if (
      contractorEmployeeBlackModal.employee?.id &&
      data?.data &&
      getFormApiRef.current
    ) {
      const items = omit([], data?.data);
      getFormApiRef.current.setValues(
        {
          ...filterEditData(items),
        },
        { isOverride: true }
      );
    } else {
      getFormApiRef.current?.reset?.();
      getFormApiRef.current?.setValues?.({}, { isOverride: true });
    }
  }, [data, contractorEmployeeBlackModal.employee?.id, getFormApiRef]);

  const handleClose = useCallback(() => {
    if (addBlackMutation.isLoading) {
      return;
    }
    // destroyDraft(`${uniqueKey}`)
    setContractorEmployeeBlackModal({
      employee: {},
      show: false,
    });
    // }, [setContractorShareModal])
  }, [setContractorEmployeeBlackModal, addBlackMutation]);

  const handleOk = () => {
    if (addBlackMutation.isLoading) {
      return;
    }
    getFormApiRef.current
      .validate()
      .then((values) => {
        let obj = {
          ...values,
        };
        if (contractorEmployeeBlackModal.employee.id) {
          addBlackMutation.mutate({
            id: contractorEmployeeBlackModal.employee.id,
            values: obj,
          });
        }
        setContractorEmployeeBlackModal({ employee: {}, show: false });
      })
      .catch((error) => {
        console.log(error);
        const [formFieldId] = Object.keys(error || {});
        if (formFieldId) {
          document.getElementById(formFieldId)?.scrollIntoViewIfNeeded?.();
        }
      });
  };
  return (
    <Modal
      title={`拉黑: ${contractorEmployeeBlackModal?.employee?.name}`}
      visible={contractorEmployeeBlackModal.show}
      onOk={handleOk}
      onCancel={handleClose}
      footer={
        <div className="flex gap-2 justify-end">
          {/* <button className="btn rounded btn-sm" onClick={handleClose}>
            取消
          </button> */}
          <button className="btn rounded btn-primary btn-sm" onClick={handleOk}>
            拉黑
          </button>
        </div>
      }
      centered
    >
      <Form
        labelPosition="left"
        labelAlign="right"
        labelWidth={100}
        getFormApi={handleSetFormApi}
      >
        {({ formState }) => (
          <>
            <Row gutter={gutter}>
              <Col span={24}>
                <Form.TextArea
                  label="拉黑原因"
                  field="blackReason"
                  autosize
                  trigger="blur"
                />
              </Col>
            </Row>
          </>
        )}
      </Form>
    </Modal>
  );
};
