import {
  Col,
  Form,
  Modal,
  Row,
  TextArea,
  Toast,
  useFormState,
} from "@douyinfe/semi-ui";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { basicInfoContractorProjectStopApis } from "api/basicInfo";
import { basicInfoContractorProjectStopAtoms } from "atoms";
import { DicSearch, RestSelect } from "components";
import { Draft, DraftTrigger, destroyDraft } from "components/Draft";
import { ContractorProjectComponentUsingFormApi } from "hooks";
import { useContractorListOptions } from "hooks/useContractorList";
import { useAtom } from "jotai";
import { omit } from "ramda";
import { useCallback, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { filterEditData } from "utils";

const FormDebugComponentUsingFormState = () => {
  const formState = useFormState();
  return (
    <>
      <TextArea rows={8} value={JSON.stringify(formState.values, null, 2)} />
    </>
  );
};

export const BasicInfoContractorProjectStopModal = () => {
  const operation = "Edit";
  const entityCname = "项目停工信息";
  const newTitle = "新增" + entityCname; //user-defined code here
  const editTitle = "编辑" + entityCname; //user-defined code here
  const requiredRule = { required: true, message: "此为必填项" };
  const gutter = 24;

  const atoms = basicInfoContractorProjectStopAtoms;
  const apis = basicInfoContractorProjectStopApis;

  const entity = atoms.entity;
  const uniqueKey = `${entity}${operation}`;

  const [editModalAtom, setEditModalAtom] = useAtom(atoms.editModal);
  const [fnAtom] = useAtom(atoms.Fn);

  const title = editModalAtom?.id ? editTitle : newTitle;
  const rules = [requiredRule];

  const queryClient = useQueryClient();
  const queryKey = "list" + entity;

  const getFormApiRef = useRef<any>(null);
  const navigate = useNavigate();

  const handleSetFormApi = useCallback(
    (formApi) => {
      getFormApiRef.current = formApi;
    },
    [getFormApiRef]
  );

  const { isLoading, data } = useQuery({
    queryKey: [`${entity}-${editModalAtom?.id ?? ""}`],
    queryFn: () => {
      if (editModalAtom?.id) {
        return apis.get(editModalAtom?.id);
      }
    },
    enabled: !!editModalAtom?.id,
  });
  // 自动填充表单
  useEffect(() => {
    if (editModalAtom?.id && data?.data?.id && getFormApiRef.current) {
      const items = omit([], data?.data);
      getFormApiRef.current.setValues(
        {
          ...filterEditData(items),
          //user-defined code here
        },
        { isOverride: true }
      );
    } else {
      getFormApiRef?.current?.reset?.();
      getFormApiRef.current?.setValues?.({}, { isOverride: true });
    }
  }, [editModalAtom?.id, data, getFormApiRef]);

  const mutation = useMutation({
    mutationFn: editModalAtom?.id ? apis.update : apis.create,
    onSuccess: async (res) => {
      if (res?.code === 0) {
        let opts = {
          content: `操作成功!`,
          duration: 2,
        };
        Toast.success(opts);
        queryClient.invalidateQueries({ queryKey: [queryKey] });
        fnAtom?.refetch?.();
        // getFormApiRef.current?.reset?.();
        getFormApiRef.current?.setValues?.({}, { isOverride: true });
        destroyDraft(uniqueKey);
        setEditModalAtom({
          id: "",
          show: false,
        });
      }
    },
  });

  //user-defined code here: extra data
  const contractorListOptions = useContractorListOptions();

  const handleClose = useCallback(() => {
    if (mutation.isLoading) {
      return;
    }
    destroyDraft(`${uniqueKey}`);
    setEditModalAtom({
      id: "",
      show: false,
    });
  }, [setEditModalAtom, mutation]);

  const handleOk = () => {
    if (mutation.isLoading) {
      return;
    }
    getFormApiRef.current
      .validate()
      .then((values) => {
        let obj = {
          ...values,
          //user-defined code here
        };
        if (editModalAtom?.id) {
          mutation.mutate({
            id: editModalAtom?.id,
            values: obj,
          });
        } else {
          mutation.mutate(obj);
        }
      })
      .catch((errors) => {
        console.log(errors);
        const [formFieldId] = Object.keys(errors || {});
        if (formFieldId) {
          document.getElementById(formFieldId)?.scrollIntoViewIfNeeded?.();
        }
      });
  };

  return (
    <>
      <DraftTrigger id={uniqueKey} draftAtom={atoms.editModal} />
      <Modal
        title={title}
        visible={editModalAtom?.show ?? false}
        onCancel={handleClose}
        maskClosable={false}
        keepDOM
        width={800}
        footer={
          <div className="flex gap-2 justify-end">
            <button className="btn rounded btn-sm" onClick={handleClose}>
              取消
            </button>
            {mutation.isLoading ? (
              <button className="btn rounded btn-primary btn-sm btn-disabled">
                <span className="loading loading-spinner loading-xs"></span>
                确定
              </button>
            ) : (
              <button
                className="btn rounded btn-primary btn-sm"
                onClick={handleOk}
              >
                确定
              </button>
            )}
          </div>
        }
        centered
      >
        <Form
          labelPosition="left"
          labelAlign="right"
          labelWidth={100}
          getFormApi={handleSetFormApi}
        >
          {({ formState }) => (
            <>
              {/* <FormDebugComponentUsingFormState /> */}
              {/* add form items here */}
              <Row gutter={gutter}>
                <Col span={12}>
                  <RestSelect
                    label="承包商"
                    field="contractorId"
                    options={contractorListOptions}
                    placeholder=""
                    isRequired
                  />
                </Col>
                <ContractorProjectComponentUsingFormApi
                  type="modal"
                  field="projectId"
                  isRequired={true}
                />
              </Row>
              <Row gutter={gutter}>
                <Col span={12}>
                  <DicSearch
                    label="停工原因"
                    field="contractorProjectSuspensionTypeValueId"
                    placeholder=""
                    name="contractorProjectSuspensionType"
                    isRequired
                  />
                </Col>
              </Row>
              {editModalAtom?.id ? null : (
                <Draft id={uniqueKey} draftAtom={atoms.editModal} />
              )}
            </>
          )}
        </Form>
      </Modal>
    </>
  );
};
