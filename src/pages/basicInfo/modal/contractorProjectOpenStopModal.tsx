import {
  Col,
  Form,
  Modal,
  Row,
  TextArea,
  Toast,
  useFormState,
} from "@douyinfe/semi-ui";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { stopContractorProject } from "api/basicInfo";
import {
  contractorProjectAtoms,
  contractorProjectOpenStopModalAtom,
} from "atoms";
import { DicSearch } from "components";
import { Draft, DraftTrigger, destroyDraft } from "components/Draft";
import { useContractorListOptions } from "hooks";
import { useAtom } from "jotai";
import { useCallback, useRef } from "react";

const FormDebugComponentUsingFormState = () => {
  const formState = useFormState();
  return (
    <>
      <TextArea rows={8} value={JSON.stringify(formState.values, null, 2)} />
    </>
  );
};

export const BasicInfoContractorProjectOpenStopModal = () => {
  const requiredRule = { required: true, message: "此为必填项" };
  const gutter = 24;

  const atoms = contractorProjectAtoms;

  const entity = atoms.entity;

  const [openStopModalAtom, setOpenStopModalAtom] = useAtom(
    contractorProjectOpenStopModalAtom
  );
  const [fnAtom] = useAtom(atoms.Fn);

  const title = "项目停工信息";
  const uniqueKey =
    "basicInfoContractorProjectStopModal" + openStopModalAtom?.record?.id;

  const queryClient = useQueryClient();
  const queryKey = "list" + entity;

  const getFormApiRef = useRef<any>(null);

  const handleSetFormApi = useCallback(
    (formApi) => {
      getFormApiRef.current = formApi;
    },
    [getFormApiRef]
  );

  const mutation = useMutation({
    mutationFn: stopContractorProject,
    onSuccess: async (res) => {
      if (res?.code === 0) {
        let opts = {
          content: `操作成功!`,
          duration: 2,
        };
        Toast.success(opts);
        queryClient.invalidateQueries({ queryKey: [queryKey] });
        fnAtom?.refetch?.();
        getFormApiRef.current?.setValues?.({}, { isOverride: true });
        destroyDraft(uniqueKey);
        setOpenStopModalAtom({
          record: {},
          show: false,
        });
      }
    },
  });

  const handleStop = useCallback(
    (record: any) => {
      mutation.mutate(record.id);
    },
    [mutation]
  );

  //user-defined code here: extra data
  const contractorListOptions = useContractorListOptions();

  const handleClose = useCallback(() => {
    if (mutation.isLoading) {
      return;
    }
    destroyDraft(`${uniqueKey}`);
    setOpenStopModalAtom({
      record: {},
      show: false,
    });
  }, [setOpenStopModalAtom, mutation]);

  const handleOk = () => {
    if (mutation.isLoading) {
      return;
    }
    getFormApiRef.current
      .validate()
      .then((values) => {
        let obj = {
          ...values,
          //user-defined code here
        };
        mutation.mutate({
          id: openStopModalAtom?.record?.id,
          values: obj,
        });
      })
      .catch((errors) => {
        console.log(errors);
        const [formFieldId] = Object.keys(errors || {});
        if (formFieldId) {
          document.getElementById(formFieldId)?.scrollIntoViewIfNeeded?.();
        }
      });
  };

  return (
    <>
      <DraftTrigger id={uniqueKey} draftAtom={atoms.editModal} />
      <Modal
        title={title}
        visible={openStopModalAtom?.show ?? false}
        onCancel={handleClose}
        maskClosable={false}
        keepDOM
        width={800}
        footer={
          <div className="flex gap-2 justify-end">
            <button className="btn rounded btn-sm" onClick={handleClose}>
              取消
            </button>
            {mutation.isLoading ? (
              <button className="btn rounded btn-primary btn-sm btn-disabled">
                <span className="loading loading-spinner loading-xs"></span>
                确定
              </button>
            ) : (
              <button
                className="btn rounded btn-primary btn-sm"
                onClick={handleOk}
              >
                确定
              </button>
            )}
          </div>
        }
        centered
      >
        <Form
          labelPosition="left"
          labelAlign="right"
          labelWidth={100}
          getFormApi={handleSetFormApi}
        >
          {({ formState }) => (
            <>
              {/* <FormDebugComponentUsingFormState /> */}
              {/* add form items here */}
              <Row gutter={gutter}>
                <Col span={12}>
                  <DicSearch
                    label="停工原因"
                    field="contractorProjectSuspensionTypeValueId"
                    placeholder=""
                    name="contractorProjectSuspensionType"
                    isRequired
                  />
                </Col>
              </Row>
              {openStopModalAtom?.record?.id ? null : (
                <Draft id={uniqueKey} draftAtom={atoms.editModal} />
              )}
            </>
          )}
        </Form>
      </Modal>
    </>
  );
};
