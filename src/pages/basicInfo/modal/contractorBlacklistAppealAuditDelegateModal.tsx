import {
  Col,
  Form,
  Modal,
  Row,
  TextArea,
  Toast,
  useFormState,
} from "@douyinfe/semi-ui";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { auditDelegateBasicInfoContractorBlacklistAppeal } from "api/basicInfo";
import {
  basicInfoContractorBlacklistAppealAtoms,
  basicInfoContractorBlacklistAppealAuditDelegateModalAtom,
} from "atoms";
import { EmployeeSearch } from "components";
import { Draft, DraftTrigger, destroyDraft } from "components/Draft";
import { useAtom } from "jotai";
import { useCallback, useRef } from "react";

const FormDebugComponentUsingFormState = () => {
  const formState = useFormState();
  return (
    <>
      <TextArea rows={8} value={JSON.stringify(formState.values, null, 2)} />
    </>
  );
};

export const BasicInfoContractorBlacklistAppealAuditDelegateModal = () => {
  const entityCname = "承包商黑名单审核代办";
  const title = entityCname;
  const gutter = 24;

  const atoms = basicInfoContractorBlacklistAppealAtoms;

  const entity = atoms.entity;
  const uniqueKey = `${entity}`;

  const [auditDelegateModalAtom, setAuditDelegateModalAtom] = useAtom(
    basicInfoContractorBlacklistAppealAuditDelegateModalAtom
  );
  const [fnAtom] = useAtom(atoms.Fn);

  const queryClient = useQueryClient();
  const queryKey = "list" + entity;

  const getFormApiRef = useRef<any>(null);

  const handleSetFormApi = useCallback(
    (formApi) => {
      getFormApiRef.current = formApi;
    },
    [getFormApiRef]
  );

  const mutation = useMutation({
    mutationFn: auditDelegateBasicInfoContractorBlacklistAppeal,
    onSuccess: async (res) => {
      if (res?.code === 0) {
        let opts = {
          content: `操作成功!`,
          duration: 2,
        };
        Toast.success(opts);
        queryClient.invalidateQueries({ queryKey: [queryKey] });
        fnAtom?.refetch?.();
        // getFormApiRef.current?.reset?.();
        getFormApiRef.current?.setValues?.({}, { isOverride: true });
        destroyDraft(uniqueKey);
        setAuditDelegateModalAtom({
          record: {},
          show: false,
        });
      }
    },
  });

  //user-defined code here: extra data

  const handleClose = useCallback(() => {
    if (mutation.isLoading) {
      return;
    }
    destroyDraft(`${uniqueKey}`);
    setAuditDelegateModalAtom({
      record: {},
      show: false,
    });
  }, [setAuditDelegateModalAtom, mutation]);

  const handleOk = () => {
    if (mutation.isLoading) {
      return;
    }
    getFormApiRef.current
      .validate()
      .then((values) => {
        let obj = {
          ...values,
          //user-defined code here
        };
        if (auditDelegateModalAtom?.record?.id) {
          mutation.mutate({
            id: auditDelegateModalAtom?.record?.id,
            values: obj,
          });
        } else {
          mutation.mutate(obj);
        }
      })
      .catch((errors) => {
        console.log(errors);
        const [formFieldId] = Object.keys(errors || {});
        if (formFieldId) {
          document.getElementById(formFieldId)?.scrollIntoViewIfNeeded?.();
        }
      });
  };

  return (
    <>
      <DraftTrigger id={uniqueKey} draftAtom={atoms.editModal} />
      <Modal
        title={title}
        visible={auditDelegateModalAtom?.show ?? false}
        onCancel={handleClose}
        maskClosable={false}
        keepDOM
        width={800}
        footer={
          <div className="flex gap-2 justify-end">
            <button className="btn rounded btn-sm" onClick={handleClose}>
              取消
            </button>
            {mutation.isLoading ? (
              <button className="btn rounded btn-primary btn-sm btn-disabled">
                <span className="loading loading-spinner loading-xs"></span>
                确定
              </button>
            ) : (
              <button
                className="btn rounded btn-primary btn-sm"
                onClick={handleOk}
              >
                确定
              </button>
            )}
          </div>
        }
        centered
      >
        <Form
          labelPosition="left"
          labelAlign="right"
          labelWidth={100}
          getFormApi={handleSetFormApi}
        >
          {({ formState }) => (
            <>
              {/* <FormDebugComponentUsingFormState /> */}
              <Row gutter={gutter}>
                <Col span={12}>
                  <EmployeeSearch
                    field="auditIdList"
                    placeholder="请选择代办人"
                    multiple
                  />
                </Col>
              </Row>
              {/* add form items here */}
              {auditDelegateModalAtom?.record?.id ? null : (
                <Draft id={uniqueKey} draftAtom={atoms.editModal} />
              )}
            </>
          )}
        </Form>
      </Modal>
    </>
  );
};
