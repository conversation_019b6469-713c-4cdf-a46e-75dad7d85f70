/* import { <PERSON><PERSON><PERSON>er, RoleContent, RoleSide } from './content'
import { RoleModal } from './modal'
 */

import {
  BasicInfoContractorEmployeeBlacklistAddContent,
  BasicInfoContractorEmployeeBlacklistAddFilter,
} from "./content";
import { BasicInfoContractorEmployeeBlacklistAddModal } from "./modal/contractorEmployeeBlacklistAddModal";

export function BasicInfoContractorEmployeeBlacklistAddPage({
  readonly = false,
  filter = {},
  ...restProps
}) {
  return (
    <div className="flex flex-col gap-4 ">
      {/* <RoleSide />
      <RoleModal />
      <RoleFilter />
      <RoleContent /> */}
      <BasicInfoContractorEmployeeBlacklistAddFilter filter={filter} />
      <BasicInfoContractorEmployeeBlacklistAddModal />
      <BasicInfoContractorEmployeeBlacklistAddContent
        readonly={readonly}
        filter={filter}
        {...restProps}
      />
    </div>
  );
}
