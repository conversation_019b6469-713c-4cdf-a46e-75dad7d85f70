/* import { <PERSON><PERSON><PERSON>er, RoleContent, RoleSide } from './content'
import { RoleModal } from './modal'
 */

import {
  BasicInfoContractorBlacklistAppealContent,
  BasicInfoContractorBlacklistAppealFilter,
} from "./content";
import { BasicInfoContractorBlacklistAppealAuditDelegateModal } from "./modal/contractorBlacklistAppealAuditDelegateModal";
import { BasicInfoContractorBlacklistAppealAuditModal } from "./modal/contractorBlacklistAppealAuditModal";
import { BasicInfoContractorBlacklistAppealModal } from "./modal/contractorBlacklistAppealModal";

export function BasicInfoContractorBlacklistAppealPage({
  readonly = false,
  filter = {},
  ...restProps
}) {
  return (
    <div className="flex flex-col gap-4 ">
      {/* <RoleSide />
      <RoleModal />
      <RoleFilter />
      <RoleContent /> */}
      <BasicInfoContractorBlacklistAppealFilter filter={filter} />
      <BasicInfoContractorBlacklistAppealModal />
      <BasicInfoContractorBlacklistAppealAuditModal />
      <BasicInfoContractorBlacklistAppealAuditDelegateModal />
      <BasicInfoContractorBlacklistAppealContent
        readonly={readonly}
        filter={filter}
        {...restProps}
      />
    </div>
  );
}
