import * as Sentry from "@sentry/react";
import { find, isEmpty, propEq } from "ramda";
import { useEffect } from "react";
import {
  createRoutesFromChildren,
  matchRoutes,
  useLocation,
  useNavigationType,
} from "react-router-dom";

export const window_protocol = window.location.protocol;
export const window_host = window.location.hostname;
export const window_port = Number(window.location.port);
// export const everest_port = window_port - 1;
export const everest_port = `:${window_port - 1}`;
// export const lhotse_port = window_port + 1;
export const lhotse_port = `:${window_port + 1}`; // contractor_site
export const dhaulagiri_port = `:${window_port + 2}`; // mobile_site

const config_everest_host = import.meta.env.VITE_HOST;
let config_everest_port = import.meta.env.VITE_PORT
  ? `:${import.meta.env.VITE_PORT}`
  : "";
if (
  (config_everest_port === ":80" && window_protocol === "https:") ||
  (config_everest_port === ":443" && window_protocol === "http:")
) {
  config_everest_port = "";
}
const config_everest_version = import.meta.env.VITE_VERSION
  ? import.meta.env.VITE_VERSION
  : "v1";

const config_lhotse_host = import.meta.env.VITE_LHOTSE_HOST;
const config_dhaulagiri_host = import.meta.env.VITE_DHAULAGIRI_HOST;
let config_lhotse_port = import.meta.env.VITE_LHOTSE_PORT
  ? `:${import.meta.env.VITE_LHOTSE_PORT}`
  : "";
if (
  (config_lhotse_port === ":80" && window_protocol === "https:") ||
  (config_lhotse_port === ":443" && window_protocol === "http:")
) {
  config_lhotse_port = "";
}
let config_dhaulagiri_port = import.meta.env.VITE_DHAULAGIRI_PORT
  ? `:${import.meta.env.VITE_DHAULAGIRI_PORT}`
  : "";
if (
  (config_dhaulagiri_port === ":80" && window_protocol === "https:") ||
  (config_dhaulagiri_port === ":443" && window_protocol === "http:")
) {
  config_dhaulagiri_port = "";
}
export const base_url = config_everest_host
  ? // ? `${import.meta.env.VITE_PROTOCOL}://${import.meta.env.VITE_HOST}:${config_port}`
    `${window_protocol}//${config_everest_host}${config_everest_port}`
  : `${window_protocol}//${window_host}${everest_port}`;
export const api_url = `${base_url}/${config_everest_version}`;
export const upload_url = `${api_url}/system/upload`;

export const lhotse_url = config_lhotse_host
  ? // ? `${import.meta.env.VITE_PROTOCOL}://${import.meta.env.VITE_LHOTSE_HOST}:${config_lhotse_port}`
    `${window_protocol}//${config_lhotse_host}${config_lhotse_port}`
  : `${window_protocol}//${window_host}${lhotse_port}`;

export const dhaulagiri_url = config_dhaulagiri_host
  ? // ? `${import.meta.env.VITE_PROTOCOL}://${import.meta.env.VITE_DHAULAGIRI_HOST}:${config_dhaulagiri_port}`
    `${window_protocol}//${config_dhaulagiri_host}${config_dhaulagiri_port}`
  : `${window_protocol}//${window_host}${dhaulagiri_port}`;

console.log(import.meta.env.VITE_TIANDITU_TOKEN);
const config_tianditu_token_list = import.meta.env.VITE_TIANDITU_TOKEN.split(
  ","
);
console.log(config_tianditu_token_list);
const randomToken =
  config_tianditu_token_list[
    Math.floor(Math.random() * config_tianditu_token_list.length)
  ];
console.log(randomToken);
export const tiandiTuMapToken = isEmpty(randomToken)
  ? "f662c465df02ee66294ca599ac027235"
  : randomToken;

export const sentry_dsn =
  import.meta.env.VITE_SENTRY_DSN ??
  "https://<EMAIL>/3";
console.log(sentry_dsn);

Sentry.init({
  dsn: sentry_dsn,
  integrations: [
    // See docs for support of different versions of variation of react router
    // https://docs.sentry.io/platforms/javascript/guides/react/configuration/integrations/react-router/
    Sentry.reactRouterV6BrowserTracingIntegration({
      useEffect,
      useLocation,
      useNavigationType,
      createRoutesFromChildren,
      matchRoutes,
    }),
    Sentry.replayIntegration(),
  ],

  // Set tracesSampleRate to 1.0 to capture 100%
  // of transactions for tracing.
  tracesSampleRate: 1.0,

  // Set `tracePropagationTargets` to control for which URLs trace propagation should be enabled
  tracePropagationTargets: [/^\//, /^https:\/\/yourserver\.io\/api/],

  // Capture Replay for 10% of all sessions,
  // plus for 100% of sessions with an error
  replaysSessionSampleRate: 0.1,
  replaysOnErrorSampleRate: 1.0,
});

export type Oem = {
  id: string;
  display_name: string;
  logo_name: string;
};
export const oem: Oem[] = [
  {
    id: "vren",
    display_name: "安全管控平台",
    // display_name: '微仁',
    logo_name: "/icons/vren-logo.png",
  },
  {
    id: "preview",
    display_name: "安全演示平台",
    logo_name: "/icons/vren-logo.png",
  },
  {
    id: "xjzy",
    display_name: "仙琚制药",
    logo_name: "/icons/xjzy-logo.png",
  },
  {
    id: "ruibo",
    display_name: "九洲药业（台州）有限公司",
    logo_name: "/icons/ruibo-logo.jpg",
  },
  {
    id: "dimai",
    display_name: "江西帝劢安全平台",
    logo_name: "/icons/dimai-logo.png",
  },
  {
    id: "guanqiao",
    display_name: "新旺气体",
    logo_name: "/icons/vren-logo.png",
  },
];

export const getCurrentOem = (): Oem => {
  const uniq_id = `${import.meta.env.VITE_OEM_ID}`;
  const item = find(propEq(uniq_id, "id"))(oem) as Oem;
  if (!item?.id) {
    // 找不到默认匹配vren
    return oem[0];
  }
  return item;
};

export const bundleVersion = "1.0.0";
export const bundleBranch = "main";
export const bundleGitCommit = import.meta.env.VITE_GIT_COMMIT;
