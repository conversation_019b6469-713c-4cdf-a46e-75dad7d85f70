// 生成与关联用重要的2个key，修改后需要重新“权限自检”以及分配角色权限

/**
 * @set src/pages/basicInfo/content/role/settingRole.tsx => update()
 * @param 菜单和权限的唯一ID字段名
 * @get 所有需要读取权限关联关系的地方都需要使用
 */
export const RoleKeyCode = 'menuCode';

/**
 * @set 菜单访问路径
 * 当前这个key用在App.tsx 内生成路径时候的表示声明的key
 * @param 存入内容为 菜单路径: router 内声明的路径，可以为window.location.pathname进行对比
 * 案例: export const RiskMap: ChildrenMap[] = generateLoader([{path: xxx, ....}]) 为generateLoader 内传入的`path`字段
 * @get 读取菜单路径，并用作于关联关系查找, 因为页面内的按钮权限生成规则为 "pathname+name"
 */
export const RoleKeyPath = 'path';

