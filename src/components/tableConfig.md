# TableConfig 组件

## 功能概述

TableConfig 组件是一个用于配置表格列显示/隐藏的模态框组件，配合 `useTableConfig` hook 使用可以实现真正的持久化存储功能，用户的自定义配置会在浏览器刷新后保持。

## 主要特性

1. **真正的持久化存储**: 通过 `useTableConfig` hook 在页面初始化时自动读取localStorage中的配置
2. **页面隔离**: 不同页面的表格配置独立存储，不会相互影响
3. **向后兼容**: 支持原有的使用方式，无需修改现有代码
4. **智能存储键**: 自动根据 entity 或页面路径生成唯一的存储键

## 核心原理

### 问题分析

之前的实现存在一个问题：TableConfig 组件只在保存时更新 columns atom，但在页面初始化时并没有读取localStorage中的配置来初始化 atom，导致配置只在当前会话中生效。

### 解决方案

创建了 `useTableConfig` hook，它：

1. 在页面初始化时自动从localStorage读取配置
2. 将读取的配置应用到 columns atom
3. 包装 setColumns 函数，自动保存到localStorage

## 使用方法

### 在 List 组件中使用（推荐）

```tsx
import { useTableConfig } from "hooks/useTableConfig";

// 在 List 组件中
const [_columns, setColumns] = useTableConfig(atoms.columns, atoms.entity);

// TableConfig 组件使用
<TableConfig
  columns={_columns}
  handleSave={setColumns}
  visible={configModal}
  handleClose={setConfigModal}
/>;
```

### 在自定义页面中使用

```tsx
import { useTableConfig } from "hooks/useTableConfig";

// 在自定义页面中
const [columns, setColumns] = useTableConfig(columnsAtom, "CustomPage");

<TableConfig
  visible={configModal}
  columns={columns}
  handleClose={() => setConfigModal(false)}
  handleSave={setColumns}
/>;
```

### 参数说明

#### TableConfig 组件参数

| 参数          | 类型                                | 必填 | 说明                 |
| ------------- | ----------------------------------- | ---- | -------------------- |
| `visible`     | `boolean`                           | 是   | 控制模态框显示/隐藏  |
| `columns`     | `ColumnConfig[]`                    | 是   | 表格列配置数组       |
| `handleSave`  | `(columns: ColumnConfig[]) => void` | 是   | 保存配置的回调函数   |
| `handleClose` | `() => void`                        | 是   | 关闭模态框的回调函数 |

#### useTableConfig hook 参数

| 参数          | 类型     | 必填 | 说明                             |
| ------------- | -------- | ---- | -------------------------------- |
| `columnsAtom` | `Atom`   | 是   | columns atom                     |
| `entity`      | `string` | 否   | 用于区分不同页面的配置，建议传入 |

### ColumnConfig 类型定义

```tsx
type ColumnConfig = {
  title: string; // 列标题
  dataIndex: string; // 数据字段名
  isShow: boolean; // 是否显示
  fixed?: boolean; // 是否固定列（固定列不参与配置）
  width?: number; // 列宽度
  ellipsis?: boolean; // 是否省略
  render?: (text: any, record: any, index: number) => React.ReactNode; // 渲染函数
  [key: string]: any; // 其他属性
};
```

## 存储机制

### 存储键生成规则

1. **有 entity 参数**: `table-config-{entity}`
2. **无 entity 参数，有路径**: `table-config-path-{pathname}`
3. **兜底方案**: `table-config-default`

### 存储数据格式

```json
[
  {
    "dataIndex": "id",
    "isShow": true
  },
  {
    "dataIndex": "name",
    "isShow": false
  }
]
```

### 工作流程

1. **页面初始化**: `useTableConfig` hook 自动从localStorage读取配置
2. **应用配置**: 将读取的配置应用到 columns atom
3. **用户操作**: 用户在 TableConfig 中修改列显示状态
4. **保存配置**: 点击保存时，`useTableConfig` 自动保存到localStorage
5. **下次访问**: 页面重新加载时，配置自动恢复

## 迁移指南

### 从旧版本升级

1. **List 组件**: 自动升级，无需修改代码
2. **自定义页面**: 需要手动使用 `useTableConfig` hook
3. **测试验证**: 升级后测试配置是否能正确保存和恢复

### 最佳实践

1. **使用有意义的 entity**: 使用能代表页面功能的 entity 名称
2. **避免 entity 冲突**: 确保不同页面使用不同的 entity
3. **测试存储功能**: 在开发时测试配置的保存和恢复功能

## 注意事项

1. **localStorage 限制**: 存储数据会占用浏览器存储空间，建议定期清理
2. **浏览器兼容性**: 依赖 localStorage API，不支持 IE8 及以下版本
3. **数据格式**: 存储的数据格式为 JSON，确保数据可序列化
4. **错误处理**: 组件内置了错误处理，存储失败时会在控制台输出错误信息

## 故障排除

### 配置没有保存

1. 检查浏览器是否支持 localStorage
2. 检查控制台是否有错误信息
3. 确认 entity 参数是否正确传入

### 配置没有恢复

1. 检查 localStorage 中是否有对应的存储键
2. 确认存储的数据格式是否正确
3. 检查 columns 数据是否正确传入
4. 确认是否正确使用了 `useTableConfig` hook

### 不同页面配置冲突

1. 确保不同页面使用不同的 entity 参数
2. 检查存储键是否正确生成
3. 使用浏览器开发者工具查看 localStorage 内容

## 技术实现细节

### useTableConfig hook 实现

```tsx
export const useTableConfig = (columnsAtom: any, entity?: string) => {
  const [columns, setColumns] = useAtom(columnsAtom);
  const { pathname } = useLocation();

  // 初始化时从localStorage读取配置
  useEffect(() => {
    if (!columns?.length) return;

    const savedConfig = loadTableConfig(entity, pathname);
    if (Object.keys(savedConfig).length === 0) return;

    // 应用保存的配置
    const updatedColumns = columns.map((col) => ({
      ...col,
      isShow:
        savedConfig[col.dataIndex] !== undefined
          ? savedConfig[col.dataIndex]
          : col.isShow,
    }));

    // 只有当配置有变化时才更新
    const hasChanges = updatedColumns.some(
      (col, index) => col.isShow !== columns[index].isShow
    );

    if (hasChanges) {
      setColumns(updatedColumns);
    }
  }, [columns, entity, pathname, setColumns]);

  // 包装setColumns函数，自动保存到localStorage
  const setColumnsWithStorage = (newColumns: any[]) => {
    setColumns(newColumns);
    saveTableConfig(entity, newColumns, pathname);
  };

  return [columns, setColumnsWithStorage];
};
```

这个实现确保了：

1. 页面初始化时自动读取配置
2. 保存时自动存储配置
3. 配置变化时自动更新atom
4. 避免不必要的重复更新
