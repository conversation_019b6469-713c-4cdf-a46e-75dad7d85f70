import { useState, useCallback, useMemo, FC, useEffect } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { Form, useFormApi, useFormState } from "@douyinfe/semi-ui";
import { useAtom } from "jotai";
import { getEventList } from "api/doubleGuard";
import { propEq, find } from "ramda";
import { useRemoteSearch } from "hooks";

export type EventSearchProps = {
  field: string;
  placeholder?: string;
  label?: string;
  isRequired?: boolean;
  association?: Array<string>; // 自动补全对应表单
  where?: any;
  disabled?: boolean;
};

export const EventSearch: FC<EventSearchProps> = ({
  field,
  placeholder,
  label,
  isRequired,
  association,
  where,
  disabled,
}) => {
  const [query, onSearch] = useRemoteSearch();
  const formApi = useFormApi();
  const formState = useFormState();
  const whereQuery = formState?.values?.[`${where}`] ?? null;

  const { isLoading, isError, error, data, refetch } = useQuery({
    queryKey: ["getEventList", whereQuery, query],
    queryFn: () => {
      return getEventList({
        pageNumber: 1,
        pageSize: 1000,
        query: query,
        filter: {
          [where]: whereQuery,
        },
      });
    },
  });

  const allArea = useMemo(() => {
    const f = Object.keys(where ?? {});
    if (f.length && f.length !== 1 && f[0] !== field && !whereQuery) {
      return [];
    }
    if (data?.data) {
      return data?.data?.results ?? [];
    }
    return [];
  }, [where, whereQuery, data]);

  useEffect(() => {
    if (
      !isLoading &&
      ((!allArea.length && formState.values[field] !== undefined) ||
        !allArea.some((o: any) => o.id === formState.values[field]))
    ) {
      formApi.setValue(field, undefined);
    }
  }, [allArea, isLoading, formState.values[field], formApi, field]);

  return (
    <Form.Select
      field={field}
      noLabel={!Boolean(label)}
      label={label ?? ""}
      isLoading={isLoading}
      filter
      onSearch={onSearch}
      placeholder={placeholder ?? "风险事件"}
      className="w-full"
      disabled={disabled || !allArea.length}
      rules={[{ required: isRequired, message: "此为必填项!" }]}
    >
      {(allArea ?? []).map((o) => (
        <Form.Select.Option value={parseInt(o?.id) ?? 0} key={o?.id ?? 0}>
          {o?.name ?? ""}
        </Form.Select.Option>
      ))}
    </Form.Select>
  );
};
