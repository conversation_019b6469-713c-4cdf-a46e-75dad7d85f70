import { Form, Tag, useFormApi, useFormState } from "@douyinfe/semi-ui";
import { useQuery } from "@tanstack/react-query";
import { getContractorEmployeeList } from "api/basicInfo";
import { useRemoteSearch } from "hooks";
import { find, propEq, type } from "ramda";
import { FC, useEffect, useMemo } from "react";

export type ContractorEmployeeSearchProps = {
  field: string;
  placeholder?: string;
  label?: string;
  isRequired?: boolean;
  filter?: boolean;
  multiple?: boolean;
  association?: string; // 自动补全对应表单
  pickKeys?: string | Array<number>; // 取部分数据
  valueType?: "object";
  max?: number;
};

export const ContractorEmployeeSearch: FC<ContractorEmployeeSearchProps> = ({
  field,
  placeholder,
  label,
  isRequired,
  association,
  filter,
  multiple,
  initValue,
  value,
  disabled,
  pickKeys,
  valueType,
  max = 99,
}) => {
  const [query, onSearch] = useRemoteSearch();
  const formApi = useFormApi();
  const formState = useFormState();
  const { isLoading, isError, error, data, refetch } = useQuery({
    queryKey: ["getContractorEmployeeList", query],
    queryFn: () => {
      return getContractorEmployeeList({
        pageNumber: 1,
        pageSize: 1000,
        query: query,
        filter: {
          status: 1,
        },
      });
    },
  });

  const allEmployee = useMemo(() => {
    if (data?.data) {
      const results = data?.data?.results ?? [];
      if (pickKeys?.length) {
        // 取部分数据
        const pick_key =
          type(pickKeys) === "String" ? JSON.parse(pickKeys || []) : pickKeys;
        return results.filter((o) => pick_key.includes(o.id));
      }
      return results;
    }
  }, [data, pickKeys]);

  useEffect(() => {
    if (formState.values?.[`${field}`] && allEmployee?.length && association) {
      const item = find(propEq(formState.values[`${field}`], "id"))(
        allEmployee,
      );
      formApi.setValue(association, item?.id ?? null);
    }
  }, [formState.values?.[`${field}`]]);

  const renderOption = (item) => {
    return (
      <Form.Select.Option
        name={item.name}
        value={
          valueType === "object"
            ? JSON.stringify({ id: parseInt(item?.id) ?? 0, name: item.name })
            : (parseInt(item?.id) ?? 0)
        }
        key={item.id}
      >
        <div className="flex flex-col items-start">
          <div className="text-[14px] flex items-center gap-2">{item.name}</div>
          <div className="text-[color:rgba(var(--color-text-2),1)] text-[12px] leading-[16px]">
            {item.contractor?.name && `${item.contractor?.name} / `}
          </div>
        </div>
      </Form.Select.Option>
    );
  };

  const renderSelectedItem = (optionNode) => optionNode?.name;

  const renderMultipleWithCustomTag = (optionNode, { onClose }) => {
    const content = (
      <Tag closable={true} onClose={onClose} size="small">
        {optionNode?.name}
      </Tag>
    );
    return {
      isRenderInTag: false,
      content,
    };
  };

  return (
    <Form.Select
      initValue={initValue}
      value={value}
      field={field}
      noLabel={!Boolean(label)}
      label={label ?? ""}
      filter
      remote
      isLoading={isLoading}
      onSearch={onSearch}
      multiple={multiple}
      max={max}
      placeholder={placeholder ?? "请选择人员"}
      className="w-full"
      disabled={disabled}
      rules={[{ required: isRequired, message: "此为必填项!" }]}
      renderSelectedItem={
        multiple ? renderMultipleWithCustomTag : renderSelectedItem
      }
    >
      {(allEmployee ?? []).map(renderOption)}
    </Form.Select>
  );
};
