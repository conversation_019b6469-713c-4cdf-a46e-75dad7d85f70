import { useState, useCallback, useMemo, FC, useEffect } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { Form, useFormApi, useFormState } from "@douyinfe/semi-ui";
import { useAtom } from "jotai";
import { getMobileList } from "api";
import { propEq, find } from "ramda";
import { useRemoteSearch } from "hooks";

export type ImeiSearchProps = {
  field: string;
  placeholder?: string;
  label?: string;
  isRequired?: boolean;
  filter?: boolean;
  association?: string; // 自动补全对应表单
};

export const ImeiSearch: FC<ImeiSearchProps> = ({
  field,
  placeholder,
  label,
  isRequired,
  association,
  filter,
}) => {
  const [query, onSearch] = useRemoteSearch();
  const formApi = useFormApi();
  const formState = useFormState();
  const { isLoading, isError, error, data, refetch } = useQuery({
    queryKey: ["getMobileList", query],
    queryFn: () => {
      return getMobileList({
        pageNumber: 1,
        pageSize: 1000,
        query: query,
      });
    },
  });

  const dataSource = useMemo(() => {
    if (data?.data) {
      return data?.data?.results ?? [];
    }
  }, [data]);

  useEffect(() => {
    if (formState.values?.[`${field}`] && dataSource?.length && association) {
      const item = find(propEq(formState.values[`${field}`], "id"))(dataSource);
      formApi.setValue(association, item?.id ?? null);
    }
  }, [formState.values?.[`${field}`]]);

  return (
    <Form.Select
      field={field}
      noLabel={!Boolean(label)}
      label={label ?? ""}
      filter={filter}
      isLoading={isLoading}
      onSearch={onSearch}
      placeholder={placeholder ?? ""}
      className="w-full"
      rules={[{ required: isRequired, message: "此为必填项!" }]}
    >
      {(dataSource ?? []).map((o) => (
        <Form.Select.Option value={parseInt(o?.id) ?? 0} key={o?.id}>
          {o?.name ?? ""}:{o?.imei}
        </Form.Select.Option>
      ))}
    </Form.Select>
  );
};
