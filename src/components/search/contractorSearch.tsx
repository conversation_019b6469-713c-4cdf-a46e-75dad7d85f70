import { Form, useFormApi, useFormState } from "@douyinfe/semi-ui";
import { useQuery } from "@tanstack/react-query";
import { getContractorList } from "api";
import { useRemoteSearch } from "hooks";
import { FC, useMemo } from "react";

type Options = {
  id: number;
  name: string;
};
export type ContractorSearchProps = {
  field: string;
  placeholder?: string;
  label?: string;
  isRequired?: boolean;
  valueType?: "object";
  filter?: any;
  multiple?: boolean;
};

export const ContractorSearch: FC<ContractorSearchProps> = ({
  field,
  placeholder,
  label,
  isRequired,
  valueType,
  filter = {},
  multiple,
}) => {
  const [query, onSearch] = useRemoteSearch();
  const formApi = useFormApi();
  const formState = useFormState();
  const { isLoading, data, refetch } = useQuery({
    queryKey: ["getContractorList", filter],
    queryFn: () => {
      return getContractorList({
        pageNumber: 1,
        pageSize: 9999,
        filter: filter,
      });
    },
  });

  const results = useMemo<Options[]>(() => {
    if (data?.data) {
      return data?.data?.results;
    }
  }, [data]);

  return (
    <Form.Select
      field={field}
      filter
      onSearch={onSearch}
      noLabel={!Boolean(label)}
      multiple={multiple}
      label={label ?? ""}
      placeholder={placeholder ?? "选择字典"}
      className="w-full"
      rules={[{ required: isRequired, message: "此为必填项!" }]}
    >
      {(results ?? []).map((o) => (
        <Form.Select.Option
          key={o?.id ?? 0}
          value={
            valueType === "object"
              ? JSON.stringify({ id: parseInt(o?.id) ?? 0, name: o.name })
              : (parseInt(o?.id) ?? 0)
          }
        >
          {o?.name ?? ""}
        </Form.Select.Option>
      ))}
    </Form.Select>
  );
};
