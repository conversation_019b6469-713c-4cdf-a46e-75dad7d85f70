import { Form, useFormApi, useFormState } from "@douyinfe/semi-ui";
import { useQuery } from "@tanstack/react-query";
import { getDicName } from "api";
import { useRemoteSearch } from "hooks";
import { FC, useMemo } from "react";

type Options = {
  id: number;
  dicKey: number;
  dicValue: string;
};
export type DicSearchProps = {
  field: string;
  placeholder?: string;
  label?: string;
  isRequired?: boolean;
  name: string;
};

export const DicSearch: FC<DicSearchProps> = ({
  field,
  placeholder,
  label,
  isRequired,
  name: moduleType,
}) => {
  const [query, onSearch] = useRemoteSearch();
  const formApi = useFormApi();
  const formState = useFormState();
  const { isLoading, data, refetch } = useQuery({
    queryKey: ["getDicName", moduleType],
    queryFn: () => {
      return getDicName(moduleType);
    },
  });

  const results = useMemo<Options[]>(() => {
    return data?.data ?? [];
  }, [data]);

  return (
    <Form.Select
      field={field}
      filter
      onSearch={onSearch}
      noLabel={!Boolean(label)}
      label={label ?? ""}
      placeholder={placeholder ?? "选择字典"}
      className="w-full"
      rules={[{ required: isRequired, message: "此为必填项!" }]}
    >
      {(results ?? []).map((o) => (
        <Form.Select.Option value={o?.id ?? 0} key={o?.id ?? 0}>
          {o?.dicValue ?? ""}
        </Form.Select.Option>
      ))}
    </Form.Select>
  );
};
