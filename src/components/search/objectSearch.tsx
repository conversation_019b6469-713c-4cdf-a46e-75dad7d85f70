import { useState, useCallback, useMemo, FC, useEffect } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { Form, useFormApi, useFormState } from "@douyinfe/semi-ui";
import { useAtom } from "jotai";
import { getRiskObjectList, RiskObjectFilter } from "api/doubleGuard";
import { propEq, find } from "ramda";
import { useRemoteSearch } from "hooks";

export type ObjectSearchProps = {
  field: string;
  placeholder?: string;
  label?: string;
  isRequired?: boolean;
  association?: Array<string>; // 自动补全对应表单
  disabled?: boolean;
  filter?: RiskObjectFilter;
};

export const ObjectSearch: FC<ObjectSearchProps> = ({
  field,
  placeholder,
  label,
  isRequired,
  association,
  disabled,
  filter,
}) => {
  const [query, onSearch] = useRemoteSearch();
  const formApi = useFormApi();
  const formState = useFormState();
  const { isLoading, isError, error, data, refetch } = useQuery({
    queryKey: ["getRiskObjectList", query],
    queryFn: () => {
      return getRiskObjectList({
        pageNumber: 1,
        pageSize: 1000,
        query: query,
        filter,
      });
    },
  });

  const allArea = useMemo(() => {
    if (data?.data) {
      return data?.data?.results ?? [];
    }
  }, [data]);
  useEffect(() => {
    if (formState.values?.[`${field}`] && allArea?.length) {
      const item = find(propEq(formState.values[`${field}`], "id"))(allArea);
      association?.forEach((o: string) => {
        if (o === "liableDepartmentId" && item) {
          formApi.setValue(o, item?.liableDepartment?.id ?? null);
        }
        if (o === "liablePersonId" && item) {
          formApi.setValue(o, item?.liablePerson?.id ?? null);
        }
      });
    }
  }, [formState.values?.[`${field}`]]);

  return (
    <Form.Select
      field={field}
      noLabel={!Boolean(label)}
      label={label ?? ""}
      isLoading={isLoading}
      filter
      onSearch={onSearch}
      placeholder={placeholder ?? "风险分区"}
      className="w-full"
      disabled={disabled}
      rules={[{ required: isRequired, message: "此为必填项!" }]}
    >
      {(allArea ?? []).map((o) => (
        <Form.Select.Option value={parseInt(o?.id) ?? 0} key={o?.id ?? 0}>
          {o?.name ?? ""}
        </Form.Select.Option>
      ))}
    </Form.Select>
  );
};
