import { Form } from "@douyinfe/semi-ui";
import { useQuery } from "@tanstack/react-query";
import { getDepartmentList } from "api/basicInfo";
import { platformConfigAtom } from "atoms";
import { departmentDataAtom } from "atoms/basicInfo";
import { convert } from "components/tree";
import { useRemoteSearch } from "hooks";
import { useAtom } from "jotai";
import { find, propEq } from "ramda";
import { FC, useEffect, useMemo, useState } from "react";

export type DepartmentSearchProps = {
  field: string;
  placeholder?: string;
  label?: string;
  isRequired?: boolean;
  disabled?: boolean;
};

export const DepartmentSearch: FC<DepartmentSearchProps> = ({
  field,
  placeholder,
  label,
  isRequired,
  disabled = false,
}) => {
  const [query, onSearch] = useRemoteSearch();
  const [departmentData, setDepartmentData] = useAtom(departmentDataAtom);
  const [platformConfig, setPlatformConfig] = useAtom(platformConfigAtom);

  const [expandedKeys, setExpandedKeys] = useState(["0"]);
  const { isLoading, isError, error, data, refetch } = useQuery({
    queryKey: ["getDepartmentList"],
    queryFn: getDepartmentList,
  });

  useEffect(() => {
    if (data?.data?.length) {
      setDepartmentData(data?.data);
    }
  }, [data]);

  // 转换函数
  function convert_deprecated(data) {
    const result = [];
    const map = {};
    const hasIds = [];

    function traverse(items, parentKey) {
      (items ?? []).forEach((_item) => {
        let item = _item;
        if (typeof item === "number") {
          item = find(propEq(item, "id"))(data.data);
        }

        const key = parentKey ? `${parentKey}-${item.id}` : String(item.id);

        const treeItem = {
          label: item.name,
          value: item.id,
          key,
          children: [],
        };

        map[key] = treeItem;

        if (item.children) {
          traverse(item.children, key);
        }
        if (hasIds.includes(item.id)) {
          return;
        }
        if (parentKey) {
          map[parentKey].children.push(treeItem);
        } else {
          result.push(treeItem);
        }
        hasIds.push(item.id);
      });
    }

    traverse(data.data, null);
    return result;
  }

  const treeData = useMemo(() => {
    if (data?.data?.length) {
      return [
        {
          label: platformConfig?.departmentRootName ?? "公司",
          value: 0,
          key: "0",
          children: convert(data),
        },
      ];
    }
    return [];
  }, [data]);

  useEffect(() => {
    const keys = [];
    function traverse(items) {
      (items ?? []).forEach((item) => {
        keys.push(item.key);
        if (item.children) {
          traverse(item.children);
        }
      });
    }
    traverse(treeData);
    setExpandedKeys(keys);
  }, [treeData]);

  return (
    <Form.TreeSelect
      field={field}
      noLabel={!Boolean(label)}
      label={label ?? ""}
      isLoading={isLoading}
      treeData={treeData}
      filterTreeNode
      expandAll
      defaultExpandAll
      placeholder={placeholder ?? "所属部门"}
      className="w-full"
      disabled={disabled}
      rules={[{ required: isRequired, message: "此为必填项!" }]}
      dropdownStyle={{ maxHeight: '300px', overflow: 'auto' }}
    />
  );
};
