import { useState, useC<PERSON>back, useMemo, FC, useEffect } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { Form, Tag, useFormApi, useFormState } from "@douyinfe/semi-ui";
import { useAtom } from "jotai";
import { getRiskObjectList } from "api/doubleGuard";
import { propEq, find } from "ramda";
import { RISK_LEVEL_EDIT_MAP, RISK_LEVEL_MAP } from "components";

export type RiskLevelSearchProps = {
  field: string;
  placeholder?: string;
  label?: string;
  isRequired?: boolean;
  type?: "form" | "all";
};

export const RiskLevelSearch: FC<RiskLevelSearchProps> = ({
  field,
  placeholder,
  label,
  isRequired,
  type = "form",
  initValue,
}) => {
  const list = useMemo(() => {
    if (type === "form") {
      return RISK_LEVEL_EDIT_MAP;
    }
    return RISK_LEVEL_MAP;
  }, [type]);

  return (
    <Form.Select
      field={field}
      initValue={initValue}
      noLabel={!Boolean(label)}
      label={label ?? ""}
      placeholder={placeholder ?? "风险等级"}
      className="w-full"
      rules={[{ required: isRequired, message: "此为必填项!" }]}
    >
      {list.map((o) => (
        <Form.Select.Option value={parseInt(o?.id) ?? 0} key={o?.id ?? 0}>
          <Tag color={o.color} type="light">
            {o?.name ?? ""}
          </Tag>
        </Form.Select.Option>
      ))}
    </Form.Select>
  );
};
