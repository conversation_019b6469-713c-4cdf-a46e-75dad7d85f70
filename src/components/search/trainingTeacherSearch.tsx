import {
  Button,
  Form,
  Modal,
  Tag,
  Toast,
  Transfer,
  useFormApi,
  useFormState,
} from "@douyinfe/semi-ui";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { getTeacherList } from "api";
import { getEmployeeList } from "api/basicInfo";
import {
  postRedirectDangerAcceptor,
  postRedirectDangerDelayApprover,
  postRedirectDangerEvaluate,
  postRedirectDangerRectifier,
  postRedirectSnapAudit,
} from "api/doubleGuard";
import { teacherAtoms } from "atoms";
import { AuthorizeRedirectType, dangerEditModalAtom } from "atoms/doubleGuard";
import {
  BASIC_GENDER_MAP,
  CT_TEACHER_TYPE_MAP,
  EDUCATION_MAP,
} from "components/enum";
import { useRemoteSearch } from "hooks";
import { useResetAtom } from "jotai/utils";
import { find, propEq, type } from "ramda";
import { FC, useCallback, useEffect, useMemo, useState } from "react";
import { listPageSizeWithoutPaging } from "utils";

export type TrainingTeacherSearchProps = {
  field: string;
  placeholder?: string;
  label?: string;
  isRequired?: boolean;
  filter?: boolean;
  multiple?: boolean;
  association?: string; // 自动补全对应表单
  pickKeys?: string | Array<number>; // 取部分数据
  valueType?: "object";
  max?: number;
  position?:
    | "top"
    | "topLeft"
    | "topRight"
    | "left"
    | "leftTop"
    | "leftBottom"
    | "right"
    | "rightTop"
    | "rightBottom"
    | "bottom"
    | "bottomLeft"
    | "bottomRight";
  callback?: (arg: any) => void;
};

export const TrainingTeacherSearch: FC<TrainingTeacherSearchProps> = ({
  field,
  placeholder,
  label,
  isRequired,
  association,
  filter,
  multiple,
  initValue,
  value,
  disabled,
  pickKeys,
  valueType,
  max = 99,
  position,
  callback,
}) => {
  const [query, onSearch] = useRemoteSearch();
  const formApi = useFormApi();
  const formState = useFormState();
  const { isLoading, isError, error, data, refetch } = useQuery({
    queryKey: ["list" + teacherAtoms.entity, query, filter], // user-defined code here
    queryFn: () => {
      return getTeacherList({
        // user-defined code here
        pageNumber: 1,
        pageSize: listPageSizeWithoutPaging,
        query: query,
        filter: {
          // status: 2,
          ...filter,
        },
      });
    },
  });

  const allTeacher = useMemo(() => {
    // user-defined code here
    if (data?.data) {
      const results = data?.data?.results ?? [];
      if (pickKeys?.length) {
        // 取部分数据
        const pick_key = [];
        if (type(pickKeys) === "Array") {
          pickKeys.forEach((i: any) => {
            pick_key.push(i.id);
          });
          // type(pickKeys) === 'String' ? JSON.parse(pickKeys || []) : pickKeys
        }
        return results.filter((o: any) => pick_key.includes(o.id));
      }
      return results;
    }
  }, [data, pickKeys]);

  useEffect(() => {
    if (formState.values?.[`${field}`] && allTeacher?.length && association) {
      const item = find(propEq(formState.values[`${field}`], "id"))(allTeacher);
      formApi.setValue(association, item?.id ?? null);
    }
  }, [formState.values?.[`${field}`]]);

  useEffect(() => {
    if (formState.values?.[`${field}`] && allTeacher?.length) {
      const item = find(propEq(formState.values[`${field}`], "id"))(allTeacher);
      callback?.(item);
    }
  }, [formState.values?.[`${field}`]]);

  // user-defined code here
  const renderOption = (item) => {
    return (
      <Form.Select.Option
        name={item.name}
        value={
          valueType === "object"
            ? JSON.stringify({ id: parseInt(item?.id) ?? 0, name: item.name })
            : (parseInt(item?.id) ?? 0)
        }
        key={item.id}
      >
        <div className="flex flex-col items-start">
          <div className="text-[14px] flex items-center gap-2">
            {item.name}
            {item.gender === BASIC_GENDER_MAP[0].id ? (
              <Tag color="blue" type="light">
                男
              </Tag>
            ) : item.gender === BASIC_GENDER_MAP[1].id ? (
              <Tag color="red" type="light">
                女
              </Tag>
            ) : null}
            {item.source === CT_TEACHER_TYPE_MAP[0].id ? (
              <Tag color="blue" type="light">
                本厂
              </Tag>
            ) : item.soure === CT_TEACHER_TYPE_MAP[1].id ? (
              <Tag color="red" type="light">
                外聘
              </Tag>
            ) : null}
          </div>
          <div className="text-[color:rgba(var(--color-text-2),1)] text-[12px] leading-[16px]">
            {item.education === EDUCATION_MAP[0].id
              ? "小学"
              : item.education === EDUCATION_MAP[1].id
                ? "初中"
                : item.education === EDUCATION_MAP[2].id
                  ? "高中"
                  : item.education === EDUCATION_MAP[3].id
                    ? "大专"
                    : item.education === EDUCATION_MAP[4].id
                      ? "本科"
                      : item.education === EDUCATION_MAP[5].id
                        ? "研究生"
                        : "-"}
            {item.major && `${item.major} / `}
            {item.goodAt && `${item.goodAt} / `}
          </div>
        </div>
      </Form.Select.Option>
    );
  };

  const renderSelectedItem = (optionNode) => optionNode?.name;

  const renderMultipleWithCustomTag = (optionNode, { onClose }) => {
    const content = (
      <Tag closable={true} onClose={onClose} size="small">
        {optionNode?.name}
      </Tag>
    );
    return {
      isRenderInTag: false,
      content,
    };
  };

  return (
    <Form.Select
      initValue={initValue}
      value={value}
      field={field}
      noLabel={!Boolean(label)}
      label={label ?? ""}
      filter
      remote
      isLoading={isLoading}
      onSearch={onSearch}
      multiple={multiple}
      max={max}
      placeholder={placeholder ?? "请选择人员"}
      className="w-full"
      disabled={disabled}
      position={position ?? "bottom"}
      rules={[{ required: isRequired, message: "此为必填项!" }]}
      renderSelectedItem={
        multiple ? renderMultipleWithCustomTag : renderSelectedItem
      }
    >
      {(allTeacher ?? []).map(renderOption)}
    </Form.Select>
  );
};

export type TrainingPaperRedirectProps = {
  id: number;
  type: AuthorizeRedirectType;
  title: string;
  initValue: number[];
};

export const TrainingPaperRedirect: FC<TrainingPaperRedirectProps> = ({
  renderType,
  onClose,
  btnType,
  id,
  type,
  title,
  initValue,
}) => {
  const [visible, setVisible] = useState(false);

  const [query, onSearch] = useRemoteSearch();
  const queryClient = useQueryClient();

  const { isLoading, isError, error, data, refetch } = useQuery({
    queryKey: ["getEmployeeList", query],
    queryFn: () => {
      return getEmployeeList({
        pageNumber: 1,
        pageSize: 1000,
        query: query,
        filter: {
          status: 1,
        },
      });
    },
  });

  const allEmployee = useMemo(() => {
    if (data?.data) {
      return (data?.data?.results ?? []).map((employee) => ({
        key: employee.id,
        value: employee.id,
        label: employee.name,
      }));
    }
    return [];
  }, [data]);

  const [value, setValue] = useState(initValue);

  const resetDangerEdit = useResetAtom(dangerEditModalAtom);
  const onSuccess = useCallback(
    async (res) => {
      if (res?.code === 0) {
        setVisible(false);
        let opts = {
          content: `操作成功!`,
          duration: 2,
        };
        Toast.success(opts);
        queryClient.invalidateQueries({ queryKey: ["getDangerList"] });
        if (typeof onClose === "function") {
          onClose();
        }
        resetDangerEdit();
      }
    },
    [resetDangerEdit],
  );
  const handleRedirectDangerEvaluate = useMutation({
    mutationFn: postRedirectDangerEvaluate,
    onSuccess,
  });
  const handleRedirectDangerDelayApprover = useMutation({
    mutationFn: postRedirectDangerDelayApprover,
    onSuccess,
  });
  const handleRedirectDangerRectifier = useMutation({
    mutationFn: postRedirectDangerRectifier,
    onSuccess,
  });
  const handleRedirectDangerAcceptor = useMutation({
    mutationFn: postRedirectDangerAcceptor,
    onSuccess,
  });
  const handleRedirectSnapAudit = useMutation({
    mutationFn: postRedirectSnapAudit,
    onSuccess,
  });

  const handleAuthorizeRedirect = () => {
    if (type === undefined || !id) {
      return;
    }
    switch (type) {
      case AuthorizeRedirectType.DangerEvaluator: {
        handleRedirectDangerEvaluate.mutate({
          id: id,
          evaluatorCandIds: JSON.stringify(value),
        });
        break;
      }
      case AuthorizeRedirectType.DangerDelayApprover: {
        handleRedirectDangerDelayApprover.mutate({
          id: id,
          delayApprovalCandIds: JSON.stringify(value),
        });
        break;
      }
      case AuthorizeRedirectType.DangerRectifier: {
        handleRedirectDangerRectifier.mutate({
          id: id,
          rectifierCandIds: JSON.stringify(value),
        });
        break;
      }
      case AuthorizeRedirectType.DangerAcceptor: {
        handleRedirectDangerAcceptor.mutate({
          id: id,
          acceptorCandIds: JSON.stringify(value),
        });
        break;
      }
      case AuthorizeRedirectType.SnapAudit: {
        handleRedirectSnapAudit.mutate({
          id: id,
          auditorIds: JSON.stringify(value),
        });
        break;
      }
    }
  };

  const isConfirmLoading =
    handleRedirectDangerEvaluate.isLoading ||
    handleRedirectDangerDelayApprover.isLoading ||
    handleRedirectDangerRectifier.isLoading ||
    handleRedirectDangerAcceptor.isLoading ||
    handleRedirectSnapAudit.isLoading;

  const modal = (
    <Modal
      title={title}
      visible={renderType === "modal" ? typeof id === "number" : visible}
      width={800}
      maskClosable={false}
      onCancel={() => {
        if (typeof onClose === "function") {
          onClose();
        }
        setVisible(false);
      }}
      footer={<div />}
      centered
    >
      <Transfer
        style={{ width: "90%", height: 416, margin: "0 auto 20px" }}
        dataSource={allEmployee}
        value={value}
        onChange={(values) => setValue(values)}
      />
      <div className="flex gap-2 justify-end">
        <button
          className="btn btn-sm rounded"
          disabled={isLoading || isConfirmLoading}
          onClick={() => {
            if (typeof onClose === "function") {
              onClose();
            }
            setVisible(false);
          }}
        >
          取消
        </button>
        <button
          className="btn rounded btn-primary btn-sm"
          disabled={isLoading || isConfirmLoading}
          onClick={handleAuthorizeRedirect}
        >
          确定
        </button>
      </div>
    </Modal>
  );

  if (renderType === "modal") {
    return modal;
  }
  return (
    <>
      {btnType === "text" ? (
        <span className="w-full text-center" onClick={() => setVisible(true)}>
          转办
        </span>
      ) : (
        <Button type="primary" onClick={() => setVisible(true)}>
          转办
        </Button>
      )}
      {modal}
    </>
  );
};
