import { useMemo, FC } from "react";
import { useQuery } from "@tanstack/react-query";
import { Form, useFormApi, useFormState } from "@douyinfe/semi-ui";
import { getCertificateList } from "api";
import { useRemoteSearch } from "hooks";

type Options = {
  id: number;
  name: string;
};
export type CertificateSearchProps = {
  field: string;
  placeholder?: string;
  label?: string;
  isRequired?: boolean;
  "field-key"?: string;
};

export const CertificateSearch: FC<CertificateSearchProps> = ({
  field,
  placeholder,
  label,
  isRequired,
}) => {
  const [query, onSearch] = useRemoteSearch();
  const formApi = useFormApi();
  const formState = useFormState();
  const { isLoading, data, refetch } = useQuery({
    queryKey: ["getCertificateListSelect"],
    queryFn: () => {
      return getCertificateList({
        pageNumber: 1,
        pageSize: 9999,
      });
    },
  });

  const results = useMemo<Options[]>(() => {
    if (data?.data) {
      return data?.data?.results;
    }
  }, [data]);

  return (
    <Form.Select
      field={field}
      filter
      onSearch={onSearch}
      noLabel={!Boolean(label)}
      label={label ?? ""}
      placeholder={placeholder ?? "请选择"}
      className="w-full"
      multiple
      max={1}
      rules={[{ required: isRequired, message: "此为必填项!" }]}
    >
      {(results ?? []).map((o) => (
        <Form.Select.Option
          value={JSON.stringify({
            id: o?.employee?.id ?? 0,
            label: o?.employee?.name,
          })}
          key={o?.id ?? 0}
        >
          {o?.employee?.name ?? ""}-
          {o?.certificateTypeValue?.dicValue ?? "暂无证书"}
        </Form.Select.Option>
      ))}
    </Form.Select>
  );
};
