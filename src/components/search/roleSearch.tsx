import { useState, useCallback, useMemo, FC } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { Form } from "@douyinfe/semi-ui";
import { useAtom } from "jotai";
import { getRoleList } from "api/basicInfo";
import { useRemoteSearch } from "hooks";

export type RoleSearchProps = {
  field: string;
  placeholder?: string;
  label?: string;
  isRequired?: boolean;
};

export const RoleSearch: FC<RoleSearchProps> = ({
  field,
  placeholder,
  isRequired,
  label,
}) => {
  const [query, onSearch] = useRemoteSearch();
  const { isLoading, data } = useQuery({
    queryKey: ["getRoleList", query],
    queryFn: () =>
      getRoleList({
        pageNumber: 1,
        pageSize: 1000,
        query: query,
      }),
  });

  const allPosition = useMemo(() => {
    if (data?.data) {
      return data?.data?.results ?? [];
    }
  }, [data]);

  return (
    <Form.Select
      field={field}
      noLabel={!Boolean(label)}
      label={label ?? ""}
      isLoading={isLoading}
      filter
      onSearch={onSearch}
      placeholder={placeholder ?? "岗位"}
      className="w-full"
      rules={[{ required: isRequired, message: "此为必填项!" }]}
    >
      {(allPosition ?? []).map((o) => (
        <Form.Select.Option value={o?.id ?? 0} key={o?.id ?? 0}>
          {o?.name ?? ""}
        </Form.Select.Option>
      ))}
    </Form.Select>
  );
};
