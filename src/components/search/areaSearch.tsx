import { Form, useFormApi, useFormState } from "@douyinfe/semi-ui";
import { useQuery } from "@tanstack/react-query";
import { getAreaList } from "api/doubleGuard";
import { useRemoteSearch } from "hooks";
import { find, propEq } from "ramda";
import { FC, useEffect, useMemo } from "react";

export type AreaSearchProps = {
  field: string;
  placeholder?: string;
  label?: string;
  isRequired?: boolean;
  multiple?: boolean;
  association?: Array<string>; // 自动补全对应表单
  valueType?: string;
  max?: number;
};

export const AreaSearch: FC<AreaSearchProps> = ({
  field,
  placeholder,
  label,
  isRequired,
  association,
  valueType,
  multiple = false,
  max,
}) => {
  const [query, onSearch] = useRemoteSearch();
  const formApi = useFormApi();
  const formState = useFormState();
  const { isLoading, isError, error, data, refetch } = useQuery({
    queryKey: ["getAreaList", query],
    queryFn: () => {
      return getAreaList({
        pageNumber: 1,
        pageSize: 1000,
        query: query,
        filter: {},
      });
    },
  });

  const allArea = useMemo(() => {
    if (data?.data) {
      return data?.data?.results ?? [];
    }
  }, [data]);

  useEffect(() => {
    if (formState.values?.[`${field}`] && allArea?.length) {
      const item = find(propEq(formState.values[`${field}`], "id"))(allArea);
      association?.forEach((o: string) => {
        if (o === "liableDepartmentId" && item) {
          formApi.setValue(o, item?.liableDepartment?.id ?? null);
        }
        if (o === "liablePersonId" && item) {
          formApi.setValue(o, item?.liablePerson?.id ?? null);
        }
      });
    }
  }, [formState.values?.[`${field}`]]);

  return (
    <Form.Select
      field={field}
      loading={isLoading}
      filter
      onSearch={onSearch}
      noLabel={!Boolean(label)}
      label={label ?? ""}
      placeholder={placeholder ?? "风险分区"}
      className="w-full"
      multiple={multiple}
      max={max}
      rules={[{ required: isRequired, message: "此为必填项!" }]}
    >
      {(allArea ?? []).map((o) => (
        <Form.Select.Option
          //value={parseInt(o?.id) ?? 0}
          value={
            (valueType ?? "") === "object"
              ? JSON.stringify({ id: parseInt(o?.id) ?? 0, name: o.name })
              : (parseInt(o?.id) ?? 0)
          }
          key={o?.id ?? 0}
        >
          {o?.name ?? ""}
        </Form.Select.Option>
      ))}
    </Form.Select>
  );
};
