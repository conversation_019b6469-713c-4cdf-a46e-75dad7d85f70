import {
  Button,
  Form,
  Modal,
  Tag,
  Toast,
  Transfer,
  useFormApi,
  useFormState,
} from "@douyinfe/semi-ui";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { getContractorEmployeeList, getEmployeeList } from "api/basicInfo";
import {
  postRedirectDangerAcceptor,
  postRedirectDangerDelayApprover,
  postRedirectDangerEvaluate,
  postRedirectDangerRectifier,
  postRedirectSnapAudit,
} from "api/doubleGuard";
import { AuthorizeRedirectType, dangerEditModalAtom } from "atoms/doubleGuard";
import { useRemoteSearch } from "hooks";
import { useResetAtom } from "jotai/utils";
import { find, propEq, type } from "ramda";
import { FC, useCallback, useEffect, useMemo, useState } from "react";
import { listPageSizeWithoutPaging } from "utils";

export type EmployeeSearchProps = {
  field: string;
  placeholder?: string;
  label?: string;
  isRequired?: boolean;
  filter?: boolean;
  multiple?: boolean; // 是否多选
  association?: string; // 自动补全对应表单
  pickKeys?: string | Array<number>; // 选中数据的key
  valueType?: "object";
  max?: number;
  callback?: (arg: any) => void;
  initValue?: number | number[]; // 初始全集
  value?: number | number[];
  disabled?: boolean;
  serviceRange?: number[];
  realField?: string;
};

export const EmployeeSearch: FC<EmployeeSearchProps> = ({
  field,
  placeholder,
  label,
  isRequired,
  association,
  filter,
  multiple,
  initValue,
  value,
  disabled,
  pickKeys,
  valueType,
  max = 99,
  callback,
  serviceRange = [1],
  realField,
}) => {
  const [query, onSearch] = useRemoteSearch();
  const formApi = useFormApi();
  const formState = useFormState();
  const { isLoading, isError, error, data, refetch } = useQuery({
    queryKey: ["getEmployeeList", query, filter],
    queryFn: () => {
      return getEmployeeList({
        pageNumber: 1,
        pageSize: 1000,
        query: query,
        filter: {
          status: 1,
          ...filter,
        },
      });
    },
  });

  const allEmployee = useMemo(() => {
    if (data?.data) {
      const results = data?.data?.results ?? [];
      // 添加_type字段: 1: 部门 2: 工作组 3: 承包商
      // 添加type字段: 1: 内部人员 2: 承包商
      results.forEach((o) => {
        o._type = 1;
        o.type = 1;
      });
      if (pickKeys?.length) {
        // 取部分数据
        const pick_key = [];
        if (type(pickKeys) === "Array") {
          pickKeys.forEach((i: any) => {
            if (i.type && i.type === 1) {
              // 内部人员
              pick_key.push(i.id);
            }
          });
          // type(pickKeys) === 'String' ? JSON.parse(pickKeys || []) : pickKeys
        }
        return results.filter((o: any) => pick_key.includes(o.id));
      }
      return results;
    }
  }, [data, pickKeys]);

  const {
    isLoading: contractorEmployeeIsLoading,
    isError: contractorEmployeeIsError,
    error: contractorEmployeeError,
    data: contractorEmployeeData,
    refetch: contractorEmployeeRefetch,
  } = useQuery({
    queryKey: ["getContractorEmployeeList"],
    queryFn: () =>
      getContractorEmployeeList({
        pageNumber: 1,
        pageSize: listPageSizeWithoutPaging,
      }),
  });
  const allContractorEmployee = useMemo(() => {
    if (!serviceRange.includes(3)) {
      console.debug(
        "serviceRange is not include 3, no need to fetch contractorEmployeeData",
      );
      return [];
    }
    if (contractorEmployeeData?.data) {
      const results = contractorEmployeeData?.data?.results ?? [];
      // 添加_type字段: 1: 部门 2: 工作组 3: 承包商
      // 添加type字段: 1: 内部人员 2: 承包商
      results.forEach((o) => {
        o._type = 3;
        o.type = 2;
      });
      if (pickKeys?.length) {
        // 取部分数据
        const pick_key = [];
        if (type(pickKeys) === "Array") {
          pickKeys.forEach((i: any) => {
            if (i.type && i.type === 2) {
              // 2 代表承包商
              pick_key.push(i.id);
            }
          });
          // type(pickKeys) === 'String' ? JSON.parse(pickKeys || []) : pickKeys
        }
        const pickRes = results.filter((o: any) => pick_key.includes(o.id));
        return pickRes;
      }
      return results;
    } else {
      console.debug("contractorEmployeeData is null");
    }
  }, [contractorEmployeeData, pickKeys]);

  useEffect(() => {
    if (serviceRange.length === 1 && serviceRange[0] === 1) {
      if (
        formState.values?.[`${field}`] &&
        allEmployee?.length &&
        association
      ) {
        const item = find(propEq(formState.values[`${field}`], "id"))(
          allEmployee,
        );
        formApi.setValue(association, item?.id ?? null);
      }
    }
  }, [formState.values?.[`${field}`]]);

  // console.log(formState.values);
  // console.log(formState.values?.[jobProcessesInfo]);
  /* console.debug(
    formState.values?.[`${field}`],
    "formState.values?.[`${field}`]"
  ); */

  // 回调函数，获取选中的人员信息，用于联动，在应急队伍modal中使用
  useEffect(() => {
    if (serviceRange.length === 1 && serviceRange[0] === 1) {
      if (formState.values?.[`${field}`] && allEmployee?.length) {
        const item = find(propEq(formState.values[`${field}`], "id"))(
          allEmployee,
        );
        callback?.(item);
      }
    }
  }, [formState.values?.[`${field}`]]);

  // 包含承包商人员，需要将id-name-type字符串转换为对象，set到realField给原先的form
  useEffect(() => {
    // console.debug(formState.values?.[`${field}`]);
    if (serviceRange.length === 1 && serviceRange[0] === 1) {
      console.debug("滚一边去");
      return;
    }

    if (formApi.getValue(field)) {
      const valueList = formApi.getValue(field) ?? [];
      if (type(valueList) !== "Array") return;
      const realValueList = [];
      valueList?.forEach((v) => {
        const values = v.split("-");
        if (values.length < 3) {
          console.error("values length < 3", values);
          return;
        }
        const obj = {
          id: parseInt(values[0]),
          name: values[1],
          type: parseInt(values[2]),
        };
        realValueList.push(obj);
      });
      console.debug("realValueList", realValueList);
      if (realField) formApi.setValue(realField, realValueList);
    }
  }, [JSON.stringify(formApi.getValue(field))]);

  const renderOption = (item) => {
    let key = item.id;
    if (item.employeeId) {
      key = `${item.id}+${item.employeeId}`; // 有承包商人员的情况下，id可能重复，需要加上employeeId
    }
    const objectItem = JSON.stringify({
      id: parseInt(item?.id) ?? 0,
      name: item.name,
      type: item._type,
    });
    return (
      <Form.Select.Option
        name={item.name}
        value={
          serviceRange.length === 1 && serviceRange[0] === 1
            ? valueType === "object"
              ? objectItem
              : (parseInt(item?.id) ?? 0)
            : `${item.id}-${item.name}-${item.type}` // serviceRange包含3，需要将id-name-type字符串作为value，好set到realField
        }
        key={key}
      >
        <div className="flex flex-col items-start">
          <div className="text-[14px] flex items-center gap-2">
            {item.name}
            {item.gender === 1 ? (
              <Tag color="blue" type="light">
                男
              </Tag>
            ) : item.gender === 2 ? (
              <Tag color="red" type="light">
                女
              </Tag>
            ) : null}
            <Tag>角色: {item.role?.name && `${item.role?.name} `}</Tag>
          </div>
          <div className="text-[color:rgba(var(--color-text-2),1)] text-[12px] leading-[16px]">
            {/* 内部人员 */}
            {item.employeeId}
            {item.department?.name && `${item.department?.name} / `}
            {item.position?.name && `${item.position?.name} / `}
            {/* 承包商人员 */}
            {item.idNumber}
            {item.contractor?.name && `${item.contractor?.name} / `}
            {item.comment && `${item.comment} / `}
          </div>
        </div>
      </Form.Select.Option>
    );
  };

  const renderSelectedItem = (optionNode) => optionNode?.name;

  const renderMultipleWithCustomTag = (optionNode, { onClose }) => {
    const content = (
      <Tag closable={true} onClose={onClose} size="small">
        {optionNode?.name}
      </Tag>
    );
    return {
      isRenderInTag: false,
      content,
    };
  };

  return (
    <Form.Select
      initValue={initValue}
      value={value}
      field={field}
      noLabel={!Boolean(label)}
      label={label ?? ""}
      filter
      remote
      isLoading={isLoading}
      onSearch={onSearch}
      multiple={multiple}
      max={max}
      placeholder={placeholder ?? "请选择人员"}
      className="w-full"
      disabled={disabled}
      rules={[{ required: isRequired, message: "此为必填项!" }]}
      renderSelectedItem={
        multiple ? renderMultipleWithCustomTag : renderSelectedItem
      }
    >
      {(allEmployee?.concat(allContractorEmployee) ?? []).map(renderOption)}
    </Form.Select>
  );
};

export type EmployRedirectProps = {
  id: number;
  type: AuthorizeRedirectType;
  title: string;
  initValue: number[];
};

export const EmployRedirect: FC<EmployRedirectProps> = ({
  renderType,
  onClose,
  btnType,
  id,
  type,
  title,
  initValue,
}) => {
  const [visible, setVisible] = useState(false);

  const [query, onSearch] = useRemoteSearch();
  const queryClient = useQueryClient();

  const { isLoading, isError, error, data, refetch } = useQuery({
    queryKey: ["getEmployeeList", query],
    queryFn: () => {
      return getEmployeeList({
        pageNumber: 1,
        pageSize: 1000,
        query: query,
        filter: {
          status: 1,
        },
      });
    },
  });

  const allEmployee = useMemo(() => {
    if (data?.data) {
      return (data?.data?.results ?? []).map((employee) => ({
        key: employee.id,
        value: employee.id,
        label: employee.name,
      }));
    }
    return [];
  }, [data]);

  const [value, setValue] = useState(initValue);

  const resetDangerEdit = useResetAtom(dangerEditModalAtom);
  const onSuccess = useCallback(
    async (res) => {
      if (res?.code === 0) {
        setVisible(false);
        let opts = {
          content: `操作成功!`,
          duration: 2,
        };
        Toast.success(opts);
        queryClient.invalidateQueries({ queryKey: ["getDangerList"] });
        if (typeof onClose === "function") {
          onClose();
        }
        resetDangerEdit();
      }
    },
    [resetDangerEdit],
  );
  const handleRedirectDangerEvaluate = useMutation({
    mutationFn: postRedirectDangerEvaluate,
    onSuccess,
  });
  const handleRedirectDangerDelayApprover = useMutation({
    mutationFn: postRedirectDangerDelayApprover,
    onSuccess,
  });
  const handleRedirectDangerRectifier = useMutation({
    mutationFn: postRedirectDangerRectifier,
    onSuccess,
  });
  const handleRedirectDangerAcceptor = useMutation({
    mutationFn: postRedirectDangerAcceptor,
    onSuccess,
  });
  const handleRedirectSnapAudit = useMutation({
    mutationFn: postRedirectSnapAudit,
    onSuccess,
  });

  const handleAuthorizeRedirect = () => {
    if (type === undefined || !id) {
      return;
    }
    switch (type) {
      case AuthorizeRedirectType.DangerEvaluator: {
        handleRedirectDangerEvaluate.mutate({
          id: id,
          evaluatorCandIds: JSON.stringify(value),
        });
        break;
      }
      case AuthorizeRedirectType.DangerDelayApprover: {
        handleRedirectDangerDelayApprover.mutate({
          id: id,
          delayApprovalCandIds: JSON.stringify(value),
        });
        break;
      }
      case AuthorizeRedirectType.DangerRectifier: {
        handleRedirectDangerRectifier.mutate({
          id: id,
          rectifierCandIds: JSON.stringify(value),
        });
        break;
      }
      case AuthorizeRedirectType.DangerAcceptor: {
        handleRedirectDangerAcceptor.mutate({
          id: id,
          acceptorCandIds: JSON.stringify(value),
        });
        break;
      }
      case AuthorizeRedirectType.SnapAudit: {
        handleRedirectSnapAudit.mutate({
          id: id,
          auditorIds: JSON.stringify(value),
        });
        break;
      }
    }
  };

  const isConfirmLoading =
    handleRedirectDangerEvaluate.isLoading ||
    handleRedirectDangerDelayApprover.isLoading ||
    handleRedirectDangerRectifier.isLoading ||
    handleRedirectDangerAcceptor.isLoading ||
    handleRedirectSnapAudit.isLoading;

  const modal = (
    <Modal
      title={title}
      visible={renderType === "modal" ? typeof id === "number" : visible}
      width={800}
      maskClosable={false}
      onCancel={() => {
        if (typeof onClose === "function") {
          onClose();
        }
        setVisible(false);
      }}
      footer={<div />}
      centered
    >
      <Transfer
        style={{ width: "90%", height: 416, margin: "0 auto 20px" }}
        dataSource={allEmployee}
        value={value}
        onChange={(values) => setValue(values)}
      />
      <div className="flex gap-2 justify-end">
        <button
          className="btn btn-sm rounded"
          disabled={isLoading || isConfirmLoading}
          onClick={() => {
            if (typeof onClose === "function") {
              onClose();
            }
            setVisible(false);
          }}
        >
          取消
        </button>
        <button
          className="btn rounded btn-primary btn-sm"
          disabled={isLoading || isConfirmLoading}
          onClick={handleAuthorizeRedirect}
        >
          确定
        </button>
      </div>
    </Modal>
  );

  if (renderType === "modal") {
    return modal;
  }
  return (
    <>
      {btnType === "text" ? (
        <span className="w-full text-center" onClick={() => setVisible(true)}>
          转办
        </span>
      ) : (
        <Button type="primary" onClick={() => setVisible(true)}>
          转办
        </Button>
      )}
      {modal}
    </>
  );
};
