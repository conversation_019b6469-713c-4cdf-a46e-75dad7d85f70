import { Form } from "@douyinfe/semi-ui";
import { useQuery } from "@tanstack/react-query";
import { getPositionList } from "api/basicInfo";
import { useRemoteSearch } from "hooks";
import { FC, useMemo } from "react";

export type PositionSearchProps = {
  field: string;
  placeholder?: string;
  label?: string;
  isRequired?: boolean;
  multiple?: boolean;
};

export const PositionSearch: FC<PositionSearchProps> = ({
  field,
  placeholder,
  isRequired,
  label,
  multiple,
}) => {
  const [query, onSearch] = useRemoteSearch();
  const { isLoading, isError, error, data, refetch } = useQuery({
    queryKey: ["getPositionList", query],
    queryFn: () =>
      getPositionList({
        pageNumber: 1,
        pageSize: 1000,
        query: query,
      }),
  });

  const allPosition = useMemo(() => {
    if (data?.data) {
      return data?.data?.results ?? [];
    }
  }, [data]);

  return (
    <Form.Select
      field={field}
      noLabel={!Boolean(label)}
      label={label ?? ""}
      isLoading={isLoading}
      filter
      multiple={multiple}
      onSearch={onSearch}
      placeholder={placeholder ?? "岗位"}
      className="w-full"
      rules={[{ required: isRequired, message: "此为必填项!" }]}
    >
      {(allPosition ?? []).map((o) => (
        <Form.Select.Option value={o?.id ?? 0} key={o?.id ?? 0}>
          {o?.name ?? ""}
        </Form.Select.Option>
      ))}
    </Form.Select>
  );
};
