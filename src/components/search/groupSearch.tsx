import { useState, useCallback, useMemo, FC, useEffect } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { Form, useFormApi, useFormState } from "@douyinfe/semi-ui";
import { useAtom } from "jotai";
import { getGroupList } from "api/basicInfo";
import { propEq, find } from "ramda";
import { useRemoteSearch } from "hooks";

export type GroupSearchProps = {
  field: string;
  placeholder?: string;
  label?: string;
  isRequired?: boolean;
  filter?: boolean;
  multiple?: boolean;
};

export const GroupSearch: FC<GroupSearchProps> = ({
  field,
  placeholder,
  label,
  isRequired,
  association,
  filter,
  multiple,
}) => {
  const [query, onSearch] = useRemoteSearch();
  const formApi = useFormApi();
  const formState = useFormState();
  const { isLoading, isError, error, data, refetch } = useQuery({
    queryKey: ["group", query],
    queryFn: () => {
      return getGroupList({
        pageNumber: 1,
        pageSize: 1000,
        query: query,
      });
    },
  });

  const dataSource = useMemo(() => {
    if (data?.data) {
      return data?.data?.results ?? [];
    }
  }, [data]);

  return (
    <Form.Select
      field={field}
      noLabel={!Boolean(label)}
      label={label ?? ""}
      filter={filter}
      isLoading={isLoading}
      onSearch={onSearch}
      multiple={multiple}
      placeholder={placeholder ?? "请选择工作组"}
      className="w-full"
      rules={[{ required: isRequired, message: "此为必填项!" }]}
    >
      {(dataSource ?? []).map((o) => (
        <Form.Select.Option value={parseInt(o?.id) ?? 0} key={o?.id}>
          {o?.name ?? ""} <span className="hidden">{o?.id}</span>
        </Form.Select.Option>
      ))}
    </Form.Select>
  );
};
