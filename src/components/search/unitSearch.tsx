import { useState, useCallback, useMemo, FC, useEffect } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { Form, useFormApi, useFormState } from "@douyinfe/semi-ui";
import { useAtom } from "jotai";
import { getRiskUnitList } from "api/doubleGuard";
import { propEq, find } from "ramda";
import { useRemoteSearch } from "hooks";

export type UnitSearchProps = {
  field: string;
  placeholder?: string;
  label?: string;
  isRequired?: boolean;
  association?: Array<string>; // 自动补全对应表单
  where?: string; // 查询约束
  disabled?: boolean;
};

export const UnitSearch: FC<UnitSearchProps> = ({
  field,
  placeholder,
  label,
  isRequired,
  association,
  where,
  disabled,
}) => {
  const [query, onSearch] = useRemoteSearch();
  const formApi = useFormApi();
  const formState = useFormState();
  const whereQuery = formState?.values?.[`${where}`] ?? null;

  const { isLoading, isError, error, data, refetch } = useQuery({
    queryKey: ["getRiskUnitList", whereQuery ?? "", query],
    queryFn: (params) => {
      return getRiskUnitList({
        pageNumber: 1,
        pageSize: 1000,
        query: query,
        filter: {
          [where]: whereQuery,
        },
      });
    },
  });

  const allArea = useMemo(() => {
    if (where && !whereQuery) {
      return [];
    }
    if (data?.data) {
      return data?.data?.results ?? [];
    }
    return [];
  }, [where, whereQuery, data]);

  useEffect(() => {
    if (
      !isLoading &&
      ((!allArea.length && formState.values[field] !== undefined) ||
        !allArea.some((o: any) => o.id === formState.values[field]))
    ) {
      formApi.setValue(field, undefined);
    }
  }, [allArea, isLoading, formState.values[field], field]);

  useEffect(() => {
    if (formState.values?.[`${field}`] && allArea?.length) {
      const item = find(propEq(formState.values[`${field}`], "id"))(allArea);
      association?.forEach((o: string) => {
        if (o === "liableDepartmentId" && item) {
          formApi.setValue(o, item?.liableDepartment?.id ?? null);
        }
        if (o === "liablePersonId" && item) {
          formApi.setValue(o, item?.liablePerson?.id ?? null);
        }
      });
    }
  }, [formState.values?.[`${field}`]]);

  return (
    <Form.Select
      field={field}
      noLabel={!Boolean(label)}
      label={label ?? ""}
      isLoading={isLoading}
      filter
      onSearch={onSearch}
      placeholder={placeholder ?? "风险分区"}
      className="w-full"
      disabled={disabled || !allArea.length}
      rules={[{ required: isRequired, message: "此为必填项!" }]}
    >
      {(allArea ?? []).map((o) => (
        <Form.Select.Option value={parseInt(o?.id) ?? 0} key={o?.id ?? 0}>
          {o?.name ?? ""}
        </Form.Select.Option>
      ))}
    </Form.Select>
  );
};
