import { Table } from "@douyinfe/semi-ui";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { TableConfig } from "components/tableConfig";
import { useBtnHooks } from "hooks";
import { useTableConfig } from "hooks/useTableConfig";
import { FC, useMemo, useState } from "react";
import { useLoaderData, useLocation } from "react-router-dom";

type ListProps = {
  entity: string;
  entityCName: string;
  columnsAtom: any;
  queryApi: any;
};

// 相对于List组件，SimpleList组件的功能更加简单，只读，没有新增、编辑、删除，筛选等功能
export const SimpleList: FC<ListProps> = ({
  entity,
  entityCName,
  columnsAtom,
  queryApi,
  ...restProps
}) => {
  const queryClient = useQueryClient();
  const queryKey = "list" + entity;

  const loderData = useLoaderData();
  const { pathname } = useLocation();
  const genBtn = useBtnHooks(loderData, pathname);

  //Atoms - 使用新的useTableConfig hook
  const [_columns, setColumns] = useTableConfig(columnsAtom, entity);
  const [configModal, setConfigModal] = useState(false);

  //Apis
  const { isLoading, data, refetch } = useQuery({
    queryKey: [queryKey],
    queryFn: () => queryApi(),
  });
  const dataSource = useMemo(() => data?.data ?? {}, [data?.data]);
  const result = useMemo(() => dataSource?.results ?? [], [dataSource]);

  /* const handlePageChange = useCallback(
    (currentPage: number) => {
      setLocalFilter({
        ...localFilter,
        ...filter,
        filter: { ...localFilter.filter, ...filter },
        pageNumber: currentPage,
        // TODO pageSize,
      });
    },
    [localFilter, setLocalFilter]
  );

  const handlePageSizeChange = useCallback(
    (pageSize: number) => {
      setLocalFilter({
        ...localFilter,
        ...filter,
        filter: { ...localFilter.filter, ...filter },
        pageSize: pageSize,
        // TODO pageNumber,
      });
    },
    [localFilter, setLocalFilter]
  ); */

  return (
    <>
      <TableConfig
        columns={_columns}
        handleSave={setColumns}
        visible={configModal}
        handleClose={() => setConfigModal(false)}
      />
      <div className="bg-white big_screen_table_filter_table_function big_screen_table_box shadow px-4 h-fit rounded">
        <Table
          {...restProps}
          resizable={true}
          bordered={true}
          className="rounded overflow-hidden"
          rowKey="id"
          // scroll={tableProps.scroll}
          columns={(_columns ?? []).filter?.((o) => o?.isShow)}
          dataSource={result}
          loading={isLoading}
          onHeaderRow={(column, index) => {
            return {
              className: "text-gray-900 text-opacity-90 bg-gray-50",
            };
          }}
          headerStyle={{ color: "blue" }}
          /* pagination={{
            showSizeChanger: true,
            //popoverRole: 'topRight',
            currentPage: dataSource?.pageNumber ?? 1,
            pageSize: dataSource?.pageSize ?? 10,
            total: dataSource?.totalCount ?? 0,
            onPageChange: handlePageChange,
            onPageSizeChange: handlePageSizeChange,
            pageSizeOpts: [10, 15, 20, 50],
            showQuickJumper: true,            
          }} */
        />
      </div>
    </>
  );
};
