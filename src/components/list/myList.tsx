import {
  IconDownload,
  IconPlus,
  IconPrint,
  IconRefresh,
  IconSetting,
  IconUpload,
} from "@douyinfe/semi-icons";
import {
  Button,
  ButtonGroup,
  Dropdown,
  Popconfirm,
  Table,
  Toast,
  Tooltip,
} from "@douyinfe/semi-ui";
import { useAuth } from "@reactivers/hooks";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { TableConfig } from "components/tableConfig";
import { UploadTmpl } from "components/tool";
import { useTableConfig } from "hooks/useTableConfig";
import { useAtom } from "jotai";
import { useResetAtom } from "jotai/utils";
import { type } from "ramda";
import { FC, useCallback, useEffect, useMemo, useState } from "react";
import { useLoaderData, useLocation } from "react-router-dom";
import { CommonApis, CommonAtoms } from "types";
import { isFunctionEnabled } from "utils";

type OperationItem = {
  engName: string;
  chnName: string;
  func: any;
};

type importProps = {
  excelType: string;
  downUrl: string;
  tip?: string;
};

type MyListProps = {
  atoms: CommonAtoms;
  apis: CommonApis;
  batchOperation?: OperationItem[];
  _operations?: [OperationItem];
  dynamicOperationFuncs?: any[];
  filter?: any;
  callback?: any;
  referAtom?: any;
  layout?: string;
  readonly?: boolean;
  cud?: number; // create/update/delete enable?
  tableProps?: any;
  importProps?: importProps;
};

export const MyList: FC<MyListProps> = ({
  atoms,
  apis,
  batchOperation = [],
  _operations = [],
  dynamicOperationFuncs = [],
  filter,
  callback,
  referAtom,
  layout,
  readonly = false,
  cud = 0b111,
  tableProps = { scroll: { x: 1200 } },
  importProps,
  ...restProps
}) => {
  if (layout === "modal") {
    readonly = true;
  }

  console.log("filter", filter);

  const { user } = useAuth();
  const queryClient = useQueryClient();
  const queryKey = "list" + atoms.entity;

  const loderData = useLoaderData();
  const { pathname } = useLocation();
  // const genBtn = useBtnHooks(loderData, pathname);

  const [rowKeys, setRowKeys] = useState<number[]>([]);

  //Atoms - 使用新的useTableConfig hook
  const [_columns, setColumns] = useTableConfig(atoms.columns, atoms.entity);

  const [listFilter, setListFilter] = useAtom(atoms.filter);
  const [editModal, setEditModal] = useAtom(atoms.editModal);
  const [refetchFn, setRefetchFn] = useAtom(atoms.Fn);
  const [configModal, setConfigModal] = useAtom(atoms.configModal);
  const reset = useResetAtom(referAtom);
  // const [refetchFn, setRefetchFn] = useAtom(atoms.Fn);

  //Apis
  const { isLoading, data, refetch } = useQuery({
    queryKey: [queryKey, { ...listFilter, ...filter }],
    // queryKey: [queryKey],
    // queryFn: () => apis.query({ id: user.userInfo?.id }),
    queryFn: () =>
      apis.query({
        id: user.userInfo?.id,
        values: { ...listFilter, filter: { ...filter, ...listFilter.filter } },
      }),
  });
  const dataSource = useMemo(() => data?.data ?? {}, [data?.data]);
  const result = useMemo(() => dataSource?.results ?? [], [dataSource]);

  useEffect(() => {
    setRefetchFn({
      refetch: refetch,
    });
  }, [refetch]);

  const mutation = useMutation({
    mutationFn: apis.remove,
    onSuccess: async (res) => {
      if (res?.code !== 0) {
        return;
      }
      let opts = {
        content: `操作成功!`,
        duration: 2,
      };
      queryClient.invalidateQueries({ queryKey: [queryKey] });
      Toast.success(opts);
      refetchFn?.refetch?.();
    },
  });

  const removes = useMutation({
    mutationFn: apis.removes,
    onSuccess: async (res) => {
      if (res?.code !== 0) {
        return;
      }
      let opts = {
        content: `操作成功!`,
        duration: 2,
      };
      Toast.success(opts);
      queryClient.invalidateQueries({ queryKey: [queryKey] });
      refetchFn?.refetch?.();
    },
  });

  const handleOpenSetting = useCallback(() => {
    console.log("Setting operation");
    setConfigModal(true);
  }, [setConfigModal]);

  // const handleOpenEdit = useCallback((id?: string) => {
  const handleOpenEdit = useCallback(
    (record) => {
      const id = record?.id;
      console.log("Edit operation", id);

      setEditModal({
        id: id ?? "",
        show: true,
      });
    },
    [setEditModal]
  );

  const handleRemoves = useCallback(() => {
    removes.mutate(rowKeys);
    setRowKeys([]);
  }, [removes, rowKeys, setRowKeys]);

  const handleRemove = useCallback(
    (record) => {
      console.log("Remove operation", record.id);

      mutation.mutate(record.id);
    },
    [mutation]
  );

  const handleSetRefer = (record: any) => {
    callback?.(record);
    reset();
  };

  const operations = isFunctionEnabled(1, cud)
    ? [
        { engName: "edit", chnName: "编辑", func: handleOpenEdit },
        ...(_operations || []),
      ]
    : [...(_operations || [])];
  //console.log(operations);

  const columns = useMemo(() => {
    const operationsColumn = {
      title: <Tooltip content="操作">操作</Tooltip>,
      isShow: true,
      dataIndex: "operate",
      key: "operate",
      fixed: "right",
      align: "center",
      width: 150,
      render: (text, record) => (
        <div className="flex justify-center">
          <ButtonGroup aria-label="操作按钮组">
            {operations.length + dynamicOperationFuncs.length < 2 ? (
              <>
                {operations?.map(
                  (item) => (
                    // genBtn(
                    // item?.engName,
                    <Button onClick={() => item?.func(record)}>
                      {item?.chnName}
                    </Button>
                  )
                  // )
                )}
                {dynamicOperationFuncs?.map((item) => {
                  if (type(item(record)) === "Array") {
                    return item(record).map(
                      (i) => (
                        // genBtn(
                        // i?.engName,
                        <Button
                          onClick={() => i?.func(record)}
                          disabled={i?.disabled}
                        >
                          {i?.chnName}
                        </Button>
                      )
                      // )
                    );
                  } else if (item(record) === null) {
                    return null;
                  } else {
                    return (
                      <Button
                        onClick={() => item(record)?.func(record)}
                        disabled={item(record)?.disabled}
                      >
                        {item(record)?.chnName}
                      </Button>
                    );
                    /* genBtn(
                      item(record)?.engName, */

                    // );
                  }
                })}
              </>
            ) : (
              <Dropdown
                trigger="hover"
                position={"bottomLeft"}
                render={
                  <Dropdown.Menu>
                    {(operations as unknown as any).map((item) => (
                      <Dropdown.Item onClick={() => item?.func(record)}>
                        {item?.chnName}
                      </Dropdown.Item>
                    ))}
                    {dynamicOperationFuncs?.map((item) => {
                      if (type(item(record)) === "Array") {
                        return item(record).map(
                          (i) => (
                            // genBtn(
                            // i?.engName,
                            <Dropdown.Item
                              onClick={() => i?.func(record)}
                              disabled={i?.disabled}
                            >
                              {i?.chnName}
                            </Dropdown.Item>
                          )
                          // )
                        );
                      } else if (item(record) === null) {
                        return null;
                      } else {
                        return (
                          <Dropdown.Item
                            onClick={() => item(record)?.func(record)}
                            disabled={item(record)?.disabled}
                          >
                            {item(record)?.chnName}
                          </Dropdown.Item>
                        );
                        /* genBtn(
                          item(record)?.engName,

                        ); */
                      }
                    })}
                  </Dropdown.Menu>
                }
              >
                <Button>操作</Button>
              </Dropdown>
            )}

            {isFunctionEnabled(0, cud) ? (
              /* genBtn(
                  "remove", */
              <Popconfirm
                position="bottomRight"
                title="确定是否要删除该项？"
                content="此修改将不可逆"
                okType="danger"
                okButtonProps={{
                  className: "semi-button semi-button-danger semi-button-light",
                }}
                onConfirm={() => {
                  handleRemove(record);
                }}
              >
                <Button type="danger">删除</Button>
              </Popconfirm>
            ) : // )
            null}
          </ButtonGroup>
        </div>
      ),
    };

    const referOperationColumn = {
      title: "引用",
      isShow: true,
      dataIndex: "refer",
      align: "center",
      width: 150,
      render: (text, record) => (
        <Button
          onClick={() => {
            handleSetRefer(record);
          }}
        >
          引用此项
        </Button>
      ),
    };

    return layout === "modal"
      ? [..._columns, referOperationColumn]
      : readonly
        ? [..._columns]
        : [..._columns, operationsColumn];
  }, [_columns, layout, operations, dynamicOperationFuncs]);

  const rowSelection = useMemo(
    () => ({
      fixed: true,
      onChange: (selectedRowKeys) => {
        setRowKeys(selectedRowKeys);
      },
    }),
    [setRowKeys]
  );

  const handlePageChange = useCallback(
    (currentPage: number) => {
      setListFilter({
        ...listFilter,
        ...filter,
        filter: { ...listFilter.filter, ...filter },
        pageNumber: currentPage,
        // TODO pageSize,
      });
    },
    [listFilter, setListFilter]
  );

  const handlePageSizeChange = useCallback(
    (pageSize: number) => {
      setListFilter({
        ...listFilter,
        ...filter,
        filter: { ...listFilter.filter, ...filter },
        pageSize: pageSize,
        // TODO pageNumber,
      });
    },
    [listFilter, setListFilter]
  );

  const OperationsLeftBtn = () => {
    return (
      <>
        {isFunctionEnabled(2, cud) ? (
          /* genBtn(
              "create", */
          <button
            className="btn rounded btn-primary btn-sm"
            onClick={() => {
              handleOpenEdit();
            }}
          >
            新增
            <IconPlus size="small" />
          </button>
        ) : // )
        null}
        {isFunctionEnabled(0, cud) ? (
          /* genBtn(
              "removes", */
          <>
            <span className="flex text-gray-900 text-opacity-60 text-sm justify-center items-center">
              已选中{rowKeys?.length ?? 0}个
            </span>
            {rowKeys?.length ? (
              <Popconfirm
                title="确定是否要删除该项？"
                content="此修改将不可逆"
                okType="danger"
                okButtonProps={{
                  className: "semi-button semi-button-danger semi-button-light",
                }}
                onConfirm={handleRemoves}
              >
                <button className="btn btn-sm rounded">批量删除</button>
              </Popconfirm>
            ) : null}
          </>
        ) : // )
        null}

        {batchOperation?.map(
          (item) => (
            /* genBtn(
            item?.engName, */
            <>
              {rowKeys?.length ? (
                <button
                  className="btn btn-sm rounded"
                  onClick={() => item?.func(rowKeys, setRowKeys)}
                >
                  {item?.chnName}
                </button>
              ) : null}
            </>
          )
          /* ) */
        )}
      </>
    );
  };

  const operationsRightBtn = () => {
    return (
      <>
        <div className="tooltip" data-tip="刷新">
          <button
            className="btn btn-sm btn-ghost rounded no-animation"
            onClick={() => {
              refetch();
            }}
          >
            <IconRefresh />
          </button>
        </div>
        <div className="tooltip" data-tip="下载">
          <button className="btn btn-sm btn-ghost rounded no-animation">
            <IconDownload />
          </button>
        </div>
        <div className="tooltip" data-tip="上传">
          <button className="btn btn-sm btn-ghost rounded no-animation">
            <IconUpload />
          </button>
        </div>
        {importProps ? (
          <UploadTmpl
            excelType={importProps?.excelType}
            downUrl={importProps?.downUrl}
            tip={importProps?.tip}
          />
        ) : null}
        <div className="tooltip" data-tip="打印">
          <button className="btn btn-sm btn-ghost rounded no-animation">
            <IconPrint />
          </button>
        </div>
        <div className="tooltip" data-tip="设置">
          <button
            className="btn btn-sm btn-ghost rounded no-animation"
            onClick={handleOpenSetting}
          >
            <IconSetting />
          </button>
        </div>
      </>
    );
  };
  return (
    <>
      <TableConfig
        columns={_columns}
        handleSave={setColumns}
        visible={configModal}
        handleClose={setConfigModal}
      />
      {/* <div className="bg-white big_screen_table_filter_table_function big_screen_table_box shadow px-4 h-fit rounded"> */}
      {/* Operation bar */}
      <div className="flex py-4 justify-between big_screen_table_filter_operation">
        {/* Operation Selection */}
        <div className="flex gap-4">
          {/* {readonly ? null : OperationsLeftBtn()} */}
          {readonly ? null : <OperationsLeftBtn />}
        </div>

        {/* Operation All */}
        <div className="flex gap">
          {layout === "modal" ? null : operationsRightBtn()}
        </div>
      </div>

      <Table
        {...restProps}
        resizable={true}
        bordered={true}
        className="rounded overflow-hidden"
        rowKey="id"
        scroll={tableProps.scroll}
        columns={(columns ?? []).filter?.((o) => o?.isShow)}
        dataSource={result}
        rowSelection={readonly ? false : rowSelection} // layout=='modal' & readonly
        loading={isLoading}
        onHeaderRow={(column, index) => {
          return {
            className: "text-gray-900 text-opacity-90 bg-gray-50",
          };
        }}
        headerStyle={{ color: "blue" }}
        pagination={{
          showSizeChanger: true,
          //popoverRole: 'topRight',
          currentPage: dataSource?.pageNumber ?? 1,
          pageSize: dataSource?.pageSize ?? 10,
          total: dataSource?.totalCount ?? 0,
          onPageChange: handlePageChange,
          onPageSizeChange: handlePageSizeChange,
          pageSizeOpts: [10, 15, 20, 50],
          showQuickJumper: true,
          /* showTotal: (total, range) => `共${total}条`,
          onChange: (page, pageSize) => {
            setPageNumber(page);
            setPageSize(pageSize);
          },
          onShowSizeChange: (current, size) => {
            setPageNumber(current);
            setPageSize(size);
          } */
        }}
      />
      {/* </div> */}
    </>
  );
};
