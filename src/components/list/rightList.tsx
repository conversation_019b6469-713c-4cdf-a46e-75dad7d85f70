import {
  IconDownload,
  IconPlus,
  IconPrint,
  IconRefresh,
  IconSetting,
  IconUpload,
} from "@douyinfe/semi-icons";
import {
  Button,
  ButtonGroup,
  Dropdown,
  Popconfirm,
  Table,
  Toast,
  Tooltip,
} from "@douyinfe/semi-ui";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { ExportContentType, ExportFormat, useExport } from "components/export";
import { TableConfig } from "components/tableConfig";
import { UploadTmpl } from "components/tool";
import { useBtnHooks } from "hooks";
import { useTableConfig } from "hooks/useTableConfig";
import { useAtom } from "jotai";
import { useResetAtom } from "jotai/utils";
import { type, union } from "ramda";
import { FC, useCallback, useEffect, useMemo, useState } from "react";
import { useLoaderData, useLocation } from "react-router-dom";
import { CommonApis, CommonAtoms } from "types";
import { isFunctionEnabled } from "utils";

type OperationItem = {
  engName: string;
  chnName: string;
  func: any;
};

type importProps = {
  entity?: string;
  excelType: string;
  downUrl: string;
  tip?: string;
};

type ListProps = {
  atoms: CommonAtoms;
  apis: CommonApis;
  batchOperation?: OperationItem[];
  _operations?: [OperationItem];
  dynamicOperationFuncs?: any[];
  filter?: any;
  callback?: any;
  referAtom?: any;
  layout?: string;
  readonly?: boolean;
  cud?: number; // create/update/delete enable?
  tableProps?: any;
  importProps?: importProps;
  checkDisabledFunc?: (record: any) => boolean;
  queryParams?: any;
  leftSelectAtom: any;
  lefstSelectKey: string;
};

export const RightList: FC<ListProps> = ({
  atoms,
  apis,
  batchOperation = [],
  _operations = [],
  dynamicOperationFuncs = [],
  filter = {},
  callback,
  referAtom,
  layout,
  readonly = false,
  cud = 0b111,
  tableProps = { scroll: { x: 1200 } },
  importProps,
  checkDisabledFunc,
  queryParams = {},
  leftSelectAtom,
  lefstSelectKey,
  ...restProps
}) => {
  if (layout === "modal") {
    readonly = true;
  }

  const queryClient = useQueryClient();
  const queryKey = "list" + atoms.entity;
  const exportContext = useExport();
  const canExport = exportContext && exportContext.exportToFile;

  // queryParams && Object.keys(queryParams).length >= 0
  // ? "list" + atoms.entity + JSON.stringify(queryParams)
  // : "list" + atoms.entity;
  const queryParamsHasInvalidValue = Object.entries(queryParams).some(
    ([key, value]) => value === null || value === undefined || value === ""
  );

  const loderData = useLoaderData();
  const { pathname } = useLocation();
  const genBtn = useBtnHooks(loderData, pathname);

  const [rowKeys, setRowKeys] = useState<number[]>([]);
  const [rows, setRows] = useState<any[]>([]);

  //Atoms - 使用新的useTableConfig hook
  const [_columns, setColumns] = useTableConfig(atoms.columns, atoms.entity);

  const [localFilter, setLocalFilter] = useAtom(atoms.filter);
  const [editModal, setEditModal] = useAtom(atoms.editModal);
  const [refetchFn, setRefetchFn] = useAtom(atoms.Fn);
  const [configModal, setConfigModal] = useAtom(atoms.configModal);
  const reset = useResetAtom(referAtom);
  const [select, setSelect] = useAtom(leftSelectAtom);
  // const [refetchFn, setRefetchFn] = useAtom(atoms.Fn);

  // console.log("filter", filter);
  // console.log("localFilter", localFilter);
  // console.log("queryParams", queryParams, Object.keys(queryParams).length);
  // console.log("queryKey", queryKey);
  // console.log("final queryKey", [queryKey, { ...localFilter, ...filter }]);
  // console.log("final outer queryKey", restProps.outerQueryKey);
  // console.log(queryKey == restProps.outerQueryKey);

  //Apis
  const { isLoading, data, refetch } = useQuery({
    // 刷新的2个解决方案(目前采用的是方案2，详见下方useEffect)：
    // 1. queryKey: [queryKey, { ...localFilter, ...filter }]: 把queryKey放到Atom中，每次刷新时，queryKey都会变化，导致重新请求数据
    // 2. refetch: refetchFn?.refetch?.(): 通过refetchFn?.refetch?.()来触发重新请求数据
    queryKey: [queryKey, { ...localFilter, ...filter }],
    // queryKey: [queryKey],
    queryFn: () =>
      apis.query({
        ...queryParams,
        ...localFilter,
        filter: { ...filter, ...localFilter.filter },
      }),
    enabled: !queryParamsHasInvalidValue,
  });
  const dataSource = useMemo(() => data?.data ?? {}, [data?.data]);
  const result = useMemo(() => {
    console.debug("select", select);
    if (select === null || select === 0) {
      return dataSource?.results ?? [];
    } else {
      const selectedRes = dataSource?.results?.filter((o) => {
        return o[lefstSelectKey]?.id === select; // TODO: category.id normaliztion
      });
      return selectedRes ?? [];
    }
  }, [dataSource, select]);

  useEffect(() => {
    setRefetchFn({
      refetch: refetch,
    });
  }, [refetch]);

  const mutation = useMutation({
    mutationFn: apis.remove,
    onSuccess: async (res) => {
      if (res?.code !== 0) {
        return;
      }
      let opts = {
        content: `操作成功!`,
        duration: 2,
      };
      queryClient.invalidateQueries({ queryKey: [queryKey] });
      Toast.success(opts);
      refetchFn?.refetch?.();
    },
  });

  const removes = useMutation({
    mutationFn: apis.removes,
    onSuccess: async (res) => {
      if (res?.code !== 0) {
        return;
      }
      let opts = {
        content: `操作成功!`,
        duration: 2,
      };
      Toast.success(opts);
      queryClient.invalidateQueries({ queryKey: [queryKey] });
      refetchFn?.refetch?.();
    },
  });

  const handleExport = (
    format: ExportFormat = "excel",
    contentType: ExportContentType = "list"
  ) => {
    exportContext?.exportToFile?.({
      format: format,
      contentType: contentType,
      // data: data,
      apiFn: apis.query,
      params: { ...localFilter, filter: { ...filter, ...localFilter.filter } },
      columns: _columns,
      entityName: atoms.entityCName,
    });
  };

  const handleOpenSetting = useCallback(() => {
    console.log("Setting operation");
    setConfigModal(true);
  }, [setConfigModal]);

  // const handleOpenEdit = useCallback((id?: string) => {
  const handleOpenEdit = useCallback(
    (record) => {
      const id = record?.id;
      console.log("Edit operation", id);

      setEditModal({
        id: id ?? "",
        show: true,
      });
    },
    [setEditModal]
  );

  const handleRemoves = useCallback(() => {
    removes.mutate(rowKeys);
    setRowKeys([]);
  }, [removes, rowKeys, setRowKeys]);

  const handleRemove = useCallback(
    (record) => {
      console.log("Remove operation", record.id);

      mutation.mutate(record.id);
    },
    [mutation]
  );

  const handleSetRefer = (record: any) => {
    callback?.(record);
    reset();
  };

  const operations = isFunctionEnabled(1, cud)
    ? [
        { engName: "edit", chnName: "编辑", func: handleOpenEdit },
        ...(_operations || []),
      ]
    : [...(_operations || [])];
  //console.log(operations);

  const columns = useMemo(() => {
    const operationsColumn = {
      title: <Tooltip content="操作">操作</Tooltip>,
      isShow: true,
      dataIndex: "operate",
      key: "operate",
      fixed: "right",
      align: "center",
      width: 150,
      render: (text, record) => (
        // <div className="flex w-full justify-center items-center py-6">
        <div className="flex justify-center">
          <ButtonGroup aria-label="操作按钮组">
            {operations.length + dynamicOperationFuncs.length < 2 ? (
              <>
                {operations?.map((item) =>
                  genBtn(
                    item?.engName,
                    <Button onClick={() => item?.func(record)}>
                      {item?.chnName}
                    </Button>
                  )
                )}
                {dynamicOperationFuncs?.map((item) => {
                  if (type(item(record)) === "Array") {
                    return item(record).map((i) =>
                      genBtn(
                        i?.engName,
                        <Button
                          onClick={() => i?.func(record)}
                          disabled={i?.disabled}
                        >
                          {i?.chnName}
                        </Button>
                      )
                    );
                  } else if (item(record) === null) {
                    return null;
                  } else {
                    return genBtn(
                      item(record)?.engName,
                      <Button
                        onClick={() => item(record)?.func(record)}
                        disabled={item(record)?.disabled}
                      >
                        {item(record)?.chnName}
                      </Button>
                    );
                  }
                })}
              </>
            ) : (
              <Dropdown
                trigger="hover"
                position={"bottomLeft"}
                render={
                  <Dropdown.Menu>
                    {/* {
                    genBtn('edit', (
                      <Button onClick={() => { handleOpenEdit(record.id) }}>编辑</Button>
                    ))
                  } */}
                    {dynamicOperationFuncs?.map((item) => {
                      if (type(item(record)) === "Array") {
                        return item(record).map((i) =>
                          genBtn(
                            i?.engName,
                            <Dropdown.Item
                              onClick={() => i?.func(record)}
                              disabled={i?.disabled}
                            >
                              {i?.chnName}
                            </Dropdown.Item>
                          )
                        );
                      } else if (item(record) === null) {
                        return null;
                      } else {
                        return genBtn(
                          item(record)?.engName,
                          <Dropdown.Item
                            onClick={() => item(record)?.func(record)}
                            disabled={item(record)?.disabled}
                          >
                            {item(record)?.chnName}
                          </Dropdown.Item>
                        );
                      }
                    })}
                    {operations.map((item) =>
                      genBtn(
                        item?.engName,
                        <Dropdown.Item onClick={() => item?.func(record)}>
                          {item?.chnName}
                        </Dropdown.Item>
                      )
                    )}
                  </Dropdown.Menu>
                }
              >
                <Button>操作</Button>
              </Dropdown>
            )}

            {isFunctionEnabled(0, cud)
              ? genBtn(
                  "remove",
                  <Popconfirm
                    position="bottomRight"
                    title="确定是否要删除该项？"
                    content="此修改将不可逆"
                    okType="danger"
                    okButtonProps={{
                      className:
                        "semi-button semi-button-danger semi-button-light",
                    }}
                    onConfirm={() => {
                      handleRemove(record);
                    }}
                  >
                    <Button type="danger">删除</Button>
                  </Popconfirm>
                )
              : null}
          </ButtonGroup>
        </div>
      ),
    };

    const referOperationColumn = {
      title: "引用",
      isShow: true,
      dataIndex: "refer",
      align: "center",
      width: 150,
      render: (text, record) => (
        <Button
          onClick={() => {
            handleSetRefer(record);
          }}
        >
          引用此项
        </Button>
      ),
    };

    return layout === "modal"
      ? [..._columns, referOperationColumn]
      : readonly
        ? [..._columns]
        : [..._columns, operationsColumn];
  }, [_columns, layout, operations, dynamicOperationFuncs, genBtn]);

  const rowSelection = useMemo(
    () => ({
      fixed: true,
      getCheckboxProps: (record) => ({
        disabled: checkDisabledFunc ? checkDisabledFunc(record) : false,
        name: record.name,
      }),
      // onChange	(selectedRowKeys: number[]|string[], selectedRows: RecordType[]) => void
      // 选中项发生变化时的回调。第一个参数会保存上次选中的 row keys，即使你做了分页受控或更新了 dataSource FAQ
      onChange: (selectedRowKeys, selectedRows) => {
        setRowKeys(selectedRowKeys);
        // 只保留当前页选中的数据
        // setRows(selectedRows);
        setRows((prev) => {
          const remainRows = prev.filter((o) => selectedRowKeys.includes(o.id));
          // return [...remainRows, ...selectedRows];
          return union(remainRows, selectedRows);
        });
      },
    }),
    [setRowKeys]
  );

  const handlePageChange = useCallback(
    (currentPage: number) => {
      setLocalFilter({
        ...localFilter,
        ...filter,
        filter: { ...localFilter.filter, ...filter },
        pageNumber: currentPage,
        // TODO pageSize,
      });
    },
    [localFilter, setLocalFilter]
  );

  const handlePageSizeChange = useCallback(
    (pageSize: number) => {
      setLocalFilter({
        ...localFilter,
        ...filter,
        filter: { ...localFilter.filter, ...filter },
        pageSize: pageSize,
        // TODO pageNumber,
      });
    },
    [localFilter, setLocalFilter]
  );

  const OperationsLeftBtn = () => {
    return (
      <>
        {isFunctionEnabled(2, cud)
          ? genBtn(
              "create",
              <button
                className="btn rounded btn-primary btn-sm"
                onClick={() => {
                  handleOpenEdit();
                }}
              >
                新增
                <IconPlus size="small" />
              </button>
            )
          : null}
        {isFunctionEnabled(0, cud)
          ? genBtn(
              "removes",
              <>
                <span className="flex text-gray-900 text-opacity-60 text-sm justify-center items-center">
                  已选中{rowKeys?.length ?? 0}个
                </span>
                {rowKeys?.length ? (
                  <Popconfirm
                    title="确定是否要删除该项？"
                    content="此修改将不可逆"
                    okType="danger"
                    okButtonProps={{
                      className:
                        "semi-button semi-button-danger semi-button-light",
                    }}
                    onConfirm={handleRemoves}
                  >
                    <button className="btn btn-sm rounded">批量删除</button>
                  </Popconfirm>
                ) : null}
              </>
            )
          : null}
        {batchOperation?.map((item) =>
          genBtn(
            item?.engName,
            <>
              {rowKeys?.length ? (
                <button
                  className="btn btn-sm rounded"
                  onClick={() => item?.func(rowKeys, setRowKeys, rows, setRows)}
                >
                  {item?.chnName}
                </button>
              ) : null}
            </>
          )
        )}
      </>
    );
  };

  const operationsRightBtn = () => {
    return (
      <>
        <div className="tooltip" data-tip="刷新">
          <button
            className="btn btn-sm btn-ghost rounded no-animation"
            onClick={() => {
              refetch();
            }}
          >
            <IconRefresh />
          </button>
        </div>
        {canExport ? (
          <div className="tooltip" data-tip="导出">
            {genBtn(
              "export",
              <button
                className="btn btn-sm btn-ghost rounded no-animation"
                onClick={() => handleExport()}
              >
                <IconDownload />
              </button>
            )}
          </div>
        ) : null}
        {importProps ? (
          <UploadTmpl
            entity={importProps?.entity}
            excelType={importProps?.excelType}
            downUrl={importProps?.downUrl}
            tip={importProps?.tip}
          />
        ) : null}
        <div className="tooltip" data-tip="设置">
          <button
            className="btn btn-sm btn-ghost rounded no-animation"
            onClick={handleOpenSetting}
          >
            <IconSetting />
          </button>
        </div>

        <div className="tooltip" data-tip="下载">
          <button className="btn btn-sm btn-ghost rounded no-animation">
            <IconDownload />
          </button>
        </div>
        <div className="tooltip" data-tip="上传">
          <button className="btn btn-sm btn-ghost rounded no-animation">
            <IconUpload />
          </button>
        </div>
        <div className="tooltip" data-tip="打印">
          <button className="btn btn-sm btn-ghost rounded no-animation">
            <IconPrint />
          </button>
        </div>
      </>
    );
  };
  return (
    <>
      <TableConfig
        columns={_columns}
        handleSave={setColumns}
        visible={configModal}
        handleClose={setConfigModal}
      />
      <div className="bg-white big_screen_table_filter_table_function big_screen_table_box shadow px-4 h-fit rounded">
        {/* Operation bar */}
        <div className="flex py-4 justify-between big_screen_table_filter_operation">
          {/* Operation Selection */}
          <div className="flex gap-4">
            {/* {readonly ? null : OperationsLeftBtn()} */}
            {readonly ? null : <OperationsLeftBtn />}
          </div>

          {/* Operation All */}
          <div className="flex gap">
            {layout === "modal" ? null : operationsRightBtn()}
          </div>
        </div>

        <Table
          {...restProps}
          resizable={true}
          bordered={true}
          className="rounded overflow-hidden"
          rowKey="id"
          scroll={tableProps.scroll}
          columns={(columns ?? []).filter?.((o) => o?.isShow)}
          dataSource={result}
          rowSelection={readonly ? false : rowSelection} // layout=='modal' & readonly
          loading={isLoading}
          onHeaderRow={(column, index) => {
            return {
              className: "text-gray-900 text-opacity-90 bg-gray-50",
            };
          }}
          headerStyle={{ color: "blue" }}
          pagination={{
            showSizeChanger: true,
            //popoverRole: 'topRight',
            currentPage: dataSource?.pageNumber ?? 1,
            pageSize: dataSource?.pageSize ?? 10,
            total: dataSource?.totalCount ?? 0,
            onPageChange: handlePageChange,
            onPageSizeChange: handlePageSizeChange,
            pageSizeOpts: [10, 15, 20, 50],
            showQuickJumper: true,
            /* showTotal: (total, range) => `共${total}条`,
            onChange: (page, pageSize) => {
              setPageNumber(page);
              setPageSize(pageSize);
            },
            onShowSizeChange: (current, size) => {
              setPageNumber(current);
              setPageSize(size);
            } */
          }}
        />
      </div>
    </>
  );
};
