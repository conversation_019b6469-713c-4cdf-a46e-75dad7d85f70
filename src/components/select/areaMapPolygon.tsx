import { Modal, useForm<PERSON>pi } from "@douyinfe/semi-ui";
import { useQuery } from "@tanstack/react-query";
import { getDrawAreas } from "api";
import { mapPolygonAtom } from "atoms";
import * as Cesium from "cesium";
import "cesium/Build/Cesium/Widgets/widgets.css";
import {
  cesiumInit,
  cesiumSceneLightOptions,
  cesiumViewerOptions,
} from "hooks/cesiumInit";
import { useAtom } from "jotai";
import { FC, useCallback, useEffect, useRef, useState } from "react";
import { renderMarkerList } from "utils";
import CesiumEntityDraw from "utils/CesiumEntityDraw";

type AreaMapPolygonProps = {
  field: string;
  entity?: string;
  data?: any;
  markers?: [];
};

/**
 * 模型地图多边形绘制组件
 *
 * See also {@link AreaDrawer} {@link AreaMapPicker} {@link AreaMapMarker}
 *
 * See also {@link MapPicker}
 *
 * See also {@link AreaPage}
 *
 * @example
 * ```typescript
 * <AreaMapPolygon data={data} markers={markers} />
 * ```
 */
export const AreaMapPolygon: FC<AreaMapPolygonProps> = ({ entity, field }) => {
  // console.log('AreaMapPolygon', data);
  // console.log('AreaMapPolygon', markers);

  const formApi = useFormApi();
  const formState = formApi.getFormState();

  const color = "#fb6260";
  const [polygonDrawer, setPolygonDrawer] = useAtom(mapPolygonAtom);

  // const id = `cesium-polygon-${polygonDrawer?.markers?.map(marker => '(' + marker?.longitude + ',' + marker.latitude).join('-')}`;
  const polygonCenterKey = `${polygonDrawer.longitude}-${polygonDrawer.latitude}`;
  const polygonDraftKey = `draft-${polygonDrawer.longitude}-${polygonDrawer.latitude}`;
  // console.log(id);

  const { isLoading, data } = useQuery({
    queryKey: ["getDrawAreas"],
    queryFn: getDrawAreas,
  });

  const [alerted, setAlerted] = useState(false);
  const [overflowBytesTimes, setOverflowBytesTimes] = useState(1);
  const cesiumRef = useRef();
  const viewerRef = useRef();
  const entities = useRef({});
  const drawer = useRef();
  const [drawing, toggleDrawing] = useState(false);
  const tilesetRef = useRef();

  const drawAreaOverlay = useCallback((entity) => {
    entities.current[entity.id] = new Cesium.Entity({
      id: entity.id,
      polygon: {
        hierarchy: Cesium.Cartesian3.fromDegreesArray(
          entity.points
            .map((point) => [point.longitude, point.latitude])
            .flat(2)
        ),
        material: Cesium.Color.fromCssColorString(color).withAlpha(0.6),
      },
    });
    viewerRef.current.entities.add(entities.current[entity.id]);
  }, []);

  const initializeCesium = useCallback(async () => {
    if (!data?.data?.cesiumUriPrefix) {
      if (cesiumRef.current) {
        cesiumRef.current.innerText = "Cesium 地址 uri prefix 不可用";
      }
      return;
    }
    cesiumRef.current.innerText = "";
    // Cesium.Ion.defaultAccessToken = import.meta.env.VITE_CESIUM_TOKEN;
    console.log(data.data.cesiumToken);
    Cesium.Ion.defaultAccessToken = data.data.cesiumToken;

    const viewer = new Cesium.Viewer(polygonCenterKey, cesiumViewerOptions);
    viewerRef.current = viewer;
    try {
      viewer._cesiumWidget._creditContainer.parentNode.removeChild(
        viewer._cesiumWidget._creditContainer
      );
      viewer.scene.light = new Cesium.DirectionalLight(cesiumSceneLightOptions);

      const tileset = await cesiumInit(data, viewer);

      viewer.scene.primitives.add(tileset);
      tilesetRef.current = tileset;
      await viewer.zoomTo(tileset);

      console.log(polygonDrawer.longitude);
      if (Math.abs(polygonDrawer.longitude - 0.0) > 0.1) {
        var entity = viewer.entities.add({
          position: Cesium.Cartesian3.fromDegrees(
            polygonDrawer.longitude,
            polygonDrawer.latitude,
            5
          ),
          point: {
            pixelSize: 10,
            color: Cesium.Color.RED,
          },
        });

        setTimeout(() => {
          console.log("triggr flyTo");
          viewer.flyTo(entity);
        }, 1000);
      }

      /* const targetPosition = Cesium.Cartesian3.fromDegrees(polygonDrawer.longitude, polygonDrawer.latitude, 200);
      setTimeout(() => {
        viewer.camera.flyTo({
          destination: targetPosition,
          orientation: {
            heading: Cesium.Math.toRadians(0),
            pitch: Cesium.Math.toRadians(-90),
            roll: 0.0,
          },
          // duration: 2,
          // complete: () => {
            // viewer.camera.lookAt(targetPosition, new Cesium.HeadingPitchRange(0, Cesium.Math.toRadians(-90), 1000));
          // }
        });
      }, 1000); */

      drawAreaOverlay({
        id: polygonDraftKey,
        points: polygonDrawer.markers,
      });
      entities.current[polygonDraftKey] = polygonDrawer.markers;

      // data?.data?.areas?.forEach?.(drawAreaOverlay);
      // localStorage.setItem('risk', JSON.stringify(data?.data?.areas))
      // console.log(data);
    } catch (error) {
      console.log(`Error loading tileset: ${error}`);
      if (error?.message?.includes("maximumCacheOverflowBytes")) {
        setOverflowBytesTimes((_) => _ + 1);
      }
    }
  }, [polygonCenterKey, overflowBytesTimes, data?.data, drawAreaOverlay]);

  useEffect(() => {
    console.log("entities", entities);
    console.log("viewer.current.entities", viewerRef.current?.entities);
    if (polygonDrawer.visible && data?.data?.cesiumUriPrefix) {
      console.log("initializeCesium");
      setTimeout(initializeCesium);
    }
    return () => {
      if (!polygonDrawer.visible) {
        if (viewerRef.current) {
          viewerRef.current.destroy();
          viewerRef.current = null;
        }
        if (cesiumRef.current) {
          cesiumRef.current.innerHTML = "";
        }
      }
    };
  }, [polygonCenterKey, initializeCesium, polygonDrawer.visible]);

  const handleClose = useCallback(() => {
    if (viewerRef.current) {
      viewerRef.current.destroy();
      viewerRef.current = null;
    }
    if (cesiumRef.current) {
      cesiumRef.current.innerHTML = "";
    }
    if (drawer.current) {
      drawer.current.clear();
      drawer.current = null;
    }
    if (drawing) {
      toggleDrawing(false);
    }
    setAlerted(false);
    setPolygonDrawer({
      visible: false,
      longitude: 0.0,
      latitude: 0.0,
      markers: [],
    });
  }, [setPolygonDrawer]);

  const onStopDraw = useCallback(
    (points) => {
      toggleDrawing(false);
      if (drawer.current) {
        drawer.current.clear();
      }
      points = points.map(([longitude, latitude]) => ({ longitude, latitude }));
      drawAreaOverlay({
        // id: polygonCenterKey,
        id: polygonDraftKey,
        points,
      });
      entities.current[polygonDraftKey] = points;
    },
    [polygonDrawer, drawAreaOverlay]
  );
  const onStartDraw = useCallback(() => {
    toggleDrawing(true);
    if (entities.current[polygonDraftKey]) {
      viewerRef.current.entities.removeById(polygonDraftKey);
      delete entities.current[polygonDraftKey];
    }
    if (drawer.current) {
      drawer.current.clear();
      drawer.current = null;
    }

    drawer.current = new CesiumEntityDraw(viewerRef.current, {
      material: Cesium.Color.fromCssColorString(color).withAlpha(0.6),
    });
    drawer.current.drawPolygon(onStopDraw);
  }, [onStopDraw, polygonDrawer]);
  const onClearDraw = () => {
    if (drawer.current) {
      drawer.current.clear();
    }

    viewerRef.current.entities.removeById(polygonDraftKey);
    delete entities.current[polygonDraftKey];
  };

  const handleOk = () => {
    const markers = entities.current[polygonDraftKey];
    if (markers?.length < 3) {
      return;
    }
    console.log(markers);
    formApi.setValue(field, markers);
    formApi.setValue(`${field}Show`, renderMarkerList(markers));
    setPolygonDrawer({
      visible: false,
      longitude: 0.0,
      latitude: 0.0,
      markers: [],
    });
  };

  const [othersVisible, setOthersVisible] = useState(true);
  const toggleVisible = useCallback(() => {
    setOthersVisible(!othersVisible);
    data?.data?.areas?.forEach(({ id }) => {
      if (id !== polygonDrawer.id) {
        if (entities.current[id]) {
          entities.current[id].show = !othersVisible;
        }
      }
    });
  }, [othersVisible, data?.data?.areas, polygonDrawer?.id]);

  useEffect(() => {
    if (polygonDrawer.visible && !alerted) {
      setAlerted(true);
      Modal.info({
        centered: true,
        title: " ",
        content: (
          <>
            <p>单击鼠标左键确定标注点，单击鼠标右键结束绘制。</p>
            <p>
              标注风险分区区域的标注点时，标注点 (三个及以上)
              所围区域即为该风险分区区域，绘制过程中会显示。
            </p>
            <p>
              注意:
              标注点时按同一方向（顺时针/逆时针）标注，否则标点所围区域会发生错乱!
            </p>
          </>
        ),
        onCancel: handleClose,
        okButtonProps: {
          className: "btn rounded btn-primary btn-sm",
        },
        maskClosable: false,
      });
    }
  }, [polygonDrawer.visible, handleClose]);

  const goHome = () => {
    viewerRef.current.zoomTo(tilesetRef.current);
  };

  const alreadyDrawed =
    polygonDraftKey && entities.current[polygonDraftKey]?.length > 0;

  console.log("cesiumRef", cesiumRef);
  console.log("AreaMapPolygon", polygonDrawer);

  return (
    <>
      <Modal
        title={`绘制${entity}多边形`}
        width="fit-content"
        height="fit-content"
        bodyStyle={{
          width: "fit-content",
          height: "fit-content",
          paddingBottom: 20,
        }}
        visible={polygonDrawer?.visible ?? false}
        onCancel={handleClose}
        maskClosable={false}
        footer={null}
        centered
      >
        <div className="flex gap-3 mb-[20px] items-center">
          <button
            className="btn rounded btn-primary btn-sm"
            disabled={drawing}
            onClick={onStartDraw}
          >
            {drawing ? "绘制中" : alreadyDrawed ? "重新绘制" : "开始绘制"}
          </button>
          {!drawing ? (
            <button
              className="btn rounded btn-neutral btn-sm"
              onClick={onClearDraw}
            >
              重置标记
            </button>
          ) : null}
          <button className="btn rounded btn-primary btn-sm" onClick={handleOk}>
            确认提交
          </button>
          <button className="btn rounded btn-accent btn-sm" onClick={goHome}>
            重置镜头
          </button>
        </div>
        {drawing ? (
          <p className="mb-[20px]">
            单击鼠标左键确定标注点，单击鼠标右键结束绘制。按住Ctrl键后，单击鼠标左键拖动渲染镜头。
          </p>
        ) : (
          ""
        )}
        <div
          ref={cesiumRef}
          className="w-[80vw] h-[70vh] min-w-[800px] min-h-[500px]"
          id={polygonCenterKey}
        ></div>
      </Modal>
    </>
  );
};
