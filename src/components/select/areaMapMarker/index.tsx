import { areaDrawer<PERSON>odalAtom } from "atoms/doubleGuard";
import * as Cesium from "cesium";
import "cesium/Build/Cesium/Widgets/widgets.css";
import { RISK_LEVEL_COLOR_MAP } from "components";
import {
  cesiumInit,
  cesiumSceneLightOptions,
  cesiumViewerOptions,
} from "hooks/cesiumInit";
import { useAtom } from "jotai";
import { FC, useCallback, useEffect, useRef, useState } from "react";

type MarkerMapPickerProps = {
  data: any;
  markers?: [];
};

const createPoint = (longitude, latitude, _h, text) => {
  const height = _h + 1; // 在原有高度基础上增加1米

  const position = Cesium.Cartesian3.fromDegrees(
    parseFloat(`${longitude}`),
    parseFloat(`${latitude}`),
    height,
  );
  const modelUrl = "/3d_lowpoly_arrow.glb";

  return new Cesium.Entity({
    position: position,
    label: {
      text: text, // 要显示的文字内容
      font: "24px sans-serif", // 文字样式
      fillColor: Cesium.Color.BLUE, // 文字颜色
      pixelOffset: new Cesium.Cartesian2(0, -50),
      heightReference: Cesium.HeightReference.RELATIVE_TO_GROUND, // 文字相对于地面的高度参考
    },
    model: {
      uri: modelUrl,
      minimumPixelSize: 50,
      maximumScale: 20000,
    },
  });
};

/**
 * 模型地图多点选择及连线组件
 *
 * See also {@link AreaDrawer} {@link AreaMapPicker} {@link AreaMapPolygon}
 *
 * See also {@link MapPicker}
 *
 * @example
 * ```typescript
 * <AreaMapPicker data={areas} field="map" />
 * ```
 */
export const AreaMapMarker: FC<MarkerMapPickerProps> = ({ data, markers }) => {
  const [areaDrawer] = useAtom(areaDrawerModalAtom);
  const [isReady, setIsReady] = useState(false);
  const id = `cesium-${areaDrawer?.id}`;
  const [marker, setMarker] = useState<Cesium.Entity | null>(null);
  const [triangleVertices, setTriangleVertices] = useState<any>([]); // 三角形顶点坐标
  const [overflowBytesTimes, setOverflowBytesTimes] = useState(1);
  const cesiumRef = useRef<any>();
  const viewerRef = useRef<any>();
  const entities = useRef<any>({});
  const tilesetRef = useRef<any>();

  const drawAreaOverlay = useCallback((entity) => {
    const color =
      RISK_LEVEL_COLOR_MAP.find((risk) => risk.id === (entity.riskLevel || 5))
        ?.color ?? "";
    entities.current[entity.id] = new Cesium.Entity({
      id: `risk-overlay-${entity.id}`,
      polygon: {
        hierarchy: Cesium.Cartesian3.fromDegreesArray(
          entity.points
            .map((point) => [point.longitude, point.latitude])
            .flat(2),
        ),
        material: Cesium.Color.fromCssColorString(color).withAlpha(0.6),
      },
    });
    viewerRef.current.entities.add(entities.current[entity.id]);
  }, []);

  const initializeCesium = useCallback(async () => {
    if (!data?.data?.cesiumUriPrefix) {
      if (cesiumRef.current) {
        cesiumRef.current.innerText = "Cesium 地址 uri prefix 不可用";
      }
      return;
    }
    if (cesiumRef.current) cesiumRef.current.innerText = "";
    Cesium.Ion.defaultAccessToken = data.data.cesiumToken;

    const viewer = new Cesium.Viewer(id, cesiumViewerOptions);
    viewerRef.current = viewer;
    try {
      viewer._cesiumWidget._creditContainer.parentNode.removeChild(
        viewer._cesiumWidget._creditContainer,
      );
      viewer.scene.light = new Cesium.DirectionalLight(cesiumSceneLightOptions);

      const tileset = await cesiumInit(data, viewer);

      const model = viewer.scene.primitives.add(tileset);
      viewer.scene.globe.depthTestAgainstTerrain = true;
      tilesetRef.current = tileset;
      await viewer.zoomTo(tileset);
      setIsReady(true);

      data?.data?.areas?.forEach?.(drawAreaOverlay);
    } catch (error) {
      console.log(`Error loading tileset: ${error} `);
      if (error?.message?.includes("maximumCacheOverflowBytes")) {
        setOverflowBytesTimes((_) => _ + 1);
      }
    }
  }, [
    id,
    overflowBytesTimes,
    data?.data,
    drawAreaOverlay,
    setTriangleVertices,
    setMarker,
    setIsReady,
    // markers
  ]);

  useEffect(() => {
    if (data?.data?.cesiumUriPrefix) {
      setTimeout(initializeCesium);
    }
  }, [id, initializeCesium]);

  useEffect(() => {
    if (viewerRef.current?.entities && isReady) {
      viewerRef.current.entities.removeAll();
      markers?.forEach((o) => {
        let longitude = o?.longitude ?? 0;
        let latitude = o?.latitude ?? 0;
        let height = 14;
        const point = createPoint(longitude, latitude, height, o.name);
        if (point) {
          viewerRef.current.entities.add(point);
        }
      });
    }
  }, [markers, viewerRef.current?.entities, isReady]);

  return (
    <div className="w-full h-[400px]">
      <div ref={cesiumRef} className="w-full h-full" id={id}></div>
    </div>
  );
};
