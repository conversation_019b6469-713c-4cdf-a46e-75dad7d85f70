import { Form } from "@douyinfe/semi-ui";
import { useQuery } from "@tanstack/react-query";
import { getFirstLevel } from "api";
import { sort } from "ramda";
import { FC, useMemo } from "react";

export type JobCategorySelectProps = {
  field: string;
  placeholder?: string;
  label?: string;
  isRequired?: boolean;
  multiple?: true;
};

export const JobCategorySelect: FC<JobCategorySelectProps> = ({
  field,
  placeholder,
  label,
  isRequired,
  multiple,
}) => {
  const { isLoading, data } = useQuery({
    queryKey: ["getFirstLevel"],
    queryFn: getFirstLevel,
  });
  const list = useMemo(() => {
    return sort((a, b) => a.firstOrder - b.firstOrder, data?.data ?? []);
  }, [data]);

  return (
    <Form.Select
      field={field}
      noLabel={!Boolean(label)}
      label={label ?? ""}
      placeholder={placeholder ?? ""}
      className="w-full"
      multiple={multiple ?? false}
      loading={isLoading}
      rules={[{ required: isRequired, message: "此为必填项!" }]}
    >
      {list.map((o) => (
        <Form.Select.Option value={o?.id} key={o?.id ?? 0}>
          {o?.name ?? ""}
        </Form.Select.Option>
      ))}
    </Form.Select>
  );
};
