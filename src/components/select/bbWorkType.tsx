import { Form, Select, useFormApi, useFormState } from "@douyinfe/semi-ui";
import { FC } from "react";
import { BB_WORK_TYPE_MAP, DANGER_REPORT_STATUS_MAP } from "../enum";

export type BbWorkTypeSelectProps = {
  field: string;
  placeholder?: string;
  label?: string;
  isRequired?: boolean;
  noForm?: boolean;
  className?: string;
  onChange?: (value: number) => void;
  edit?: boolean;
};

export const BbWorkTypeSelect: FC<BbWorkTypeSelectProps> = ({
  field,
  placeholder,
  label,
  isRequired,
  noForm,
  className,
  onChange,
  edit,
}) => {
  const formApi = useFormApi();
  const formState = useFormState();

  if (noForm) {
    return (
      <>
        <Select
          label={label ?? ""}
          placeholder={placeholder ?? "请选择任务类型"}
          className={className || "w-full"}
          onChange={onChange}
        >
          {BB_WORK_TYPE_MAP.map((o) => (
            <Select.Option value={parseInt(o?.id) ?? 0} key={o?.id ?? 0}>
              {o?.name ?? ""}
            </Select.Option>
          ))}
        </Select>
      </>
    );
  }
  return (
    <>
      <Form.Select
        field={field}
        noLabel={!Boolean(label)}
        label={label ?? ""}
        placeholder={placeholder ?? "请选择任务类型"}
        className="w-full"
        rules={[{ required: isRequired, message: "此为必填项!" }]}
      >
        {BB_WORK_TYPE_MAP.map((o) => (
          <Form.Select.Option value={parseInt(o?.id) ?? 0} key={o?.id ?? 0}>
            {o?.name ?? ""}
          </Form.Select.Option>
        ))}
      </Form.Select>
      {!edit ? (
        <Form.Select
          placeholder="上报状态"
          field="uploadStatus"
          noLabel
          className="w-full"
        >
          {DANGER_REPORT_STATUS_MAP.map((item) => (
            <Form.Select.Option key={item.id} value={item.id}>
              {item.name}
            </Form.Select.Option>
          ))}
        </Form.Select>
      ) : null}
    </>
  );
};
