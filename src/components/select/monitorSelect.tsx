import { useState, useCallback, useMemo, FC, useEffect } from "react";
import { Form, useFormApi, useFormState } from "@douyinfe/semi-ui";
import { useQuery, useMutation } from "@tanstack/react-query";
import { getMonitorList } from "api";
import type { FieldList } from "../enum";

export type MonitorSelectProps = {
  field: string;
  placeholder?: string;
  label?: string;
  isRequired?: boolean;
};

export const MonitorSelect: FC<MonitorSelectProps> = ({
  field,
  placeholder,
  label,
  isRequired,
  ...rest
}) => {
  const { data } = useQuery({
    queryKey: ["getMonitorList"],
    queryFn: () =>
      getMonitorList({
        filter: {},
        pageNumber: 1,
        pageSize: 100,
      }),
  });

  const options = useMemo(() => {
    return data?.data?.results ?? [];
  }, [data]);

  return (
    <Form.Select
      field={field}
      noLabel={!Boolean(label)}
      label={label ?? ""}
      placeholder={placeholder ?? ""}
      className="w-full"
      rules={[{ required: isRequired, message: "此为必填项!" }]}
      {...rest}
    >
      {options.map((o) => (
        <Form.Select.Option value={parseInt(o?.id) ?? 0} key={o?.id ?? 0}>
          {o?.name ?? ""}-{o?.area?.name}
        </Form.Select.Option>
      ))}
    </Form.Select>
  );
};
