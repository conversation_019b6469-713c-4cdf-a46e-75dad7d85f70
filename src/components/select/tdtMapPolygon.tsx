import { Modal, useFormApi } from "@douyinfe/semi-ui";
import { useQuery } from "@tanstack/react-query";
import { getDrawAreas } from "api";
import { mapPolygonAtom } from "atoms";
import "cesium/Build/Cesium/Widgets/widgets.css";
import { DrawItem, TdPcMap } from "components/tiandituMap";
import { useAtom } from "jotai";
import { useResetAtom } from "jotai/utils";
import { type } from "ramda";
import { FC, useEffect, useMemo, useRef, useState } from "react";
import { renderMarkerList } from "utils";

type TdtMapPolygonProps = {
  field: string;
  entity?: string;
};

/**
 * 模型地图多边形绘制组件
 *
 * See also {@link AreaDrawer} {@link AreaMapPicker} {@link AreaMapMarker}
 *
 * See also {@link MapPicker}
 *
 * See also {@link AreaPage}
 *
 * @example
 * ```typescript
 * <TdtMapPolygon  />
 * ```
 */
export const TdtMapPolygon: FC<TdtMapPolygonProps> = ({ entity, field }) => {
  const [markers, setMarkers] = useState<DrawItem>();
  const [curPostion, setCurPostion] = useState<DrawItem>();
  const mapRef = useRef()

  const formApi = useFormApi();
  const formState = formApi.getFormState();
  const color = "#fb6260";
  const [polygonDrawer, setPolygonDrawer] = useAtom(mapPolygonAtom);
  const reset = useResetAtom(mapPolygonAtom)


  const { isLoading, data } = useQuery({
    queryKey: ["getDrawAreas"],
    queryFn: getDrawAreas,
  });

  useEffect(() => {
    if (
      !formApi.getValue(`${field}`) && !curPostion
    ) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          // 在控制台输出经纬度和海拔高度
          console.log(`纬度：${position.coords.latitude}`);
          console.log(`经度：${position.coords.longitude}`);
          const lng = position.coords.longitude
          const lat = position.coords.latitude;
          // const bd = wgs84tobd09(lng, lat)

          setCurPostion({
            lng: lng,
            lat: lat,
          });
        },
        (error) => {
          // 在控制台输出错误信息
          console.log(error);
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 1,
        },
      );
    }
  }, [
    formApi.getValue(`${field}`),
  ]);

  const handleOk = () => {

    const tmp = mapRef.current?.getOverlays();
    const list = []
    tmp.forEach((o) => {
      const ll = o?.getLngLats?.();
      ll?.forEach?.((item: any) => {
        item.forEach((i: any) => {
          list.push({
            longitude: i.getLng(),
            latitude: i.getLat()
          })
        });

      });
    });
    if (list.length) {
      const markers = renderMarkerList(list)
      formApi.setValue(field, list);
      formApi.setValue(`${field}Show`, markers);
    }
    reset()
  }

  const pol = useMemo(() => {
    if (
      formApi.getValue(`${field}`) &&
      type(formApi.getValue(`${field}`)) === "Array" &&
      !markers
    ) {

      const lnglats = formApi.getValue(`${field}`)
      const list = []
      lnglats?.forEach?.((o: any) => {
        list.push({
          lng: o.longitude,
          lat: o.latitude
        })
      })
      return list;
    }
    return []
  }, [formApi.getValue(`${field}`)])


  return (
    <>
      <Modal
        title={`绘制${entity}多边形`}
        width="fit-content"
        height="fit-content"
        visible={polygonDrawer?.visible ?? false}
        onCancel={reset}
        onOk={handleOk}
        maskClosable={false}
        centered
      >

        <div
          className="w-[80vw] h-[70vh] min-w-[800px] min-h-[500px]"
        >
          <TdPcMap
            position={curPostion}
            polygon={pol}
            callbackRef={(ref) => {
              mapRef.current = ref;
            }}
          />

        </div>
      </Modal>
    </>
  );
};
