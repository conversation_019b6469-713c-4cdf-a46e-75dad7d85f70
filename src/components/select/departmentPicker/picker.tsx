import { Form } from "@douyinfe/semi-ui";
import { useQuery } from "@tanstack/react-query";
import { getDepartmentList } from "api";
import { convert } from "components/tree";
import { find, propEq } from "ramda";
import { useEffect, useMemo, useState } from "react";

export const DepartmentPicker = (porps) => {
  const [expandedKeys, setExpandedKeys] = useState(["0"]);
  const { data } = useQuery({
    queryKey: ["getDepartmentList"],
    queryFn: getDepartmentList,
  });

  // 转换函数
  function convert_deprecated(data) {
    const result = [];
    const map = {};
    const hasIds = [];

    function traverse(items, parentKey) {
      (items ?? []).forEach((_item) => {
        let item = _item;
        if (typeof item === "number") {
          item = find(propEq(item, "id"))(data.data);
        }

        const key = parentKey ? `${parentKey}-${item.id}` : String(item.id);
        const v =
          porps?.valueType == "object"
            ? JSON.stringify({ id: item.id, name: item.name })
            : item.id;
        const treeItem = {
          label: item.name,
          value: v,
          key,
          children: [],
        };

        map[key] = treeItem;

        if (item.children) {
          traverse(item.children, key);
        }
        if (hasIds.includes(item.id)) {
          return;
        }
        if (parentKey) {
          map[parentKey].children.push(treeItem);
        } else {
          result.push(treeItem);
        }
        hasIds.push(item.id);
      });
    }

    traverse(data.data, null);
    return result;
  }

  const treeData = useMemo(() => {
    if (data?.data?.length) {
      return convert(data);
    }
    return [];
  }, [data]);

  useEffect(() => {
    const keys = [];
    function traverse(items) {
      (items ?? []).forEach((item) => {
        keys.push(item.key);
        if (item.children) {
          traverse(item.children);
        }
      });
    }
    traverse(treeData);
    setExpandedKeys(keys);
  }, [treeData]);

  return (
    <>
      <Form.TreeSelect
        {...porps}
        treeData={treeData}
        filterTreeNode
        className="w-full"
        checkRelation="unRelated"
        onExpand={(expandedKeys) => {
          setExpandedKeys(expandedKeys);
        }}
        onSearch={(inputValue, filteredExpandedKeys) => {
          setExpandedKeys([...filteredExpandedKeys]);
        }}
      />
    </>
  );
};
