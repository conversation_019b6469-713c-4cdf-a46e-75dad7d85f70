import { Modal, useFormApi } from "@douyinfe/semi-ui";
//import { useGeolocationControl, useMap, useMarker } from '@uiw/react-baidu-map';

import { mapPickerAtom } from "atoms";
import { DrawItem, TdMap } from "components/tiandituMap/tdMap";
import { useAtom } from "jotai";
import { useResetAtom } from "jotai/utils";
import { type } from "ramda";
import { useEffect, useRef, useState } from "react";

/**
 * 普通地图单点选择组件
 *
 * See also {@link AreaDrawer} {@link AreaMapPicker} {@link AreaMapMarker} {@link AreaMapPolygon}
 *
 * @example
 * ```typescript
 * <AreaMapPicker data={areas} field="map" />
 * ```
 */
export const MapPicker = ({ field }) => {
  const [mapPicker, setMapPicker] = useAtom(mapPickerAtom);
  const mapRef = useRef();
  const reset = useResetAtom(mapPickerAtom);
  const [postion, setPostion] = useState<DrawItem>();
  const [curPostion, setCurPostion] = useState<DrawItem>();

  const formApi = useFormApi();

  useEffect(() => {
    if (
      formApi.getValue(`${field}`) &&
      type(formApi.getValue(`${field}`)) === "String"
    ) {
      const lnglat = formApi.getValue(`${field}`).split(",");
      setPostion({
        lng: parseFloat(lnglat[0]),
        lat: parseFloat(lnglat[1]),
      });
    } else {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          // 在控制台输出经纬度和海拔高度
          console.log(`纬度：${position.coords.latitude}`);
          console.log(`经度：${position.coords.longitude}`);
          const lng = formApi.getValue("longitude")
            ? formApi.getValue("longitude")
            : position.coords.longitude;
          const lat = formApi.getValue("latitude")
            ? formApi.getValue("latitude")
            : position.coords.latitude;
          // const bd = wgs84tobd09(lng, lat)

          setCurPostion({
            lng: lng,
            lat: lat,
          });
        },
        (error) => {
          // 在控制台输出错误信息
          console.log(error);
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 1,
        },
      );
    }
  }, [
    formApi.getValue("latitude"),
    formApi.getValue("longitude"),
    formApi.getValue(`${field}`),
  ]);

  const handleSubmit = () => {
    if (mapRef.current?.getOverlays?.()) {
      const tmp = mapRef.current?.getOverlays();
      tmp.forEach((o) => {
        const ll = o?.getLngLat?.();

        formApi.setValue(field, `${ll?.lng},${ll?.lat}`);
        formApi.setValue("latitude", ll?.lat ?? 0);
        formApi.setValue("longitude", ll?.lng ?? 0);
        reset();
      });
    }
  };

  return (
    <Modal
      title="设置定位"
      visible={mapPicker.visible}
      width={800}
      height={800}
      closeOnEsc={true}
      onCancel={() => {
        reset();
      }}
      footer={
        <div className="flex gap-2 justify-end">
          {/* <button className="btn rounded btn-sm" onClick={() => { mapRef.current?.clearOverLays() }}>
            清空标记
          </button> */}
          <button
            className="btn rounded btn-sm"
            onClick={() => {
              reset();
            }}
          >
            取消
          </button>
          <button
            className="btn rounded btn-primary btn-sm"
            onClick={handleSubmit}
          >
            确定
          </button>
        </div>
      }
    >
      <div className="w-full h-full">
        <TdMap
          position={curPostion}
          marker={postion}
          callbackRef={(ref) => {
            mapRef.current = ref;
          }}
          tools={["marker", "search", "clearAll"]}
          mode="pc"
        />
      </div>
    </Modal>
  );
};
