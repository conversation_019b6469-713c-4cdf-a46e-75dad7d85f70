import { Form, useFormApi, useFormState } from "@douyinfe/semi-ui";
import { filter, propEq } from "ramda";
import { FC, useEffect, useState } from "react";
import { CLASSIFY1_MAP, CLASSIFY2_MAP } from "../enum";

export type ClassifySelectProps = {
  selectType: 1 | 2;
  field: string;
  placeholder?: string;
  label?: string;
  isRequired?: boolean;
  association?: Array<string>; // 自动补全对应表单
  where?: string; // 查询约束
  disabled?: boolean;
};

export const ClassifySelect: FC<ClassifySelectProps> = ({
  selectType,
  field,
  placeholder,
  label,
  isRequired,
  association,
  where,
  disabled,
}) => {
  const formApi = useFormApi();
  const formState = useFormState();
  const whereQuery = formApi.getValue([`${where}`]);
  const [list, setList] = useState([]);
  useEffect(() => {
    if (selectType === 2) {
      const pid = whereQuery;
      const filterArr = filter(propEq(pid, "pid"))(CLASSIFY2_MAP);
      setList(filterArr);
      if (filterArr.length) {
        if (!filterArr.some(({ id }) => id === formState.values[field])) {
          // formApi.setValue(field, `${filterArr[0].pid}-${filterArr[0].id}`);
          formApi.setValue(field, undefined);
        } else {
          const value = filterArr.filter(
            ({ id }) => id === formState.values[field]
          );
          formApi.setValue(field, `${value[0].pid}-${value[0].id}`);
        }
      } else if (formState.values?.[field] !== undefined) {
        formApi.setValue(field, undefined);
      }
    }
  }, [selectType, whereQuery]);

  return selectType === 1 ? (
    <Form.Select
      field={field}
      noLabel={!Boolean(label)}
      label={label ?? ""}
      placeholder={placeholder ?? "请选择一级分类"}
      className="w-full"
      disabled={disabled}
      rules={[{ required: isRequired, message: "此为必填项!" }]}
    >
      {CLASSIFY1_MAP.map((o) => (
        <Form.Select.Option value={parseInt(o?.id) ?? 0} key={o?.id ?? 0}>
          {o?.name ?? ""}
        </Form.Select.Option>
      ))}
    </Form.Select>
  ) : (
    <Form.Select
      field={field}
      noLabel={!Boolean(label)}
      label={label ?? ""}
      placeholder={placeholder ?? "请选择二级分类"}
      className="w-full"
      disabled={disabled}
      rules={[{ required: isRequired, message: "此为必填项!" }]}
    >
      {list.map((o) => (
        <Form.Select.Option
          value={`${parseInt(o?.pid) ?? 0}-${parseInt(o?.id) ?? 0}`}
          key={o?.id ?? 0}
        >
          {o?.name ?? ""}
        </Form.Select.Option>
      ))}
    </Form.Select>
  );
};
