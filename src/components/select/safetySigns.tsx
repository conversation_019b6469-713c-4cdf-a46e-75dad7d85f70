import { useState, useCallback, useMemo, FC, useEffect } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import {
  Form,
  useFormApi,
  useFormState,
  Avatar,
  Modal,
  Button,
  CheckboxGroup,
  Checkbox,
} from "@douyinfe/semi-ui";
import { IconSpin } from "@douyinfe/semi-icons";
import { useAtom } from "jotai";
import { propEq, find, filter, equals } from "ramda";
import type { FieldList } from "../enum";
import { base_url } from "config";

export type SafetySignsProps = {
  field: string;
  label: string;
  placeholder: string;
  location: string;
  defaultValue: string[];
  onOk: () => void;
};

export const SafetySigns: FC<SafetySignsProps> = ({
  field,
  label,
  placeholder,
  defaultValue,
  onOk,
  suffix,
}) => {
  const { isLoading, data } = useQuery({
    queryKey: [`safetySigns`],
    queryFn: () => {
      return fetch(`${base_url}/static/signs/index.json`).then((res) =>
        res.json(),
      );
    },
  });
  const formApi = useFormApi();
  const [value, setValue] = useState(defaultValue);
  const [visible, setVisible] = useState(false);
  const showDialog = () => setVisible(true);
  const hideDialog = () => {
    formApi.setValue(field, value);
    setVisible(false);
  };

  useEffect(() => {
    if (!equals(value, defaultValue)) {
      setValue(defaultValue);
    }
  }, [defaultValue]);

  return (
    <Form.Slot
      label={{
        text: label,
      }}
    >
      <div className="flex items-start justify-start gap-2">
        <button className="btn btn-primary rounded btn-sm" onClick={showDialog}>
          {placeholder}
        </button>
        {suffix}
      </div>
      <Modal
        style={{ width: "80vw" }}
        title={placeholder}
        visible={visible}
        footer={
          <Button
            onClick={() => {
              onOk(value);
              hideDialog();
            }}
          >
            完成
          </Button>
        }
        onCancel={hideDialog}
        closeOnEsc={true}
      >
        {isLoading ? (
          <div className="w-full h-full flex items-center justify-center">
            <IconSpin spin />
          </div>
        ) : (
          <CheckboxGroup
            onChange={setValue}
            value={value}
            field={field}
            label={" "}
            type="card"
          >
            <div className="flex justify-start items-start flex-wrap gap-3 unit-safety-signs">
              {(data ?? [])?.map((sign) => {
                return (
                  <Checkbox value={sign} style={{ width: 200 }}>
                    <Avatar
                      style={{
                        display: "block",
                        width: "fit-content",
                        height: "fit-content",
                      }}
                      shape="square"
                      src={`${base_url}${sign}`}
                    />
                  </Checkbox>
                );
              })}
            </div>
          </CheckboxGroup>
        )}
      </Modal>
    </Form.Slot>
  );
};
