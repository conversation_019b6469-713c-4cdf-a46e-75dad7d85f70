import React, { useState, useMemo, useEffect, useCallback } from "react";
import { useAtom } from "jotai";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Checkbox } from "@douyinfe/semi-ui";
import { findIndex, find, propEq, last, remove, pick, clone } from "ramda";
import { getCertificateList } from "api";
import { certificatePickerSearchAtom, certificatePickerPickAtom } from "atoms";
import dayjs from "dayjs";
import { formatArrayImg } from "utils";

export const Right = () => {
  const [selectedKey, setSelectedKey] = useAtom(certificatePickerSearchAtom);
  const [pickUser, setPickUser] = useAtom(certificatePickerPickAtom);

  const { isLoading, data, refetch } = useQuery({
    queryKey: ["getCertificateList", selectedKey?.lastUser?.id],
    queryFn: () =>
      getCertificateList({
        pageNumber: 1,
        pageSize: 100,
        filter: {
          employeeId: selectedKey?.lastUser?.id,
        },
      }),
    enabled: Bo<PERSON><PERSON>(selectedKey?.lastUser?.id),
  });

  const certificateList = useMemo(() => {
    return data?.data?.results ?? [];
  }, [selectedKey, data]);

  useEffect(() => {
    const uid = selectedKey?.lastUser?.id;

    // 判断是否已经选中该人员
    if (!uid) {
      return;
    }
    console.log(uid, "uiduid", pickUser, certificateList?.length);
    // 检查该员工是否只有一个证书，只有一个证书直接勾选
    if (certificateList?.length === 1) {
      // 检查证书是否过期
      if (dayjs().diff(certificateList[0].expireDate, "day") < 0) {
        const tmp = {
          id: uid,
          type: 1,
          certificateIds: [certificateList[0]],
        };
        setPickUser([...pickUser, tmp]);
      }
    }
    if (certificateList?.length === 0 && !isLoading) {
      const tmp = {
        id: uid,
        type: 1,
        certificateIds: [],
      };
      setPickUser([...pickUser, tmp]);
    }
  }, [certificateList, isLoading, selectedKey?.lastUser?.id]);

  const handleSetChecked = useCallback(
    (checked, item) => {
      console.log(pickUser);
      const _pickUser = clone(pickUser);
      const uid = selectedKey.lastUser.id;
      console.log(_pickUser, "xxxxxxxxxxxxxxx", item);
      _pickUser.forEach((o) => {
        if (o.id === uid) {
          if (checked) {
            if (!o.certificateIds.includes(item)) {
              o.certificateIds.push(item);
            }
          } else {
            const index = findIndex((o) => o === item.id)(o.certificateIds);
            o.certificateIds = remove(index, 1, o.certificateIds);
          }
        } else {
          const tmp = [
            {
              id: o.id,
              type: 1,
              certificateIds: [item],
            },
          ];
          setPickUser(tmp);
        }
      });
      setPickUser(_pickUser);
    },
    [setPickUser, pickUser],
  );

  const hasExpireDate = (str: string): boolean => {
    return dayjs().diff(str, "day") > 0;
  };

  const hasChecked = (item: any): boolean => {
    const tmp = find(propEq(selectedKey?.lastUser?.id, "id"))(pickUser);
    const has = tmp?.certificateIds?.includes?.(item);
    return has;
  };
  console.log(pickUser, "pickUserpickUserpickUserpickUserpickUser");
  return isLoading ? null : (
    <div className="flex gap-2 flex-col w-[300px] border rounded-md h-[450px] overflow-y-auto">
      <div className="gap-2 w-full flex flex-col p-2 overflow-auto">
        {certificateList?.length ? null : (
          <p className="text-gray-500 text-center">请先选择人员</p>
        )}
        {certificateList.map((item, k) => (
          <div className="p-1 px-2 rounded-md border" key={k}>
            <Checkbox
              onChange={(e) => {
                handleSetChecked(e.target.checked, item);
              }}
              disabled={hasExpireDate(item?.expireDate)}
              checked={hasChecked(item)}
            >
              <div className="flex flex-col gap-2 ">
                <img src={formatArrayImg(item?.images)} className="w-full" />
                <div className="ml-2 text-sm">
                  <div className="name">
                    证书名称：{item?.certificateTypeValue?.dicValue}
                  </div>
                  <div className="text-xs text-gray-500">
                    证书编号: {item?.code}
                  </div>
                  <div className="text-xs text-gray-500">
                    有效期: {dayjs(item?.expireDate).format("YYYY-MM-DD")}
                  </div>
                </div>
              </div>
            </Checkbox>
          </div>
        ))}
      </div>
    </div>
  );
};
