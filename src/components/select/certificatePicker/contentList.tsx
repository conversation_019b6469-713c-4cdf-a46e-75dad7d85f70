import React, { useState, useMemo, useEffect, useCallback } from "react";
import { useAtom } from "jotai";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Table, Tree, Checkbox, Avatar } from "@douyinfe/semi-ui";
import { findIndex, find, propEq, last, remove } from "ramda";
import { getEmployeeList } from "api";
import { certificatePickerSearchAtom } from "atoms";

export const ContentList = () => {
  const [selectedKey, setSelectedKey] = useAtom(certificatePickerSearchAtom);

  const { isLoading, isError, error, data, refetch } = useQuery({
    queryKey: ["getEmployeeList", selectedKey?.selected],
    queryFn: () =>
      getEmployeeList({
        pageNumber: 1,
        pageSize: 100,
        filter: {
          status: 1,
        },
        departmentId: selectedKey?.selected,
      }),
    enabled: Boolean(selectedKey?.selected),
  });

  const userlist = useMemo(() => {
    return data?.data?.results ?? [];
  }, [selectedKey, data]);

  const handleSetChecked = useCallback(
    (checked, item) => {
      let tmp = [...selectedKey.checkedUser];
      if (checked) {
        tmp.push(item);
      } else {
        const index = tmp.findIndex((o) => o.id === item.id);
        tmp = remove(index, index + 1, tmp);
      }
      setSelectedKey({
        ...selectedKey,
        checkedUser: tmp,
        lastUser: checked ? item : null,
      });
    },
    [setSelectedKey, selectedKey],
  );

  const handleAll = useCallback(() => {
    setSelectedKey({
      ...selectedKey,
      checkedUser: selectedKey?.checkedUser?.length === 0 ? userlist : [],
    });
  }, [userlist, setSelectedKey, selectedKey]);

  return (
    <div className="flex gap-2 flex-col min-w-[510px] max-w-full border rounded-md h-[450px] overflow-y-auto">
      <div className="w-full bg-gray-100 rounded p-1 px-2 flex justify-between items-center">
        <span>已选{selectedKey?.checkedUser?.length}项</span>
        <button className="btn rounded btn-primary btn-sm" onClick={handleAll}>
          {selectedKey?.checkedUser?.length === 0 ? "全选" : "重置"}
        </button>
      </div>
      <div className="gap-2 w-full grid grid-cols-2 px-2">
        {userlist.map((item, k) => (
          <div className="p-1 px-2 rounded-md border" key={k}>
            <Checkbox
              onChange={(e) => {
                handleSetChecked(e.target.checked, item);
              }}
              checked={
                findIndex(propEq(item?.id, "id"))(selectedKey?.checkedUser) >= 0
              }
              className="flex items-center h-[52px]"
            >
              <Avatar color={"blue"} size="small">
                {last(item?.name ?? "")}
              </Avatar>
              <div className="ml-2 text-sm">
                <div className="name">{item?.name}</div>
                <div className="text-xs text-gray-500">
                  {item?.position?.name}-{item?.employeeId}
                </div>
              </div>
            </Checkbox>
          </div>
        ))}
      </div>
    </div>
  );
};
