import React, { useState, useMemo, useEffect, useCallback } from "react";
import { useAtom } from "jotai";
import { Tree } from "@douyinfe/semi-ui";
import { find, propEq } from "ramda";
import { useQuery, useMutation } from "@tanstack/react-query";
import { getDepartmentList } from "api";
import { certificatePickerSearchAtom } from "atoms";

export const Insider = () => {
  const [selected, setSelectedKey] = useAtom(certificatePickerSearchAtom);
  const [expandedKeys, setExpandedKeys] = useState(["0"]);
  const { data } = useQuery({
    queryKey: ["getDepartmentList"],
    queryFn: getDepartmentList,
  });

  // 转换函数
  function convert(data) {
    const result = [];
    const map = {};
    const hasIds = [];

    function traverse(items, parentKey) {
      (items ?? []).forEach((_item) => {
        let item = _item;
        if (typeof item === "number") {
          item = find(propEq(item, "id"))(data.data);
        }

        const key = parentKey ? `${parentKey}-${item.id}` : String(item.id);

        const treeItem = {
          label: item.name,
          value: item.id,
          key,
          children: [],
        };

        map[key] = treeItem;

        if (item.children) {
          traverse(item.children, key);
        }
        if (hasIds.includes(item.id)) {
          return;
        }
        if (parentKey) {
          map[parentKey].children.push(treeItem);
        } else {
          result.push(treeItem);
        }
        hasIds.push(item.id);
      });
    }

    traverse(data.data, null);
    return result;
  }

  const treeData = useMemo(() => {
    if (data?.data?.length) {
      return convert(data);
    }
    return [];
  }, [data]);

  useEffect(() => {
    const keys = [];
    function traverse(items) {
      (items ?? []).forEach((item) => {
        keys.push(item.key);
        if (item.children) {
          traverse(item.children);
        }
      });
    }
    traverse(treeData);
    setExpandedKeys(keys);
  }, [treeData]);

  const handleSelect = useCallback(
    (selectedKey, s, selectedNode) => {
      setSelectedKey({
        ...selected,
        selected: selectedNode.value,
      });
    },
    [setSelectedKey, selected],
  );

  return (
    <div className="bg-white px max-h-full rounded-md w-[220px] border h-[450px] overflow-y-auto">
      <Tree
        treeData={treeData}
        filterTreeNode
        // value={value}
        onSelect={handleSelect}
        onExpand={(expandedKeys) => {
          setExpandedKeys(expandedKeys);
        }}
        onSearch={(inputValue, filteredExpandedKeys) => {
          setExpandedKeys([...filteredExpandedKeys]);
        }}
      />
    </div>
  );
};
