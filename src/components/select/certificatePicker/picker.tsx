import { FC, useCallback } from "react";
/* import { useQuery, useMutation } from '@tanstack/react-query' */
import { IconApartment, IconHelpCircle } from "@douyinfe/semi-icons";
import { Modal, TabPane, Tabs } from "@douyinfe/semi-ui";
import {
  certificatePickerAtom,
  certificatePickerPickAtom,
  certificatePickerSearchAtom,
} from "atoms";
import { useAtom } from "jotai";
import { find, propEq } from "ramda";
import { ContentList } from "./contentList";
import { Insider } from "./insider";
import { Right } from "./right";

export type CertificatePickerProps = {
  callback: (v: any[]) => void;
};

export const CertificatePicker: FC<CertificatePickerProps> = ({ callback }) => {
  const [atom, setAtom] = useAtom(certificatePickerAtom);
  const [pickUser, setPickUser] = useAtom(certificatePickerPickAtom);
  const [selected, setSelectedKey] = useAtom(certificatePickerSearchAtom);

  const handleClose = useCallback(() => {
    setAtom({
      ...atom,
      visible: false,
    });
  }, [setAtom, atom]);

  const handleSave = useCallback(() => {
    setAtom({
      ...atom,
      visible: false,
    });
    const mergeList = [];
    selected.checkedUser.forEach((e) => {
      const item = find(propEq(e.id, "id"))(pickUser);
      if (item?.id) {
        mergeList.push({
          ...item,
          other: e,
        });
      }
    });

    callback(mergeList);
  }, [setAtom, pickUser, callback, selected]);

  return (
    <Modal
      title={"选择作业人员"}
      visible={atom.visible}
      width={1100}
      height={750}
      zIndex={10000}
      onCancel={handleClose}
      maskClosable={false}
      keepDOM
      footer={
        <div className="flex gap-2 justify-end">
          <button className="btn rounded btn-sm" onClick={handleClose}>
            取消
          </button>
          <button
            className="btn rounded btn-primary btn-sm"
            onClick={handleSave}
          >
            确定
          </button>
        </div>
      }
      centered
    >
      <Tabs>
        <TabPane
          tab={
            <span>
              <IconApartment />
              本公司
            </span>
          }
          itemKey="1"
        >
          <div className="w-full flex gap-2 pt-1">
            <Insider />
            <ContentList />
            <Right />
            {/* <Department />
                <ContentList /> */}
          </div>
        </TabPane>
        <TabPane
          tab={
            <span>
              <IconHelpCircle />
              承包商
            </span>
          }
          itemKey="3"
        >
          空
        </TabPane>
      </Tabs>
    </Modal>
  );
};
