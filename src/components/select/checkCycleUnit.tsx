import { useState, useCallback, useMemo, FC, useEffect } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { Form, useFormApi, useFormState } from "@douyinfe/semi-ui";
import { useAtom } from "jotai";
import { propEq, find, filter } from "ramda";
import { CHECK_CYCLE_UNIT_MAP, CLASSIFY2_MAP } from "../enum";

export type CheckCycleUnitSelectProps = {
  field: string;
  placeholder?: string;
  label?: string;
  isRequired?: boolean;
};

export const CheckCycleUnitSelect: FC<CheckCycleUnitSelectProps> = ({
  field,
  placeholder,
  label,
  isRequired,
}) => {
  const formApi = useFormApi();
  const formState = useFormState();

  return (
    <Form.Select
      field={field}
      noLabel={!Boolean(label)}
      label={label ?? ""}
      placeholder={placeholder ?? "请选择单位"}
      className="w-full"
      rules={[{ required: isRequired, message: "此为必填项!" }]}
    >
      {CHECK_CYCLE_UNIT_MAP.map((o) => (
        <Form.Select.Option value={parseInt(o?.id) ?? 0} key={o?.id ?? 0}>
          {o?.name ?? ""}
        </Form.Select.Option>
      ))}
    </Form.Select>
  );
};
