import { Modal, useForm<PERSON>pi } from "@douyinfe/semi-ui";
import { areaDrawerModalAtom } from "atoms/doubleGuard";
import * as Cesium from "cesium";
import "cesium/Build/Cesium/Widgets/widgets.css";
import { RISK_LEVEL_COLOR_MAP } from "components";
import {
  cesiumInit,
  cesiumSceneLightOptions,
  cesiumViewerOptions,
} from "hooks/cesiumInit";
import { useAtom } from "jotai";
import { useResetAtom } from "jotai/utils";
import { FC, useCallback, useEffect, useRef, useState } from "react";

type AreaMapPickerProps = {
  data: any;
  field?: string;
};

const createPoint = (longitude, latitude, _h) => {
  const height = _h + 1; // 在原有高度基础上增加1米

  const position = Cesium.Cartesian3.fromDegrees(
    parseFloat(`${longitude}`),
    parseFloat(`${latitude}`),
    height,
  );
  const modelUrl = "/3d_lowpoly_arrow.glb";

  return new Cesium.Entity({
    position: position,
    model: {
      uri: modelUrl,
      minimumPixelSize: 80,
      maximumScale: 20000,
    },
  });
};

/**
 * 模型地图单点选择组件
 *
 * See also {@link AreaDrawer} {@link AreaMapMarker} {@link AreaMapPolygon}
 *
 * See also {@link MapPicker}
 *
 * @example
 * ```typescript
 * <AreaMapPicker data={areas} field="map" />
 * ```
 */
export const AreaMapPicker: FC<AreaMapPickerProps> = ({ data, field }) => {
  const reset = useResetAtom(areaDrawerModalAtom);
  const [areaDrawer] = useAtom(areaDrawerModalAtom);
  const id = `cesium-${areaDrawer?.id}`;
  const [marker, setMarker] = useState<Cesium.Entity | null>(null);
  const [triangleVertices, setTriangleVertices] = useState<any>([]); // 三角形顶点坐标
  const [overflowBytesTimes, setOverflowBytesTimes] = useState(1);
  const cesiumRef = useRef<any>();
  const viewerRef = useRef<any>();
  const entities = useRef<any>({});
  const drawer = useRef<any>();
  const tilesetRef = useRef<any>();
  const [drawing, toggleDrawing] = useState(false);
  const formApi = useFormApi();

  const drawAreaOverlay = useCallback((entity) => {
    const color =
      RISK_LEVEL_COLOR_MAP.find((risk) => risk.id === (entity.riskLevel || 5))
        ?.color ?? "";
    entities.current[entity.id] = new Cesium.Entity({
      id: `risk-overlay-${entity.id}`,
      polygon: {
        hierarchy: Cesium.Cartesian3.fromDegreesArray(
          entity.points
            .map((point) => [point.longitude, point.latitude])
            .flat(2),
        ),
        material: Cesium.Color.fromCssColorString(color).withAlpha(0.6),
      },
    });
    viewerRef.current.entities.add(entities.current[entity.id]);
  }, []);

  const initializeCesium = useCallback(async () => {
    if (!data?.data?.cesiumUriPrefix) {
      if (cesiumRef.current) {
        cesiumRef.current.innerText = "Cesium 地址 uri prefix 不可用";
      }
      return;
    }
    cesiumRef.current.innerText = "";
    // Cesium.Ion.defaultAccessToken = import.meta.env.VITE_CESIUM_TOKEN;
    console.log(data.data.cesiumToken);
    Cesium.Ion.defaultAccessToken = data.data.cesiumToken;

    const viewer = new Cesium.Viewer(id, cesiumViewerOptions);
    viewerRef.current = viewer;
    try {
      viewer._cesiumWidget._creditContainer.parentNode.removeChild(
        viewer._cesiumWidget._creditContainer,
      );
      viewer.scene.light = new Cesium.DirectionalLight(cesiumSceneLightOptions);

      const tileset = await cesiumInit(data, viewer);

      const model = viewer.scene.primitives.add(tileset);
      viewer.scene.globe.depthTestAgainstTerrain = true;
      tilesetRef.current = tileset;
      await viewer.zoomTo(tileset);

      // 根据表单填充内容，初始化标记
      let longitude = formApi.getValue("longitude");
      let latitude = formApi.getValue("latitude");
      let height = 14;

      if (!longitude || !latitude) {
        const maps = formApi.getValue(field);
        longitude = parseFloat(`${maps?.split(",")?.[0] ?? 0}`);
        latitude = parseFloat(`${maps?.split(",")?.[1] ?? 0}`);

        // height = Cesium.Cartographic.fromDegrees(longitude, latitude)?.height ?? 14;

        setTimeout(() => {
          const position = Cesium.Cartesian3.fromDegrees(longitude, latitude);
          const heightPosition = viewer.scene.clampToHeight(position);
          const cartographic =
            Cesium.Cartographic.fromCartesian(heightPosition);
          const h = cartographic.height;
          viewerRef.current.entities.removeAll();
          viewerRef.current.entities.add(createPoint(longitude, latitude, h));
        }, 3000);
        const position = Cesium.Cartesian3.fromDegrees(longitude, latitude);

        const heightPosition = viewer.scene.clampToHeight(position);

        if (heightPosition) {
          const cartographic =
            Cesium.Cartographic.fromCartesian(heightPosition);
          // height = cartographic.height;
          console.log("模型最高点的高度:", height);
        } else {
          console.log("该位置上方没有模型");
        }
      }

      const point = createPoint(longitude, latitude, height);

      if (point) {
        // 创建一个立体的三角形
        const newMarker = viewerRef.current.entities.add(point);
        setTriangleVertices([longitude, latitude]);
        setMarker(newMarker);
      }

      data?.data?.areas?.forEach?.(drawAreaOverlay);
    } catch (error) {
      console.log(`Error loading tileset: ${error} `);
      if (error?.message?.includes("maximumCacheOverflowBytes")) {
        setOverflowBytesTimes((_) => _ + 1);
      }
    }
  }, [
    id,
    overflowBytesTimes,
    data?.data,
    drawAreaOverlay,
    setTriangleVertices,
    setMarker,
    formApi.getValue(field),
  ]);

  useEffect(() => {
    if (areaDrawer.show && data?.data?.cesiumUriPrefix) {
      setTimeout(initializeCesium);
    }
    return () => {
      if (!areaDrawer.show) {
        if (viewerRef.current) {
          viewerRef.current.destroy();
          viewerRef.current = null;
        }
        if (cesiumRef.current) {
          cesiumRef.current.innerHTML = "";
        }
      }
    };
  }, [id, initializeCesium, areaDrawer.show]);

  const handleClose = useCallback(() => {
    if (viewerRef.current) {
      viewerRef.current.destroy();
      viewerRef.current = null;
    }
    if (cesiumRef.current) {
      cesiumRef.current.innerHTML = "";
    }
    if (drawer.current) {
      drawer.current.clear();
      drawer.current = null;
    }
    if (drawing) {
      toggleDrawing(false);
    }
    reset();
  }, [reset]);

  const handleResetMarker = useCallback(() => {
    if (marker && viewerRef.current) {
      viewerRef.current.entities.removeAll();
      setMarker(null);
      setTriangleVertices([]);
    }
  }, [areaDrawer, marker, setTriangleVertices]);

  const handleStartMark = useCallback(() => {
    handleResetMarker(); // 先清除现有的标记
    if (!viewerRef.current) return;
    const handler = new Cesium.ScreenSpaceEventHandler(
      viewerRef.current.scene.canvas,
    );
    handler.setInputAction(
      (click: Cesium.ScreenSpaceEventHandler.PositionedEvent) => {
        viewerRef.current.entities.removeAll();
        const cartesian = viewerRef.current.scene.pickPosition(click.position);

        if (Cesium.defined(cartesian)) {
          const cartographic = Cesium.Cartographic.fromCartesian(cartesian);
          const longitude = Cesium.Math.toDegrees(cartographic.longitude);
          const latitude = Cesium.Math.toDegrees(cartographic.latitude);
          const height = cartographic.height;

          // 移除之前的标记
          if (marker) {
            viewerRef.current.entities.removeAll();
          }

          const point = createPoint(longitude, latitude, height);

          // 创建一个立体的三角形
          const newMarker = viewerRef.current.entities.add(point);

          // 保存顶点坐标到状态中
          setTriangleVertices([longitude, latitude]);
          setMarker(newMarker);
          // 立即移除监听器，监听是一次性的
          handler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
        }
      },
      Cesium.ScreenSpaceEventType.LEFT_CLICK,
    );
  }, [marker, areaDrawer.id, handleResetMarker, setTriangleVertices]);

  useEffect(() => {
    if (!viewerRef?.current?.scene?.canvas) return;
    if (triangleVertices.filter((o: number) => o === 0).length === 2) {
      handleStartMark();
    }
  }, [viewerRef?.current?.scene?.canvas]);

  const handleOk = () => {
    formApi?.setValue?.("longitude", triangleVertices?.[0]);
    formApi?.setValue?.("latitude", triangleVertices?.[1]);
    if (field) {
      formApi?.setValue?.(
        field,
        `${triangleVertices?.[0]},${triangleVertices?.[1]}`,
      );
    }
    handleClose();
  };

  const goHome = () => {
    viewerRef.current.zoomTo(tilesetRef.current);
  };

  return (
    <>
      <Modal
        title="选择定位"
        width="fit-content"
        height="fit-content"
        bodyStyle={{
          width: "fit-content",
          height: "fit-content",
          paddingBottom: 20,
        }}
        visible={areaDrawer?.show ?? false}
        onCancel={handleClose}
        maskClosable={false}
        footer={null}
        centered
      >
        <div className="flex gap-3 mb-[20px] items-center">
          <button
            className="btn rounded btn-primary btn-sm"
            onClick={handleStartMark}
          >
            {!marker ? "开始标记" : "重新标记"}
          </button>

          <button className="btn rounded btn-primary btn-sm" onClick={handleOk}>
            确认提交
          </button>

          <button className="btn rounded btn-accent btn-sm" onClick={goHome}>
            重置镜头
          </button>
        </div>

        <div
          ref={cesiumRef}
          className="w-[80vw] h-[70vh] min-w-[800px] min-h-[500px]"
          id={id}
        ></div>
        <div className="flex flex-col text-gray-700 my-4">
          请拖动地图中的标记确定您的定位
          {triangleVertices?.length && (
            <span>
              经度{triangleVertices?.[0]} &nbsp; 纬度{triangleVertices?.[1]}
            </span>
          )}
        </div>
      </Modal>
    </>
  );
};
