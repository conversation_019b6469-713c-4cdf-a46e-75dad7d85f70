import { Tree } from "@douyinfe/semi-ui";
import { useQuery } from "@tanstack/react-query";
import { getDepartmentList } from "api";
import { departmentDataAtom, departmentFnAtom } from "atoms/basicInfo";
import { departmentSearchAtom } from "atoms/search";
import { useAtom } from "jotai";
import { find, propEq } from "ramda";
import { useCallback, useEffect, useMemo, useState } from "react";

export const Department = () => {
  const [departmentData, setDepartmentData] = useAtom(departmentDataAtom);
  const [selected, setSelectedKey] = useAtom(departmentSearchAtom);
  const [departmentFn, setDepartmentFn] = useAtom(departmentFnAtom);

  const [expandedKeys, setExpandedKeys] = useState(["0"]);
  const { isLoading, isError, error, data, refetch } = useQuery({
    queryKey: ["getDepartmentList"],
    queryFn: getDepartmentList,
  });
  useEffect(() => {
    setDepartmentFn({
      refetch: refetch,
    });
  }, [refetch]);

  useEffect(() => {
    if (data?.data?.length) {
      setDepartmentData(data?.data);
    }
  }, [data]);

  // 转换函数
  function convert(data) {
    const result = [
      {
        label: "全部",
        value: null,
        key: "null",
        children: [],
      },
    ];
    const map = {};
    const hasIds = [];

    function traverse(items, parentKey) {
      (items ?? []).forEach((_item) => {
        let item = _item;
        if (typeof item === "number") {
          item = find(propEq(item, "id"))(data.data);
        }

        const key = parentKey ? `${parentKey}-${item.id}` : String(item.id);

        const treeItem = {
          label: item.name,
          value: item.id,
          key,
          children: [],
        };

        map[key] = treeItem;

        if (item.children) {
          traverse(item.children, key);
        }
        if (hasIds.includes(item.id)) {
          return;
        }
        if (parentKey) {
          map[parentKey].children.push(treeItem);
        } else {
          result.push(treeItem);
        }
        hasIds.push(item.id);
      });
    }

    traverse(data.data, null);
    console.log(result, "resultresultresult");

    return result;
  }

  const treeData = useMemo(() => {
    if (data?.data?.length) {
      return convert(data);
    }
    return [];
  }, [data]);

  useEffect(() => {
    const keys = [];
    function traverse(items) {
      (items ?? []).forEach((item) => {
        keys.push(item.key);
        if (item.children) {
          traverse(item.children);
        }
      });
    }
    traverse(treeData);
    setExpandedKeys(keys);
  }, [treeData]);

  const handleSelect = useCallback(
    (selectedKey, s, selectedNode) => {
      setSelectedKey({
        ...selected,
        selected: selectedNode.value,
      });
    },
    [setSelectedKey, selected],
  );

  return (
    <div className="bg-white px  max-h-full rounded-md w-[220px] border h-[450px] overflow-y-auto">
      <Tree
        treeData={treeData}
        filterTreeNode
        // value={value}
        onSelect={handleSelect}
        onExpand={(expandedKeys) => {
          setExpandedKeys(expandedKeys);
        }}
        onSearch={(inputValue, filteredExpandedKeys) => {
          setExpandedKeys([...filteredExpandedKeys]);
        }}
      />
    </div>
  );
};
