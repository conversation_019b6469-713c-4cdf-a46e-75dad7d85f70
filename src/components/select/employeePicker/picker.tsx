import { FC, useCallback, useEffect, useMemo, useState } from "react";
/* import { useQuery, useMutation } from '@tanstack/react-query' */
import {
  IconApartment,
  IconHelpCircle,
  IconUserSetting,
} from "@douyinfe/semi-icons";
import {
  Avatar,
  AvatarGroup,
  Modal,
  TabPane,
  Tabs,
  Tag,
} from "@douyinfe/semi-ui";
import { departmentSearchAtom } from "atoms";
import { useAtom } from "jotai";
import { type } from "ramda";
import { ContentList } from "./contentList";
import { ContractorGroup } from "./contractorGroup";
import { Department } from "./department";
import { WorkGroup } from "./workGroup";

type ServiceRangeItem = 1 | 2 | 3;

export const composeEmployeeNames = (list: any[]) => {
  if (!list?.length) return [];
  return list?.map((o) => {
    if (o?.department) {
      return `${o.name}(${o.department?.name})`;
    }
    // 承包商
    if (o?.contractor) {
      return `${o.name}(${o.contractor?.name})`;
    }
    return `${o?.name}`;
  });
};

export const renderEmployeeSimple = (o: any) => {
  if (o?.type && o?.type === 1) {
    return `${o?.name}`;
  }
  if (o?.type && o?.type === 2) {
    return `${o?.name}(承包商)`;
  }
  return `${o?.name ?? ""}`;
};

export const renderEmployeeSimpleNames = (list: any[]) => {
  if (!list?.length) return [];
  return list?.map((o) => renderEmployeeSimple(o));
};

export const employeePickerCallback = (
  list: any,
  formApi,
  getFormApiRef,
  formField,
  fieldList,
) => {
  const newList = list?.map((o: any) => {
    let obj: { [key: string]: any } = {
      type: o._type !== 3 ? 1 : 2, // 1和2代表内部人员(1)；3代表外部人员(2)
    };
    fieldList.forEach((field) => {
      if (o[field]) {
        obj[field] = o[field];
      }
      obj["_type"] = o._type;
    });
    return obj;
  });
  const names: string[] = composeEmployeeNames(list);

  if (formApi) {
    formApi?.setValue(formField, newList);
    formApi.setValue(`${formField}-renderToText`, names.toString());
  } else if (getFormApiRef) {
    getFormApiRef?.current?.setValue(formField, newList);
    getFormApiRef?.current?.setValue(
      `${formField}-renderToText`,
      names.toString(),
    );
  } else {
    console.error("formApi和getFormApiRef不能同时为空");
  }
  // return newList;
};

export type EmployeePickerProps = {
  // 选择完成后的回调
  callback: (v: any[]) => void;
  showOptions?: boolean;
  showBtn?: boolean;
  show?: boolean;
  // 当前选中的项
  options?: any[];
  // tab渲染范围
  serviceRange?: Array<ServiceRangeItem>;
  // 需要全选的面板
  checkedTabs?: Array<ServiceRangeItem>;
  // 通过外部控制组件是否显示
  setShow?: (arg: boolean) => void;
};

// 人员选择器
// _type(前置下划线，避免和其它type变量名冲突) 1: 部门 2: 工作组 3: 承包商
export const EmployeePicker: FC<EmployeePickerProps> = ({
  callback,
  showOptions,
  showBtn = true,
  setShow,
  show,
  options,
  serviceRange = [1, 2, 3],
  checkedTabs,
}) => {
  const [selectedKey, setSelectedKey] = useAtom(departmentSearchAtom);
  const [visible, setVisible] = useState(false);

  useEffect(() => {
    if (type(show) === "Boolean") {
      setVisible(show ?? false);
    }
  }, [show]);

  useEffect(() => {
    setSelectedKey({
      ...selectedKey,
      checkedUser: options?.filter?.((o) => o?._type == 1) ?? [],
      workCheckedUser: options?.filter?.((o) => o?._type == 2) ?? [],
      contractorCheckedUser: options?.filter?.((o) => o?._type == 3) ?? [],
    });
  }, [options]);

  const list = useMemo(() => {
    const joinData = [
      ...(selectedKey?.checkedUser ?? []),
      ...(selectedKey?.workCheckedUser ?? []),
      ...(selectedKey?.contractorCheckedUser ?? []),
    ];
    const filterJoinData = joinData.filter((o) =>
      serviceRange.includes(o?._type ?? 1),
    );
    return filterJoinData;
  }, [selectedKey, options, serviceRange]);

  const handleClose = useCallback(() => {
    setVisible(false);
    setShow?.(false);
  }, [setVisible, setShow]);

  const handleSave = useCallback(() => {
    setVisible(false);
    setShow?.(false);
    const joinData = [
      ...(selectedKey?.checkedUser ?? []),
      ...(selectedKey?.workCheckedUser ?? []),
      ...(selectedKey?.contractorCheckedUser ?? []),
    ];
    callback?.(joinData);
  }, [setVisible, callback, selectedKey, setShow]);

  const handleOpen = useCallback(() => {
    setVisible(true);
    setShow?.(false);
  }, [visible, setVisible, setShow]);

  return (
    <>
      {showBtn ? (
        <span className="btn rounded btn-primary btn-sm" onClick={handleOpen}>
          配置
        </span>
      ) : null}
      {showOptions ? (
        <div className="flex flex-col gap-2 mt-2">
          <p>候选人范围</p>
          <p className="text-sm text-slate-400">
            {!list?.length ? "默认所有人可选" : null}
          </p>
          <p className="flex gap-1 flex-wrap">
            {list.map((o, i) => (
              <Tag key={i} color="light-blue" size="large" shape="circle">
                {o.name}
              </Tag>
            ))}
          </p>
        </div>
      ) : null}
      <Modal
        title={"人员选择器"}
        visible={visible}
        width={800}
        height={750}
        zIndex={10000}
        onCancel={handleClose}
        maskClosable={false}
        // keepDOM
        footer={
          <div className="flex gap-2 justify-end">
            <button className="btn rounded btn-sm" onClick={handleClose}>
              取消
            </button>
            <span
              className="btn rounded btn-primary btn-sm"
              onClick={handleSave}
            >
              确定
            </span>
          </div>
        }
        centered
      >
        <Tabs
          defaultActiveKey={serviceRange?.[0]?.toString()}
          className="h-[450px]"
        >
          <TabPane
            tab={
              <span>
                <IconApartment />
                部门
              </span>
            }
            disabled={!(serviceRange ?? [])?.includes(1)}
            itemKey="1"
          >
            <div className="w-full flex gap-2 pt-1">
              <Department />
              <ContentList
                defaultCheckedAll={checkedTabs?.includes?.(1) ?? false}
              />
            </div>
            <div className="flex items-center mt-2 gap-2">
              <span className="text-md text-slate-400 ">已选中人员:</span>
              <AvatarGroup maxCount={9}>
                {(selectedKey?.checkedUser ?? []).map((o) => (
                  <Avatar color="red" key={o.id}>
                    {o.name}
                  </Avatar>
                ))}
              </AvatarGroup>
            </div>
          </TabPane>
          {serviceRange?.includes?.(2) ? (
            <TabPane
              disabled={!(serviceRange ?? [])?.includes(2)}
              tab={
                <span>
                  <IconUserSetting />
                  工作组
                </span>
              }
              itemKey="2"
            >
              <WorkGroup
                defaultCheckedAll={checkedTabs?.includes?.(2) ?? false}
              />
            </TabPane>
          ) : null}
          {serviceRange?.includes?.(3) ? (
            <TabPane
              disabled={!(serviceRange ?? [])?.includes(3)}
              tab={
                <span>
                  <IconHelpCircle />
                  承包商
                </span>
              }
              itemKey="3"
            >
              <ContractorGroup
                defaultCheckedAll={checkedTabs?.includes?.(3) ?? false}
              />
            </TabPane>
          ) : null}
        </Tabs>
      </Modal>
    </>
  );
};
