import { IconSearch } from "@douyinfe/semi-icons";
import { Avatar, Checkbox, Input } from "@douyinfe/semi-ui";
import { useQuery } from "@tanstack/react-query";
import { useDebounceFn } from "ahooks";
import { getEmployeeList } from "api";
import { departmentSearchAtom } from "atoms/search";
import { useAtom } from "jotai";
import { findIndex, last, propEq, remove } from "ramda";
import { FC, useCallback, useEffect, useMemo, useState } from "react";
import { listAddType } from "utils";

type ContentListProps = {
  defaultCheckedAll?: boolean
}

export const ContentList: FC<ContentListProps> = ({
  defaultCheckedAll
}) => {
  const [selectedKey, setSelectedKey] = useAtom(departmentSearchAtom);
  const [query, setQuery] = useState<string>("");
  const { run } = useDebounceFn(
    (value) => {
      setQuery(value);
    },
    {
      wait: 300,
    },
  );
  const { isLoading, data, refetch } = useQuery({
    queryKey: ["getEmployeeList", selectedKey?.selected, query],
    queryFn: () =>
      getEmployeeList({
        pageNumber: 1,
        pageSize: 100,
        filter: {
          status: 1,
        },
        query: query,
        departmentId: selectedKey?.selected,
      }),
  });

  const userlist = useMemo(() => {
    return data?.data?.results ?? [];
  }, [selectedKey, data]);

  const handleSetChecked = useCallback(
    (checked, item) => {
      let tmp = selectedKey?.checkedUser?.length
        ? [...selectedKey.checkedUser]
        : [];
      if (checked) {
        tmp.push(item);
      } else {
        const index = tmp.findIndex((o) => o.id === item.id);
        tmp = remove(index, 1, tmp);
      }
      setSelectedKey({
        ...selectedKey,
        checkedUser: listAddType(tmp, 1),
      });
    },
    [setSelectedKey, selectedKey],
  );

  // 参数配置是否-默认全部选中
  useEffect(() => {
    if (!isLoading && defaultCheckedAll) {
      handleAll()
    }
  }, [isLoading, defaultCheckedAll])

  const handleAll = useCallback(() => {
    setSelectedKey({
      ...selectedKey,
      checkedUser: listAddType(
        selectedKey?.checkedUser?.length === 0 ? userlist : [],
        1,
      ),
    });
  }, [userlist, setSelectedKey, selectedKey]);

  const handleChangeQuery = useCallback(
    (value) => {
      run(value);
    },
    [run],
  );

  return (
    <div className="flex gap-2 flex-col w-full border rounded-md h-[450px] overflow-y-auto">
      <Input
        prefix={<IconSearch />}
        placeholder="搜索人员"
        onChange={handleChangeQuery}
        showClear
      />
      <div className="w-full bg-gray-100 rounded p-1 px-2 flex justify-between items-center">
        <span>已选{selectedKey?.checkedUser?.length}项</span>
        <button className="btn rounded btn-primary btn-sm" onClick={handleAll}>
          {selectedKey?.checkedUser?.length === 0 ? "全选" : "重置"}
        </button>
      </div>
      <div className="gap-2 w-full grid grid-cols-3 px-2">
        {userlist.map((item, k) => (
          <div className="p-1 px-2 rounded-md border" key={k}>
            <Checkbox
              onChange={(e) => {
                handleSetChecked(e.target.checked, item);
              }}
              checked={
                findIndex(propEq(item?.id, "id"))(
                  selectedKey?.checkedUser ?? [],
                ) >= 0
              }
              className="flex items-center h-[52px]"
            >
              <Avatar color={"blue"} size="small">
                {last(item?.name ?? "")}
              </Avatar>
              <div className="ml-2 text-sm">
                <div className="name">{item?.name}</div>
                <div className="text-xs text-gray-500">
                  {item?.position?.name}-{item?.employeeId}
                </div>
              </div>
            </Checkbox>
          </div>
        ))}
      </div>
    </div>
  );
};
