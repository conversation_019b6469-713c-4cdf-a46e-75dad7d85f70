import { IconSearch } from "@douyinfe/semi-icons";
import { Avatar, AvatarGroup, Checkbox, Input } from "@douyinfe/semi-ui";
import { useQuery } from "@tanstack/react-query";
import { useDebounceFn } from "ahooks";
import { contractorApis, contractorEmployeeApis } from "api";
import { departmentSearchAtom } from "atoms/search";
import { useAtom } from "jotai";
import { findIndex, last, propEq, remove } from "ramda";
import { FC, useCallback, useEffect, useMemo, useState } from "react";
import { listAddType } from "utils";

type ContractorGroupProps = {
  defaultCheckedAll?: boolean
}

export const ContractorGroup:FC<ContractorGroupProps> = ({defaultCheckedAll}) => {
  const [selectedKey, setSelectedKey] = useAtom(departmentSearchAtom);
  const [cur, setCur] = useState({});
  const [queryRight, setQueryRight] = useState<string>("");
  const { run: runRight } = useDebounceFn(
    (value) => {
      setQueryRight(value);
    },
    {
      wait: 300,
    },
  );
  const [query, setQuery] = useState<string>("");
  const { run } = useDebounceFn(
    (value) => {
      setQuery(value);
    },
    {
      wait: 300,
    },
  );

  const { data: left, isLoading } = useQuery({
    queryKey: [`${contractorApis.entity}_picker`, query],
    queryFn: () => {
      return contractorApis.query({
        filter: {
          isBlack: 2,
        },
        query: query,
        pageNumber: 1,
        pageSize: 999,
      });
    },
  });

  const dataList = useMemo(() => {
    return left?.data?.results ?? [];
  }, [left]);

  useEffect(() => {
    if (!cur?.id) {
      setCur(dataList?.[0] ?? {});
    }
  }, [cur, dataList]);

  
  const { data } = useQuery({
    queryKey: [`${contractorEmployeeApis.entity}`, cur?.id, queryRight],
    queryFn: () => {
      if (!cur?.id) {
        return null;
      }
      return contractorEmployeeApis.query({
        pageNumber: 1,
        pageSize: 100,
        filter: {
          contractorId: cur.id,
          isBlackFixLogic: 2,
          contractorIsBlackFixLogic: 2,
        },
        query: queryRight,
      });
    },
    enabled: Boolean(cur?.id),
  });

  const userlist = useMemo(() => {
    return data?.data?.results ?? [];
  }, [data]);

  // 参数配置是否-默认全部选中
  useEffect(() => {
    if (userlist?.length && defaultCheckedAll) {
      handleAll()
    }
  }, [userlist, defaultCheckedAll])


  const handleSetChecked = useCallback(
    (checked, item) => {
      let tmp = selectedKey?.contractorCheckedUser?.length
        ? [...selectedKey.contractorCheckedUser]
        : [];
      if (checked) {
        tmp.push(item);
      } else {
        const index = tmp.findIndex((o) => o.id === item.id);
        tmp = remove(index, 1, tmp);
      }
      setSelectedKey({
        ...selectedKey,
        contractorCheckedUser: listAddType(tmp, 3),
      });
    },
    [setSelectedKey, selectedKey],
  );

  const handleAll = useCallback(() => {
    
    setSelectedKey({
      ...selectedKey,
      contractorCheckedUser: listAddType(
        selectedKey?.contractorCheckedUser?.length === 0 ? userlist : [],
        3,
      ),
    });
  }, [userlist, setSelectedKey, selectedKey]);

  const handleChangeQuery = useCallback(
    (value) => {
      run(value);
    },
    [run],
  );

  const handleSelect = useCallback(
    (item: any) => {
      setCur(item);
    },
    [cur, setCur],
  );

  return (
    <div>
      <div className="w-full flex gap-2 pt-1">
        <div className="bg-white max-h-full rounded  w-[240px] gap-2 flex flex-col h-[450px] overflow-y-auto">
          <Input
            prefix={<IconSearch />}
            placeholder="搜索承包商"
            onChange={handleChangeQuery}
            showClear
          />
          {dataList.map((item, i) => (
            <div
              className={`p-2 px-2 rounded-md border cursor-pointer transition-colors hover:bg-slate-100 ${cur?.id == item.id ? "bg-slate-200" : ""}`}
              key={i}
              onClick={() => {
                handleSelect(item);
              }}
            >
              <div className="ml-2 text-sm">
                <div className="name">{item?.name}</div>
              </div>
            </div>
          ))}
        </div>
        <div className="flex gap-2 flex-col w-full border rounded-md h-[450px] overflow-y-auto">
          <Input
            prefix={<IconSearch />}
            placeholder="搜索人员"
            onChange={handleChangeQuery}
            showClear
          />
          <div className="w-full bg-gray-100 rounded p-1 px-2 flex justify-between items-center">
            <span>已选{selectedKey?.contractorCheckedUser?.length}项</span>
            <button
              className="btn rounded btn-primary btn-sm"
              onClick={handleAll}
            >
              {selectedKey?.contractorCheckedUser?.length === 0
                ? "全选"
                : "重置"}
            </button>
          </div>
          <div className="gap-2 w-full grid grid-cols-3 px-2">
            {userlist.map((item, k) => (
              <div className="p-1 px-2 rounded-md border" key={k}>
                <Checkbox
                  onChange={(e) => {
                    handleSetChecked(e.target.checked, item);
                  }}
                  checked={
                    findIndex(propEq(item?.id, "id"))(
                      selectedKey?.contractorCheckedUser ?? [],
                    ) >= 0
                  }
                  className="flex items-center h-[52px]"
                >
                  <Avatar color={"blue"} size="small">
                    {last(item?.name ?? "")}
                  </Avatar>
                  <div className="ml-2 text-sm">
                    <div className="name">{item?.name}</div>
                    <div className="text-xs text-gray-500">
                      {item?.contractor?.name}-{item?.comment}
                    </div>
                  </div>
                </Checkbox>
              </div>
            ))}
          </div>
        </div>
      </div>
      <div className="flex items-center mt-2 gap-2">
        <span className="text-md text-slate-400 ">已选中人员:</span>
        <AvatarGroup>
          {(selectedKey?.contractorCheckedUser ?? []).map((o) => (
            <Avatar color="red" key={o.id}>
              {o.name}
            </Avatar>
          ))}
        </AvatarGroup>
      </div>
    </div>
  );
};
