import React, { useState, useMemo, useEffect, useCallback, FC } from "react";
import { Select } from "tdesign-react";

type TmplSelectProps = {
  initOptions: any[];
  multiple?: boolean;
  callback?: (val: any) => void;
};

export const TmplSelect = ({
  initOptions,
  multiple,
  callback,
}): FC<TmplSelectProps> => {
  const [options, setOptions] = useState([...initOptions]);
  /*   const [value, setValue] = useState(initOptions.slice(1)); */
  const [value, setValue] = useState();

  const handleChange = (v, context) => {
    console.log("context:", context);
    callback?.(v);
    setValue(v);
  };

  return (
    <Select
      // value={value}
      // onChange={handleChange}
      // filterable
      multiple={multiple ?? false}
      options={options}
    />
  );
};
