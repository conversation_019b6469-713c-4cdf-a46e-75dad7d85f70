import { IconPrint } from "@douyinfe/semi-icons";

export function Print({ tableId, totalColumns = [], columns = [] }) {
  const handlePrint = () => {
    // 获取当前页面所有样式表
    const style = Array.from(document.querySelectorAll("style"))
      .map((s) => s.innerHTML)
      .join("");
    // 获取表格 html
    const table = document.querySelector(`#${tableId} table`).cloneNode(true);
    const allColumns = totalColumns.map((col) => col.dataIndex);
    const printColumns = columns.map((col) => col.dataIndex);
    const removeColumns = allColumns.reduce((cols, col, idx) => {
      if (!printColumns.includes(col)) {
        cols.push(idx);
      }
      return cols;
    }, []);
    const colgroup = table.querySelector("colgroup");
    removeColumns.forEach((idx) => {
      colgroup.children[idx].remove();
    });
    Array.from(table.querySelectorAll("tr")).forEach((tr) => {
      removeColumns.forEach((idx) => {
        tr.children[idx].remove();
      });
    });
    // 打开新标签页
    const printWindow = window.open("", "PrintWindow");
    // 写入数据
    printWindow.document.write(
      `<html><head><style>${style}*{font-size:12px!important;}</style></head><body>${table.outerHTML}</body></html>`,
    );
    // 关闭写入流
    printWindow.document.close();
    printWindow.onload = () => {
      // 调用浏览器打印
      printWindow.print();
    };
  };

  return (
    <div className="tooltip" data-tip="打印">
      <button className="btn btn-sm btn-ghost rounded no-animation">
        <IconPrint onClick={handlePrint} />
      </button>
    </div>
  );
}
