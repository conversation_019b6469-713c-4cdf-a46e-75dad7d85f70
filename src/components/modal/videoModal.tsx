import { Col, Form, Modal, Row, Toast, Tooltip } from "@douyinfe/semi-ui";
import Video from "pages/bigScreen/component/Element/Video";
import { useCallback, useEffect, useRef, FC, useState } from "react";

type Record = {
  chromeVideoPath?: string;
  name: string;
};

type VideoModalProps = {
  data: Record;
  type?: "modal" | "page";
};

export const VideoModal: FC<VideoModalProps> = ({ data, type = "modal" }) => {
  const [show, setShow] = useState(false);

  const handleClose = useCallback(() => {
    setShow(false);
  }, [setShow]);

  const handleOpen = useCallback(() => {
    setShow(true);
  }, [setShow]);

  const el = (
    <>
      {data?.chromeVideoPath ? (
        <Video streamUrl={data?.chromeVideoPath ?? ""} autoplay />
      ) : (
        <p>该视频无法播放,请联系管理员!</p>
      )}
    </>
  );
  if (type === "page") {
    return el;
  }

  return (
    <>
      <Tooltip content="点击播放">
        <span className="cursor-pointer text-blue-500" onClick={handleOpen}>
          {data.name}
        </span>
      </Tooltip>
      <Modal
        title={data?.name ?? ""}
        visible={show}
        onCancel={handleClose}
        className="[&_.semi-modal-body]:pb-5"
        maskClosable={true}
        footer={null}
        width={800}
        centered
      >
        {el}
      </Modal>
    </>
  );
};
