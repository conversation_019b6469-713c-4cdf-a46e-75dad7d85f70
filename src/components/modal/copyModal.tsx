import { Button, Modal, TextArea } from "@douyinfe/semi-ui";
import { useAtom } from "jotai";
import { atomWithReset } from "jotai/utils";

export const copyModalAtom = atomWithReset({
  visible: false,
  content: undefined,
});

export const CopyModal = () => {
  const [copyModal, setCopyModal] = useAtom(copyModalAtom);

  // const showModal = () => setIsModalVisible(true);
  const hideModal = () => setCopyModal({ visible: false, content: null });

  return (
    <div>
      {/* <Button onClick={showModal}>Show Message</Button> */}
      <Modal
        title="详细信息"
        visible={copyModal.visible}
        onOk={hideModal}
        onCancel={hideModal}
        footer={
          <Button type="primary" onClick={hideModal}>
            确定
          </Button>
        }
      >
        <TextArea value={copyModal.content} rows={40} readOnly />
      </Modal>
    </div>
  );
};
