// import { T } from "ramda";
import { IconSearch } from "@douyinfe/semi-icons";
import { Button, Form, Toast } from "@douyinfe/semi-ui";
import { useSize } from "ahooks";
import * as Cesium from "cesium";
import { tiandiTuMapToken } from "config";
import { isEmpty } from "ramda";
import { FC, useEffect, useRef, useState } from "react";

export type DrawItem = {
  lat: number;
  lng: number;
};
export type Tools = "marker" | "polygon" | "search" | "clearAll" | "submit";

type TdMapProps = {
  position?: DrawItem;
  polygon?: DrawItem[];
  marker?: DrawItem;
  callbackRef?: (ref: any) => void;
  tools: Tools[];
  type: "marker" | "polygon";
  mode?: "app" | "pc";
  floor?: string;
  readonly?: boolean
};


const CoreMap: FC<TdMapProps> = ({
  position,
  marker,
  type,
  polygon = [],
  callbackRef,
  tools = [],
  mode,
  readonly
}) => {
  const size = useSize(document.querySelector("body"));
  const [drawLnglats, setDrawLnglats] = useState<Array<DrawItem>>([]);

  const mapRef = useRef<any>();
  const handler = useRef<any>();
  const zoom = 17;
  const T = window?.T ?? {};

  console.log("tiandiTuMapToken", tiandiTuMapToken);

  const onLoad = () => {
    const imageURL = `https://t0.tianditu.gov.cn/img_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=img&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=${tiandiTuMapToken}`;
    //创建自定义图层对象
    const lay = new T.TileLayer(imageURL, { minZoom: 1, maxZoom: 18 });
    const config = {
      layers: [lay],
      showLabel: true,
      color: "blue",
      weight: 3,
      opacity: 0.5,
      fillColor: "#FFFFFF",
      fillOpacity: 0.5,
      datasourcesControl: true,
    };
    //初始化地图对象
    const map = new T.Map("mapDiv", config);
    //设置显示地图的中心点和级别

    if (position?.lng) {
      map.centerAndZoom(new T.LngLat(position.lng, position.lat), zoom);
    } else {
      map.centerAndZoom(new T.LngLat(116.40769, 39.89945), zoom);
    }

    //允许鼠标滚轮缩放地图
    map.enableScrollWheelZoom();
    map.disableDrag();
    setTimeout(function () {
      map.enableDrag();
    }, 1000);

    /* if (mode !== 'app') {
      const ctrl = new T.Control.MapType();
      map.addControl(ctrl);
    } */

    const control = new T.Control.Zoom();
    //添加缩放平移控件
    map.addControl(control);
    mapRef.current = map;
    callbackRef?.(mapRef.current);
  };


  useEffect(() => {
    if (mapRef.current) {
      return;
    }
    try {
      onLoad();
    } catch (e) {
      console.log("error ===========", JSON.stringify(e));
    }

    return () => {
      mapRef.current?.clearOverLays();
    };
  }, [mapRef.current, mode, position, T]);

  // 设置标记
  useEffect(() => {


    if (!tools.includes("marker")) {
      return;
    }
    /* mapRef.current?.clearOverLays()
    if (handler.current) {
      handler.current?.close?.();
    }
    handler.current = new T.MarkTool(mapRef.current, {
      follow: true,
    });
    handler.current?.open(); */

    if (marker) {
      const point = new T.LngLat(marker.lng, marker.lat);
      const _marker = new T.Marker(point);
      // enableDragging
      mapRef.current?.addOverLay(_marker);
      // 开启拖拽
      // _marker.enableDragging();
      mapRef.current?.centerAndZoom(new T.LngLat(marker.lng, marker.lat), zoom);
    }
  }, [marker, mapRef.current, handler.current, tools, T]);

  useEffect(() => {
    if (!tools.includes("marker")) {
      return;
    }
    function addMapClick() {
      removeMapClick();
      mapRef.current?.addEventListener("click", MapClick);
    }

    function removeMapClick() {
      mapRef.current?.removeEventListener("click", MapClick);
    }

    const MapClick = ({ type, target, lnglat, containerPoint }) => {
      const point = new T.LngLat(lnglat.lng, lnglat.lat);
      const _marker = new T.Marker(point);
      mapRef.current?.clearOverLays();
      mapRef.current?.addOverLay(_marker);
    };
    if (!readonly) {
      addMapClick();
    }

  }, [mapRef.current, tools, T]);

  // 设置围栏
  useEffect(() => {
    if (!tools.includes("polygon")) {
      return;
    }

    if (!polygon.length) {
      openPolygonTool();
    } else {
      if (handler.current) {
        return;
      }

      mapRef.current?.clearOverLays();

      const points: any[] = [];

      polygon.forEach((o) => {
        const _marker = new T.LngLat(o.lng, o.lat);
        points.push(_marker);
      });
      const line = new T.Polyline(points);
      mapRef.current?.addOverLay(line);
      mapRef.current?.centerAndZoom(
        new T.LngLat(polygon[0].lng, polygon[0].lat),
        zoom
      );
    }
  }, [polygon, mapRef.current, handler.current, tools, T]);

  const openPolygonTool = () => {
    // mapRef.current?.clearOverLays()
    if (handler.current) {
      handler.current?.close?.();
      handler.current?.removeEventListener("draw", mapDraw);
    }
    handler.current = new T.PolygonTool(mapRef.current);

    handler.current?.open();
    handler.current?.addEventListener("draw", mapDraw);
  };

  const mapDraw = ({
    type,
    target,
    currentLnglats,
    currentArea,
    currentPolygon,
    allPolygons,
  }) => {
    setDrawLnglats(currentLnglats);
  };

  const handleClearAll = () => {
    if (tools.includes("polygon")) {
      mapRef.current?.clearOverLays();
      openPolygonTool();
    }
    if (tools.includes("marker")) {
      mapRef.current?.clearOverLays();
      openMarkerTool();
    }
  };

  const openMarkerTool = () => {
    mapRef.current?.clearOverLays();
    if (handler.current) {
      handler.current?.close?.();
    }
    handler.current = new T.MarkTool(mapRef.current, { follow: true });
    handler.current?.open();
  };

  const fineLocation = (latitude, longitude) => {
    if (window?.AndroidInterface?.fineLocation) {
      window.AndroidInterface.fineLocation(
        "marker",
        JSON.stringify({
          location: [
            {
              lat: latitude,
              lng: longitude,
            },
          ],
          floor: "",
        })
      );
    }
  };
  const fineLocations = (list) => {
    if (window?.AndroidInterface?.fineLocation) {
      const tmp = [];

      list.forEach((o) => {
        o?.getLngLats?.()?.forEach?.((i: any) => {
          console.log(i, "debug");
          i.forEach((item) => {
            tmp.push({
              latitude: item.getLat(),
              longitude: item.getLng(),
            });
          });
        });
      });
      window.AndroidInterface.fineLocation(
        "polygon",
        JSON.stringify({
          location: tmp,
          floor: "",
        })
      );
    }
  };

  const handleCancel = () => {
    if (window?.AndroidInterface?.cancel) {
      window.AndroidInterface.cancel();
    }
  };

  const handleConfirm = () => {
    if (mapRef.current?.getOverlays?.()) {
      const tmp = mapRef.current?.getOverlays();
      if (polygon.length || type === "polygon") {
        fineLocations(tmp);
      } else {
        tmp.forEach((o) => {
          const ll = o?.getLngLat?.();
          fineLocation(ll.lat, ll.lng);
        });
      }
    }
  };

  const handleSearch = async (values: any) => {
    const req = await fetch(
      `https://api.tianditu.gov.cn/geocoder?ds={"keyWord":"${values?.keyword ?? ""}"}&tk=${tiandiTuMapToken}`
    );
    const res = await req.json();
    if (res?.msg === "ok") {
      mapRef.current?.panTo(
        new T.LngLat(res.location.lon, res.location.lat),
        zoom
      );
    } else {
      Toast.error("未找到该地址");
    }
  };


  return (
    <>
      <div
        className="w-full h-min-[500px] h-full relative flex-col justify-end "
        style={
          mode === "app"
            ? {
              width: size?.width,
              height: size?.height,
              position: "relative",
            }
            : {}
        }
      >
        <div
          id="mapDiv"
          className={`w-full ${mode === "app" ? "h-[92vh]" : "h-full"} absolute z-0`}
          style={
            mode === "app"
              ? {
                width: size?.width,
                height: size?.height - 80,
                position: "absolute",
                zIndex: 0,
              }
              : {}
          }
        ></div>

        {mode === "app" ? (
          <div
            className={`w-full grid grid-cols-${tools.includes("polygon") ? 3 : 2} px-4 gap-x-2 pb-3`}
            style={{
              width: size?.width,
              position: "absolute",
              background: "#fff",
              left: 0,
              bottom: 0,
              zIndex: 10,
            }}
          >
            <Button
              size="large"
              theme="light"
              type="tertiary"
              onClick={handleCancel}
              style={{
                width: "100%",
              }}
            >
              取消
            </Button>

            {tools.includes("polygon") ? (
              <Button
                size="large"
                theme="light"
                type="tertiary"
                onClick={handleClearAll}
                style={{
                  width: "100%",
                }}
              >
                清空标记
              </Button>
            ) : null}
            <Button
              size="large"
              type="primary"
              theme="solid"
              onClick={handleConfirm}
              style={{
                width: "100%",
              }}
            >
              结束并保存
            </Button>
          </div>
        ) : null}

        {tools.includes("search") ? (
          <div
            className="absolute rounded-sm shadow-md bg-white left-[15px] z-10"
            style={{
              background: "#fff",
              position: mode === "app" ? "fixed" : "absolute",
              bottom: mode === "app" ? 100 : 10,
              left: 15,
              zIndex: 20,
            }}
          >
            <Form
              layout="horizontal"
              onSubmit={(values) => {
                handleSearch(values);
              }}
            >
              <Form.Input
                prefix={<IconSearch />}
                showClear
                noLabel
                field="keyword"
                minLength={1}
                className="w-full"
                placeholder="搜索并跳转位置"
              />
              <Button type="primary" htmlType="submit">
                搜索地址
              </Button>
            </Form>
          </div>
        ) : null}
      </div>
    </>
  );
};

export const TdMap: FC<TdMapProps> = (props) => {

  const T = window?.T ?? {};

  const hasInitMap = (): boolean => {
    return !T || isEmpty(T) || !T?.LngLat;
  }


  if (hasInitMap()) {
    return (
      <div className="w-full h-full flex items-center justify-center bg-slate-100">
        地图加载出错,清刷新页面重试!
      </div>
    )
  }


  return (
    <>
      <CoreMap {...props} />
    </>
  )
}


export const toLoadTdMap = (viewer) => {
  console.log("tiandiTuMapToken", tiandiTuMapToken);
  const subdomains = ["0", "1", "2", "3", "4", "5", "6", "7"];
  const layers = viewer.imageryLayers;
  const blackMarble1 = layers.addImageryProvider(
    new Cesium.WebMapTileServiceImageryProvider({
      //影像底图
      url:
        "http://t{s}.tianditu.com/img_w/wmts?service=wmts&request=GetTile&version=1.0.0&LAYER=img&tileMatrixSet=w&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default&format=tiles&tk=" +
        tiandiTuMapToken,
      subdomains: subdomains, //URL模板中用于{s}占位符的子域。如果该参数是单个字符串，则字符串中的每个字符都是一个子域。如果它是一个数组，数组中的每个元素都是一个子域
      layer: "tdtImgLayer",
      style: "default",
      format: "image/jpeg",
      tileMatrixSetID: "w", //使用谷歌的瓦片切片方式
      maximumLevel: 18,
      // show: true
    })
  );
  const blackMarble2 = layers.addImageryProvider(
    new Cesium.WebMapTileServiceImageryProvider({
      //影像注记
      url:
        "http://t{s}.tianditu.com/cia_w/wmts?service=wmts&request=GetTile&version=1.0.0&LAYER=cia&tileMatrixSet=w&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default.jpg&tk=" +
        tiandiTuMapToken,
      subdomains: subdomains,
      layer: "tdtCiaLayer",
      style: "default",
      format: "image/jpeg",
      tileMatrixSetID: "w", //使用谷歌的瓦片切片方式
      maximumLevel: 18,
      // show: true
    })
  );
  blackMarble1.alpha = 0.5; // 透明度 imageryLayer
  blackMarble1.brightness = 1.0; // 亮度 imageryLayer
  blackMarble2.alpha = 0.5;
  blackMarble2.brightness = 1.0;
};
