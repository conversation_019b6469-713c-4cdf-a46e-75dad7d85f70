// import { T } from "ramda";
import { IconSearch } from "@douyinfe/semi-icons";
import { Button, Form, Toast, Tooltip } from "@douyinfe/semi-ui";
import { tiandiTuMapToken } from "config";
import { FC, useEffect, useRef, useState } from "react";
import { DrawItem } from "./tdMap";

type TdPcMapProps = {
  position?: DrawItem;
  polygon?: DrawItem[];
  callbackRef?: (ref: any) => void;
};

export const TdPcMap: FC<TdPcMapProps> = ({
  position,
  polygon = [],
  callbackRef,
}) => {
  const [drawLnglats, setDrawLnglats] = useState<Array<DrawItem>>([]);

  const mapRef = useRef<any>();
  const handler = useRef<any>();
  const zoom = 17;
  const T = window.T;

  const onLoad = () => {
    const imageURL = `https://t0.tianditu.gov.cn/img_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=img&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=${tiandiTuMapToken}`;
    //创建自定义图层对象
    const lay = new T.TileLayer(imageURL, { minZoom: 1, maxZoom: 18 });
    const config = {
      layers: [lay],
      showLabel: true,
      color: "blue",
      weight: 3,
      opacity: 0.5,
      fillColor: "#FFFFFF",
      fillOpacity: 0.5,
      datasourcesControl: true,
    };
    //初始化地图对象
    const map = new T.Map("mapDiv", config);
    //设置显示地图的中心点和级别

    if (position?.lng) {
      map.centerAndZoom(new T.LngLat(position.lng, position.lat), zoom);
    } else {
      map.centerAndZoom(new T.LngLat(116.40769, 39.89945), zoom);
    }

    //允许鼠标滚轮缩放地图
    map.enableScrollWheelZoom();
    map.disableDrag();
    setTimeout(function () {
      map.enableDrag();
    }, 1000);

    /* if (mode !== 'app') {
      const ctrl = new T.Control.MapType();
      map.addControl(ctrl);
    } */

    const control = new T.Control.Zoom();
    //添加缩放平移控件
    map.addControl(control);
    mapRef.current = map;
    callbackRef?.(mapRef.current);
  };

  useEffect(() => {
    if (mapRef.current) {
      return;
    }
    try {
      onLoad();
    } catch (e) {
      console.log("error ===========", JSON.stringify(e));
    }

    return () => {
      mapRef.current?.clearOverLays();
    };
  }, [mapRef.current, position]);

  // 设置围栏
  useEffect(() => {
    if (!polygon.length) {
      openPolygonTool();
    } else {
      if (handler.current) {
        return;
      }
      mapRef.current?.clearOverLays();

      const points: any[] = [];

      polygon.forEach((o) => {
        const _marker = new T.LngLat(o.lng, o.lat);
        points.push(_marker);
      });
      const line = new T.Polygon(points);
      //const line = new T.Polyline(points);
      mapRef.current?.addOverLay(line);
      mapRef.current?.centerAndZoom(
        new T.LngLat(polygon[0].lng, polygon[0].lat),
        zoom
      );
    }
  }, [polygon, mapRef.current, handler.current]);

  const openPolygonTool = () => {
    if (handler.current) {
      handler.current?.close?.();
      handler.current?.removeEventListener("draw", mapDraw);
    }
    handler.current?.open?.();
    // handler.current?.removeEventListener("draw", mapDraw);
    handler.current = new T.PolygonTool(mapRef.current);
  };

  const mapDraw = ({
    type,
    target,
    currentLnglats,
    currentArea,
    currentPolygon,
    allPolygons,
  }) => {
    setDrawLnglats(currentLnglats);
  };

  const handleClearAll = () => {
    mapRef.current?.clearOverLays();
    openPolygonTool();
  };

  const handleSearch = async (values: any) => {
    const req = await fetch(
      `https://api.tianditu.gov.cn/geocoder?ds={"keyWord":"${values?.keyword ?? ""}"}&tk=${tiandiTuMapToken}`
    );
    const res = await req.json();
    if (res?.msg === "ok") {
      mapRef.current?.panTo(
        new T.LngLat(res.location.lon, res.location.lat),
        zoom
      );
    } else {
      Toast.error("未找到该地址");
    }
  };

  return (
    <>
      <div className="w-full h-min-[500px] h-full relative flex-col justify-end ">
        <div id="mapDiv" className={`w-full h-full absolute z-0`}></div>

        <ul className="absolute rounded-sm shadow-md bg-white right-[15px] top-[50px] z-10">
          <Tooltip position="left" content="标记区域">
            <li
              className="border-b border-solid border-gray-300 w-9 h-9 cursor-pointer flex justify-center items-center"
              onClick={openPolygonTool}
            >
              <i className="ri-pentagon-line text-xl"></i>
            </li>
          </Tooltip>
          <Tooltip position="left" content="清空所有">
            <li
              className="border-b border-solid border-gray-300 w-9 h-9 cursor-pointer flex justify-center items-center"
              onClick={handleClearAll}
            >
              <i className="ri-delete-bin-5-fill text-xl text-red-500"></i>
            </li>
          </Tooltip>
        </ul>

        <div className="absolute rounded-sm shadow-md bg-white left-[15px] bottom-9 z-10">
          <Form
            layout="horizontal"
            onSubmit={(values) => {
              handleSearch(values);
            }}
          >
            <Form.Input
              prefix={<IconSearch />}
              showClear
              noLabel
              field="keyword"
              minLength={1}
              className="w-full"
              placeholder="搜索并跳转位置"
            />
            <Button type="primary" htmlType="submit">
              搜索地址
            </Button>
          </Form>
        </div>
      </div>
    </>
  );
};
