import { useForm<PERSON><PERSON>, useFormState } from "@douyinfe/semi-ui";
import { useAuth } from "@reactivers/hooks";
import { useAtomValue, useSetAtom } from "jotai";
import React from "react";
import { userInfoNameInLocalStorage } from "utils/constants";

const DRAFT_KEY = "form-draft";

export function DraftTrigger({ id, draftAtom, onTrigger }) {
  const setAtomValue = useSetAtom(draftAtom);
  const {
    user: { userInfo },
  } = useAuth();

  React.useEffect(() => {
    if (!userInfo?.id) {
      return;
    }
    const item = localStorage.getItem(DRAFT_KEY);
    const exist =
      (typeof item === "string" ? JSON.parse(item || "{}") : item) || {};
    if (exist && exist[userInfo.id] && exist[userInfo.id][id]?.atom) {
      // 重新登录后跳转到上一次打开的页面，并打开上一次打开的弹窗
      if (typeof onTrigger === "function") {
        onTrigger(exist[userInfo.id][id]);
      }
      setAtomValue(exist[userInfo.id][id]?.atom);
    }
  }, []);

  return null;
}

export function Draft({ id, draftAtom, draftInfos }) {
  const atomValue = useAtomValue(draftAtom);
  const formApi = useFormApi();
  const formState = useFormState();
  const {
    user: { userInfo },
  } = useAuth();

  React.useEffect(() => {
    if (!userInfo?.id) {
      return;
    }
    try {
      const item = localStorage.getItem(DRAFT_KEY);
      const exist =
        (typeof item === "string" ? JSON.parse(item || "{}") : item) || {};
      if (exist && exist[userInfo?.id] && exist[userInfo?.id]?.[id]?.values) {
        // 重新登录后跳转到上一次打开的页面，并打开上一次打开的弹窗，并恢复上一次未完成的草稿
        Object.keys(exist[userInfo.id][id].values).forEach((field) => {
          formApi.setValue(field, exist[userInfo.id][id].values[field]);
        });
      }
    } catch (e) {
      console.log(e, "<< === error");
    }
  }, []);

  React.useEffect(() => {
    if (!userInfo?.id) {
      return;
    }
    if (
      (atomValue === true || atomValue?.show === true) &&
      Object.values(formState.touched || {}).indexOf(true) >= 0
    ) {
      const item = localStorage.getItem(DRAFT_KEY);
      const next =
        (typeof item === "string" ? JSON.parse(item || "{}") : item) || {};
      if (!next[userInfo.id]) {
        next[userInfo.id] = {};
      }
      // 记录不同账户的不同草稿
      next[userInfo.id][id] = {
        ...draftInfos,
        // 记录上一次打开的页面
        pathname: location.pathname,
        // 记录上一次打开的弹窗是哪个
        atom: atomValue,
        values: formState.values,
      };
      // 在打开弹窗时开始记录草稿到 localstorage
      localStorage.setItem(DRAFT_KEY, JSON.stringify(next));
    }
  }, [atomValue, formState, userInfo?.id, id, draftInfos]);

  return null;
}

export function getInitialStateFromDraftManually(id) {
  const user =
    JSON.parse(localStorage.getItem(userInfoNameInLocalStorage))?.userInfo ||
    {};
  const next = JSON.parse(localStorage.getItem(DRAFT_KEY) || "{}");
  if (!next[user.id]) {
    next[user.id] = {};
  }
  return next[user.id][id];
}

export function updateDraftManually(id, values) {
  const user =
    JSON.parse(localStorage.getItem(userInfoNameInLocalStorage))?.userInfo ||
    {};
  const next = JSON.parse(localStorage.getItem(DRAFT_KEY) || "{}");
  if (!next[user.id]) {
    next[user.id] = {};
  }
  next[user.id][id] = {
    ...next[user.id][id],
    ...values,
  };
  localStorage.setItem(DRAFT_KEY, JSON.stringify(next));
}

export function destroyDraft(id) {
  const user =
    JSON.parse(localStorage.getItem(userInfoNameInLocalStorage))?.userInfo ||
    {};
  const next = JSON.parse(localStorage.getItem(DRAFT_KEY) || "{}");
  if (!next[user.id]) {
    next[user.id] = {};
  }
  delete next[user.id][id];
  localStorage.setItem(DRAFT_KEY, JSON.stringify(next));
}

export function redirectToDraftIfNeeded(user, callback) {
  const item = JSON.parse(localStorage.getItem(DRAFT_KEY) || "{}");
  if (item && item[user.id]) {
    const draft = Object.entries(item[user.id]).find(([, v]) => !!v.pathname);
    if (draft) {
      // 重新登录后跳转到上一次打开的页面
      window.location.replace(draft[1].pathname);
    } else {
      callback();
    }
  } else {
    callback();
  }
}
