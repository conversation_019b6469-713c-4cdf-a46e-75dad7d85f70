import { useQuery } from "@tanstack/react-query";
import { Echart } from "pages/bigScreen/component/Chart";
import React, { useMemo, useState } from "react";

export interface TableChartTab {
  label: string;
  value: any;
  queryKey: any[];
  queryFn: (params: any) => Promise<any>;
  columns: TableChartColumn[];
  optionBuilder?: (data: any) => object;
}

export interface TableChartColumn {
  title: string;
  dataIndex?: string; // 支持嵌套，如 area.name
  render?: (value: any, record: any, index: number) => React.ReactNode;
  width?: number | string;
  align?: "left" | "center" | "right";
}

export interface TableChartProps {
  title: string;
  queryKey?: any[]; // 单一API时使用
  queryFn?: (params: any) => Promise<any>; // 单一API时使用
  filter?: object;
  columns?: TableChartColumn[]; // 单一API时使用
  optionBuilder?: (data: any) => object; // 单一API时使用
  height?: number;
  emptyText?: string;
  tabList?: TableChartTab[]; // 多API时使用，每个tab对应不同API和columns
  tabParamName?: string; // 默认为'type'
}

function getValueByPath(obj: any, path: string) {
  if (!path) return obj;
  return path
    .split(".")
    .reduce((acc, key) => (acc ? acc[key] : undefined), obj);
}

export function TableChart({
  title,
  queryKey,
  queryFn,
  filter = {},
  columns,
  optionBuilder,
  height = 400,
  emptyText = "暂无数据",
  tabList,
  tabParamName = "type",
}: TableChartProps) {
  // tab切换状态
  const [tabValue, setTabValue] = useState(tabList?.[0]?.value);

  // 获取当前tab的配置
  const currentTab = useMemo(() => {
    if (!tabList) return null;
    return tabList.find(tab => tab.value === tabValue) || tabList[0];
  }, [tabList, tabValue]);

  // 组合请求参数
  const params = useMemo(() => {
    if (tabList && tabParamName) {
      return { ...filter, [tabParamName]: tabValue };
    }
    return filter;
  }, [filter, tabList, tabParamName, tabValue]);

  // 确定当前使用的queryKey、queryFn和columns
  const currentQueryKey = useMemo(() =>
    currentTab ? currentTab.queryKey : queryKey,
    [currentTab, queryKey]
  );

  const currentQueryFn = useMemo(() =>
    currentTab ? currentTab.queryFn : queryFn,
    [currentTab, queryFn]
  );

  const currentColumns = useMemo(() =>
    currentTab ? currentTab.columns : columns || [],
    [currentTab, columns]
  );

  const currentOptionBuilder = useMemo(() =>
    currentTab ? currentTab.optionBuilder : optionBuilder,
    [currentTab, optionBuilder]
  );

  // useQuery拉取数据
  const { data, isLoading } = useQuery({
    queryKey: [...(currentQueryKey || []), params],
    queryFn: () => currentQueryFn?.(params),
    enabled: !!currentQueryFn,
  });

  const dataSource = useMemo(() => data?.data ?? [], [data]);
  const option = useMemo(
    () => (currentOptionBuilder ? currentOptionBuilder(data) : undefined),
    [data, currentOptionBuilder]
  );
  const isEmpty = !dataSource || dataSource.length === 0;

  return (
    <div
      className="flex flex-col bg-white border border-[#EDEDEE] rounded-lg p-6 divide-y gap-y-5"
      style={{ minHeight: height }}
    >
      <div className="flex justify-between">
        <div className="flex items-center gap-x-[10px] mb-2">
          <div className="bg-[#60B7FF] w-1 h-[22px]"></div>
          <span className="text-[18px] font-bold">{title}</span>
        </div>
        {tabList && tabList.length > 0 && (
          <div className="flex gap-2">
            <div className="border border-[#60B7FF] text-[#60B7FF] text-sm cursor-pointer overflow-hidden rounded-md flex">
              {tabList.map((tab) => (
                <div
                  key={tab.value}
                  className={`leading-none px-[14px] py-[8px] ${tabValue === tab.value ? "text-white bg-[#60B7FF]" : ""}`}
                  onClick={() => setTabValue(tab.value)}
                >
                  {tab.label}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
      {option && (
        <div className="w-full h-[300px]">
          <Echart option={option} />
        </div>
      )}
      <div className="flex flex-col w-full gap-y-2 pt-2">
        <div className="bg-[#F1F3F6] text-xs text-[#333] font-semibold h-10 items-center px-5 w-full flex">
          {currentColumns.map((col, idx) => (
            <div
              key={col.title}
              className="flex-1"
              style={{ width: col.width, textAlign: col.align || "left" }}
            >
              {col.title}
            </div>
          ))}
        </div>
        <div className="flex flex-col w-full h-full overflow-y-auto max-h-[320px]">
          {isLoading ? (
            <div className="text-center text-gray-400 py-8">加载中...</div>
          ) : isEmpty ? (
            <div className="text-center text-gray-400 py-8">{emptyText}</div>
          ) : (
            dataSource.map((row: any, idx: number) => (
              <div
                className="bg-white text-sm text-[#666] h-10 items-center px-5 w-full flex"
                style={{ background: idx % 2 !== 0 ? "#F4FAFF" : "" }}
                key={row.id || idx}
              >
                {currentColumns.map((col, colIdx) => (
                  <div
                    key={col.title}
                    className="flex-1"
                    style={{ width: col.width, textAlign: col.align || "left" }}
                  >
                    {col.render
                      ? col.render(
                          getValueByPath(row, col.dataIndex || ""),
                          row,
                          idx
                        )
                      : getValueByPath(row, col.dataIndex || "")}
                  </div>
                ))}
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
}
