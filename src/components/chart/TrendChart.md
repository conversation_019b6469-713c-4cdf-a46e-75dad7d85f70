# TrendChart 通用趋势图组件文档

## 1. 组件简介

`TrendChart` 是一个高度可复用的趋势图中间层组件，适用于所有需要折线图/趋势图的业务场景。它统一了结构、样式、数据请求、Tab切换、option生成等逻辑，极大减少了重复代码。

---

## 2. Props 参数说明

| 参数名        | 类型                                  | 说明                                                              | 是否必填 |
| ------------- | ------------------------------------- | ----------------------------------------------------------------- | -------- |
| title         | string                                | 图表标题                                                          | 是       |
| queryKey      | any[]                                 | 用于 react-query 的缓存 key，建议包含接口名和依赖参数             | 是       |
| queryFn       | (params: any) => Promise<any>         | 数据请求函数，返回 Promise，参数为 filter+tab切换参数             | 是       |
| filter        | object                                | 额外的请求参数（如 areaId、时间范围等），会自动合并到请求参数     | 否       |
| tabList       | {label: string, value: any}[]         | Tab切换项（如近7天/30天/12月），为空则不显示Tab切换               | 否       |
| tabParamName  | string                                | Tab切换参数名，默认为 'type'                                      | 否       |
| optionBuilder | (data: any, tabValue?: any) => object | ECharts option生成器，接收接口返回数据和当前tab值，返回option对象 | 否       |
| optionConfig  | BuildTrendOptionConfig                | 通用option配置，自动生成optionBuilder，适合常见折线/柱状场景      | 否       |
| height        | number                                | 图表高度，默认300                                                 | 否       |
| isEmptyFunc   | (option: any, data: any) => boolean   | 自定义判空逻辑，返回true则显示empty，优先于默认判空               | 否       |

---

## 3. 组件结构与交互

- **标题区**：左侧蓝色竖条+标题
- **Tab切换区**：可选，右侧按钮组，切换时自动请求新数据
- **图表区**：Echart折线图，option由optionBuilder生成
- **数据请求**：自动合并filter和tab参数，自动缓存
- **样式**：对标首页，白色卡片、圆角、阴影、padding

---

## 4. 典型用法示例

### 4.1 推荐：optionConfig + buildTrendOption（单系列趋势图）

```tsx
import { TrendChart } from "components/chart/TrendChart";
import { getAlarmTrendStat } from "api/alarm/alarmStat";
import { formatDate } from "utils";

export default function AlarmTrendChart({ filter }) {
  return (
    <TrendChart
      title="报警数量趋势"
      queryKey={["getAlarmTrendStat"]}
      queryFn={getAlarmTrendStat}
      filter={filter}
      optionConfig={{
        xField: "statTime",
        yField: "num",
        xFormatter: formatDate,
        chartType: "line",
        seriesName: "报警数",
      }}
      height={300}
    />
  );
}
```

---

### 4.2 完全自定义 optionBuilder（特殊场景）

```tsx
import { TrendChart } from "components/chart/TrendChart";
import { getAlarmTrendStat } from "api/alarm/alarmStat";
import { formatDate } from "utils";

export default function AlarmTrendChart({ filter }) {
  return (
    <TrendChart
      title="报警数量趋势"
      queryKey={["getAlarmTrendStat"]}
      queryFn={getAlarmTrendStat}
      filter={filter}
      optionBuilder={(data) => {
        const list = data?.data?.alarmStatList ?? [];
        return {
          xAxis: {
            type: "category",
            data: list.map((i) => formatDate(i.statTime)),
          },
          yAxis: { type: "value" },
          series: [{ data: list.map((i) => i.num), type: "line" }],
        };
      }}
      height={300}
    />
  );
}
```

---

### 4.3 自定义判空逻辑（isEmptyFunc）示例

有些业务下，0 也是有效数据，不能仅靠 data 是否为 0 判空。可通过 isEmptyFunc props 自定义判空逻辑：

```tsx
import { TrendChart } from "components/chart/TrendChart";
import { getAlarmTrendStat } from "api/alarm/alarmStat";
import { formatDate } from "utils";

export default function AlarmTrendChart({ filter }) {
  const isEmptyFunc = (option, data) => {
    const list = data?.data?.alarmStatList ?? [];
    return list.length === 0;
  };

  return (
    <TrendChart
      title="报警数量趋势"
      queryKey={["getAlarmTrendStat"]}
      queryFn={getAlarmTrendStat}
      filter={filter}
      isEmptyFunc={isEmptyFunc}
      height={300}
    />
  );
}
```

---

### 4.4 渐变平滑折线图 builder（buildSmoothAreaLineOption）

适用于大多数首页/统计类趋势图，带渐变面积、平滑线、定制色、无点、分割线。

```tsx
import {
  TrendChart,
  buildSmoothAreaLineOption,
} from "components/chart/TrendChart";
import { getAlarmTrendStat } from "api/alarm/alarmStat";
import { formatDate } from "utils";

<TrendChart
  title="报警数量趋势"
  queryKey={["getAlarmTrendStat"]}
  queryFn={getAlarmTrendStat}
  filter={filter}
  optionBuilder={buildSmoothAreaLineOption({
    xField: "statTime",
    yField: "num",
    xFormatter: formatDate,
    lineColor: "#60B7FF",
    areaColor: "#60B7FF",
    areaTo: "#fff",
  })}
  height={300}
/>;
```

---

## 5. optionConfig/buildTrendOption 参数说明

| 参数名         | 类型               | 说明                          |
| -------------- | ------------------ | ----------------------------- |
| xField         | string             | x轴字段名                     |
| yField         | string             | y轴字段名                     |
| xFormatter     | (v: any) => string | x轴格式化函数（如formatDate） |
| chartType      | "line" \| "bar"    | 图表类型，默认line            |
| seriesName     | string             | 系列名（legend显示）          |
| yAxisFormatter | (v: any) => string | y轴刻度格式化                 |

---

## 6. buildSmoothAreaLineOption 参数说明

| 参数名         | 类型               | 说明                    |
| -------------- | ------------------ | ----------------------- |
| xField         | string             | x轴字段名               |
| yField         | string             | y轴字段名               |
| xFormatter     | (v: any) => string | x轴格式化函数           |
| lineColor      | string             | 折线颜色，默认#60B7FF   |
| areaColor      | string             | 渐变起始色，默认#60B7FF |
| areaTo         | string             | 渐变终止色，默认#fff    |
| smooth         | boolean            | 是否平滑，默认true      |
| showSymbol     | boolean            | 是否显示点，默认false   |
| axisLabelColor | string             | 坐标轴文字色，默认#666  |
| grid           | object             | grid配置，默认同首页    |

---

## 7. 优先级说明

- optionBuilder > optionConfig > 默认空option
- 推荐优先用 optionConfig，常见首页折线用 buildSmoothAreaLineOption，特殊场景用 optionBuilder

---

## 8. 各业务组件调用方式

### @AlarmTrendChart.tsx

- **无Tab切换**，只传 filter、optionBuilder
- 见上方 4.1 示例

---

### @dangerNumStat.tsx

- **有Tab切换**，传 tabList、tabParamName、optionBuilder
- 见上方 4.2 示例

---

### @dangerRectifyRateStat.tsx

- **有Tab切换**，optionBuilder 生成 y 轴为百分比的折线图
- 只需调整 optionBuilder 处理 rate 字段和 y 轴格式

---

### @bottom.tsx（specialWorkIndex/bottom.tsx、inspectionIndex/bottom.tsx）

- **有Tab切换**，optionBuilder 生成多 series 折线/柱状图
- 只需传不同的 tabList、optionBuilder

---

### @recordLeft.tsx

- **有Tab切换**，参数 queryKey、queryFn、title、tabList、optionBuilder
- 只需将原本的 useQuery、option 逻辑迁移到 optionBuilder

---

## 9. 迁移/复用建议

- 只需将原有组件的 useQuery、option 生成、tab 切换逻辑，迁移到 TrendChart 的参数和 optionBuilder 中
- 其它 UI、样式、交互全部由 TrendChart 统一维护
- 这样所有趋势图组件都能用同一个 TrendChart，极大提升复用性和维护性

---

如需更详细的迁移示例或 optionBuilder 写法，请随时告知！
