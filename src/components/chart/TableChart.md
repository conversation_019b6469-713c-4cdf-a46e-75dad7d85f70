# TableChart 通用统计表格中间层组件文档

## 1. 组件简介

`TableChart` 是一个高度可复用的统计表格中间层组件，适用于首页、统计页等所有需要表格/排行/统计列表的业务场景。它统一了结构、样式、数据请求、表头配置、可选图表等逻辑，极大减少了重复代码。

现在支持多 Tab 切换功能，每个 Tab 可以调用不同的 API 并展示不同的列和数据。

---

## 2. Props 参数说明

| 参数名        | 类型                          | 说明                                                          | 是否必填 |
| ------------- | ----------------------------- | ------------------------------------------------------------- | -------- |
| title         | string                        | 表格标题                                                      | 是       |
| queryKey      | any[]                         | 用于 react-query 的缓存 key，建议包含接口名和依赖参数         | 单Tab时必填 |
| queryFn       | (params: any) => Promise<any> | 数据请求函数，返回 Promise，参数为 filter                     | 单Tab时必填 |
| filter        | object                        | 额外的请求参数（如 areaId、时间范围等），会自动合并到请求参数 | 否       |
| columns       | TableChartColumn[]            | 表头配置，支持自定义渲染、嵌套字段                            | 单Tab时必填 |
| optionBuilder | (data: any) => object         | 可选，生成 ECharts option，表格+图表混合展示                  | 否       |
| height        | number                        | 组件最小高度，默认400                                         | 否       |
| emptyText     | string                        | 无数据时的提示文本，默认"暂无数据"                            | 否       |
| tabList       | TableChartTab[]               | Tab切换列表，每个Tab可以调用不同API并展示不同列               | 多Tab时必填 |
| tabParamName  | string                        | Tab切换参数名，默认为 'type'                                  | 否       |

### TableChartColumn 说明

| 字段      | 类型                              | 说明               |
| --------- | --------------------------------- | ------------------ |
| title     | string                            | 列标题             |
| dataIndex | string                            | 字段路径，支持嵌套 |
| render    | (value, record, idx) => ReactNode | 自定义渲染函数     |
| width     | number/string                     | 列宽               |
| align     | "left" \| "center" \| "right"     | 对齐方式           |

### TableChartTab 说明

| 字段          | 类型                          | 说明                                |
| ------------- | ----------------------------- | ----------------------------------- |
| label         | string                        | Tab显示名称                         |
| value         | any                           | Tab值，用于标识当前选中的Tab        |
| queryKey      | any[]                         | 该Tab的react-query缓存key           |
| queryFn       | (params: any) => Promise<any> | 该Tab的数据请求函数                 |
| columns       | TableChartColumn[]            | 该Tab的表头配置                     |
| optionBuilder | (data: any) => object         | 可选，该Tab的ECharts option生成函数 |

---

## 3. 组件结构与交互

- **标题区**：左侧蓝色竖条+标题
- **Tab切换区**：支持多Tab切换，每个Tab可以调用不同API并展示不同列
- **表头区**：支持自定义列、嵌套字段、对齐、宽度
- **表体区**：斑马纹、无数据/加载中提示
- **可选图表区**：如传 optionBuilder，可在表格上方展示 ECharts 图表
- **样式**：对标首页，白色卡片、圆角、阴影、padding

---

## 4. 典型用法示例

### 4.1 单Tab用法

```tsx
<TableChart
  title="区域报警统计"
  queryKey={["getAreaStat"]}
  queryFn={getAreaStat}
  filter={filter}
  columns={[
    { title: "排名", render: (_, __, idx) => idx + 1, align: "center", width: 60 },
    { title: "区域", dataIndex: "area.name", align: "center" },
    { title: "报警数量", dataIndex: "num", align: "center" },
  ]}
  height={400}
/>
```

### 4.2 多Tab用法

```tsx
<TableChart
  title="报警统计"
  filter={filter}
  tabList={[
    {
      label: "区域统计",
      value: "area",
      queryKey: ["getAreaStat"],
      queryFn: getAreaStat,
      columns: [
        { title: "区域名称", dataIndex: "area.name", align: "left" },
        { title: "报警数量", dataIndex: "num", align: "center" },
      ],
    },
    {
      label: "设备统计",
      value: "device",
      queryKey: ["getDeviceStat"],
      queryFn: getDeviceStat,
      columns: [
        { title: "设备名称", dataIndex: "equipment.name", align: "left" },
        { title: "报警数量", dataIndex: "num", align: "center" },
      ],
    },
  ]}
  height={400}
  emptyText="暂无报警数据"
/>
```

### 4.3 其它业务表格组件调用方式

#### @sensorAlarm.tsx

```tsx
<TableChart
  title="实时监测报警"
  queryKey={["getDashSensorAlarm"]}
  queryFn={getDashSensorAlarm}
  columns={[
    { title: "监测名称", dataIndex: "name" },
    { title: "持续时长", dataIndex: "duration" },
    { title: "报警类型", dataIndex: "priorityName" },
    { title: "当前值", dataIndex: "alarmValue" },
    { title: "报警优先级", dataIndex: "priority", render: (v) => StatusBtn(v) },
  ]}
/>
```

#### @paperStat.tsx

```tsx
<TableChart
  title="考试统计"
  queryKey={["getPaperStat"]}
  queryFn={getPaperStat}
  columns={[
    { title: "试卷名称", dataIndex: "paper.name" },
    { title: "平均分", dataIndex: "averageScore" },
    { title: "中位数", dataIndex: "middleScore" },
    { title: "参与人次", dataIndex: "attendance" },
    {
      title: "通过率",
      dataIndex: "passRate",
      render: (v) => numeral(v).format("0%"),
    },
  ]}
/>
```

#### @left.tsx（培训计划/巡检任务等）

```tsx
<TableChart
  title="进行中的培训计划"
  queryKey={["getOngoingPlan"]}
  queryFn={getOngoingPlan}
  columns={[
    { title: "培训计划名称", dataIndex: "name" },
    { title: "培训类型", dataIndex: "trainingTypeValue.dicValue" },
    {
      title: "培训时间",
      dataIndex: "endTime",
      render: (v) => (v ? dayjs(v).format("YYYY/MM/DD") : "-"),
    },
    { title: "参与人数", dataIndex: "candidateNum" },
    { title: "通过人数", dataIndex: "passNum" },
    {
      title: "通过率",
      dataIndex: "passRate",
      render: (v) => numeral(v).format("0%"),
    },
  ]}
/>
```

#### @gcWorkStat.tsx

```tsx
<TableChart
  title="政府专项任务"
  queryKey={["getGcWorkStat"]}
  queryFn={getGcWorkStat}
  columns={[
    { title: "任务名称", dataIndex: "taskName" },
    {
      title: "检查类型",
      dataIndex: "checkType",
      render: (v) => CHECKTYPE_MAP[v],
    },
    {
      title: "任务时间",
      dataIndex: "taskEndTime",
      render: (v) => (v ? dayjs(v).format("YYYY/MM/DD") : "-"),
    },
    {
      title: "完成率",
      dataIndex: "workFinishRate",
      render: (v) => numeral(v).format("0.0%"),
    },
  ]}
/>
```

#### @dangerInfo.tsx @taskInfo.tsx

```tsx
<TableChart
  title="隐患治理情况"
  queryKey={["getDangerInfo"]}
  queryFn={getDangerInfo}
  columns={[
    { title: "责任部门", dataIndex: "department.name" },
    { title: "隐患数", dataIndex: "checkTaskTotalNum" },
    { title: "已验收数", dataIndex: "checkTaskFinishNum" },
    {
      title: "完成率",
      dataIndex: "checkTaskFinishRate",
      render: (v) => numeral(v).format("0.0%"),
    },
  ]}
/>
```

---

## 5. 迁移/复用建议

- 只需将原有组件的 useQuery、表头、表体、option 逻辑，迁移到 TableChart 的参数和 columns/optionBuilder 中
- 其它 UI、样式、交互全部由 TableChart 统一维护
- 这样所有统计表格组件都能用同一个 TableChart，极大提升复用性和维护性

---

如需更详细的迁移示例或 columns 写法，请随时告知！
