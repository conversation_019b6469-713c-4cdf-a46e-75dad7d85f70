import { DatePicker, InputNumber, Modal, Select } from "@douyinfe/semi-ui";
import { getSensorHistoricalData } from "api";
import classNames from "classnames";
import dayjs from "dayjs";
import { useAtomValue } from "jotai";
import { atomWithReset, useResetAtom } from "jotai/utils";
import { fullChart as FullChart } from "pages/bigScreen/component/Chart";
import css from "pages/bigScreen/component/Element/index.module.css";
import hideScrollBar from "pages/bigScreen/component/index.module.css";
import useQuery from "pages/bigScreen/hooks/useQuery";
import React from "react";

export const LineChartAtom = atomWithReset({
  show: false,
  data: {},
});

interface SensorData {
  id: number;
  name: string;
  code: string;
  unit: string;
  highAlarm: number;
  highHighAlarm: number;
  lowAlarm: number;
  lowLowAlarm: number;
  monitorTypeValue: {
    id: number;
    dicKey: number;
    dicValue: string;
  };
  sampleValue: number;
  sampleTime: string;
  measurementName: string;
  fieldName: string;
  type: number;
  priority: number;
}

function getTime(intervalInSeconds: number, unit: string, time?: string) {
  // 创建一个新的 Date 对象，表示当前时间
  const now = time ? new Date(time) : new Date();

  // 根据单位转换时间间隔为秒
  let timeInMilliseconds = 0;
  switch (unit.toLowerCase()) {
    case "minute":
      timeInMilliseconds = intervalInSeconds * 60 * 1000;
      break;
    case "hours":
      timeInMilliseconds = intervalInSeconds * 60 * 60 * 1000;
      break;
    case "day":
      timeInMilliseconds = intervalInSeconds * 24 * 60 * 60 * 1000;
      break;
    default:
      throw new Error(`Unsupported unit: ${unit}`);
  }

  // 计算未来时间
  const futureTime = new Date(now.getTime() - timeInMilliseconds);

  // 返回一个包含未来时间和当前时间的数组
  return [futureTime.toISOString(), now.toISOString()];
}

const getTimeX = (value, unit) => {
  // 将ISO 8601格式的时间转换为 YYYY/MM/DD HH:mm 格式
  const date = new Date(value);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0"); // JavaScript月份从0开始计数
  const day = String(date.getDate()).padStart(2, "0");
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const seconds = String(date.getSeconds()).padStart(2, "0");
  if (unit === 1) {
    return `${year}/${month}/${day} ${hours}:${minutes}:${seconds}`;
  }
  if (unit === 2) {
    return `${year}/${month}/${day} ${hours}:${minutes}`;
  }
  if (unit === 3) {
    return `${year}/${month}/${day} ${hours}:${minutes}`;
  }
  if (unit === 4) {
    return `${year}/${month}/${day}`;
  }
  return `${year}/${month}/${day} ${hours}:${minutes}:${seconds}`;
};

export const LineChart = () => {
  const lineChartData = useAtomValue(LineChartAtom);
  const reset = useResetAtom(LineChartAtom);

  const ref = React.useRef();

  const [date, setDate] = React.useState(getTime(8, "hours"));
  const [interval, setInterval] = React.useState(15);
  const [unit, setUnit] = React.useState(1);

  const _beg = dayjs(new Date(lineChartData?.data?.beg ?? null).toISOString())
    .subtract(1, "minute")
    .toISOString();
  const _end = dayjs(new Date(lineChartData?.data?.end ?? null).toISOString())
    .add(1, "minute")
    .toISOString();

  const [form, setForm] = React.useState({
    beginTime: lineChartData?.data?.beg ? _beg : date[0],
    endTime: lineChartData?.data?.end ? _end : date[1],
    interval,
    intervalUnit: unit,
    sensorId: lineChartData?.data?.id,
    fieldName: lineChartData?.data?.fieldName,
    measurementName: lineChartData?.data?.measurementName,
  });

  const { data: chartData = {}, State } = useQuery({
    queryKey: ["getSensorHistoricalData", form],
    queryFn: () => {
      return getSensorHistoricalData(form);
    },
  });

  const datas = (chartData.stats || []).reduce(
    (obj, item) => {
      obj.x.push(getTimeX(item.sampleTime, unit));
      obj.y.push(item.sampleValue);
      return obj;
    },
    {
      x: [],
      y: [],
    },
  );

  const option = React.useMemo(() => {
    return {
      backgroundColor: "rgba(1,1,1, 0.4)",
      toolbox: {
        feature: {
          dataZoom: {},
          restore: {},
          dataView: {},
          download: {},
          mark: true,
          magicType: {
            type: ["line", "bar"], // 默认的魔术类型切换
          },
          saveAsImage: {},
        },
      },
      dataZoom: [
        {
          type: "slider",
          filterMode: "empty",
        },
      ],
      grid: {
        left: 20,
        top: 40,
        right: 30,
        bottom: 60,
        containLabel: true, // 确保图表内部包含标签
      },
      tooltip: {
        trigger: "axis",
      },
      xAxis: {
        type: "category",
        boundaryGap: false,
        axisLine: {
          lineStyle: {
            color: "rgba(255, 255, 255, 0.50)",
          },
        },
        axisLabel: {
          color: "rgba(255, 255, 255, 0.60)",
          // interval: 0, // 调整标签与刻度之间的间距
          margin: 10,
        },
        axisTick: {
          show: true,
        },
        data: datas.x,
      },
      yAxis: {
        type: "value",
        splitNumber: 5,
        axisLabel: {
          show: true,
          textStyle: {
            color: "rgba(255, 255, 255, 0.80)",
            fontSize: 12,
          },
        },
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: "rgba(255, 255, 255, 0.60)",
            type: "dashed",
          },
        },
      },
      visualMap: [
        {
          show: false,
          dimension: 1,
          seriesIndex: 0,
          pieces: [
            { min: 5, max: 10, color: "#00bfff" },
            { min: 10, max: 20, color: "yellow" },
            { min: 20, max: 30, color: "orange" },
            { min: 30, color: "red" },
          ],
          outOfRange: {
            color: "#00FFAA",
          },
        },
      ],
      series: [
        {
          data: datas.y,
          type: "line",
          symbolSize: 6,
          itemStyle: {
            borderWidth: 2,
            borderColor: "RGB(113, 255, 241)",
          },
        },
      ],
    };
  }, [datas]);

  const onClickBtn = (date: string[]) => {
    setDate(date);
    setForm({
      ...form,
      beginTime: date[0],
      endTime: date[1],
    });
  };

  return (
    <Modal
      title={"历史波动图"}
      visible={true}
      //className='[&_.semi-modal-body]:pb-5'
      maskClosable={true}
      onCancel={() => {
        reset();
      }}
      footer={null}
      width={1040}
      height={800}
      centered
    >
      <div
        className={classNames(
          css.tableContainer,
          "w-full h-full overflow-x-auto  flex flex-col",
          hideScrollBar.hideScrollBar,
        )}
      >
        <div
          className={classNames(
            "h-[60px]  w-full relative flex items-center justify-between",
          )}
        >
          <DatePicker
            showClear={false}
            value={date}
            type="dateTimeRange"
            style={{ width: 350 }}
            onChange={setDate}
          />
          <InputNumber
            showClear={false}
            style={{ width: 80, marginLeft: "10px" }}
            placeholder="间隔值"
            value={interval}
            onChange={setInterval}
          />
          <Select
            defaultValue={unit}
            placeholder="采样间隔单位"
            style={{ width: 60, marginRight: "10px" }}
            onChange={setUnit}
            optionList={[
              { value: 1, label: "秒" },
              { value: 2, label: "分钟" },
              { value: 3, label: "小时" },
              { value: 4, label: "天" },
            ]}
          />
          <div className="flex gap-2">
            <span
              onClick={() => onClickBtn(getTime(15, "minute"))}
              className="btn rounded btn-primary btn-sm"
            >
              15分钟
            </span>
            <span
              onClick={() => onClickBtn(getTime(1, "hours"))}
              className="btn rounded btn-primary btn-sm"
            >
              1小时
            </span>
            <span
              onClick={() => onClickBtn(getTime(8, "hours"))}
              className="btn rounded btn-primary btn-sm"
            >
              8小时
            </span>
            <span
              onClick={() => onClickBtn(getTime(1, "day"))}
              className="btn rounded btn-primary btn-sm"
            >
              1天
            </span>
            <span
              onClick={() => onClickBtn(getTime(7, "day"))}
              className="btn rounded btn-primary btn-sm"
            >
              7天
            </span>
            <span
              onClick={() => onClickBtn(getTime(30, "day"))}
              className="btn rounded btn-primary btn-sm"
            >
              30天
            </span>
            <span
              onClick={() => {
                setForm({
                  ...form,
                  beginTime: date[0],
                  endTime: date[1],
                  interval,
                  intervalUnit: unit,
                });
              }}
              className="btn rounded btn-primary btn-sm"
            >
              查看
            </span>
          </div>
        </div>
        <div ref={ref} className="w-full h-full  ">
          <State>
            {datas?.x?.length ? (
              <FullChart option={option} />
            ) : (
              <div className="text-[20px] text-gray-300  w-full h-full flex items-center justify-center">
                暂无数据
              </div>
            )}
          </State>
        </div>
      </div>
    </Modal>
  );
};
