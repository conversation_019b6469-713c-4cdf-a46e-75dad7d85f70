# PieChart 通用饼图中间层组件文档

## 1. 组件简介

`PieChart` 是一个高度可复用的饼图中间层组件，适用于首页、统计页等所有需要 ECharts 饼图的业务场景。它统一了结构、样式、数据请求、Tab切换、option生成、中心统计等逻辑，极大减少了重复代码。

---

## 2. Props 参数说明

| 参数名        | 类型                                     | 说明                                                              | 是否必填 |
| ------------- | ---------------------------------------- | ----------------------------------------------------------------- | -------- |
| title         | string                                   | 图表标题                                                          | 是       |
| queryKey      | any[]                                    | 用于 react-query 的缓存 key，建议包含接口名和依赖参数             | 是       |
| queryFn       | (params: any) => Promise<any>            | 数据请求函数，返回 Promise，参数为 filter+tab切换参数             | 是       |
| filter        | object                                   | 额外的请求参数（如 areaId、时间范围等），会自动合并到请求参数     | 否       |
| tabList       | {label: string, value: any}[]            | Tab切换项（如本周/本月/本年），为空则不显示Tab切换                | 否       |
| tabParamName  | string                                   | Tab切换参数名，默认为 'type'                                      | 否       |
| optionBuilder | (data: any, tabValue?: any) => object    | ECharts option生成器，接收接口返回数据和当前tab值，返回option对象 | 是       |
| centerContent | (data: any, tabValue?: any) => ReactNode | 饼图中心自定义内容（如总数统计），可选                            | 否       |
| height        | number                                   | 图表高度，默认300                                                 | 否       |

---

## 3. 组件结构与交互

- **标题区**：左侧蓝色竖条+标题
- **Tab切换区**：可选，右侧按钮组，切换时自动请求新数据
- **图表区**：Echart饼图，option由optionBuilder生成
- **中心统计区**：可选，居中显示自定义内容
- **数据请求**：自动合并filter和tab参数，自动缓存
- **样式**：对标首页，白色卡片、圆角、阴影、padding

---

## 4. 典型用法示例

### 4.1 推荐用法：buildPieOption + buildPieCenterContent

```tsx
import {
  PieChart,
  buildPieOption,
  buildPieCenterContent,
} from "components/chart/PieChart";
import { getMonitorTypeStat } from "api/alarm/alarmStat";

export default function MonitorTypePie({ filter }) {
  return (
    <PieChart
      title="监测类型分布"
      queryKey={["getMonitorTypeStat"]}
      queryFn={getMonitorTypeStat}
      filter={filter}
      optionBuilder={buildPieOption({ nameField: "name", valueField: "num" })}
      centerContent={buildPieCenterContent({
        totalField: "num",
        label: "报警总计",
      })}
      height={300}
    />
  );
}
```

---

### 4.2 AlarmPriorityPie（无Tab切换）

```tsx
import { PieChart } from "components/chart/PieChart";
import { getPriorityStat } from "api/alarm/alarmStat";

export default function AlarmPriorityPie({ filter }) {
  return (
    <PieChart
      title="报警优先级分布"
      queryKey={["getPriorityStat"]}
      queryFn={getPriorityStat}
      filter={filter}
      optionBuilder={(data) => ({
        tooltip: { trigger: "item" },
        legend: { left: "right" },
        series: [
          {
            type: "pie",
            radius: ["40%", "70%"],
            data: (data?.data ?? []).map((i) => ({
              value: i.num,
              name: i.name,
            })),
          },
        ],
      })}
      centerContent={(data) => (
        <div>
          <div className="text-base text-[#666]">报警总计</div>
          <div className="text-[30px] text-[#333] font-black">
            {(data?.data ?? []).reduce((sum, i) => sum + (i.num ?? 0), 0)}
          </div>
        </div>
      )}
      height={300}
    />
  );
}
```

---

### 4.3 recordRight.tsx/right.tsx（有Tab切换）

```tsx
import { PieChart } from "components/chart/PieChart";
import { getRecordStat } from "api/xxx";

export default function RecordRight({ filter }) {
  const tabList = [
    { label: "本周", value: 1 },
    { label: "本月", value: 2 },
    { label: "本年", value: 3 },
  ];
  return (
    <PieChart
      title="报警类型分布"
      queryKey={["getRecordStat"]}
      queryFn={getRecordStat}
      filter={filter}
      tabList={tabList}
      tabParamName="type"
      optionBuilder={(data, tabValue) => ({
        // ...生成option
      })}
      centerContent={(data, tabValue) => (
        <div>报警总计 {data?.data?.totalNum ?? 0}</div>
      )}
      height={300}
    />
  );
}
```

---

## 5. 迁移/复用建议

- 只需将原有组件的 useQuery、option 生成、tab 切换逻辑，迁移到 PieChart 的参数和 optionBuilder/centerContent 中
- 其它 UI、样式、交互全部由 PieChart 统一维护
- 这样所有饼图组件都能用同一个 PieChart，极大提升复用性和维护性

---

如需更详细的迁移示例或 optionBuilder 写法，请随时告知！

---

## 6. buildPieOption / buildPieCenterContent 参数说明

### buildPieOption

| 参数名     | 类型            | 说明                |
| ---------- | --------------- | ------------------- |
| nameField  | string          | 名称字段            |
| valueField | string          | 数值字段            |
| colorList  | string[]        | 饼图配色（可选）    |
| legend     | object          | legend配置（可选）  |
| radius     | [string,string] | 饼图半径（可选）    |
| center     | [string,string] | 饼图中心（可选）    |
| label      | object          | label配置（可选）   |
| tooltip    | object          | tooltip配置（可选） |

### buildPieCenterContent

| 参数名     | 类型   | 说明                   |
| ---------- | ------ | ---------------------- |
| totalField | string | 统计总数的字段名       |
| label      | string | 中心显示的标题（可选） |

---

## 7. 其它业务组件调用方式
