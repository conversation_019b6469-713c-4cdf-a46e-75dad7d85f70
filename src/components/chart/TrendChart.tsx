import { useQuery } from "@tanstack/react-query";
import LoadingOrError from "components/LoadingOrError";
import { Echart } from "pages/bigScreen/component/Chart";
import NoData from "pages/bigScreen/component/Element/NoData";
import { useMemo, useState } from "react";

export interface TrendChartTab {
  label: string;
  value: any;
}

export interface TrendChartProps {
  title: string;
  queryKey: any[];
  queryFn: (params: any) => Promise<any>;
  filter?: object;
  tabList?: TrendChartTab[];
  tabParamName?: string; // 默认为'type'
  optionBuilder?: (data: any, tabValue?: any) => object;
  optionConfig?: BuildTrendOptionConfig;
  height?: number;
  /**
   * 自定义判空逻辑，返回true则显示empty
   * @param option optionBuilder生成的option
   * @param data 接口返回的原始数据
   */
  isEmptyFunc?: (option: any, data: any) => boolean;
}

/**
 * 通用趋势图option生成工厂参数
 */
export interface BuildTrendOptionConfig {
  xField: string;
  yField: string;
  xFormatter?: (v: any) => string;
  chartType?: "line" | "bar";
  seriesName?: string;
  yAxisFormatter?: (v: any) => string;
}

/**
 * 通用趋势图option生成工厂，支持单系列折线/柱状
 */
export function buildTrendOption(config: BuildTrendOptionConfig) {
  return (data: any) => {
    const list = data?.data?.alarmStatList ?? [];
    return {
      xAxis: {
        type: "category",
        data: list.map((i: any) =>
          config.xFormatter
            ? config.xFormatter(i[config.xField])
            : i[config.xField]
        ),
      },
      yAxis: {
        type: "value",
        axisLabel: config.yAxisFormatter
          ? { formatter: config.yAxisFormatter }
          : undefined,
      },
      series: [
        {
          name: config.seriesName,
          data: list.map((i: any) => i[config.yField]),
          type: config.chartType || "line",
        },
      ],
    };
  };
}

/**
 * 通用渐变平滑折线图option生成器
 */
export function buildSmoothAreaLineOption(config: {
  xField: string;
  yField: string;
  xFormatter?: (v: any) => string;
  lineColor?: string;
  areaColor?: string;
  areaTo?: string;
  smooth?: boolean;
  showSymbol?: boolean;
  axisLabelColor?: string;
  grid?: object;
}) {
  return (data: any) => {
    const list = data?.data?.alarmStatList ?? [];
    return {
      tooltip: { trigger: "axis" },
      grid: config.grid || {
        left: "2%",
        right: "2%",
        bottom: "0%",
        top: "8%",
        containLabel: true,
      },
      xAxis: {
        type: "category",
        boundaryGap: false,
        data: list.map((item: any) =>
          config.xFormatter
            ? config.xFormatter(item[config.xField])
            : item[config.xField]
        ),
        axisLabel: { color: config.axisLabelColor || "#666" },
      },
      yAxis: {
        type: "value",
        minInterval: 1,
        axisLabel: { color: config.axisLabelColor || "#666" },
        splitLine: { lineStyle: { type: "dashed", color: "#eee" } },
      },
      series: [
        {
          data: list.map((item: any) => item[config.yField]),
          type: "line",
          showSymbol: config.showSymbol ?? false,
          lineStyle: { color: config.lineColor || "#60B7FF" },
          smooth: config.smooth ?? true,
          areaStyle: {
            opacity: 1,
            color: {
              type: "linear",
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: config.areaColor || "#60B7FF" },
                { offset: 1, color: config.areaTo || "#fff" },
              ],
            },
          },
        },
      ],
    };
  };
}

export function TrendChart({
  title,
  queryKey,
  queryFn,
  filter = {},
  tabList,
  tabParamName = "type",
  optionBuilder: optionBuilderProp,
  optionConfig,
  height = 300,
  isEmptyFunc,
}: TrendChartProps) {
  // tab切换状态
  const [tabValue, setTabValue] = useState(tabList?.[0]?.value);

  // 组合请求参数
  const params = useMemo(() => {
    if (tabList && tabParamName) {
      return { ...filter, [tabParamName]: tabValue };
    }
    return filter;
  }, [filter, tabList, tabParamName, tabValue]);

  // useQuery拉取数据
  const { data, isLoading } = useQuery({
    queryKey: [...queryKey, params],
    queryFn: () => queryFn(params),
    enabled: !tabList || tabValue !== undefined,
  });

  // 生成optionBuilder：优先用props.optionBuilder，其次optionConfig
  const optionBuilder = useMemo(() => {
    if (optionBuilderProp) return optionBuilderProp;
    if (optionConfig) return buildTrendOption(optionConfig);
    return () => ({}); // 默认空option
  }, [optionBuilderProp, optionConfig]);

  // 生成option
  const option = useMemo(
    () => optionBuilder(data, tabValue),
    [data, tabValue, optionBuilder]
  );

  // 判断empty状态：优先用props.isEmptyFunc，否则只判断series.data是否为空数组
  const isEmpty = useMemo(() => {
    if (typeof isEmptyFunc === "function") {
      return isEmptyFunc(option, data);
    }
    const opt = option as any;
    if (!opt || !opt.series) return true;
    const seriesArr = Array.isArray(opt.series) ? opt.series : [opt.series];
    // 只要所有 series 的 data 都为空数组才算 empty
    return seriesArr.every((s: any) => !s.data || s.data.length === 0);
  }, [option, data, isEmptyFunc]);

  return (
    <div className="flex flex-col bg-white border border-[#EDEDEE] rounded-lg p-6 divide-y gap-y-5 relative">
      <div className="flex justify-between">
        <div className="flex items-center gap-x-[10px]">
          <div className="bg-[#60B7FF] w-1 h-[22px]"></div>
          <span className="text-[18px] font-bold">{title}</span>
        </div>
        {tabList && tabList.length > 0 && (
          <div className="flex gap-2">
            <div className="border border-[#60B7FF] text-[#60B7FF] text-sm cursor-pointer overflow-hidden rounded-md flex">
              {tabList.map((tab) => (
                <div
                  key={tab.value}
                  className={`leading-none px-[14px] py-[8px] ${tabValue === tab.value ? "text-white bg-[#60B7FF]" : ""}`}
                  onClick={() => setTabValue(tab.value)}
                >
                  {tab.label}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
      <div style={{ width: "100%", height: 300, position: "relative" }}>
        {isLoading ? (
          <LoadingOrError />
        ) : isEmpty ? (
          <NoData />
        ) : (
          <Echart option={option} />
        )}
      </div>
    </div>
  );
}
