// src/components/export/types.ts

// 导出格式类型
export type ExportFormat = "excel" | "csv";
// 导出内容类型
export type ExportContentType = "list" | "object"; // list: 数组类型, object: 对象类型

// 导出函数的上下文类型
export interface ExportContextType {
  exportToFile: (options: ExportOptions) => Promise<void>;
  exportError: string | null;
}

// ExportProvider 的 props 类型
export type ExportProviderProps = {
  children: React.ReactNode;
  // exportFunction?: ExportContextType["exportToFile"];
};

// 列表页导出的列信息类型
export type Column = {
  header: string; // 表头显示的文字
  field: string; // 数据字段名称
  renderText?: (value: any, array: any[]) => any; // 数据格式化函数，例如将整型转换为字符
};

// 导出函数的参数类型
export type ExportOptions = {
  // apiUrl: string;
  // type: ExportFormat;
  // data: any[];
  // params?: object;
  format: ExportFormat;
  contentType: ExportContentType;
  apiFn: (params: object) => Promise<any[]>; // 包装的API函数，接受params参数并返回Promise的数据数组
  params?: object; // API函数的参数
  columns: Column[]; // 导出的列信息，包括表头、字段名、渲染函数等
  entityName?: string; // 导出的实体名称，用于生成文件名
  fileName?: string; // 导出的文件名，不包含扩展名
};
