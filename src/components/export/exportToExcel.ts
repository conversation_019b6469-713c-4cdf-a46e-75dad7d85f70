// src/components/export/exportToExcel.ts
import dayjs from "dayjs";
import ExcelJS from "exceljs";
import { saveAs } from "file-saver";

// 生成有意义的文件名
const generateExcelFileName = (entityName?: string): string => {
  const timestamp = dayjs().format("YYYY-MM-DD");
  const prefix = entityName ? entityName : "导出数据";
  return `${prefix}_${timestamp}.xlsx`;
};

export const exportListToExcel = async (
  data: any[],
  columns: { header: string; field: string }[],
  entityName?: string,
  fileName?: string | null
) => {
  // console.debug("导出 Excel", data, columns);
  try {
    // 创建一个新的工作簿和工作表
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet("Export");

    // 设置表头
    worksheet.columns = columns.map((column) => ({
      header: column.header,
      key: column.field,
      width: 20, // 可以根据需要调整列宽
    }));

    // 添加数据
    data.forEach((item) => {
      const row = columns.reduce(
        (obj, column) => {
          obj[column.field] = item[column.field];
          return obj;
        },
        {} as { [key: string]: any }
      );
      worksheet.addRow(row);
    });

    // 设置样式，例如让表头加粗
    worksheet.getRow(1).font = { bold: true };

    // 将工作簿写入 buffer
    const buffer = await workbook.xlsx.writeBuffer();

    // 使用 file-saver 保存文件，使用生成的有意义文件名
    const finalFileName = fileName || generateExcelFileName(entityName);
    saveAs(new Blob([buffer]), finalFileName);
  } catch (error) {
    console.error("导出失败", error);
  }
};
