// src/components/export/useExport.ts
import { useContext } from "react";
import { ExportContext } from "./exportProvider";
import { ExportContextType } from "./types";

export const useExport = (): ExportContextType | undefined => {
  const context = useContext(ExportContext);
  if (!context) return undefined;
  // throw new Error("useExport must be used within an ExportProvider");
  /* if (!context) {
    return {
      exportToFile: async () => console.warn("导出功能未启用"),
    } as ExportContextType;
  } */
  return context;
};
