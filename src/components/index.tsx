import { Image, ImagePreview, Tooltip } from "@douyinfe/semi-ui";
import { base_url } from "config";

export * from "./enum";
export * from "./export";
export * from "./list";
export * from "./modal";
export * from "./print";
export * from "./search";
export * from "./select";
export * from "./side";
export * from "./SignaturePad";
export * from "./table";
export * from "./tableConfig";
export * from "./tiandituMap";
export * from "./tool";
export * from "./tree";
export * from "./upload";

export const renderImgs = (list: string[]) => {
  const uri: string[] = [];
  list.forEach((o) => {
    if (
      o === "" ||
      o === null ||
      o === undefined ||
      o === "null" ||
      o === "undefined"
    ) {
      return;
    }
    uri.push(`${base_url}${o}`);
  });

  return (
    <ImagePreview>
      {uri.map((src, index) => {
        return (
          <Image
            key={index}
            src={src}
            width={80}
            alt={`lamp${index + 1}`}
            style={{ marginRight: 5 }}
          />
        );
      })}
    </ImagePreview>
  );
};

export const renderFiles = (list: string[]) => {
  console.debug("renderFiles", list);
  const content = list
    ? list.map((item, index, array) => {
        if (
          item === "" ||
          item === null ||
          item === undefined ||
          item === "null" ||
          item === "undefined"
        ) {
          return;
        }
        return (
          <>
            <span key={index}>
              {index + 1}:{" "}
              <a
                href={base_url + item}
                target="_blank"
                style={{
                  color: "#0052cc",
                  textDecoration: "none",
                  cursor: "pointer",
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.textDecoration = "underline";
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.textDecoration = "none";
                }}
              >
                {item?.split("/")?.pop()}
              </a>
            </span>
            <br />
          </>
        );
      })
    : null;

  if (!content) {
    return null;
  }
  return (
    <Tooltip content={content}>
      <p>{content}</p>
    </Tooltip>
  );
};
