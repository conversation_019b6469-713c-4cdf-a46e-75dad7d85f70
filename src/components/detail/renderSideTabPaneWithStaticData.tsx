import { Button, Table, Tag, Tooltip, Typography } from "@douyinfe/semi-ui";
import { renderFiles, renderImgs } from "components";
import dayjs from "dayjs";
import { find, propEq, type } from "ramda";
import { FC, useMemo } from "react";
import { useNavigate } from "react-router-dom";
import {
  formatDate,
  formatDateDay,
  formatNumToRate,
  millisecondsOfOnemonth,
} from "utils";

type schemeGroup = {
  groupName: string;
  list?: any;
  table?: any;
};

type RenderSideProps = {
  entity: string;
  entityTitle: string;
  scheme: Array<schemeGroup> | object;
  dataSource?: Array<any>;
  infoOrList: number;
  // data?: any;
  gridCol?: number;
  setFilterList?: any;
  style?: React.CSSProperties; // 添加 style 属性
};

export const RenderSideTabPaneWithStaticData: FC<RenderSideProps> = ({
  entity,
  entityTitle,
  scheme,
  dataSource,
  infoOrList,
  // data,
  gridCol = 6,
  setFilterList,
  style, // 添加 style 参数
}) => {
  // console.log('scheme', scheme)
  // console.log('entityTitle', entityTitle)
  // console.log('detailAtom', detailAtom)

  // const isGroup = type(scheme) === 'Array'

  // const [infoAtom, setInfoAtom] = useAtom(detailAtom);
  // console.log('infoAtom', infoAtom)

  // console.log('dataApiRes', dataApiRes)

  const navigate = useNavigate();

  const infoData = useMemo(() => {
    // console.log('infoOrList', infoOrList)
    // console.log('scheme', scheme)
    if (dataSource) {
      return dataSource;
    }
  }, [dataSource, infoOrList, scheme]);

  // console.log('infoData', infoData)

  /* return (
    <div className="flex gap-2" >
      {
        uri.map((o: string, i: number) => (
          // <img src={o} alt="" key={i} className="size-10 rounded-md" />
          <img src={o} alt="" key={i} className="max-w-96" />
        ))
      }
    </div>
  ) */

  const renderAtom = (item, index) => {
    // console.log('item', item, index)
    const label = item.label;
    let value = infoData?.[item.name];
    // console.log('label, value', label, value, type(value))
    /* if (type(value) === 'Object') {
      value = infoData[item.name]?.name || '-'
    }
    console.log('label, value', label, value, type(value)) */

    if (type(value) === "Array" && entity === "jobProcesses") {
      return (
        <>
          {value?.map((v, k) => (
            <>
              <div className="bg-zinc-100 p-4 text-end border-r border-b">
                {v?.name || "-"}:
              </div>
              <div className="col-span-2 p-4 border-r border-b">
                {v?.candidatePerson?.map((c, ckey) => (
                  <Tag key={ckey} size="large" style={{ marginRight: "1px" }}>
                    {c.name}
                  </Tag>
                ))}
              </div>
            </>
          ))}
        </>
      );
    }
    if (type(value) === "Array" || item?.type === "array") {
      return (
        <>
          {item?.padding ? (
            <>
              <div className="bg-zinc-100 p-4 text-end border-r border-b"></div>
              <div className="col-span-2 p-4 border-r border-b"></div>
            </>
          ) : null}
          <div className="bg-zinc-100 p-4 text-end border-r border-b">
            {label}:
          </div>
          <div className="col-span-5 p-4 border-r border-b">
            {item?.type === "image"
              ? // <img src={value} alt={label} className="w-32 h-32" />
                renderImgs(value)
              : value?.map((c, ckey) => (
                  <Tag key={ckey} size="large" style={{ marginRight: "1px" }}>
                    {c?.name ?? c?.label ?? c?.content ?? "-"}
                  </Tag>
                ))}
          </div>
        </>
      );
    }

    return (
      <>
        {item?.padding ? (
          <>
            <div className="bg-zinc-100 p-4 text-end border-r border-b"></div>
            <div className="col-span-2 p-4 border-r border-b"></div>
          </>
        ) : null}
        <div className="bg-zinc-100 p-4 text-end border-r border-b">
          {label}:
        </div>
        {item?.type === "image" ||
        item?.type === "file" ||
        item?.type === "text" ? (
          <div className="col-span-5 p-4 border-r border-b">
            {item?.type === "image" ? (
              <img src={value} alt={label} className="w-32 h-32" />
            ) : item?.type === "file" ? (
              <a href={value} target="_blank" rel="noreferrer">
                {" "}
                {label}{" "}
              </a>
            ) : (
              value
            )}
          </div>
        ) : (
          <div className="col-span-2 p-4 border-r border-b">
            {item?.type === "dic"
              ? value?.dicValue
              : item?.type === "date"
                ? formatDateDay(value)
                : item?.type === "datetime"
                  ? formatDate(value)
                  : item?.type === "enum"
                    ? find(propEq(value, "id"))(item.enumMap)?.name
                    : item?.type === "entity"
                      ? item?.key
                        ? value?.[item.key]
                        : value?.name
                      : value}
          </div>
        )}
      </>
    );
  };

  const renderItem = (list) => {
    if (!list) {
      return null;
    }
    return <>{list.map((item, index) => renderAtom(item, index))}</>;
  };

  // 生成表格表头
  const generateColumns = (columns) => {
    if (!columns) {
      return [];
    }
    const tmp = [];
    const renderCol = (text, record, col) => {
      // console.debug("text, record, col", text, record, col);

      let value = text;
      if (col.type === "entity") {
        value = text?.name ?? "-";
      } else if (col.type === "dic") {
        value = text?.dicValue ?? "-";
      } else if (col.type === "date") {
        value = formatDateDay(text);
      } else if (col.type === "datetime") {
        value = formatDate(text);
      } else if (col.type === "imagelist") {
        if (col.render === "old") {
          value = JSON.parse(text ?? "[]");
        }
      } else if (col.type === "image") {
      } else if (col.type === "filelist") {
      } else if (col.type === "file") {
      } else if (col.type === "text") {
      } else if (col.type === "enum") {
        // value = col.enumMap(col.name)
        const i = find(propEq(text, "id"))(col.enumMap);
        value = i?.color ? <Tag color={i?.color}>{i?.name}</Tag> : i?.name;
      }

      return (
        <>
          {col.type === "button" ? (
            col.buttons.map((button, index) => {
              if (!button(record)) return null;
              return (
                <Button onClick={() => button(record)?.func(record)}>
                  {button(record)?.chnName}
                </Button>
              );
            })
          ) : col.type === "image" || col.type === "imagelist" ? (
            renderImgs(type(value) === "Array" ? value : [value])
          ) : col.type === "file" || col.type === "filelist" ? (
            renderFiles(type(value) === "Array" ? value : [value])
          ) : col.render == "rate" ? (
            formatNumToRate(value)
          ) : col.render === "expire" ? (
            <p>
              {dayjs(value).diff(dayjs()) < millisecondsOfOnemonth ? (
                <Tag color="red">{formatDateDay(value)}</Tag>
              ) : dayjs(value).diff(dayjs()) < 3 * millisecondsOfOnemonth ? (
                <Tag color="yellow">{formatDateDay(value)}</Tag>
              ) : (
                <Tag color="white">{formatDateDay(value)}</Tag>
              )}
            </p>
          ) : col.render === "link" ? (
            <span
              className="cursor-pointer"
              onClick={() => {
                const setFilter = setFilterList[col.name]; // 根据列的 name 获取对应的 setFilter 函数
                if (setFilter) {
                  setFilter({
                    filter: {
                      [col.filterTargetField]: record[col.filterField],
                    },
                  });
                }
                // setDangerFilter({ filter: { id } });
                navigate(col.link);
              }}
            >
              {value}
            </span>
          ) : (
            <Tooltip content={value}>
              <p>{value}</p>
            </Tooltip>
          )}
        </>
      );
    };
    columns.forEach((col) => {
      tmp.push({
        title: col.label,
        dataIndex: col.name,
        width: col.width ?? 80,
        render: (text, record, index) => renderCol(text, record, col),
      });
    });
    return tmp;
  };

  const renderTable = (item) => {
    if (!item) {
      return null;
    }
    const columns = generateColumns(item?.columns);

    // 优化列宽配置，使表格更好地自适应内容
    if (columns.length > 0) {
      // 计算总宽度和可分配宽度
      let totalWidth = 0;
      let flexibleColumns = [];

      // 第一轮：根据列的类型设置基础宽度
      columns.forEach((col, index) => {
        if (
          col.dataIndex === "name" ||
          col.title?.includes("名称") ||
          col.title?.includes("描述")
        ) {
          col.width = 180;
        } else if (col.dataIndex === "id" || col.title?.includes("ID")) {
          col.width = 100;
        } else if (
          col.title?.includes("时间") ||
          col.dataIndex?.includes("Time") ||
          col.dataIndex?.includes("Date")
        ) {
          col.width = 160;
        } else if (
          col.title?.includes("状态") ||
          col.dataIndex?.includes("Status")
        ) {
          col.width = 100;
        } else {
          col.width = 120;
          // 标记为可灵活调整的列
          flexibleColumns.push(index);
        }
        totalWidth += col.width;
      });

      // 设置表格为100%宽度，不使用scroll
      // 移除最后一列的特殊处理，让所有列按比例分配
    }

    return (
      <div className="w-full border rounded overflow-hidden">
        <Table
          className="w-full"
          rowKey="id"
          scroll={{ x: false }}
          resizable={true}
          bordered={true}
          columns={columns}
          dataSource={infoData}
          onHeaderRow={(columns, index) => {
            return {
              className: "text-gray-900 text-opacity-90 bg-gray-50",
            };
          }}
          headerStyle={{ color: "blue" }}
          pagination={false}
          style={{ width: "100%" }}
          tableLayout="fixed" // 使用fixed布局让表格列宽平均分配
          empty={
            <div
              style={{ width: "100%", padding: "20px 0", textAlign: "center" }}
            >
              暂无数据
            </div>
          }
        />
      </div>
    );
  };

  return (
    <div
      className="w-full overflow-y-auto flex flex-col gap-y-4"
      style={{
        ...style,
        width: "100%",
        backgroundColor: "#fff",
      }}
    >
      {scheme?.map?.((o, i) => (
        <div className="w-full pt-4" key={i}>
          {o.groupName ? (
            <Typography.Title heading={5}>{o.groupName}</Typography.Title>
          ) : null}
          <div
            className={`grid grid-cols-${gridCol} rounded-lg overflow-hidden border border-b-0`}
            style={{
              gridTemplateColumns: `repeat(${gridCol}, minmax(0, 1fr))`,
              width: "100%", // 确保宽度为100%
            }}
          >
            {renderItem(o?.list)}
          </div>
          <div className={`mt-4`} style={{ width: "100%" }}>
            {renderTable(o?.table)}
          </div>
        </div>
      ))}
    </div>
  );
};
