import { Table } from "@douyinfe/semi-ui";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { ExportContentType, ExportFormat, useExport } from "components";
import { useBtnHooks } from "hooks";
import { useAtom } from "jotai";
import { filter } from "ramda";
import { FC, useCallback, useMemo, useState } from "react";
import { useLoaderData, useLocation } from "react-router-dom";

type RenderSideDetailListProps = {
  entity: string;
  columnsAtom: any;
  filterAtom: any;
  queryApi: any;
  detailAtom: any;
  expandedDataCallback?: any;
};
type InfoAtomType = {
  id: string | number;
  show?: boolean;
};

export const RenderSideDetailList: FC<RenderSideDetailListProps> = ({
  entity,
  columnsAtom,
  filterAtom,
  queryApi,
  detailAtom,
  expandedDataCallback,
  ...restProps
}) => {
  const [infoAtom, setInfoAtom] = useAtom<InfoAtomType>(detailAtom);

  const queryClient = useQueryClient();
  const queryKey = `list${entity}`;

  const exportContext = useExport();
  const canExport = exportContext && exportContext.exportToFile;

  const loderData = useLoaderData();
  const { pathname } = useLocation();
  const genBtn = useBtnHooks(loderData, pathname);

  const [rowKeys, setRowKeys] = useState<number[]>([]);
  const [rows, setRows] = useState<any[]>([]);

  //Atoms
  const [_columns, setColumns] = useAtom(columnsAtom);

  const [localFilter, setLocalFilter] = useAtom(filterAtom);
  /* const [editModal, setEditModal] = useAtom(atoms.editModal);
  const [refetchFn, setRefetchFn] = useAtom(atoms.Fn);
  const [configModal, setConfigModal] = useAtom(atoms.configModal);
  const reset = useResetAtom(referAtom); */

  //Apis
  const { isLoading, data, refetch } = useQuery({
    queryKey: [queryKey, localFilter],
    queryFn: () =>
      queryApi({
        // 兼容2种API形式
        // 1. API形式: /xxx/{id}/xxx
        id: infoAtom?.id ?? "",
        values: {
          ...localFilter,
          filter: { ...localFilter.filter },
        },
        // 2. API形式: /xxx/.../xxx
        ...localFilter,
        filter: { ...filter, ...localFilter.filter },
      }),
  });
  const dataSource = useMemo(() => data?.data ?? {}, [data?.data]);
  const result = useMemo(() => dataSource?.results ?? [], [dataSource]);
  const expandedData = useMemo(() => {
    return dataSource?.expandedResults ?? [];
  }, [dataSource]);

  const expandRowRender = expandedDataCallback
    ? expandedDataCallback(expandedData)
    : null;

  /* useEffect(() => {
    setRefetchFn({
      refetch: refetch,
    });
  }, [refetch]); */

  const handleExport = (
    format: ExportFormat = "excel",
    contentType: ExportContentType = "list"
  ) => {
    exportContext?.exportToFile?.({
      format: format,
      contentType: contentType,
      // data: data,
      apiFn: queryApi,
      params: {
        id: infoAtom?.id ?? "",
        values: { ...localFilter, filter: { ...localFilter.filter } },
        ...localFilter,
        filter: { ...filter, ...localFilter.filter },
      },
      columns: _columns,
      entityName: entity,
    });
  };

  const columns = useMemo(() => {
    return [..._columns];
  }, [_columns, genBtn]);

  /* const rowSelection = useMemo(
    () => ({
      fixed: true,
      getCheckboxProps: (record) => ({
        disabled: checkDisabledFunc ? checkDisabledFunc(record) : false,
        name: record.name,
      }),
      // onChange	(selectedRowKeys: number[]|string[], selectedRows: RecordType[]) => void
      // 选中项发生变化时的回调。第一个参数会保存上次选中的 row keys，即使你做了分页受控或更新了 dataSource FAQ
      onChange: (selectedRowKeys, selectedRows) => {
        setRowKeys(selectedRowKeys);
        // 只保留当前页选中的数据
        // setRows(selectedRows);
        setRows((prev) => {
          const remainRows = prev.filter((o) => selectedRowKeys.includes(o.id));
          // return [...remainRows, ...selectedRows];
          return union(remainRows, selectedRows);
        });
      },
    }),
    [setRowKeys]
  ); */

  const handlePageChange = useCallback(
    (currentPage: number) => {
      console.debug("handlePageChange", currentPage);
      setLocalFilter({
        ...localFilter,
        filter: { ...localFilter.filter },
        pageNumber: currentPage,
        // TODO pageSize,
      });
    },
    [localFilter, setLocalFilter]
  );

  const handlePageSizeChange = useCallback(
    (pageSize: number) => {
      setLocalFilter({
        ...localFilter,
        filter: { ...localFilter.filter },
        pageSize: pageSize,
        // TODO pageNumber,
      });
    },
    [localFilter, setLocalFilter]
  );

  const operationsRightBtn = () => {
    return (
      <>
        {/* <div className="tooltip" data-tip="刷新">
          <button
            className="btn btn-sm btn-ghost rounded no-animation"
            onClick={() => {
              refetch();
            }}
          >
            <IconRefresh />
          </button>
        </div>
        {canExport ? (
          <div className="tooltip" data-tip="导出">
            {genBtn(
              "export",
              <button
                className="btn btn-sm btn-ghost rounded no-animation"
                onClick={() => handleExport()}
              >
                <IconDownload />
              </button>
            )}
          </div>
        ) : null}
        {importProps ? (
          <UploadTmpl
            entity={importProps?.entity}
            excelType={importProps?.excelType}
            downUrl={importProps?.downUrl}
            tip={importProps?.tip}
          />
        ) : null}
        <div className="tooltip" data-tip="设置">
          <button
            className="btn btn-sm btn-ghost rounded no-animation"
            onClick={handleOpenSetting}
          >
            <IconSetting />
          </button>
        </div>

        <div className="tooltip" data-tip="下载">
          <button className="btn btn-sm btn-ghost rounded no-animation">
            <IconDownload />
          </button>
        </div>
        <div className="tooltip" data-tip="上传">
          <button className="btn btn-sm btn-ghost rounded no-animation">
            <IconUpload />
          </button>
        </div>
        <div className="tooltip" data-tip="打印">
          <button className="btn btn-sm btn-ghost rounded no-animation">
            <IconPrint />
          </button>
        </div> */}
      </>
    );
  };
  return (
    <>
      {/* <TableConfig
        columns={_columns}
        handleSave={setColumns}
        visible={configModal}
        handleClose={setConfigModal}
      /> */}
      <div className="bg-white big_screen_table_filter_table_function big_screen_table_box shadow px-4 h-fit rounded">
        {/* Operation bar */}
        <div className="flex py-4 justify-between big_screen_table_filter_operation">
          {/* Operation Selection */}
          <div className="flex gap-4">
            {/* {readonly ? null : OperationsLeftBtn()} */}
            {/* {readonly ? null : <OperationsLeftBtn />} */}
          </div>

          {/* Operation All */}
          <div className="flex gap">{operationsRightBtn()}</div>
        </div>

        <Table
          {...restProps}
          resizable={true}
          bordered={true}
          className="rounded overflow-hidden"
          rowKey="id"
          // scroll={tableProps.scroll}
          columns={columns ?? []}
          dataSource={result}
          // rowSelection={readonly ? false : rowSelection} // layout=='modal' & readonly
          loading={isLoading}
          onHeaderRow={(column, index) => {
            return {
              className: "text-gray-900 text-opacity-90 bg-gray-50",
            };
          }}
          headerStyle={{ color: "blue" }}
          pagination={{
            showSizeChanger: true,
            //popoverRole: 'topRight',
            currentPage: dataSource?.pageNumber ?? 1,
            pageSize: dataSource?.pageSize ?? 10,
            total: dataSource?.totalCount ?? 0,
            onPageChange: handlePageChange,
            onPageSizeChange: handlePageSizeChange,
            pageSizeOpts: [10, 15, 20, 50],
            showQuickJumper: true,
            /* showTotal: (total, range) => `共${total}条`,
            onChange: (page, pageSize) => {
              setPageNumber(page);
              setPageSize(pageSize);
            },
            onShowSizeChange: (current, size) => {
              setPageNumber(current);
              setPageSize(size);
            } */
          }}
          expandedRowRender={expandRowRender}
          // hideExpandedColumn={false}
        />
      </div>
    </>
  );
};
