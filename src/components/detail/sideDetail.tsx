//import React from 'react'

import { SideSheet, TabPane, Tabs, Typography } from "@douyinfe/semi-ui";
import { useAtom } from "jotai";
import { useResetAtom } from "jotai/utils";
import { RenderSideTabPane } from "./renderSideTabPane";

type TabPaneProps = {
  entity: string;
  entityTitle: string;
  api: any;
  params?: any;
  scheme: any;
  infoOrList: number;
  child?: any;
  childProps?: any;
};

type Props = {
  entityTitle: string;
  detailAtom: any;
  tabPaneList: Array<TabPaneProps>;
};

const SideDetail = (props: Props) => {
  const [info, setInfo] = useAtom(props.detailAtom);
  const reset = useResetAtom(props.detailAtom);

  // console.log('info', info)

  return (
    <SideSheet
      title={
        <Typography.Title heading={4}>
          {props.entityTitle}详细信息
        </Typography.Title>
      }
      headerStyle={{ borderBottom: "1px solid var(--semi-color-border)" }}
      bodyStyle={{
        borderBottom: "1px solid var(--semi-color-border)",
        // added by Trae AI
        // display: "flex",
        // flexDirection: "column",
        // width: "100%",
        // overflow: "hidden",
      }}
      visible={info?.show}
      closeIcon={null} // TODO
      disableScroll={false}
      onCancel={() => {
        reset();
      }}
      size="large"
      // style={{ width: "auto", maxWidth: "80vw" }} // 设置最大宽度为视口宽度的80%
    >
      <Tabs type="card" style={{ width: "100%", flex: 1, overflow: "hidden" }}>
        {props.tabPaneList.map((tabPane, index) => {
          return (
            <TabPane
              tab={tabPane.entityTitle}
              itemKey={tabPane.entity}
              style={{ width: "100%", overflow: "hidden" }}
            >
              {tabPane.child ? (
                // 如果有 child 字段，直接渲染 child 指向的组件
                <tabPane.child {...tabPane.childProps} />
              ) : (
                // 否则使用 RenderSideTabPane 的逻辑
                <RenderSideTabPane
                  entity={tabPane.entity}
                  entityTitle={tabPane.entityTitle}
                  scheme={tabPane.scheme}
                  dataApi={tabPane.api}
                  params={tabPane.params}
                  detailAtom={props.detailAtom}
                  infoOrList={tabPane.infoOrList}
                />
              )}
            </TabPane>
          );
        })}
      </Tabs>
    </SideSheet>
  );
};

export default SideDetail;
