import { Button, Table, Tag, Tooltip, Typography } from "@douyinfe/semi-ui";
import { useQuery } from "@tanstack/react-query";
import {
  SAFETY_ANALYSIS_JOBSTEP,
  VideoModal,
  renderFiles,
  renderImgs,
} from "components";
import dayjs from "dayjs";
import { useAtom } from "jotai";
import { find, isEmpty, propEq, type } from "ramda";
import { FC, useMemo } from "react";
import {
  formatDate,
  formatDateDay,
  formatNumToRate,
  millisecondsOfOnemonth,
} from "utils";

type schemeGroup = {
  groupName: string;
  list?: any;
  table?: any;
};

type RenderSideProps = {
  entity: string;
  entityTitle: string;
  scheme: Array<schemeGroup> | object;
  dataApi: any;
  params?: any;
  infoOrList: number;
  detailAtom: any;
  // data?: any;
  gridCol?: number;
};

const EnumMap = {
  jobStep: SAFETY_ANALYSIS_JOBSTEP,
};

export const RenderSideTabPane: FC<RenderSideProps> = ({
  entity,
  entityTitle,
  scheme,
  dataApi,
  params,
  infoOrList,
  detailAtom,
  // data,
  gridCol = 6,
}) => {
  // console.log('scheme', scheme)
  // console.log('entityTitle', entityTitle)
  // console.log('detailAtom', detailAtom)

  // const isGroup = type(scheme) === 'Array'

  const [infoAtom, setInfoAtom] = useAtom(detailAtom);
  // console.log('infoAtom', infoAtom)

  const { data: dataApiRes } = useQuery({
    queryKey: [`get${entity}`, infoAtom?.id],
    queryFn: () => {
      if (infoOrList === 1) {
        return dataApi(infoAtom?.id);
      } else if (infoOrList === 2) {
        return dataApi(params);
        // return dataApi({ ...params, filter: { ...params?.filter, [params?.filter?.key]: infoAtom?.id } })
      } else {
        console.error("-----------------infoOrList error-----------------");
      }
    },
    enabled: !!infoAtom?.id,
  });
  // console.log('dataApiRes', dataApiRes)

  const infoData = useMemo(() => {
    // console.log('dataApiRes', dataApiRes)
    // console.log('infoOrList', infoOrList)
    // console.log('scheme', scheme)
    if (infoOrList === 1) {
      return dataApiRes?.data ?? {};
    } else if (infoOrList === 2) {
      // return dataApiRes?.data?.results ?? []
      //TODO
      const dataSource = scheme[0]?.table?.dataSource;
      // console.log('scheme.dataSource', dataSource)
      console.log(!isEmpty(dataSource));
      const dataRes =
        dataSource && !isEmpty(dataSource)
          ? dataApiRes?.data?.[dataSource]
          : dataApiRes?.data;
      // console.log('dataRes', dataRes)
      return dataRes ?? [];
    }
  }, [dataApiRes]);

  // console.log('infoData', infoData)

  /* return (
    <div className="flex gap-2" >
      {
        uri.map((o: string, i: number) => (
          // <img src={o} alt="" key={i} className="size-10 rounded-md" />
          <img src={o} alt="" key={i} className="max-w-96" />
        ))
      }
    </div>
  ) */

  const renderAtom = (item, index, record) => {
    // console.log('item', item, index)
    const label = item.label;
    let value = infoData?.[item.name];
    // console.log('label, value', label, value, type(value))
    /* if (type(value) === 'Object') {
      value = infoData[item.name]?.name || '-'
    }
    console.log('label, value', label, value, type(value)) */

    if (type(value) === "Array" && entity === "jobProcesses") {
      return (
        <>
          {value?.map((v, k) => (
            <>
              <div className="bg-zinc-100 p-4 text-end border-r border-b">
                {v?.name || "-"}:
              </div>
              <div className="col-span-2 p-4 border-r border-b">
                {v?.candidatePerson?.map((c, ckey) => (
                  <Tag key={ckey} size="large" style={{ marginRight: "1px" }}>
                    {c.name}
                  </Tag>
                ))}
              </div>
            </>
          ))}
        </>
      );
    }
    if (type(value) === "Array" || item?.type === "array") {
      return (
        <>
          {item?.padding ? (
            <>
              <div className="bg-zinc-100 p-4 text-end border-r border-b"></div>
              <div className="col-span-2 p-4 border-r border-b"></div>
            </>
          ) : null}
          <div className="bg-zinc-100 p-4 text-end border-r border-b">
            {label}:
          </div>
          <div className="col-span-5 p-4 border-r border-b">
            {item?.type === "image"
              ? // <img src={value} alt={label} className="w-32 h-32" />
                renderImgs(value)
              : item?.type === "file"
                ? renderFiles(value)
                : value?.map((c, ckey) => (
                    <Tag key={ckey} size="large" style={{ marginRight: "1px" }}>
                      {c?.name ?? c?.label ?? c?.content ?? "-"}
                    </Tag>
                  ))}
          </div>
        </>
      );
    }

    return (
      <>
        {item?.padding ? (
          <>
            <div className="bg-zinc-100 p-4 text-end border-r border-b"></div>
            <div className="col-span-2 p-4 border-r border-b"></div>
          </>
        ) : null}
        <div className="bg-zinc-100 p-4 text-end border-r border-b">
          {label}:
        </div>
        {item?.type === "image" ||
        item?.type === "file" ||
        item?.type === "video" ||
        item?.type === "text" ? (
          <div className="col-span-5 p-4 border-r border-b">
            {item?.type === "image" ? (
              <img src={value} alt={label} className="w-32 h-32" />
            ) : item?.type === "file" ? (
              <a href={value} target="_blank" rel="noreferrer">
                {" "}
                {label}{" "}
              </a>
            ) : item?.type === "video" ? (
              <VideoModal data={record} />
            ) : (
              value
            )}
          </div>
        ) : (
          <div className="col-span-2 p-4 border-r border-b">
            {item?.type === "dic"
              ? value?.dicValue
              : item?.type === "date"
                ? formatDateDay(value)
                : item?.type === "datetime"
                  ? formatDate(value)
                  : item?.type === "enum"
                    ? find(propEq(value, "id"))(item.enumMap)?.name
                    : item?.type === "entity"
                      ? item?.key
                        ? value?.[item.key]
                        : value?.name
                      : value}
          </div>
        )}
      </>
    );
  };

  const renderItem = (list) => {
    if (!list) {
      return null;
    }
    return (
      <>{list.map((item, index, record) => renderAtom(item, index, record))}</>
    );
  };

  // 生成表格表头
  const generateColumns = (columns) => {
    if (!columns) {
      return [];
    }
    const tmp = [];
    const renderCol = (text, record, col) => {
      let value = text;
      if (col.type === "entity") {
        value = text?.name ?? "-";
      } else if (col.type === "dic") {
        value = text?.dicValue ?? "-";
      } else if (col.type === "date") {
        value = formatDateDay(text);
      } else if (col.type === "datetime") {
        value = formatDate(text);
      } else if (col.type === "imagelist") {
        if (col.render === "old") {
          value = JSON.parse(text ?? "[]");
        }
      } else if (col.type === "image") {
      } else if (col.type === "filelist") {
      } else if (col.type === "file") {
      } else if (col.type === "text") {
      } else if (col.type === "enum") {
        // value = col.enumMap(col.name)
        const i = find(propEq(text, "id"))(col.enumMap);
        // value = i?.name;
        value = i?.color ? <Tag color={i?.color}>{i?.name}</Tag> : i?.name;
      } else if (col.type === "array") {
        if (col.render === "entity") {
          console.debug(record?.[col.name]);
          value = record?.[col.name]?.map((c) => c.name).join("、");
        }
      }

      return (
        <>
          {col.type === "button" ? (
            col.buttons.map((button, index) => {
              if (!button(record)) return null;
              return (
                <Button onClick={() => button(record)?.func(record)}>
                  {button(record)?.chnName}
                </Button>
              );
            })
          ) : col.type === "image" || col.type === "imagelist" ? (
            renderImgs(type(value) === "Array" ? value : [value])
          ) : col.type === "file" || col.type === "filelist" ? (
            renderFiles(type(value) === "Array" ? value : [value])
          ) : col.type === "video" ? (
            <VideoModal data={record} />
          ) : col.render == "rate" ? (
            formatNumToRate(value)
          ) : col.render === "expire" ? (
            <p>
              {dayjs(value).diff(dayjs()) < millisecondsOfOnemonth ? (
                <Tag color="red">{formatDateDay(value)}</Tag>
              ) : dayjs(value).diff(dayjs()) < 3 * millisecondsOfOnemonth ? (
                <Tag color="yellow">{formatDateDay(value)}</Tag>
              ) : (
                <Tag color="white">{formatDateDay(value)}</Tag>
              )}
            </p>
          ) : (
            <Tooltip content={value}>
              <p>{value}</p>
            </Tooltip>
          )}
        </>
      );
    };
    columns.forEach((col) => {
      tmp.push({
        title: col.label,
        dataIndex: col.name,
        render: (text, record, index) => renderCol(text, record, col),
      });
    });
    return tmp;
  };

  const renderTable = (item) => {
    if (!item) {
      return null;
    }
    const columns = generateColumns(item?.columns);

    return (
      <Table
        className="rounded overflow-hidden"
        rowKey="id"
        // scroll={{ x:1200 }}
        columns={columns}
        dataSource={infoData}
        onHeaderRow={(columns, index) => {
          return {
            className: "text-gray-900 text-opacity-90 bg-gray-50",
          };
        }}
        headerStyle={{ color: "blue" }}
        pagination={false}
      />
    );
  };

  return (
    <div className="w-full overflow-y-auto flex flex-col gap-y-4">
      {scheme?.map?.((o, i) => (
        <div className="w-full pt-4" key={i}>
          {o.groupName ? (
            <Typography.Title heading={5}>{o.groupName}</Typography.Title>
          ) : null}
          {/* <div className={`grid grid-cols-${gridCol} mt-4`}> */}
          {/* <div className="flex flex-col gap-2 pt-4"> */}
          <div
            className={`grid grid-cols-${gridCol} rounded-lg overflow-hidden border border-b-0`}
            style={{
              gridTemplateColumns: `repeat(${gridCol}, minmax(0, 1fr))`,
            }}
          >
            {/* {gridCol}gridColgridColgridCol */}
            {renderItem(o?.list)}
          </div>
          <div className={`mt-4`}>{renderTable(o?.table)}</div>
        </div>
      ))}
    </div>
  );
};
