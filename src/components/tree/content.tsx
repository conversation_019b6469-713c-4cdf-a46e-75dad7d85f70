import { IconPlus, IconRefresh } from "@douyinfe/semi-icons";
import {
  Button,
  ButtonGroup,
  Popconfirm,
  Table,
  Toast,
  Tooltip,
} from "@douyinfe/semi-ui";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { TableConfig } from "components/index";
import { useBtnHooks } from "hooks";
import { useAtom } from "jotai";
import { FC, useCallback, useMemo, useState } from "react";
import { useLoaderData, useLocation } from "react-router-dom";
import { CommonApis, CommonAtoms } from "types";

type TreeContentProps = {
  // entityName: string; // 定义一个变量，是entity的显示中文名称
  atoms: CommonAtoms;
  dataAtom: any;
  selectAtom: any;
  apis: CommonApis; // api集合: { get: Function, create: Function, update: Function }
};

export const TreeContent: FC<TreeContentProps> = ({
  atoms,
  dataAtom,
  selectAtom,
  apis,
}) => {
  const loderData = useLoaderData();
  const { pathname } = useLocation();
  const genBtn = useBtnHooks(loderData, pathname);
  const [rows, setRows] = useState<number[]>([]);
  const rowSelection = useMemo(
    () => ({
      fixed: true,
      onChange: (selectedRowKeys) => {
        setRows(selectedRowKeys);
      },
    }),
    [setRows]
  );

  const [edit, setEdit] = useAtom(atoms.editModal);
  // const [configModal, setShow] = useAtom(showConfigModalAtom); // TODO
  const [_columns, setColumns] = useAtom(atoms.columns);

  const [allData, setAllData] = useAtom(dataAtom);
  const [selectedData, setSelectedData] = useAtom(selectAtom);
  const [fn, setFn] = useAtom(atoms.Fn);

  const queryClient = useQueryClient();
  const queryKey = "list" + atoms.entity;

  const mutation = useMutation({
    mutationFn: apis.remove,
    onSuccess: async (res) => {
      if (res?.code !== 0) {
        return;
      }
      let opts = {
        content: `操作成功!`,
        duration: 2,
      };
      Toast.success(opts);
      queryClient.invalidateQueries({ queryKey: [queryKey] });
      fn?.refetch?.();
    },
  });

  const removes = useMutation({
    mutationFn: apis.removes,
    onSuccess: async (res) => {
      if (res?.code !== 0) {
        return;
      }
      let opts = {
        content: `操作成功!`,
        duration: 2,
      };
      Toast.success(opts);
      queryClient.invalidateQueries({ queryKey: [queryKey] });
      fn?.refetch?.();
    },
  });

  const dataSource = useMemo(() => {
    if (selectedData === null) {
      return allData;
    }
    return allData.filter((item) => item.parentId === selectedData);
  }, [allData, selectedData]);

  const handleConfirm = useCallback(
    (record) => {
      mutation.mutate(record.id);
    },
    [mutation]
  );

  /* const handleOpenSetting = useCallback(() => {
    setShow(true);
  }, [setShow]); */

  const handleOpenEdit = useCallback(
    (id?: string) => {
      setEdit({
        id: id ?? "",
        show: true,
      });
    },
    [setEdit]
  );

  const handleRemoves = useCallback(() => {
    removes.mutate(rows);
    setRows([]);
  }, [removes, rows, setRows]);

  const columns = useMemo(() => {
    return [
      ..._columns,
      {
        title: <Tooltip content="操作">操作</Tooltip>,
        isShow: true,
        dataIndex: "operate",
        key: "operate",
        align: "center",
        width: 150,
        render: (text, record) => (
          <div>
            <ButtonGroup aria-label="操作按钮组">
              {genBtn(
                "edit",
                <Button
                  onClick={() => {
                    handleOpenEdit(record.id);
                  }}
                >
                  编辑
                </Button>
              )}
              {genBtn(
                "remove",
                <Popconfirm
                  position="bottomRight"
                  title="确定是否要删除该项？"
                  content="此修改将不可逆"
                  okType="danger"
                  okButtonProps={{
                    className:
                      "semi-button semi-button-danger semi-button-light",
                  }}
                  onConfirm={() => {
                    handleConfirm(record);
                  }}
                >
                  <Button type="danger">删除</Button>
                </Popconfirm>
              )}
            </ButtonGroup>
          </div>
        ),
      },
    ];
  }, [_columns, handleOpenEdit, handleConfirm]);

  return (
    <>
      <TableConfig
        // visible={configModal}
        columns={columns}
        // handleClose={setShow}
        handleSave={setColumns}
      />
      <div className="bg-white shadow px-4 h-fit rounded w-fit grow-10">
        <div className="flex py-4 justify-between">
          <div className="flex gap-4">
            {genBtn(
              "create",
              <button
                className="btn rounded btn-primary btn-sm"
                onClick={() => {
                  handleOpenEdit();
                }}
              >
                新增
                <IconPlus size="small" />
              </button>
            )}
            {genBtn(
              "removes",
              <>
                {rows?.length ? (
                  <>
                    <span className="flex text-gray-900 text-opacity-60 text-sm justify-center items-center">
                      已选中{rows?.length ?? 0}个
                    </span>
                    <Popconfirm
                      title="确定是否要删除该项？"
                      content="此修改将不可逆"
                      okType="danger"
                      okButtonProps={{
                        className:
                          "semi-button semi-button-danger semi-button-light",
                      }}
                      onConfirm={handleRemoves}
                    >
                      <button className="btn btn-sm rounded">批量删除</button>
                    </Popconfirm>
                  </>
                ) : null}
              </>
            )}
          </div>

          <div className="flex gap">
            <div className="tooltip" data-tip="刷新">
              <button
                className="btn btn-sm btn-ghost rounded no-animation"
                onClick={() => {
                  refetch();
                }}
              >
                <IconRefresh />
              </button>
            </div>
          </div>
        </div>
        <Table
          className="rounded overflow-hidden"
          childrenRecordName="no"
          rowKey="id"
          columns={columns?.filter?.((o) => o.fixed || o.isShow)}
          dataSource={dataSource}
          pagination={false}
          rowSelection={rowSelection}
          onHeaderRow={(columns, index) => {
            return {
              className: "text-gray-900 text-opacity-90 bg-gray-50",
            };
          }}
          headerStyle={{ color: "blue" }}
        />
      </div>
    </>
  );
};
