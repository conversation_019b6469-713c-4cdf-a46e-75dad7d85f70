import { Tree } from "@douyinfe/semi-ui";
import { useQuery } from "@tanstack/react-query";
import { platformConfigAtom } from "atoms";
import { useAtom } from "jotai";
import { FC, useCallback, useEffect, useMemo, useState } from "react";
import { convert } from ".";

type TreeLeftProps = {
  entity: string; // 定义一个变量，是entity的英文名称
  entityName: string; // 定义一个变量，是entity的显示中文名称
  dataAtom: any;
  selectAtom: any;
  fnAtom: any;
  getListApi: any; // 定义一个变量，是获取列表的api
};

export const TreeLeft: FC<TreeLeftProps> = ({
  entity,
  entityName,
  dataAtom,
  selectAtom,
  fnAtom,
  getListApi,
}) => {
  const queryKey = `list${entity}`;
  const [allData, setAllData] = useAtom(dataAtom);
  const [selectedData, setSelectedData] = useAtom(selectAtom);
  const [fn, setFn] = useAtom(fnAtom);
  const [platformConfig, setPlatformConfig] = useAtom(platformConfigAtom);

  const [expandedKeys, setExpandedKeys] = useState(["0"]);
  const { isLoading, isError, error, data, refetch } = useQuery({
    queryKey: [queryKey],
    queryFn: getListApi,
  });

  useEffect(() => {
    setFn({
      refetch: refetch,
    });
  }, [refetch]);

  useEffect(() => {
    if (data?.data?.length) {
      setAllData(data?.data);
    }
  }, [data]);

  const treeData = useMemo(() => {
    if (data?.data?.length) {
      return [
        {
          label: "全部",
          value: null,
          key: "all",
        },
        {
          label: entityName,
          value: 0,
          key: "0",
          children: convert(data),
        },
      ];
    }
    return [];
  }, [data]);

  useEffect(() => {
    const keys = [];
    function traverse(items) {
      (items ?? []).forEach((item) => {
        keys.push(item.key);
        if (item.children) {
          traverse(item.children);
        }
      });
    }
    traverse(treeData);
    setExpandedKeys(keys);
  }, [treeData]);

  const handleSelect = useCallback((selectedKey, s, selectedNode) => {
    setSelectedData(selectedNode.value);
  }, []);

  return (
    <div className="w-fit bg-white shadow px h-fit max-h-full rounded grow-0">
      <Tree
        treeData={treeData}
        filterTreeNode
        expandAll
        expandedKeys={expandedKeys}
        value={selectedData}
        onSelect={handleSelect}
        onExpand={(expandedKeys) => {
          setExpandedKeys(expandedKeys);
        }}
        onSearch={(inputValue, filteredExpandedKeys) => {
          setExpandedKeys([...filteredExpandedKeys]);
        }}
      />
    </div>
  );
};
