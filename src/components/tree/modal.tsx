import { Form, Modal, Toast, useFormApi } from "@douyinfe/semi-ui";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { Draft, DraftTrigger, destroyDraft } from "components/Draft";
import { useAtom, useAtomValue } from "jotai";
import { find, omit, propEq } from "ramda";
import { FC, useCallback, useEffect, useMemo, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { filterEditData } from "utils";

type TreeModalProps = {
  entity: string; // 定义一个变量，是entity的英文名称
  entityName: string; // 定义一个变量，是entity的显示中文名称
  treeRootName: string; // 定义一个变量，是树的根节点名称
  editModalAtom: any;
  fnAtom: any;
  dataAtom: any;
  selectAtom: any;
  apis: any; // api集合: { get: Function, create: Function, update: Function }
};

export const TreeModal: FC<TreeModalProps> = ({
  entity,
  entityName,
  treeRootName,
  editModalAtom,
  fnAtom,
  dataAtom,
  selectAtom,
  apis,
}) => {
  const operation = "Edit";
  const newTitle = "新增" + entityName; //user-defined code here
  const editTitle = "编辑" + entityName; //user-defined code here
  const requiredRule = { required: true, message: "此为必填项" };
  const gutter = 24;

  const uniqueKey = `${entity}${operation}`;

  const [editModal, setEditModal] = useAtom(editModalAtom);

  const title = editModal?.id ? editTitle : newTitle;
  const rules = [requiredRule];

  const queryClient = useQueryClient();
  const queryKey = "list" + entity;

  const getFormApiRef = useRef<any>(null);
  const navigate = useNavigate();

  const [allData, setAllData] = useAtom(dataAtom);
  const [fn, setFn] = useAtom(fnAtom);
  const selectedData = useAtomValue(selectAtom);

  const handleSetFormApi = useCallback(
    (formApi) => {
      getFormApiRef.current = formApi;
    },
    [getFormApiRef]
  );

  const ComponentUsingFormApi = () => {
    const formApi = useFormApi();
    const [edit, setEdit] = useAtom(editModalAtom);
    const [allData, setAllData] = useAtom(dataAtom);
    const selectedData = useAtomValue(selectAtom);

    useEffect(() => {
      console.log(edit?.id, "Edit?.id", formApi);

      if (formApi) {
        const item = find(propEq(edit?.id, "id"))(allData);
        console.log(selectedData, "selectedData", item);
        formApi.setValue("name", item?.name || "");
        formApi.setValue("parentId", item?.parentId || selectedData);
        // formApi.setValues({
        //   name: item.name,
        //   parentId: item.parentId
        // }, { isOverride: true })
      }
    }, [edit?.id, formApi]);

    return null;
  };

  const { isLoading, data } = useQuery({
    queryKey: [`${entity}-${editModal?.id ?? ""}`],
    queryFn: () => {
      if (editModal?.id) {
        return apis.get(editModal?.id);
      }
    },
    enabled: !!editModal?.id,
  });
  // 自动填充表单
  useEffect(() => {
    if (editModal?.id && getFormApiRef.current) {
      const items = omit([], data?.data);
      const item = find(propEq(editModal?.id, "id"))(allData);
      getFormApiRef.current.setValues(
        {
          ...filterEditData(items),
          //user-defined code here
          name: item?.name || "",
          parentId: item?.parentId || selectedData,
        },
        { isOverride: true }
      );
    } else {
      getFormApiRef?.current?.reset?.();
      getFormApiRef.current?.setValues?.({}, { isOverride: true });
    }
  }, [editModal?.id, data, getFormApiRef]);

  const mutation = useMutation({
    mutationFn: editModal?.id ? apis.update : apis.create,
    onSuccess: async (res) => {
      if (res?.code === 0) {
        let opts = {
          content: `操作成功!`,
          duration: 2,
        };
        Toast.success(opts);
        queryClient.invalidateQueries({ queryKey: [queryKey] });
        fn?.refetch?.();
        // getFormApiRef.current?.reset?.();
        getFormApiRef.current?.setValues?.({}, { isOverride: true });
        destroyDraft(uniqueKey);
        setEditModal({
          id: "",
          show: false,
        });
      }
    },
  });

  //user-defined code here: extra data
  const allDataFinalized = useMemo(() => {
    return allData ?? [];
  }, [allData]);

  const handleClose = useCallback(() => {
    if (mutation.isLoading) {
      return;
    }
    destroyDraft(`${uniqueKey}`);
    setEditModal({
      id: "",
      show: false,
    });
  }, [setEditModal, mutation]);

  const handleOk = () => {
    if (mutation.isLoading) {
      return;
    }
    getFormApiRef.current
      .validate()
      .then((values) => {
        let obj = {
          ...values,
          //user-defined code here
        };
        if (editModal?.id) {
          mutation.mutate({
            id: editModal?.id,
            values: obj,
          });
        } else {
          mutation.mutate(obj);
        }
      })
      .catch((errors) => {
        console.log(errors);
        const [formFieldId] = Object.keys(errors || {});
        if (formFieldId) {
          document.getElementById(formFieldId)?.scrollIntoViewIfNeeded?.();
        }
      });
  };

  return (
    <>
      <DraftTrigger id={uniqueKey} draftAtom={editModalAtom} />
      <Modal
        title={title}
        visible={editModal?.show ?? false}
        onCancel={handleClose}
        maskClosable={false}
        keepDOM
        width={400}
        footer={
          <div className="flex gap-2 justify-end">
            <button className="btn rounded btn-sm" onClick={handleClose}>
              取消
            </button>
            {mutation.isLoading ? (
              <button className="btn rounded btn-primary btn-sm btn-disabled">
                <span className="loading loading-spinner loading-xs"></span>
                确定
              </button>
            ) : (
              <button
                className="btn rounded btn-primary btn-sm"
                onClick={handleOk}
              >
                确定
              </button>
            )}
          </div>
        }
        centered
      >
        <Form
          labelPosition="left"
          labelAlign="right"
          labelWidth={120}
          getFormApi={handleSetFormApi}
        >
          {/* add form items here */}
          {({ formState }) => (
            <>
              <Form.Input
                field="name"
                label={`${entityName}名称`}
                trigger="blur"
                rules={[{ required: true, message: "此为必填项!" }]}
              />
              <Form.Select
                field="parentId"
                label={`所属${entityName}`}
                initValue="0"
                className="w-full"
              >
                <Form.Select.Option value={0}>
                  {treeRootName}
                </Form.Select.Option>
                {(allDataFinalized ?? []).map((o) => (
                  <Form.Select.Option value={o?.id ?? 0} key={o?.id ?? 0}>
                    {o?.name ?? ""}
                  </Form.Select.Option>
                ))}
              </Form.Select>
              <ComponentUsingFormApi />
              {editModal?.id ? null : (
                <Draft id={uniqueKey} draftAtom={editModalAtom} />
              )}
            </>
          )}
        </Form>
      </Modal>
    </>
  );
};
