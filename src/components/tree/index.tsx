export * from "./content";
export * from "./left";
export * from "./modal";

// 转换函数
export function convert(data) {
  if (!data?.data || !Array.isArray(data.data)) {
    return [];
  }

  const result = [];
  const map = {};
  const hasIds = new Set();
  const MAX_DEPTH = 100;

  // 先建立 id 到 item 的映射
  const idToItem = data.data.reduce((acc, item) => {
    acc[item.id] = item;
    return acc;
  }, {});

  // 第一遍遍历：建立所有节点
  data.data.forEach((item) => {
    if (!item?.id || !item?.name) {
      console.warn("Invalid department item:", item);
      return;
    }

    const key = String(item.id);
    const treeItem = {
      label: item.name,
      value: item.id,
      key,
      children: [],
    };
    map[key] = treeItem;
    hasIds.add(item.id);
  });

  // 第二遍遍历：建立父子关系
  function buildRelations(depth = 0) {
    if (depth > MAX_DEPTH) {
      console.warn("Maximum depth exceeded in department tree");
      return;
    }

    data.data.forEach((item) => {
      if (!item?.id) return;

      const key = String(item.id);
      const parentId = item.parentId ?? item.parent_id; // 兼容不同的属性名

      // 如果有父节点，将当前节点添加到父节点的children中
      if (parentId !== undefined && parentId !== null && map[parentId]) {
        map[parentId].children.push(map[key]);
      } else if (!parentId) {
        // 如果没有父节点，则为顶级节点
        result.push(map[key]);
      }
    });
  }

  buildRelations();
  return result;
}
