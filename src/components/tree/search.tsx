import { Form } from "@douyinfe/semi-ui";
import { useQuery } from "@tanstack/react-query";
import { platformConfigAtom } from "atoms";
import { useRemoteSearch } from "hooks";
import { useAtom } from "jotai";
import { FC, useEffect, useMemo, useState } from "react";
import { CommonApis, CommonAtoms } from "types";
import { convert } from ".";

export type TreeSearchProps = {
  field: string;
  placeholder?: string;
  label?: string;
  isRequired?: boolean;
  disabled?: boolean;
  atoms: CommonAtoms;
  dataAtom: any;
  apis: CommonApis;
  treeRootName?: string;
};

export const TreeSearch: FC<TreeSearchProps> = ({
  field,
  placeholder,
  label,
  isRequired,
  disabled = false,
  atoms,
  dataAtom,
  apis,
  treeRootName = "全部",
}) => {
  const queryKey = `list${atoms.entity}`;
  const [query, onSearch] = useRemoteSearch();
  const [allData, setAllData] = useAtom(dataAtom);
  const [platformConfig, setPlatformConfig] = useAtom(platformConfigAtom);

  const [expandedKeys, setExpandedKeys] = useState(["0"]);
  const { isLoading, isError, error, data, refetch } = useQuery({
    queryKey: [queryKey],
    queryFn: apis.query,
  });

  useEffect(() => {
    if (data?.data?.length) {
      setAllData(data?.data);
    }
  }, [data]);

  const treeData = useMemo(() => {
    if (data?.data?.length) {
      return [
        {
          label: treeRootName,
          value: 0,
          key: "0",
          children: convert(data),
        },
      ];
    }
    return [];
  }, [data]);

  useEffect(() => {
    const keys = [];
    function traverse(items) {
      (items ?? []).forEach((item) => {
        keys.push(item.key);
        if (item.children) {
          traverse(item.children);
        }
      });
    }
    traverse(treeData);
    setExpandedKeys(keys);
  }, [treeData]);

  return (
    <Form.TreeSelect
      field={field}
      noLabel={!Boolean(label)}
      label={label ?? ""}
      isLoading={isLoading}
      treeData={treeData}
      filterTreeNode
      expandAll
      defaultExpandAll
      placeholder={placeholder}
      className="w-full"
      disabled={disabled}
      rules={[{ required: isRequired, message: "此为必填项!" }]}
    />
  );
};
