import { Table, Tag, Tooltip, Typography } from "@douyinfe/semi-ui";
import { SAFETY_ANALYSIS_JOBSTEP } from "components";
import dayjs from "dayjs";
import { find, propEq, type } from "ramda";
import { FC } from "react";

type schemeGroup = {
  groupName: string;
  list?: any;
  table?: any;
};

type RenderSideProps = {
  scheme: Array<schemeGroup> | object;
  gridCol?: number;
  data: any;
};

const EnumMap = {
  jobStep: SAFETY_ANALYSIS_JOBSTEP,
};

export const RenderSide: FC<RenderSideProps> = ({
  scheme,
  gridCol = 1,
  data,
}) => {
  const isGroup = type(scheme) === "Array";

  const renderAtom = (item, key, index) => {
    const label = item[key];
    let value = data?.[key];
    if (type(value) === "Object") {
      value = data[key]?.name || "-";
    }
    if (type(value) === "Array" && key === "jobProcesses") {
      return (
        <>
          {value?.map((v, k) => (
            <div key={k} className="grid grid-cols-3">
              <div className="flex items-center justify-end p-2 text-base">
                {v?.name || "-"}:
              </div>
              <div className="flex items-center justify-start p-2 font-bold text-base col-span-2">
                {v?.candidatePerson?.map((c, ckey) => (
                  <Tag key={ckey} size="large">
                    {c.name}
                  </Tag>
                ))}
              </div>
            </div>
          ))}
        </>
      );
    }
    if (type(value) === "Array") {
      return (
        <div key={index} className="grid grid-cols-3">
          <div className="flex items-center justify-end p-2 text-base">
            {label}:
          </div>
          <div className="flex items-center justify-start p-2 font-bold text-base col-span-2">
            {value?.map((c, ckey) => (
              <Tooltip content={c?.name} key={ckey}>
                <Tag key={ckey} size="large">
                  {c?.name ?? c?.label ?? "-"}
                </Tag>
              </Tooltip>
            ))}
          </div>
        </div>
      );
    }

    return (
      <div key={index} className="grid grid-cols-3">
        <div className="flex items-center justify-end p-2 text-base">
          {label}:
        </div>
        <div className="flex items-center justify-start p-2 font-bold text-base col-span-2">
          {dayjs(value).isValid() && value?.length >= 20
            ? dayjs(value).format("YYYY/MM/DD HH:mm")
            : value}
        </div>
      </div>
    );
  };

  const renderItem = (item) => {
    if (!item) {
      return null;
    }
    return <>{Object.keys(item).map((it, k) => renderAtom(item, it, k))}</>;
  };

  // 生成表格表头
  const generateColumns = (col) => {
    if (!col) {
      return [];
    }
    const tmp = [];
    const renderCol = (record, key) => {
      let value = record;
      if (type(record) === "Object") {
        value = record?.name ?? record?.dicValue ?? "-";
      }
      if (type(record) === "Number" && EnumMap?.[key]?.length) {
        value = find(propEq(record, "id"))(EnumMap?.[key])?.name;
      }

      return <p>{value}</p>;
    };
    Object.keys(col).forEach((key) => {
      tmp.push({
        title: col[key],
        dataIndex: key,
        render: (record) => renderCol(record, key),
      });
    });
    return tmp;
  };

  const renderTable = (item) => {
    if (!item) {
      return null;
    }
    const columns = generateColumns(item?.columns);
    return (
      <Table
        className="rounded overflow-hidden"
        rowKey="id"
        // scroll={{ x:1200 }}
        columns={columns}
        dataSource={data?.[item?.dataSource]}
        onHeaderRow={(columns, index) => {
          return {
            className: "text-gray-900 text-opacity-90 bg-gray-50",
          };
        }}
        headerStyle={{ color: "blue" }}
        pagination={false}
      />
    );
  };

  return isGroup ? (
    <div className="w-full overflow-y-auto flex flex-col gap-y-4">
      {scheme?.map?.((o, i) => (
        <div className="w-full pt-4" key={i}>
          <Typography.Title heading={5}>{o.groupName}</Typography.Title>
          <div className={`grid grid-cols-${gridCol} mt-4`}>
            {renderItem(o?.list)}
          </div>
          <div className={`mt-4`}>{renderTable(o?.table)}</div>
        </div>
      ))}
    </div>
  ) : null;
};
