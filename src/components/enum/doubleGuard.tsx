import { range } from "ramda";
import { FieldList } from "./common";

export const RISK_LEVEL_EDIT_MAP: FieldList = [
  {
    id: 1,
    name: "重大风险",
    color: "red",
    label: "红",
    levelImg: "riskLevel1.jpg",
    rangeValue: range(10, 17),
  },
  {
    id: 2,
    name: "较大风险",
    color: "orange",
    label: "橙",
    levelImg: "riskLevel2.jpg",
    rangeValue: range(7, 10),
  },
  {
    id: 3,
    name: "一般风险",
    color: "yellow",
    label: "黄",
    levelImg: "riskLevel3.jpg",
    rangeValue: range(4, 7),
  },
  {
    id: 4,
    name: "低风险",
    label: "蓝",
    color: "blue",
    levelImg: "riskLevel4.jpg",
    rangeValue: range(1, 4),
  },
];

export const RISK_LEVEL_MAP: FieldList = [
  ...RISK_LEVEL_EDIT_MAP,
  {
    id: 5,
    name: "未评估",
    color: "grey",
  },
];

export const RISK_LEVEL_COLOR_MAP: FieldList = [
  {
    id: 1,
    name: "重大风险",
    color: "#fb6260",
  },
  {
    id: 2,
    name: "较大风险",
    color: "#ff9900",
  },
  {
    id: 3,
    name: "一般风险",
    color: "#fadb14",
  },
  {
    id: 4,
    name: "低风险",
    color: "#66b1ff",
  },
  {
    id: 5,
    name: "未评估",
    color: "#e9e9e9",
  },
];

export const IS_MAJOR_HAZARD_MAP: FieldList = [
  {
    id: 2,
    name: "否",
    color: "green",
  },
  {
    id: 1,
    name: "是",
    color: "red",
  },
];

export const EVALUATION_TYPE_MAP: FieldList = [
  {
    id: 1,
    name: "LS分析法",
    color: "green",
  },
  {
    id: 2,
    name: "LEC分析法",
    color: "red",
  },
  {
    id: 3,
    name: "MES分析法",
    color: "red",
  },
];

export const CONTROL_LEVEL_MAP: FieldList = [
  {
    id: 1,
    name: "公司、部门、车间、班组、岗位",
    color: "red",
  },
  {
    id: 2,
    name: "部门、车间、班组、岗位",
    color: "orange",
  },
  {
    id: 3,
    name: "车间、班组、岗位",
    color: "yellow",
  },
  {
    id: 4,
    name: "班组、岗位",
    color: "blue",
  },
];

export const CONTROL_LEVEL_RENDER_MAP: FieldList = [
  ...CONTROL_LEVEL_MAP,
  {
    id: 5,
    name: "未定义",
    color: "grey",
  },
];

export const CLASSIFY1_MAP: FieldList = [
  {
    id: 1,
    name: "工程技术",
  },
  {
    id: 2,
    name: "维护保养",
  },
  {
    id: 3,
    name: "操作行为",
  },
  {
    id: 4,
    name: "应急措施",
  },
];

export const CLASSIFY2_MAP: FieldList = [
  {
    pid: 1,
    id: 1,
    name: "工艺控制",
  },
  {
    pid: 1,
    id: 2,
    name: "关键设备/部件",
  },
  {
    pid: 1,
    id: 3,
    name: "安全附件",
  },
  {
    pid: 1,
    id: 4,
    name: "安全仪表",
  },
  {
    pid: 1,
    id: 5,
    name: "其他",
  },
  {
    pid: 2,
    id: 1,
    name: "动设备",
  },
  {
    pid: 2,
    id: 2,
    name: "静设备",
  },
  {
    pid: 2,
    id: 3,
    name: "其他",
  },
  {
    pid: 3,
    id: 1,
    name: "人员资质",
  },
  {
    pid: 3,
    id: 2,
    name: "操作设备",
  },
  {
    pid: 3,
    id: 3,
    name: "交接班",
  },
  {
    pid: 3,
    id: 4,
    name: "其他",
  },
  {
    pid: 4,
    id: 1,
    name: "应急设施",
  },
  {
    pid: 4,
    id: 2,
    name: "个体防护",
  },
  {
    pid: 4,
    id: 3,
    name: "消防设施",
  },
  {
    pid: 4,
    id: 4,
    name: "应急预案",
  },
  {
    pid: 4,
    id: 5,
    name: "其他",
  },
];

export const CONTROLTYPE_MAP: FieldList = [
  {
    id: 1,
    name: "自动化监控",
  },
  {
    id: 2,
    name: "隐患排查",
  },
];

export const UNIT_STATUS_MAP: FieldList = [
  {
    id: 1,
    name: "运行",
    color: "green",
  },
  {
    id: 2,
    name: "检修/停工",
    color: "red",
  },
];

export const BB_WORK_TYPE_MAP: FieldList = [
  {
    id: 1,
    name: "主要负责人任务",
  },
  {
    id: 2,
    name: "技术负责人任务",
  },
  {
    id: 3,
    name: "操作负责人任务",
  },
];

export const INCENTIVE_TYPE_MAP: FieldList = [
  {
    id: 1,
    name: "奖励",
    color: "green",
  },
  {
    id: 2,
    name: "惩罚",
    color: "red",
  },
];

export const SNAP_LEVEL_MAP: FieldList = [
  {
    id: 1,
    name: "一般隐患",
    color: "green",
  },
  {
    id: 2,
    name: "重大隐患",
    color: "red",
  },
];

export const SNAP_SOURCE_MAP: FieldList = [
  {
    id: 1,
    name: "日常排查",
  },
  {
    id: 2,
    name: "综合性排查",
  },
  {
    id: 3,
    name: "专业性排查",
  },
  {
    id: 4,
    name: "季节性排查",
  },
  {
    id: 5,
    name: "重点时段及节假日前排查",
  },
  {
    id: 6,
    name: "事故类比排查",
  },
  {
    id: 7,
    name: "复产复工前排查",
  },
  {
    id: 8,
    name: "外聘专家诊断式排查",
  },
  {
    id: 9,
    name: "管控措施失效",
  },
  {
    id: 10,
    name: "其他",
  },
];

export const SNAP_DANGER_TYPE_MAP: FieldList = [
  {
    id: 1,
    name: "安全",
  },
  {
    id: 2,
    name: "工艺",
  },
  {
    id: 3,
    name: "电气",
  },
  {
    id: 4,
    name: "仪表",
  },
  {
    id: 5,
    name: "消防",
  },
  {
    id: 6,
    name: "总图",
  },
  {
    id: 7,
    name: "设备",
  },
  {
    id: 8,
    name: "其他",
  },
];

export const SNAP_DANGER_CATEGORY_MAP: FieldList = [
  {
    id: 1,
    name: "其他隐患",
  },
  {
    id: 2,
    name: "主要负责人登记隐患",
  },
  {
    id: 3,
    name: "技术负责人登记隐患",
  },
  {
    id: 4,
    name: "操作负责人登记隐患",
  },
];

export const SNAP_STATUS_MAP: FieldList = [
  {
    id: 1,
    name: "待审核",
    color: "grey",
  },
  {
    id: 2,
    name: "正常",
    color: "green",
  },
  {
    id: 3,
    name: "隐患",
    color: "red",
  },
];

export const SNAP_SELF_EVALUATION_MAP: FieldList = [
  {
    id: 1,
    name: "自评",
  },
  {
    id: 2,
    name: "他评",
  },
];

export const TASK_STATUS_MAP: FieldList = [
  {
    id: 1,
    name: "待排查",
    color: "grey",
  },
  {
    id: 2,
    name: "无隐患",
    color: "green",
  },
  {
    id: 3,
    name: "有隐患",
    color: "red",
  },
];

export const TASK_CHECK_RESULT_MAP: FieldList = [
  {
    id: 1,
    name: "无隐患",
    color: "green",
  },
  {
    id: 2,
    name: "有隐患",
    color: "red",
  },
];

export const DANGER_MANAGE_TYPE_MAP: FieldList = [
  {
    id: 1,
    name: "即查即改",
  },
  {
    id: 2,
    name: "限期整改",
  },
];

export const DANGER_STATUS_MAP: FieldList = [
  {
    id: 1,
    name: "待评估",
    color: "yellow",
  },
  {
    id: 2,
    name: "无需整改",
    color: "blue",
  },
  {
    id: 3,
    name: "待整改",
    color: "grey",
  },
  {
    id: 4,
    name: "待验收",
    color: "grey",
  },
  {
    id: 5,
    name: "验收通过",
    color: "green",
  },
  {
    id: 6,
    name: "验收不通过",
    color: "red",
  },
  {
    id: 7,
    name: "申请延期",
    color: "red",
  },
];

export const DANGER_REPORT_STATUS_MAP: FieldList = [
  {
    id: 1,
    name: "不上报",
    color: "red",
  },
  {
    id: 2,
    name: "待上报",
    color: "blue",
  },
  {
    id: 3,
    name: "上报待确认",
    color: "orange",
  },
  {
    id: 4,
    name: "上报成功",
    color: "green",
  },
  {
    id: 5,
    name: "上报失败",
    color: "red",
  },
];

export const DANGER_EVALUATE_RESULT_MAP: FieldList = [
  {
    id: 1,
    name: "正常",
    color: "green",
  },
  {
    id: 2,
    name: "整改",
    color: "red",
  },
];

export const DANGER_ACCEPT_RESULT_MAP: FieldList = [
  {
    id: 1,
    name: "通过",
    color: "green",
  },
  {
    id: 2,
    name: "不通过",
    color: "red",
  },
];

// deprecated 废弃，留着兼容旧数据。
// 使用 import.meta.env.VITE_BASE/static/signs/index.json
export const SAFETY_SIGNS_MAP: FieldList = [
  {
    id: "01",
    name: "当心爆炸",
    ext: "png",
  },
  {
    id: "02",
    name: "当心表面高温",
    ext: "png",
  },
  {
    id: "03",
    name: "当心叉车",
    ext: "png",
  },
  {
    id: "04",
    name: "当心车辆",
    ext: "png",
  },
  {
    id: "05",
    name: "当心触电",
    ext: "png",
  },
  {
    id: "06",
    name: "当心低温",
    ext: "png",
  },
  {
    id: "07",
    name: "当心电缆",
    ext: "png",
  },
  {
    id: "08",
    name: "当心电离辐射",
    ext: "png",
  },
  {
    id: "09",
    name: "当心吊物",
    ext: "png",
  },
  {
    id: "10",
    name: "当心跌落",
    ext: "png",
  },
  {
    id: "11",
    name: "当心缝隙",
    ext: "png",
  },
  {
    id: "12",
    name: "当心腐蚀",
    ext: "png",
  },
  {
    id: "13",
    name: "当心滑倒",
    ext: "png",
  },
  {
    id: "14",
    name: "当心火灾",
    ext: "png",
  },
  {
    id: "15",
    name: "当心机械伤人",
    ext: "png",
  },
  {
    id: "16",
    name: "当心激光",
    ext: "png",
  },
  {
    id: "17",
    name: "当心挤压",
    ext: "png",
  },
  {
    id: "18",
    name: "当心夹手",
    ext: "png",
  },
  {
    id: "19",
    name: "当心坑洞",
    ext: "png",
  },
  {
    id: "20",
    name: "当心裂变物质",
    ext: "png",
  },
  {
    id: "21",
    name: "当心落水",
    ext: "png",
  },
  {
    id: "22",
    name: "当心落物",
    ext: "png",
  },
  {
    id: "23",
    name: "当心冒顶",
    ext: "png",
  },
  {
    id: "24",
    name: "当心碰头",
    ext: "png",
  },
  {
    id: "25",
    name: "当心伤手",
    ext: "png",
  },
  {
    id: "26",
    name: "当心塌方",
    ext: "png",
  },
  {
    id: "27",
    name: "当心烫伤",
    ext: "png",
  },
  {
    id: "28",
    name: "当心扎脚",
    ext: "png",
  },
  {
    id: "29",
    name: "当心障碍物",
    ext: "png",
  },
  {
    id: "30",
    name: "当心中毒",
    ext: "png",
  },
  {
    id: "31",
    name: "当心坠落",
    ext: "png",
  },
  {
    id: "32",
    name: "当心自动启动",
    ext: "png",
  },
  {
    id: "33",
    name: "注意安全",
    ext: "png",
  },
  {
    id: "34",
    name: "当心辐射",
    ext: "png",
  },
  {
    id: "35",
    name: "噪声有害",
    ext: "png",
  },
  {
    id: "36",
    name: "注意防尘",
    ext: "png",
  },
  {
    id: "37",
    name: "注意弧光",
    ext: "png",
  },
  {
    id: "38",
    name: "必须穿戴绝缘保护用品",
    ext: "png",
  },
  {
    id: "39",
    name: "必须穿救生衣",
    ext: "png",
  },
  {
    id: "40",
    name: "必须带防护眼镜",
    ext: "png",
  },
  {
    id: "41",
    name: "必须带自救器",
    ext: "png",
  },
  {
    id: "42",
    name: "必须戴安全帽",
    ext: "png",
  },
  {
    id: "43",
    name: "必须戴防尘口罩",
    ext: "png",
  },
  {
    id: "44",
    name: "必须戴防毒口罩",
    ext: "png",
  },
  {
    id: "45",
    name: "必须戴防护帽",
    ext: "png",
  },
  {
    id: "46",
    name: "必须戴防护手套",
    ext: "png",
  },
  {
    id: "47",
    name: "必须戴护耳器",
    ext: "png",
  },
  {
    id: "48",
    name: "必须加锁",
    ext: "png",
  },
  {
    id: "49",
    name: "必须桥上通过",
    ext: "png",
  },
  {
    id: "50",
    name: "必须系安全带",
    ext: "png",
  },
  {
    id: "51",
    name: "必须携带矿灯",
    ext: "png",
  },
  {
    id: "52",
    name: "注意通风",
    ext: "png",
  },
  {
    id: "53",
    name: "走人行道",
    ext: "png",
  },
];

export const RISK_MEASURE_ACCIDENTTYPE: FieldList = [
  {
    id: 1,
    name: "物体打击",
  },
  {
    id: 2,
    name: "车辆伤害",
  },
  {
    id: 3,
    name: "机械伤害",
  },
  {
    id: 4,
    name: "起重伤害",
  },
  {
    id: 5,
    name: "触电",
  },
  {
    id: 6,
    name: "淹溺",
  },
  {
    id: 7,
    name: "灼烫",
  },
  {
    id: 8,
    name: "火灾",
  },
  {
    id: 9,
    name: "高处坠落",
  },
  {
    id: 10,
    name: "坍塌",
  },
  {
    id: 11,
    name: "冒顶片帮",
  },
  {
    id: 12,
    name: "透水",
  },
  {
    id: 13,
    name: "放炮",
  },
  {
    id: 14,
    name: "火药爆炸",
  },
  {
    id: 15,
    name: "瓦斯爆炸",
  },
  {
    id: 16,
    name: "锅炉爆炸",
  },
  {
    id: 17,
    name: "容器爆炸",
  },
  {
    id: 18,
    name: "其他爆炸",
  },
  {
    id: 19,
    name: "中毒和窒息",
  },
  {
    id: 20,
    name: "其他伤害",
  },
];

export const DANGER_CHECK_TYPE: FieldList = [
  {
    id: 1,
    name: "企业自查",
  },
  {
    id: 2,
    name: "市级及以下检查",
  },
  {
    id: 3,
    name: "省级检查",
  },
  {
    id: 4,
    name: "部级检查",
  },
];

export const RIKSMANAGEMENT_STATUS_MAP = [
  {
    id: 1,
    name: "待审批",
    color: "grey",
  },
  {
    id: 2,
    name: "通过",
    color: "green",
  },
  {
    id: 3,
    name: "不通过",
    color: "red",
  },
];

export const RISKMANAGEMENT_METHOD_MAP = [
  {
    id: 1,
    name: "JHA",
  },
  {
    id: 2,
    name: "SCL",
  },
  {
    id: 3,
    name: "HAZOP",
  },
  {
    id: 4,
    name: "其他",
  },
];

export const RISKMANAGEMENT_SAFETYIMPLEMENTATIONSTATE_MAP = [
  {
    id: 1,
    name: "是",
  },
  {
    id: 2,
    name: "否",
  },
  {
    id: 3,
    name: "未涉及",
  },
];

export const DANGER_SOURCE_MAP: FieldList = [
  {
    id: 0,
    name: "-",
  },
  {
    id: 1,
    name: "随手拍",
  },
  {
    id: 2,
    name: "隐患排查",
  },
  {
    id: 3,
    name: "包保责任",
  },
  {
    id: 4,
    name: "巡检任务",
  },
  {
    id: 5,
    name: "非计划巡检",
  },
  {
    id: 6,
    name: "自增隐患",
  },
  {
    id: 7,
    name: "政府专项任务",
  },
  {
    id: 8,
    name: "企业专项检查",
  },
];

export const DG_GC_STATUS_MAP: FieldList = [
  {
    id: 1,
    name: "未下发",
    color: "grey",
  },
  {
    id: 2,
    name: "已下发",
    color: "green",
  },
];

export const ALLOCATION_HASCHECKPLAN: FieldList = [
  {
    id: 1,
    name: "有",
  },
  {
    id: 2,
    name: "没有",
  },
];

export const ENTITY_TYPE_MAP: FieldList = [
  {
    id: 1,
    name: "排查任务",
  },
  {
    id: 2,
    name: "随手拍任务",
  },
  {
    id: 3,
    name: "隐患任务",
  },
];

export const GC_CHECK_ITEM_RESULT: FieldList = [
  {
    id: 1,
    name: "正常",
    color: "green",
  },
  {
    id: 2,
    name: "异常",
    color: "red",
  },
];

export const GC_CHECK_STATUS = [
  {
    id: 1,
    name: "待分配",
    color: "grey",
  },
  {
    id: 2,
    name: "待检查",
    color: "blue",
  },
  {
    id: 3,
    name: "已检查",
    color: "green",
  },
];

export const GC_CHECK_ITEM_HARM_TYPE = [
  {
    id: 1,
    name: "事故隐患",
    color: "orange",
  },
  {
    id: 2,
    name: "火灾隐患",
    color: "red",
  },
];
