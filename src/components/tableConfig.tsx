import { Checkbox, CheckboxGroup, Modal } from "@douyinfe/semi-ui";
import type { FC } from "react";
import { useCallback, useEffect, useMemo, useState } from "react";

// 定义列配置的类型
export type ColumnConfig = {
  title: string; // 列标题
  dataIndex: string; // 列数据字段
  isShow: boolean; // 是否显示
  fixed?: boolean; // 是否固定
  width?: number; // 列宽
  ellipsis?: boolean; // 是否省略
  render?: (text: any, record: any, index: number) => React.ReactNode; // 渲染函数
  [key: string]: any; // 其他属性
};

export type TableConfigProps = {
  visible: boolean;
  handleSave: (columns: ColumnConfig[]) => void;
  handleClose: () => void;
  columns?: ColumnConfig[];
};

export const TableConfig: FC<TableConfigProps> = ({
  visible,
  handleSave,
  handleClose,
  columns = [],
}) => {
  const [values, setValues] = useState<string[]>([]);

  // 初始化时设置当前显示的列
  useEffect(() => {
    if (!columns?.length) return;

    const tmp: string[] = [];
    columns.forEach((col) => {
      if (!col.fixed && col.isShow) {
        tmp.push(col.dataIndex);
      }
    });

    setValues(tmp);
  }, [columns]);

  const options = useMemo(() => {
    const tmp: Array<{
      label: string;
      value: string;
      [key: string]: any;
    }> = [];

    columns?.forEach((col) => {
      if (!col.fixed) {
        tmp.push({
          ...col,
          label: col.title,
          value: col.dataIndex,
        });
      }
    });
    return tmp;
  }, [columns]);

  const handleChangeSave = useCallback(() => {
    const tmp: ColumnConfig[] = [];
    columns?.forEach((col) => {
      tmp.push({
        ...col,
        isShow: values.includes(col.dataIndex),
      });
    });

    // 调用父组件的保存函数
    handleSave(tmp);
    handleClose();
  }, [values, handleSave, handleClose, columns]);

  const checkedAll = useMemo(() => {
    return options.length > 0 && options.length === values.length;
  }, [options, values]);

  const handleChangeBox = (checkedValue: string[]) => {
    setValues(checkedValue);
  };

  const handleCheckAll = (e: any) => {
    const checked = e.target.checked;
    const tmp: string[] = [];

    if (checked) {
      const filterCol = columns?.filter?.((col) => !col.fixed);
      filterCol?.forEach((col) => {
        tmp.push(col.dataIndex);
      });
    }

    setValues(tmp);
  };

  return (
    <Modal
      title="自定义设置表格列"
      visible={visible}
      onCancel={() => {
        handleClose(false);
      }}
      closeOnEsc={true}
      width={600}
      footer={
        <div className="flex gap-2 justify-end">
          <button
            className="btn btn-sm rounded"
            onClick={() => handleClose(false)}
          >
            取消
          </button>
          <button
            className="btn rounded btn-primary btn-sm"
            onClick={handleChangeSave}
          >
            保存修改
          </button>
        </div>
      }
      centered
    >
      <div className="flex border border-gray-900 border-opacity-10 flex-col rounded">
        <div className="px-6 py-3 border-b border-gray-900 border-opacity-10 flex-col justify-start items-start gap-2 flex">
          <Checkbox
            checked={checkedAll}
            aria-label="全选/反选"
            onChange={handleCheckAll}
          >
            全选
          </Checkbox>
        </div>
        <div className="p-6">
          <CheckboxGroup
            options={options}
            direction="horizontal"
            aria-label="CheckboxGroup"
            value={values}
            onChange={handleChangeBox}
          />
        </div>
      </div>
    </Modal>
  );
};
