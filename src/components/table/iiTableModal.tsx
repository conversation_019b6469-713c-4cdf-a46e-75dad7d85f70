import { Modal, Table } from "@douyinfe/semi-ui";
import { useQuery } from "@tanstack/react-query";
import { atom, useAtom, useAtomValue } from "jotai";
import { union } from "ramda";
import { useCallback, useMemo, useState } from "react";

export const IIPickData = atom([]); // 选中的数据，与PickTable组件中的selectedRecord对应
export const IIPickDataKeys = atom([]); // 选中的数据的keys, 与PickTable组件中的keys对应

export const IITableModal = ({
  children,
  callback,
  visible,
  onClose,
  title,
  entity,
  entityTitle,
  listApi,
  columnsAtom,
  // keyColumn,
  filterAtom,
  initialSelectedKeys,
}) => {
  const [selectedRecord, setSelectedRecord] = useAtom(IIPickData);
  const [keys, setKeys] = useAtom(IIPickDataKeys);
  const columns = useAtomValue(columnsAtom);
  const [filter, setFilter] = useAtom(filterAtom);
  const [pageSize, setPageSize] = useState<number>(10);

  const { isLoading, data, refetch } = useQuery({
    queryKey: [entity, filter, pageSize],
    queryFn: () => {
      return listApi({
        ...filter,
        pageSize: pageSize,
      });
    },
  });

  const dataSource = useMemo(() => {
    return data?.data ?? {};
  }, [data]);

  const result = useMemo(() => dataSource?.results ?? [], [dataSource]);

  const handlePageChange = useCallback(
    (currentPage: number) => {
      setFilter({
        ...filter,
        pageNumber: currentPage,
      });
    },
    [filter, setFilter]
  );

  const handlePageSizeChange = (pageSize: number) => {
    setPageSize(pageSize);
  };

  const rowSelection = {
    onSelect: (record, selected) => {
      console.log(`select row: ${selected}`, record);
    },
    onSelectAll: (selected, selectedRows) => {
      console.log(`select all rows: ${selected}`, selectedRows);
    },
    onChange: (selectedRowKeys, selectedRows) => {
      console.log(
        selectedRowKeys,
        selectedRows,
        "selectedRowKeys, selectedRow"
      );
      setKeys(selectedRowKeys);
      /* selectedRows.forEach(o => {
        o[keyColumn ?? 'id'] = o.id // 复制原始数据的ID到关联字段keyColumn
      }) */
      setSelectedRecord((prev) => {
        const remainRows = prev.filter((o) => selectedRowKeys.includes(o.id));
        console.log("remainRows", remainRows);

        const diffSelectedRows = selectedRows.filter((o) => {
          return remainRows.findIndex((r) => r.id === o.id) === -1;
        });
        console.log("diffSelectedRows", diffSelectedRows);

        return union(remainRows, diffSelectedRows);
        // return [...remainRows, ...selectedRows];
      });
    },
    selectedRowKeys: keys.length ? keys : initialSelectedKeys,
  };

  const handleSave = () => {
    callback(selectedRecord);
  };

  return (
    <Modal
      title={entityTitle ?? title}
      visible={visible}
      onCancel={onClose}
      // onOk={handleSave}
      width={1200}
      keepDOM
      maskClosable={false}
      footer={
        <div className="flex gap-2 justify-end">
          <button
            className="btn rounded btn-sm"
            onClick={() => {
              onClose();
            }}
          >
            取消
          </button>
          <button
            className="btn rounded btn-primary btn-sm"
            onClick={handleSave}
          >
            确定
          </button>
        </div>
      }
      centered
    >
      {children}
      <Table
        // className="rounded overflow-hidden"
        rowKey="id"
        columns={columns?.filter?.((o) => o.fixed || o.isShow)}
        dataSource={result}
        loading={isLoading}
        rowSelection={rowSelection}
        pagination={{
          currentPage: filter?.pageNumber ?? 1,
          pageSize: pageSize ?? 10,
          total: dataSource?.totalCount ?? 0,
          popoverPosition: "topRight",
          pageSizeOpts: [10, 15, 20, 50],
          onChange: handlePageChange,
          onPageSizeChange: handlePageSizeChange,
          showSizeChanger: true,
          showQuickJumper: true,
          // showTotal: (total, range) => `共 ${total} 条`,
        }}
        resizable
        bordered
      />
    </Modal>
  );
};
