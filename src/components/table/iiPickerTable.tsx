import { IconArrowDown, IconArrowUp } from "@douyinfe/semi-icons";
import { Button, Form, Table, useFormApi } from "@douyinfe/semi-ui";
import { inspectionPlaceEditStandardModalAtom } from "atoms";
import { Atom, useAtom } from "jotai";
import InspectionPlaceEditStandardModal from "pages/intelligentInspection/modal/inspectionPlaceEditStandardModal";
import { clone, map, prop, swap } from "ramda";
import { useCallback, useEffect, useMemo, useState } from "react";
import { IIPickData, IIPickDataKeys, IITableModal } from "./iiTableModal";

type Props = {
  title: string;
  entity: string; // 用于queyrKey
  entityTitle: string;
  field: string; // formApi的field
  entityField?: string; // 上述field包装成entity的字段
  list: any[];
  listApi: any;
  columnsAtom: Atom<any[]>;
  modalColumnsAtom: Atom<any[]>;
  filterAtom: Atom<any>;
  keyColumn?: string;
  isSort?: boolean; // 是否允许更改顺序
};

const IIPickerTable = (props: Props) => {
  const text = "关联" + props.title;
  const formApi = useFormApi();
  const [editStandardModalAtom, setEditStandardModalAtom] = useAtom(
    inspectionPlaceEditStandardModalAtom
  );
  const [visible, setVisible] = useState(false);
  // const [selectedRecord, setSelectedRecord] = useState([])
  const [selectedRecord, setSelectedRecord] = useAtom(IIPickData);
  const [keys, setKeys] = useAtom(IIPickDataKeys);
  const [_columns, setColumns] = useAtom(props.columnsAtom);

  const rules = [{ required: true, message: "该项不可为空" }];

  useEffect(() => {
    const tmp = [];
    (props.list ?? []).forEach((o, index) => {
      tmp.push({
        ...o,
        newMinValue: o.minValue,
        newMaxValue: o.maxValue,
        sortColumn: index,
      });
    });
    console.log(tmp);

    setSelectedRecord(tmp);
  }, [props.list]);

  const handleChangeSort = useCallback(
    (sort: number, record: any) => {
      // 边界操作
      if (
        (record.sortColumn == 0 && sort == -1) ||
        (record.sortColumn == selectedRecord.length - 1 && sort == 1)
      ) {
        return;
      }
      let indexa, indexb;
      if (record.sortColumn + sort < record.sortColumn) {
        indexa = record.sortColumn + sort;
        indexb = record.sortColumn;
      } else {
        indexb = record.sortColumn + sort;
        indexa = record.sortColumn;
      }
      // const temp = swap(record.sortColumn, record.sortColumn + sort, selectedRecord)
      const temp = swap(indexa, indexb, selectedRecord).map((item, i) => {
        item.sortColumn = i;
        return item;
      });

      setSelectedRecord(temp);
    },
    [selectedRecord, setSelectedRecord]
  );

  const handleStandardEdit = (values) => {
    console.log("handleStandardEdit", values);
    //TODO: add standard
    const tmp = clone(selectedRecord);
    tmp.forEach((item) => {
      if (item[props.keyColumn ?? "id"] === editStandardModalAtom.standardId) {
        item.newMinValue = values.newMinValue;
        item.newMaxValue = values.newMaxValue;
      }
    });
    setSelectedRecord(tmp);

    setEditStandardModalAtom({
      standardId: "",
      name: "",
      newMinValue: NaN,
      newMaxValue: NaN,
      show: false,
    });
  };

  const columns = useMemo(() => {
    const operationColumn = {
      title: "操作",
      isShow: true,
      dataIndex: "delete",
      align: "center",
      render: (text: any, record: any) => (
        <>
          {/* <Button
            // className='text-primary cursor-pointer'
            type="tertiary"
            onClick={() => {
              console.log("edit", JSON.stringify(record));
              setEditStandardModalAtom({
                standardId: record.standardId, // 用于查询从而处理数据
                name: record.category, // 用于显示
                newMinValue: record.newMinValue,
                newMaxValue: record.newMaxValue,
                show: true,
              });
            }}
          >
            编辑
          </Button> */}
          <Button
            // className='text-primary cursor-pointer'
            type="danger"
            onClick={() => {
              console.log(record);

              setSelectedRecord((prveState) =>
                prveState.filter(
                  (o) =>
                    o[props.keyColumn ?? "id"] !==
                    record[props.keyColumn ?? "id"]
                )
              );
              setKeys((prveState) =>
                prveState.filter((o) => o !== record[props.keyColumn ?? "id"])
              );

              /* const newFieldValue = formApi.getValue(props.field)?.filter(o => o !== record.id)
              formApi.setValue(props.field, newFieldValue);
              if (props.entityField) {
                formApi.setValue(props.entityField, formApi.getValue(props.entityField)?.filter(o => o.id !== record.id));
              } */
            }}
          >
            删除
          </Button>
        </>
      ),
    };

    const sortColumnAction = {
      title: "顺序",
      isShow: true,
      dataIndex: "sortColumnAction",
      align: "center",
      render: (text: any, record: any) => (
        <div className="flex gap-2 justify-center">
          <Button
            type="primary"
            theme="borderless"
            disabled={record.sortColumn === 0}
            icon={<IconArrowUp />}
            onClick={() => {
              handleChangeSort(-1, record);
            }}
          />
          <Button
            type="primary"
            theme="borderless"
            disabled={record.sortColumn === selectedRecord.length - 1}
            icon={<IconArrowDown />}
            onClick={() => {
              handleChangeSort(1, record);
            }}
          />
        </div>
      ),
    };

    const sortColumn = {
      title: "顺序",
      isShow: true,
      dataIndex: "sortColumn",
      align: "center",
      render: (t) => <p>{t + 1}</p>,
    };

    let tmp = [];
    if (props?.isSort) {
      tmp = [sortColumn, ..._columns, sortColumnAction, operationColumn];
    } else {
      tmp = [..._columns, operationColumn];
    }
    return tmp;
  }, [_columns, props?.isSort, selectedRecord, setSelectedRecord]);

  const handleOpen = () => {
    const ids = [];
    selectedRecord.forEach((o: any) => {
      ids.push(o?.standardId);
    });
    setKeys(ids);
    setVisible(true);
  };

  const handleCb = (record: any[]) => {
    console.debug("handleCb", record);
    setVisible(false);
    // setSelectedRecord(record)
    const tmp = [];
    record.forEach((o) => {
      // console.log(o[props.keyColumn]);
      // o[props.keyColumn ?? 'id'] = o.id // 复制原始数据的ID到关联字段keyColumn
      // 复制已存在数据的ID过来作为真正的ID：如果已存在数据，就用已存在数据的ID，否则用null
      // const existingData = props.list?.filter((item) => item[props.keyColumn ?? 'id'] === o.id)
      // o.id = existingData?.[0]?.id ?? null
      // 不行，如果为null，违反了table的rowKey的非空约束，如果不为null，可能有重复
      // 结论：还是用原始数据ID作为rowKey吧，然后在formApi的field中存储真实ID: 新数据为null，老数据为list中的ID
      tmp.push(o.id);
    });

    const selectedRecordCopy = clone(selectedRecord);
    selectedRecordCopy.forEach((item) => {
      item[props.keyColumn ?? "id"] = item.id;
      if (!item.newMinValue) {
        item.newMinValue = item.minValue;
      }
      if (!item.newMaxValue) {
        item.newMaxValue = item.maxValue;
      }
      /* const existingStandard = props.list?.filter((standard) => standard[props.keyColumn ?? 'id'] === item[props.keyColumn ?? 'id'])
      item.id = existingStandard?.[0]?.id ?? null */
    });
    // console.log(selectedRecordCopy);
    setSelectedRecord(selectedRecordCopy);

    formApi.setValue(props.field, tmp);
    if (props.entityField) {
      formApi.setValue(props.entityField, selectedRecordCopy);
    }
  };

  return (
    <div className="flex flex-col gap-4 mt-8">
      <Form.Section text={text} className="relative">
        <div className="absolute right-0 top-[-10px]">
          <span className="btn rounded btn-primary btn-sm" onClick={handleOpen}>
            新增
          </span>
        </div>
        <IITableModal
          callback={handleCb}
          onClose={() => {
            setVisible(false);
          }}
          visible={visible}
          title={props.title}
          entity={props.entity}
          entityTitle={props.entityTitle}
          listApi={props.listApi}
          columnsAtom={props.modalColumnsAtom ?? props.columnsAtom}
          keyColumn={props.keyColumn}
          filterAtom={props.filterAtom}
          initialSelectedKeys={map(
            prop(props.keyColumn ?? "id"),
            selectedRecord
          )}
        >
          {/* <div className='mb-8'>
            <EmployeeFilter layout="modal" />
          </div> */}
        </IITableModal>

        <InspectionPlaceEditStandardModal callback={handleStandardEdit} />
        <Table
          columns={columns}
          dataSource={selectedRecord}
          rowKey={props.keyColumn ?? "id"}
          // rowKey='id'
          pagination={false}
          resizable
          bordered
        />
      </Form.Section>
    </div>
  );
};

export default IIPickerTable;
