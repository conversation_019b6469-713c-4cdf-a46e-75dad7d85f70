import { Modal, Table } from "@douyinfe/semi-ui";
import { useQuery } from "@tanstack/react-query";
import { atom, useAtom, useAtomValue } from "jotai";
import { union } from "ramda";
import { FC, useCallback, useMemo } from "react";

export const secondPickData = atom([]); // 选中的数据，与PickTable组件中的selectedRecord对应
export const secondPickDataKeys = atom([]); // 选中的数据的keys, 与PickTable组件中的keys对应

type SecondTableModalProps = {
  children?: React.ReactNode;
  callback: (selectedRecord: any[]) => void;
  visible: boolean;
  onClose: () => void;
  title: string;
  entity: string;
  entityTitle?: string;
  listApi: any;
  columnsAtom: any;
  filterAtom: any;
  initialSelectedKeys: any;
  checkDisabledFunc?: (record: any) => boolean;
};

export const SecondTableModal: FC<SecondTableModalProps> = ({
  children,
  callback,
  visible,
  onClose,
  title,
  entity,
  entityTitle,
  listApi,
  columnsAtom,
  filterAtom,
  initialSelectedKeys,
  checkDisabledFunc,
}) => {
  // const params = useParams<{ id: string, cid: string }>();
  // const [selectedRecord, setSelectedRecord] = useState([])
  const [selectedRecord, setSelectedRecord] = useAtom(secondPickData);
  // const [keys, setKeys] = useState([])
  const [keys, setKeys] = useAtom(secondPickDataKeys);
  const columns = useAtomValue(columnsAtom);
  const [filter, setFilter] = useAtom(filterAtom);

  //???
  // const sensorReferValue = useAtomValue(sensorReferValues)

  const { isLoading, data, refetch } = useQuery({
    //props
    queryKey: [entity, filter],
    queryFn: () => {
      return listApi({
        // props
        ...filter,
        filter: {
          ...filter,
        },
      });
    },
  });

  const dataSource = useMemo(() => {
    return data?.data ?? {};
  }, [data]);

  const result = useMemo(() => dataSource?.results ?? [], [dataSource]);

  const handlePageChange = useCallback(
    (currentPage: number) => {
      setFilter({
        ...filter,
        pageNumber: currentPage,
      });
    },
    [filter, setFilter]
  );

  const handlePageSizeChange = useCallback(
    (pageSize: number) => {
      setFilter({
        ...filter,
        pageSize: pageSize,
      });
    },
    [filter, setFilter]
  );

  /* const rowSelection = {
    onSelect: (record, selected) => {
      if (selected) {
        setSelectedRecord([...selectedRecord, record])
      } else {
        setSelectedRecord(selectedRecord.filter(o => o.id !== record.id))
      }
    },
    onSelectAll: (selected, selectedRows, changeRows) => {
      if (selected) {
        setSelectedRecord([...selectedRecord, ...changeRows])
      } else {
        setSelectedRecord(selectedRecord.filter(o => !find(propEq('id', o.id))(changeRows)))
      }
    },
    selectedRowKeys: keys,
    onChange: (selectedRowKeys, selectedRows) => {
      setKeys(selectedRowKeys)
    }
  } */
  const rowSelection = {
    getCheckboxProps: (record) => ({
      disabled: checkDisabledFunc ? checkDisabledFunc(record) : false,
      name: record.name,
    }),
    onSelect: (record, selected) => {
      console.log(`select row: ${selected}`, record);
    },
    onSelectAll: (selected, selectedRows) => {
      console.log(`select all rows: ${selected}`, selectedRows);
    },
    onChange: (selectedRowKeys, selectedRows) => {
      console.log(
        selectedRowKeys,
        selectedRows,
        "selectedRowKeys, selectedRows"
      );
      setKeys(selectedRowKeys);
      // setSelectedRecord(selectedRows)
      setSelectedRecord((prev) => {
        const remainRows = prev.filter((o) => selectedRowKeys.includes(o.id));
        console.log("remainRows", remainRows);

        const diffSelectedRows = selectedRows.filter((o) => {
          return remainRows.findIndex((r) => r.id === o.id) === -1;
        });
        console.log("diffSelectedRows", diffSelectedRows);

        return union(remainRows, diffSelectedRows);
        // return [...remainRows, ...selectedRows];
      });
    },
    selectedRowKeys: keys.length ? keys : initialSelectedKeys,
  };

  const handleSave = () => {
    callback(selectedRecord);
  };

  return (
    <Modal
      title={entityTitle ?? title}
      visible={visible}
      onCancel={onClose}
      // onOk={handleSave}
      width={800}
      keepDOM
      maskClosable={false}
      footer={
        <div className="flex gap-2 justify-end">
          <button
            className="btn rounded btn-sm"
            onClick={() => {
              onClose();
            }}
          >
            取消
          </button>
          <button
            className="btn rounded btn-primary btn-sm"
            onClick={handleSave}
          >
            确定
          </button>
        </div>
      }
      centered
    >
      {children}
      <Table
        // className="rounded overflow-hidden"
        rowKey="id"
        columns={columns?.filter?.((o) => o.fixed || o.isShow)}
        dataSource={result}
        loading={isLoading}
        rowSelection={rowSelection}
        pagination={{
          currentPage: filter.pageNumber ?? 1,
          pageSize: filter.pageSize ?? 10,
          total: dataSource?.totalCount ?? 0,
          popoverPosition: "topRight",
          pageSizeOpts: [10, 15, 20, 50],
          onChange: handlePageChange,
          onPageSizeChange: handlePageSizeChange,
          showSizeChanger: true,
          showQuickJumper: true,
          // showTotal: (total, range) => `共 ${total} 条`,
        }}
      />
    </Modal>
  );
};
