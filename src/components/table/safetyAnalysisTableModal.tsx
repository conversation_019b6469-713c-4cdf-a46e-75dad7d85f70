import { Modal, Table } from "@douyinfe/semi-ui";
import { useQuery } from "@tanstack/react-query";
import { getSafetyAnalysisList } from "api";
import { safetyAnalysisReferValues } from "atoms";
import {
  safetyAnalysisColumnsAtom,
  safetyAnalysisFilterAtom,
} from "atoms/specialWork";
import { useAtom, useAtomValue } from "jotai";
import { map, prop } from "ramda";
import { useCallback, useMemo, useState } from "react";
import { useParams } from "react-router-dom";

export const SafetyAnalysisTableModal = ({
  children,
  callback,
  visible,
  onClose,
}) => {
  const params = useParams<{ id: string; cid: string }>();
  const [selectedRecord, setSelectedRecord] = useState([]);
  const [keys, setKeys] = useState([]);
  const columns = useAtomValue(safetyAnalysisColumnsAtom);
  const [safetyAnalysisFilter, setSafetyAnalysisFilter] = useAtom(
    safetyAnalysisFilterAtom,
  );
  const safetyAnalysisReferValue = useAtomValue(safetyAnalysisReferValues);

  const { isLoading, data, refetch } = useQuery({
    queryKey: ["safetyAnalysisPage", safetyAnalysisFilter],
    queryFn: () => {
      return getSafetyAnalysisList({
        ...safetyAnalysisFilter,
        filter: {
          ...safetyAnalysisFilter,
          categoryId: parseInt(params?.cid ?? "0"),
        },
      });
    },
  });

  const dataSource = useMemo(() => {
    return data?.data ?? {};
  }, [data]);

  const result = useMemo(() => dataSource?.results ?? [], [dataSource]);

  const handlePageChange = useCallback(
    (currentPage: number) => {
      setSafetyAnalysisFilter({
        ...safetyAnalysisFilter,
        pageNumber: currentPage,
      });
    },
    [safetyAnalysisFilter, setSafetyAnalysisFilter],
  );

  const handlePageSizeChange = useCallback(
    (pageSize: number) => {
      setSafetyAnalysisFilter({
        ...safetyAnalysisFilter,
        pageSize: pageSize,
      });
    },
    [safetyAnalysisFilter, setSafetyAnalysisFilter],
  );

  const rowSelection = {
    onSelect: (record, selected) => {
      console.log(`select row: ${selected}`, record);
    },
    onSelectAll: (selected, selectedRows) => {
      console.log(`select all rows: ${selected}`, selectedRows);
    },
    onChange: (selectedRowKeys, selectedRows) => {
      console.log(
        selectedRowKeys,
        selectedRows,
        "selectedRowKeys, selectedRowsselectedRowKeys, selectedRows",
      );
      setKeys(selectedRowKeys);
      setSelectedRecord(selectedRows);
    },
    selectedRowKeys: keys?.length
      ? keys
      : map(prop("id"), safetyAnalysisReferValue),
  };
  const handleSave = () => {
    callback(selectedRecord);
  };
  return (
    <Modal
      title={"添加作业安全分析"}
      visible={visible}
      keepDOM
      width={1000}
      maskClosable
      onCancel={onClose}
      footer={
        <div className="flex gap-2 justify-end">
          <button
            className="btn rounded btn-sm"
            onClick={() => {
              onClose();
            }}
          >
            取消
          </button>
          <button
            className="btn rounded btn-primary btn-sm"
            onClick={handleSave}
          >
            确定
          </button>
        </div>
      }
      centered
    >
      {children}
      <Table
        className="rounded overflow-hidden"
        rowKey="id"
        columns={columns?.filter?.((o) => o.fixed || o.isShow)}
        dataSource={result}
        loading={isLoading}
        rowSelection={rowSelection}
        onHeaderRow={(columns, index) => {
          return {
            className: "text-gray-900 text-opacity-90 bg-gray-50",
          };
        }}
        headerStyle={{ color: "blue" }}
        pagination={{
          showSizeChanger: true,
          popoverPosition: "topRight",
          currentPage: dataSource?.pageNumber ?? 1,
          pageSize: dataSource?.pageSize ?? 10,
          total: dataSource?.totalCount ?? 0,
          onPageChange: handlePageChange,
          onPageSizeChange: handlePageSizeChange,
          pageSizeOpts: [10, 15, 20, 50],
        }}
      />
    </Modal>
  );
};
