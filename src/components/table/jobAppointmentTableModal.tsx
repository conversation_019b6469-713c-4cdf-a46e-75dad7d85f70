import { Modal, Table } from "@douyinfe/semi-ui";
import { useQuery } from "@tanstack/react-query";
import { useParams } from "react-router-dom";

import { getJobAppointmentList } from "api";
import {
  jobAppointmentColumnsAtom,
  jobAppointmentFilterAtom,
} from "atoms/specialWork";
import { useAtom, useAtomValue } from "jotai";
import { useResetAtom } from "jotai/utils";
import { useCallback, useMemo, useState } from "react";
import { useNavigate } from "react-router-dom";
import { SpecialWorkRoutes } from "utils/routerConstants";

export const JobAppointmentTableModal = ({ callback, onClose, visible }) => {
  const navigate = useNavigate();
  const [selectedRecord, setSelectedRecord] = useState([]);
  const columns = useAtomValue(jobAppointmentColumnsAtom);
  const params = useParams();
  const categoryId = Number(params?.cid ?? "0");

  const [jobAppointmentFilter, setSafetyAnalysisFilter] = useAtom(
    jobAppointmentFilterAtom,
  );
  const reset = useResetAtom(jobAppointmentFilterAtom);

  const { isLoading, data, refetch } = useQuery({
    queryKey: ["jobAppointmentPage", jobAppointmentFilter],
    queryFn: () => {
      return getJobAppointmentList(jobAppointmentFilter);
    },
  });

  const dataSource = useMemo(() => {
    return data?.data ?? {};
  }, [data]);

  const result = useMemo(() => {
    // 2. 处理dataSource数据
    const processedResults = [];

    dataSource?.results?.forEach?.((item) => {
      let isAvailable = false;
      // 情形1: firstJobCategoryId为0时所有票都可用
      if (item.firstJobCategoryId === 0) {
        isAvailable = true;
      }
      // 情形2: 有主票类型时的逻辑
      else if (item.firstJobCategoryId > 0) {
        // 已开主票: 当前票不是主票类型才可用
        if (item.firstJobCategoryFinished === 1) {
          isAvailable = categoryId !== item.firstJobCategoryId;
        }
        // 未开主票: 当前票是主票类型才可用
        else if (item.firstJobCategoryFinished === 2) {
          isAvailable = categoryId === item.firstJobCategoryId;
        }
      }

      processedResults.push({
        ...item,
        isAvailable: isAvailable,
      });
    }) || [];

    return processedResults;
  }, [dataSource, categoryId]);

  const handlePageChange = useCallback(
    (currentPage: number) => {
      setSafetyAnalysisFilter({
        ...jobAppointmentFilter,
        pageNumber: currentPage,
      });
    },
    [jobAppointmentFilter, setSafetyAnalysisFilter],
  );

  const handlePageSizeChange = useCallback(
    (pageSize: number) => {
      setSafetyAnalysisFilter({
        ...jobAppointmentFilter,
        pageSize: pageSize,
      });
    },
    [jobAppointmentFilter, setSafetyAnalysisFilter],
  );

  const rowSelection = {
    getCheckboxProps: (record) => {
      if (!record.isAvailable) {
        return {
          disabled: true,
        };
      }
      return {
        disabled: !selectedRecord?.length
          ? false
          : selectedRecord?.[0]?.id !== record?.id,
      };
    },
    onSelect: (record, selected) => {},
    onChange: (selectedRowKeys, selectedRows) => {
      setSelectedRecord(selectedRows);
    },
    fixed: true,
  };
  const handleSave = () => {
    reset();
    callback(selectedRecord);
  };

  const handleLink = (url: string) => {
    reset();
    navigate(url);
  };

  return (
    <Modal
      title="选择作业预约号"
      visible={visible}
      keepDOM
      width={1000}
      maskClosable
      onCancel={() => {
        onClose?.();
      }}
      footer={
        <div className="flex gap-2 justify-end">
          <button
            className="btn rounded btn-sm"
            onClick={() => {
              onClose?.();
            }}
          >
            取消
          </button>
          <button
            className="btn rounded btn-primary btn-sm"
            onClick={handleSave}
          >
            确定
          </button>
        </div>
      }
      centered
    >
      <Table
        className="rounded overflow-hidden"
        rowKey="id"
        columns={columns?.filter?.((o) => o.fixed || o.isShow)}
        dataSource={result}
        loading={isLoading}
        rowSelection={rowSelection}
        scroll={{
          x: 1800,
        }}
        onHeaderRow={(columns, index) => {
          return {
            className: "text-gray-900 text-opacity-90 bg-gray-50",
          };
        }}
        empty={
          <div>
            暂无预约信息，请先
            <span
              onClick={() => {
                handleLink(SpecialWorkRoutes.JOB_APPOIMENT); // "/job_appoiment");
              }}
              className="text-blue-500 cursor-pointer"
            >
              创建预约
            </span>
          </div>
        }
        headerStyle={{ color: "blue" }}
        pagination={{
          showSizeChanger: true,
          popoverPosition: "topRight",
          currentPage: dataSource?.pageNumber ?? 1,
          pageSize: dataSource?.pageSize ?? 10,
          total: dataSource?.totalCount ?? 0,
          onPageChange: handlePageChange,
          onPageSizeChange: handlePageSizeChange,
          pageSizeOpts: [10, 15, 20, 50],
        }}
      />
    </Modal>
  );
};
