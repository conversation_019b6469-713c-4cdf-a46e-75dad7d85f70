import { IconArrowDown, IconArrowUp } from "@douyinfe/semi-icons";
import { Button, Form, Table, useFormApi } from "@douyinfe/semi-ui";
import { Atom, useAtom } from "jotai";
import { map, prop, swap } from "ramda";
import { useCallback, useEffect, useMemo, useState } from "react";
import {
  SecondTableModal,
  secondPickData,
  secondPickDataKeys,
} from "./secondTableModal";

type Props = {
  title: string;
  entity: string; // 用于queyrKey
  entityTitle: string;
  field: string; // formApi的field
  entityField?: string; // 上述field包装成entity的字段
  list: any[];
  listApi: any;
  columnsAtom: Atom<any[]>;
  filterAtom: Atom<any>;
  isSort?: boolean; // 是否允许更改顺序
};

const SecondPickerTable = (props: Props) => {
  const text = "关联" + props.title;
  const formApi = useFormApi();
  const [visible, setVisible] = useState(false);
  // const [selectedRecord, setSelectedRecord] = useState([])
  const [selectedRecord, setSelectedRecord] = useAtom(secondPickData);
  const [keys, setKeys] = useAtom(secondPickDataKeys);
  const [_columns, setColumns] = useAtom(props.columnsAtom);

  const rules = [{ required: true, message: "该项不可为空" }];

  useEffect(() => {
    const tmp = [];
    (props.list ?? []).forEach((o, index) => {
      tmp.push({
        ...o,
        sortColumn: index,
      });
    });
    setSelectedRecord(tmp);
  }, [props.list]);

  const handleChangeSort = useCallback(
    (sort: number, record: any) => {
      // 边界操作
      if (
        (record.sortColumn == 0 && sort == -1) ||
        (record.sortColumn == selectedRecord.length - 1 && sort == 1)
      ) {
        return;
      }
      let indexa, indexb;
      if (record.sortColumn + sort < record.sortColumn) {
        indexa = record.sortColumn + sort;
        indexb = record.sortColumn;
      } else {
        indexb = record.sortColumn + sort;
        indexa = record.sortColumn;
      }
      // const temp = swap(record.sortColumn, record.sortColumn + sort, selectedRecord)
      const temp = swap(indexa, indexb, selectedRecord).map((item, i) => {
        item.sortColumn = i;
        return item;
      });

      setSelectedRecord(temp);
    },
    [selectedRecord, setSelectedRecord],
  );

  const columns = useMemo(() => {
    const deleteColumn = {
      title: "操作",
      isShow: true,
      dataIndex: "delete",
      align: "center",
      render: (text: any, record: any) => (
        <Button
          // className='text-primary cursor-pointer'
          type="danger"
          onClick={() => {
            setSelectedRecord((prveState) =>
              prveState.filter((o) => o.id !== record.id),
            );
            setKeys((prveState) => prveState.filter((o) => o !== record.id));

            const newFieldValue = formApi
              .getValue(props.field)
              ?.filter((o) => o !== record.id);
            formApi.setValue(props.field, newFieldValue);
            if (props.entityField) {
              formApi.setValue(
                props.entityField,
                formApi
                  .getValue(props.entityField)
                  ?.filter((o) => o.id !== record.id),
              );
            }
          }}
        >
          删除
        </Button>
      ),
    };

    const sortColumnAction = {
      title: "顺序",
      isShow: true,
      dataIndex: "sortColumnAction",
      align: "center",
      render: (text: any, record: any) => (
        <div className="flex gap-2 justify-center">
          <Button
            type="primary"
            theme="borderless"
            disabled={record.sortColumn === 0}
            icon={<IconArrowUp />}
            onClick={() => {
              handleChangeSort(-1, record);
            }}
          />
          <Button
            type="primary"
            theme="borderless"
            disabled={record.sortColumn === selectedRecord.length - 1}
            icon={<IconArrowDown />}
            onClick={() => {
              handleChangeSort(1, record);
            }}
          />
        </div>
      ),
    };

    const sortColumn = {
      title: "顺序",
      isShow: true,
      dataIndex: "sortColumn",
      align: "center",
      render: (t) => <p>{t + 1}</p>,
    };

    let tmp = [];
    if (props?.isSort) {
      tmp = [sortColumn, ..._columns, sortColumnAction, deleteColumn];
    } else {
      tmp = [..._columns, deleteColumn];
    }
    return tmp;
  }, [_columns, props?.isSort, selectedRecord, setSelectedRecord]);

  const handleOpen = () => {
    setVisible(true);
  };

  const handleCb = (record: any[]) => {
    setVisible(false);
    // setSelectedRecord(record)
    const tmp = [];
    record.forEach((o) => {
      tmp.push(o.id);
    });

    formApi.setValue(props.field, tmp);
    if (props.entityField) {
      formApi.setValue(props.entityField, record);
    }
  };

  return (
    <div className="flex flex-col gap-4 mt-8">
      <Form.Section text={text} className="relative">
        <div className="absolute right-0 top-[-10px]">
          <span className="btn rounded btn-primary btn-sm" onClick={handleOpen}>
            新增
          </span>
        </div>

        <SecondTableModal
          callback={handleCb}
          onClose={() => {
            setVisible(false);
          }}
          visible={visible}
          title={props.title}
          entity={props.entity}
          entityTitle={props.entityTitle}
          listApi={props.listApi}
          columnsAtom={props.columnsAtom}
          filterAtom={props.filterAtom}
          initialSelectedKeys={map(prop("id"), selectedRecord)}
        >
          {/* <div className='mb-8'>
            <EmployeeFilter layout="modal" />
          </div> */}
        </SecondTableModal>

        <Table
          columns={columns}
          dataSource={selectedRecord}
          rowKey="id"
          pagination={false}
        />
      </Form.Section>
    </div>
  );
};

export default SecondPickerTable;
