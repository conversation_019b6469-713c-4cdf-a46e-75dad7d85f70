import { Modal, TabPane, Tabs, useFormApi } from "@douyinfe/semi-ui";
import {
  certificatePickerAtom,
  certificatePickerDataAtom,
  jobCertificatesReferValues,
} from "atoms";
import { useAtom, useAtomValue } from "jotai";
import { useResetAtom } from "jotai/utils";
import {
  CertificateContent,
  CertificateFilter,
  ContractorEmployeeCertificateContent,
  ContractorEmployeeCertificateFilter,
} from "pages/basicInfo/content";
import { both, eqBy, find, propEq, props, uniqWith } from "ramda";
import { useCallback, useEffect, useState } from "react";

export const CertificateTableModal = () => {
  const formApi = useFormApi();
  const [list, setList] = useState([]);
  const [record1, setRecord1] = useState([]);
  const [record2, setRecord2] = useState([]);
  const [initRows1, setInitRows1] = useState([]);
  const [initRows2, setInitRows2] = useState([]);
  const [tab, setTab] = useState(1);
  const [show, setShow] = useAtom(certificatePickerAtom);
  const [data, setData] = useAtom(certificatePickerDataAtom);
  const reset = useResetAtom(certificatePickerAtom);
  const jobCertificatesReferValue = useAtomValue(jobCertificatesReferValues);

  const handleSave = () => {
    const tmp: any = [];
    list?.forEach((i) => {
      // 创建一个组合谓词函数，用于检查对象的id和type属性
      const matchIdAndType = both(propEq(i.id, "id"), propEq(i.type, "type"));
      const record: any[] = [];
      record1.forEach((o) => {
        record.push({
          ...o,
          type: 1,
        });
      });
      record2.forEach((o) => {
        record.push({
          ...o,
          type: 2,
        });
      });
      const item = find(matchIdAndType)(record ?? []);

      if (item?.id) {
        tmp.push(item);
      }
    });

    setData({
      record: tmp,
    });
    const formList = [];
    tmp.forEach((i: any) => {
      formList.push({
        type: i.type,
        id: i?.id,
      });
    });
    formApi.setValue("jobCertificateInfo", formList);
    reset();
  };

  useEffect(() => {
    if (jobCertificatesReferValue?.length) {
      const r1 = [];
      const r2 = [];
      jobCertificatesReferValue.forEach((o) => {
        if (o?.type == 1) {
          r1.push(o);
        } else {
          r2.push(o);
        }
      });
      setInitRows1(r1);
      setInitRows2(r2);
      setRecord1(r1);
      setRecord2(r2);
    }
  }, [jobCertificatesReferValue]);

  const initRecord = (type: 1 | 2, record: any[]) => {
    if (type == 1) {
      setRecord1(record);
    } else {
      setRecord2(record);
    }
  };

  // 添加/删除，数据同步相关直接在关闭modal时做，不然多处同步，太乱
  const handleSetRecord = useCallback(
    (ids: any, type: 1 | 2) => {
      if (!ids?.length) {
        const typeIds = list.filter?.((o) => o?.type != type);
        setList(typeIds?.length ? typeIds : []);
      } else {
        const typeIds = [];
        ids?.forEach?.((o: any) => {
          const has = list?.filter?.(
            (i) => o?.type == type && o?.id == o
          )?.length;
          if (!Boolean(has)) {
            typeIds.push({
              id: o,
              type: type,
            });
          }
        });
        const uniqueByIdAndType = uniqWith(eqBy(props(["id", "type"])));

        // @ts-ignore
        setList(uniqueByIdAndType([...typeIds, ...(list ?? [])]));
      }
    },
    [setList, list]
  );

  return (
    <Modal
      title={"选择持证作业人员"}
      visible={show}
      keepDOM
      width={1200}
      onCancel={() => {
        reset();
      }}
      footer={
        <div className="flex gap-2 justify-end">
          <button
            className="btn rounded btn-sm"
            onClick={() => {
              reset();
            }}
          >
            取消
          </button>
          <button
            className="btn rounded btn-primary btn-sm"
            onClick={handleSave}
          >
            确定
          </button>
        </div>
      }
      centered
    >
      <Tabs
        type="line"
        onChange={(v) => {
          setTab(parseInt(v));
        }}
      >
        <TabPane tab="本厂" itemKey="1">
          <div className="flex flex-col gap-4 ">
            <CertificateFilter filter={{ isValid: 1 }} isValidDisabled={true} />
            {/* isValid: 1 有效 */}
            <div className="grid">
              <CertificateContent
                mode="modal"
                cb={handleSetRecord}
                initData={initRecord}
                initRows={initRows1}
                filter={{
                  isValid: 1, // 有效
                }}
              />
            </div>
          </div>
        </TabPane>
        <TabPane tab="承包商" itemKey="2">
          <div className="flex flex-col gap-4 ">
            <ContractorEmployeeCertificateFilter
              filter={{ isValid: 1 }}
              isValidDisabled={true}
            />
            <div className="grid">
              <ContractorEmployeeCertificateContent
                mode="modal"
                cb={handleSetRecord}
                initData={initRecord}
                initRows={initRows2}
                filter={{
                  isValid: 1, // 有效
                }}
              />
            </div>
          </div>
        </TabPane>
      </Tabs>
    </Modal>
  );
};
