import { Modal, useFormApi } from "@douyinfe/semi-ui";
import {
  certificatePickerDataAtom,
  jobCertificatesReferValues,
  jobSliceFilterAtom,
  referJsAtom,
  safetyAnalysisReferValues,
} from "atoms";
import { base_url } from "config";
import { useAtom, useSetAtom } from "jotai";
import { useResetAtom } from "jotai/utils";
import { TicketListContent, TicketListFilter } from "pages/ticket/content";
import { find, map, pick, prop, propEq, type } from "ramda";
import { useEffect } from "react";
import { useParams } from "react-router-dom";
import { renderMarkerList } from "utils";

export const ReferJsTableModal = () => {
  const formApi = useFormApi();
  const params = useParams<{ id: string; cid: string }>();
  const [referJs] = useAtom(referJsAtom);
  const reset = useResetAtom(referJsAtom);
  const reset1 = useResetAtom(safetyAnalysisReferValues);
  const reset2 = useResetAtom(jobCertificatesReferValues);
  const reset3 = useResetAtom(certificatePickerDataAtom);
  const [filter, setFilter] = useAtom(jobSliceFilterAtom);
  const setSafetyAnalysisReferValues = useSetAtom(safetyAnalysisReferValues);
  const setJobCertificatesReferValues = useSetAtom(jobCertificatesReferValues);
  const [data, setCertificatePickerData] = useAtom(certificatePickerDataAtom);

  const handleSave = () => {
    reset();
    reset1();
    reset2();
    reset3();
  };
  useEffect(() => {
    if (params?.cid && params?.id) {
      setFilter({
        ...filter,
        filter: {
          categoryId: parseInt(params.cid),
          // templateId: parseInt(params.id),
        },
      });
    }
  }, [params]);

  const handleSetRefer = (record: any) => {
    if (record?.form) {
      try {
        const formJSON = JSON.parse(record?.form) ?? [];

        let tmpForm: any = {};

        formJSON.forEach((o: any) => {
          const value = o?.formData?.actualValue as any;
          if (o.business && value) {
            if (
              o?.compType == "annexImgPicker" ||
              o?.compType === "annexFilePicker"
            ) {
              const tmp = [];

              (type(value?.values) === "Array" ? value.values : value).forEach(
                (o) => {
                  tmp.push({
                    name: o?.split?.("/")?.pop?.(),
                    preview: true,
                    url: `${base_url}${o}`,
                  });
                },
              );

              tmpForm[`${o.business}`] = tmp;
            } else if (
              (o?.compType == "employeePicker" || o?.compType === "selector") &&
              type(value) === "Array"
            ) {
              let tmp = [];
              (value ?? []).forEach((o) => {
                if (type(o) === "Object") {
                  tmp.push(JSON.stringify(o));
                } else {
                  tmp.push(o);
                }
              });
              tmpForm[`${o.business}`] = tmp;
            } else {
              tmpForm[`${o.business}`] = value;
            }
            if (
              o.business == "guardianCertificate" ||
              o.business == "temporaryPowerJobInChargeCertificate"
            ) {
              if (type(value) == "String") {
                tmpForm[`${o.business}-name`] = [value];
              } else {
                tmpForm[`${o.business}-name`] =
                  value?.map?.((item: any) => {
                    return item?.code ?? "";
                  }) ?? [];
              }
            }
            if (
              o.business == "guardianInCharge" ||
              o.business == "temporaryPowerJobInCharge"
            ) {
              tmpForm[`${o.business}`] = value;
              tmpForm[`${o.business}-name`] =
                value?.map?.((item: any) => {
                  return item?.name ?? "";
                }) ?? [];
            }
            if (o.business == "otherInCharge") {
              tmpForm[`${o.business}-renderToText`] = map(prop("name"), value);
              tmpForm[o.business] = value;
            }
          } else {
            if (
              (o?.compType == "employeePicker" || o?.compType === "selector") &&
              type(value) === "Array"
            ) {
              let tmp = [];
              (value ?? []).forEach((o) => {
                if (type(o) === "Object") {
                  tmp.push(JSON.stringify(o));
                } else {
                  tmp.push(o);
                }
              });

              tmpForm[`${o.itemId}`] = tmp;
            } else if (o?.compType === "table") {
              (o?.children ?? []).forEach((col: any) => {
                (col?.children ?? []).forEach((cell: any) => {
                  const value = cell?.formData?.actualValue as any;
                  if (value) {
                    tmpForm[`${cell.itemId}`] = value;
                  }
                });
              });
            } else {
              tmpForm[`${o.itemId}`] = value;
            }
          }
        });
        console.log(tmpForm, "form数据", formJSON);

        // 填充作业安全分析信息
        setSafetyAnalysisReferValues(record?.safetyAnalysisResults ?? []);
        // 填充持证作业人员
        setJobCertificatesReferValues(record?.jobCertificates ?? []);
        setCertificatePickerData({
          record: record?.jobCertificates ?? [],
        });
        console.debug("引用作业票数据源:", record);

        const jobProcessesInfo: any = [];
        record?.jobProcesses?.forEach((o: any) => {
          const candidatePersonList: any[] = [];
          o?.candidatePerson?.forEach?.((o) => {
            candidatePersonList.push({
              ...o,
              _type: o?.type ?? 1,
            });
          });

          jobProcessesInfo.push({
            name: o.name,
            candidatePersonList: candidatePersonList,
            "candidatePersonList-renderToText": map(
              prop("name"),
              o?.candidatePerson ?? [],
            ).toString(),
          });
        });
        // 找到交集数据,然后替换
        formApi
          .getValues()
          ?.jobProcessesInfo?.forEach?.((o: any, index: number) => {
            const item = find(propEq(o.name, "name"))(jobProcessesInfo);
            if (item) {
              formApi.setValue(`jobProcessesInfo[${index}]`, item);
            }
          });

        formApi.setValues(
          {
            ...formApi.getValues(),
            referJsId: record?.id,
            map: [record?.longitude, record?.latitude],
            longitude: record?.longitude,
            latitude: record?.latitude,
            monitorId: record?.monitor?.id ?? null,
            electronType: record?.electronType ?? null,
            radius: record?.radius ?? null,
            electronPointList: record?.electronPointList ?? [],
            electronPointListShow: renderMarkerList(
              record?.electronPointList ?? [],
            ),
            safetyAnalysisPerson: record?.safetyAnalysisPerson ?? "",
            safetyAnalysisIds: JSON.stringify(
              map(prop("id"), record?.safetyAnalysisResults ?? []),
            ),
            jobCertificateInfo: map(
              pick(["id", "type"]),
              record?.jobCertificates ?? [],
            ),
            acceptCandidateIds: map(prop("id"), record?.acceptCandidates ?? []),
            // jobProcessesInfo: jobProcessesInfo,
            form: {
              ...tmpForm,
            },
          },
          {
            isOverride: true,
          },
        );
      } catch (e) {
        console.log(e, "xxxxx");
      }
    }
  };
  return (
    <Modal
      title={"引用作业票"}
      visible={referJs.visible}
      keepDOM
      width={1200}
      onCancel={() => {
        reset();
      }}
      footer={
        <div className="flex gap-2 justify-end">
          <button
            className="btn rounded btn-sm"
            onClick={() => {
              reset();
            }}
          >
            取消
          </button>
          <button
            className="btn rounded btn-primary btn-sm"
            onClick={handleSave}
          >
            确定
          </button>
        </div>
      }
      centered
    >
      <div className="flex flex-col gap-4 ">
        <TicketListFilter mode="modal" />
        <div className="grid">
          <TicketListContent mode="modal" cb={handleSetRefer} />
        </div>
      </div>
    </Modal>
  );
};
