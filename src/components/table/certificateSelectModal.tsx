import { Modal, useFormApi } from "@douyinfe/semi-ui";
import { certificateSelectAtom } from "atoms";
import { useAtom } from "jotai";
import { useResetAtom } from "jotai/utils";
import { CertificateContent, CertificateFilter } from "pages/basicInfo/content";
import { type } from "ramda";
import { useMemo } from "react";

export const CertificateSelectModal = () => {
  const formApi = useFormApi();
  const [show, setShow] = useAtom(certificateSelectAtom);
  const reset = useResetAtom(certificateSelectAtom);

  const handleSave = () => {
    reset();
  };

  const initRows = useMemo(() => {
    if (!show.type) {
      return [];
    }
    const list =
      show.type === "guardian"
        ? formApi.getValue("form.guardianCertificate")
        : formApi.getValue("form.temporaryPowerJobInChargeCertificate");
    const ids: any[] = [];
    list?.forEach?.((o) => {
      try {
        const item = type(o) === "String" ? JSON.parse(o) : o;
        if (item?.id) {
          ids.push(item);
        }
      } catch (e) {
        console.error(e);
      }
    });
    return ids;
  }, [
    show.type,
    formApi.getValue("form.guardianCertificate"),
    formApi.getValue("form.temporaryPowerJobInChargeCertificate"),
  ]);

  const handleSetRecord = (record: any) => {
    const guardianInChargeList: any[] = [];
    const guardianInChargeName: any[] = [];
    const codeList: any[] = [];
    const codeListName: string[] = [];

    record?.forEach?.((r: any) => {
      guardianInChargeList.push({
        id: r?.employee?.id ?? "",
        name: r?.employee?.name ?? "",
      });
      guardianInChargeName.push(
        r?.employee?.name ?? r?.contractorEmployee?.name ?? ""
      );
      codeList.push({
        id: r?.id ?? "",
        code: r?.code ?? "",
      });
      codeListName.push(r?.code ?? "");
    });

    const setGuardianFormValues = () => {
      formApi.setValue(`form.guardianInCharge`, guardianInChargeList);
      formApi.setValue(`form.guardianInCharge-name`, guardianInChargeName);
      formApi.setValue(`form.guardianCertificate`, codeList);
      formApi.setValue(`form.guardianCertificate-name`, codeListName);
    };
    const setTemporaryPowerJobInChargeFormValues = () => {
      formApi.setValue(`form.temporaryPowerJobInCharge`, guardianInChargeList);
      formApi.setValue(
        `form.temporaryPowerJobInCharge-name`,
        guardianInChargeName
      );
      formApi.setValue(`form.temporaryPowerJobInChargeCertificate`, codeList);
      formApi.setValue(
        `form.temporaryPowerJobInChargeCertificate-name`,
        codeListName
      );
    };

    if (show.type === "guardian") {
      setGuardianFormValues();
    } else {
      setTemporaryPowerJobInChargeFormValues();
    }
  };

  return (
    <Modal
      title={"作业监护人员证书"}
      visible={show.visible}
      keepDOM
      width={1200}
      onCancel={() => {
        reset();
      }}
      footer={
        <div className="flex gap-2 justify-end">
          <button
            className="btn rounded btn-sm"
            onClick={() => {
              reset();
            }}
          >
            取消
          </button>
          <button
            className="btn rounded btn-primary btn-sm"
            onClick={handleSave}
          >
            确定
          </button>
        </div>
      }
      centered
    >
      <div className="flex flex-col gap-4 ">
        <CertificateFilter filter={{ isValid: 1 }} isValidDisabled={true} />
        {/* isValid: 1 有效 */}
        <div className="grid">
          <CertificateContent
            mode="select"
            cb={handleSetRecord}
            initRows={initRows}
            filter={{
              isValid: 1, // 有效
            }}
          />
        </div>
      </div>
    </Modal>
  );
};
