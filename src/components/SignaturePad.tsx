import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
} from "react";

const SignaturePad: React.FC = forwardRef((props, ref) => {
  const canvasRef = useRef<HTMLCanvasElement | null>(null);
  const isDrawing = useRef(false);
  const lastPoint = useRef<{ x: number; y: number } | null>(null);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const context = canvas.getContext("2d");
    if (!context) return;

    const handleMouseDown = (event: MouseEvent) => {
      isDrawing.current = true;
      lastPoint.current = { x: event.offsetX, y: event.offsetY };
    };

    const handleMouseMove = (event: MouseEvent) => {
      if (!isDrawing.current || !lastPoint.current) return;

      const currentPoint = { x: event.offsetX, y: event.offsetY };
      context.beginPath();
      context.moveTo(lastPoint.current.x, lastPoint.current.y);
      context.lineTo(currentPoint.x, currentPoint.y);
      context.stroke();
      lastPoint.current = currentPoint;
    };

    const handleMouseUp = () => {
      isDrawing.current = false;
      lastPoint.current = null;
    };

    canvas.addEventListener("mousedown", handleMouseDown);
    canvas.addEventListener("mousemove", handleMouseMove);
    canvas.addEventListener("mouseup", handleMouseUp);
    canvas.addEventListener("mouseleave", handleMouseUp);

    return () => {
      canvas.removeEventListener("mousedown", handleMouseDown);
      canvas.removeEventListener("mousemove", handleMouseMove);
      canvas.removeEventListener("mouseup", handleMouseUp);
      canvas.removeEventListener("mouseleave", handleMouseUp);
    };
  }, []);

  const clearCanvas = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const context = canvas.getContext("2d");
    if (!context) return;

    context.clearRect(0, 0, canvas.width, canvas.height);
  };

  useImperativeHandle(ref, () => ({
    getSignatureImage: () => {
      const canvas = canvasRef.current;
      if (!canvas) return { image: null, isSigned: false };

      const context = canvas.getContext("2d");
      if (!context) return { image: null, isSigned: false };

      // 检查画布是否为空
      const isCanvasBlank = !context
        .getImageData(0, 0, canvas.width, canvas.height)
        .data.some((channel) => channel !== 0);

      if (isCanvasBlank) {
        return { image: null, isSigned: false };
      }

      // 创建一个新的画布用于添加白色背景
      const tempCanvas = document.createElement("canvas");
      tempCanvas.width = canvas.width;
      tempCanvas.height = canvas.height;
      const tempContext = tempCanvas.getContext("2d");

      if (tempContext) {
        // 填充白色背景
        tempContext.fillStyle = "#FFFFFF";
        tempContext.fillRect(0, 0, tempCanvas.width, tempCanvas.height);
        // 绘制原始签名
        tempContext.drawImage(canvas, 0, 0);
      }

      return { image: tempCanvas.toDataURL("image/jpeg"), isSigned: true };
    },
  }));

  return (
    <div className="w-full">
      <canvas
        ref={canvasRef}
        width={600}
        height={300}
        className="border rounded p-2 "
      />
      <button className="btn btn-sm mt-2" onClick={clearCanvas}>
        清除签名
      </button>
    </div>
  );
});

export default SignaturePad;
