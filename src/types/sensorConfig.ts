import { CommonApis } from "types";

// 传感器配置接口
export interface SensorConfig {
  // API配置
  apis: CommonApis;

  // 实体名称
  entity: string;

  // 原子状态配置
  atoms?: {
    entity: string;
    editModal: any;
    Fn: any;
  };

  // 字段验证配置
  fieldValidation: {
    equipmentId: {
      required: boolean;
      rules?: any[];
    };
    [key: string]: {
      required: boolean;
      rules?: any[];
    };
  };

  // 导入配置
  importProps: {
    entity: string;
    excelType: string;
    downUrl: string;
    tip: string;
  };

  // 操作配置
  operations: {
    resumeApi?: (id: number) => Promise<any>;
    stopApi?: (id: number) => Promise<any>;
    batchResumeApi?: (ids: number[]) => Promise<any>;
    batchStopApi?: (ids: number[]) => Promise<any>;
  };

  // 模态框配置
  modalConfig: {
    newTitle: string;
    editTitle: string;
    requiredFields: string[];
  };
}

// 传感器组件Props
export interface SensorComponentProps {
  config?: SensorConfig;
}
