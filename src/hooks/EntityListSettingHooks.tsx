import { useCallback } from "react";

type ShowSetter = (show: boolean) => void;

export const useEntityListSetting = (setShow: ShowSetter) => {
  return useCallback(() => {
    console.log("Setting operation");
    setShow(true);
  }, [setShow]);
};

/* function useEntityListSetting(setShow) {
	return useCallback(() => {
		console.log("Setting operation");
		setShow(true);
	}, [setShow]);
}
export default useEntityListSetting
 */
