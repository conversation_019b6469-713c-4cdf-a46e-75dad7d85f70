import { useQuery } from "@tanstack/react-query";
import { getAreaList } from "api";
import { useMemo } from "react";
import { listPageSizeWithoutPaging } from "utils";

export function useAreaListOptions(apiFilter: any = {}) {
  const { data: areaList } = useQuery({
    queryKey: ["getAreaList", 1, listPageSizeWithoutPaging, apiFilter],
    queryFn: () =>
      getAreaList({
        pageNumber: 1,
        pageSize: listPageSizeWithoutPaging,
        filter: apiFilter,
      }),
  });
  const areaListOptions = useMemo(() => {
    return areaList?.data?.results ?? [];
  }, [areaList]);
  return areaListOptions;
}
