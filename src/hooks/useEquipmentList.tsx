import { useQuery } from "@tanstack/react-query";
import { getEquipmentList } from "api";
import { useMemo } from "react";
import { listPageSizeWithoutPaging } from "utils";

export function useEquipmentListOptions(apiFilter = {}) {
  const { data: equipmentList } = useQuery({
    queryKey: ["getEquipmentList", apiFilter],
    queryFn: () =>
      getEquipmentList({
        pageNumber: 1,
        pageSize: listPageSizeWithoutPaging,
        filter: apiFilter,
      }),
  });

  const equipmentListOptions = useMemo(() => {
    return equipmentList?.data?.results ?? [];
  }, [equipmentList]);

  return equipmentListOptions;
}
