import * as Cesium from "cesium";
import "cesium/Build/Cesium/Widgets/widgets.css";
import { base_url } from "config";
import { threeModuleSize } from "utils";

export const cesiumViewerOptions = {
  animation: false, //是否显示动画控件
  baseLayerPicker: false, //是否显示图层选择控件
  fullscreenButton: false, //是否显示全屏按钮
  vrButton: false, // vr部件
  geocoder: false, // 位置搜索部件
  homeButton: false, //是否显示Home按钮
  infoBox: false, //是否显示点击要素之后显示的信息
  sceneModePicker: false, // 二三维切换部件
  timeline: false, //是否显示时间线控件
  navigationHelpButton: false, //是否显示帮助信息控件
};

export const cesiumSceneLightOptions = {
  direction: new Cesium.Cartesian3(1.0, -1.0, -1.0),
  color: new Cesium.Color(1.0, 1.0, 1.0, 1.0),
};

export const cesiumInit = async (data, viewer) => {  
  const res = data.data;
  const token = res?.cesiumToken;
  const modelUrl = `${base_url}${res?.cesiumUri}`;
  const cesiumUris = res?.cesiumUris?.map((item) => {
    return `${base_url}${item}`;
  });

  if (!token) {
    console.error("token 错误:", token);
    return;
  }
  Cesium.Ion.defaultAccessToken = token;

  // 这里的模型分层了，不能直接加载，要先循环查
  if (cesiumUris?.length) {
    cesiumUris.forEach(async (item, index) => {
      if (!index) return;
      const tilestData = await Cesium.Cesium3DTileset.fromUrl(item, {
        cacheBytes: threeModuleSize,
        maximumCacheOverflowBytes: threeModuleSize,
        loadSiblings: true,
        maximumScreenSpaceError: 0.01, // 降低这个值以提高清晰度
        preferLeaves: true, // 优先加载叶子节点
        dynamicScreenSpaceError: true, // 启用动态屏幕空间误差
        dynamicScreenSpaceErrorDensity: 0.5, // 调整动态调整的强度
        skipLevels: 1, // 跳过一级细节
        preloadWhenHidden: true, // 预加载隐藏的模型
        debugShowUrl: false, // 关闭调试模式
      });
      viewer?.scene.primitives.add(tilestData);
    });
  }

  const setTilesetHeight = (tileset: any, height: number) => {
    //调整高度 让模型贴地面
    const cartographic = Cesium.Cartographic.fromCartesian(
      tileset.boundingSphere.center
    );
    const surface = Cesium.Cartesian3.fromRadians(
      cartographic.longitude,
      cartographic.latitude
    );

    const offset = Cesium.Cartesian3.fromRadians(
      cartographic.longitude,
      cartographic.latitude,
      height && height !== 0
        ? height
        : cartographic.height > 30
          ? 0
          : cartographic.height
    );
    const translation = Cesium.Cartesian3.subtract(
      offset,
      surface,
      new Cesium.Cartesian3()
    );
    tileset.modelMatrix = Cesium.Matrix4.fromTranslation(translation);
  };


  const tileset = await Cesium.Cesium3DTileset.fromUrl(
    cesiumUris?.[0] || modelUrl,
    {
      cacheBytes: threeModuleSize,
      maximumCacheOverflowBytes: threeModuleSize,
      loadSiblings: true,
      maximumScreenSpaceError: 0.01, // 降低这个值以提高清晰度
      preferLeaves: true, // 优先加载叶子节点
      dynamicScreenSpaceError: true, // 启用动态屏幕空间误差
      dynamicScreenSpaceErrorDensity: 0.5, // 调整动态调整的强度
      skipLevels: 1, // 跳过一级细节
      preloadWhenHidden: true, // 预加载隐藏的模型
      debugShowUrl: false, // 关闭调试模式
    },
  );
  
  setTilesetHeight(
    tileset,
    5
  );
  return tileset;
};
