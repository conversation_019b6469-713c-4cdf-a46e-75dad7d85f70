import { useLocalStorage } from "@reactivers/hooks";
import { useQuery } from "@tanstack/react-query";
import { getEmployee } from "api";
import { RoleKeyCode } from "config";
import { find, propEq } from "ramda";
import { useMemo } from "react";
import { getAdmins } from "utils";
import { userInfoNameInLocalStorage } from "utils/constants";

export const useBtnHooks = (loderData, pathname) => {
  const { getItem } = useLocalStorage(userInfoNameInLocalStorage);
  const id = getItem()?.userInfo?.id;
  const employeeId = getItem()?.userInfo?.employeeId;

  const { data } = useQuery({
    queryKey: [`getRole`],
    queryFn: () => {
      return getEmployee(id);
    },
    enabled: Boolean(id),
  });

  const list = useMemo(() => {
    const permission = data?.data?.role?.permission;
    return JSON.parse(permission ? permission : "[]");
  }, [data]);

  const genBtn = (action, elm) => {
    if (getAdmins(employeeId)) {
      return elm;
    }
    const menuId = loderData?.[0]?.menuId;
    const btnPermissions = find(propEq(menuId, RoleKeyCode))(list)?.permissions;

    const btnRole = [];

    (btnPermissions ?? []).forEach?.((o) => {
      if (o) {
        btnRole.push(JSON.parse(o));
      }
    });

    const items = btnRole?.filter?.(
      (o) => o?.clientPath === `${pathname}_${action}`
    );

    if (Boolean(items?.length)) {
      return elm;
    }
    return null;
  };
  return genBtn;
};
