import { useAuth } from "@reactivers/hooks";
import {
  Danger,
  DangerStatus,
  DangerSteps,
  ManageType,
} from "atoms/doubleGuard";
import { find, propEq } from "ramda";
import { useMemo } from "react";

export default function useDangerAuth(record: Danger) {
  const { user } = useAuth();

  // 即查即改 的上报信息、评估信息、整改信息，谁都能看
  const isImmediate = useMemo(
    () => record?.manageType === ManageType.Immediately,
    [record?.manageType]
  );

  // 登录用户需要在评估候选人列表
  const enableEvaluate =
    isImmediate ||
    find(propEq(user.userInfo?.id, "id"))(record?.evalatorCands || []);
  const disableEvaluate = !record?.id;
  // || !enableEvaluate;
  // 登录用户需要在整改人候选列表
  const enableRectify =
    isImmediate ||
    find(propEq(user.userInfo?.id, "id"))(record?.rectifierCands || []);
  const disableRectify =
    !record?.id || record?.status < DangerStatus.WaitEvaluate;
  // || !enableRectify
  // 登录用户需要在验收人候选列表
  const enableAccept = find(propEq(user.userInfo?.id, "id"))(
    record?.acceptorCands || []
  );
  const disableAccept =
    !record?.id || record?.status < DangerStatus.WaitRectify;
  // || !enableAccept
  const currentStep = useMemo(() => {
    let type = DangerSteps.Report;
    /* if (
      enableEvaluate &&
      (record?.status === DangerStatus.WaitEvaluate ||
        record?.status === DangerStatus.RequestDelay)
    ) { */
    if (record?.status === DangerStatus.WaitEvaluate) {
      // 评估 -> 无需整改 = DangerStatus.NoRectify
      type = DangerSteps.Evaluate;
      // } else if (enableRectify && record?.status === DangerStatus.WaitRectify) {
    } else if (record?.status === DangerStatus.WaitRectify) {
      // 整改
      type = DangerSteps.Rectify;
      // } else if (enableAccept && record?.status === DangerStatus.WaitAccept) {
    } else if (record?.status === DangerStatus.WaitAccept) {
      // 验收
      type = DangerSteps.Accept;
    }
    return type;
  }, [enableEvaluate, enableRectify, enableAccept, record?.status]);

  return {
    enableEvaluate,
    disableEvaluate,
    enableRectify,
    disableRectify,
    enableAccept,
    disableAccept,
    currentStep,
  };
}
