import { useFormState } from "@douyinfe/semi-ui";
import { useQuery } from "@tanstack/react-query";
import { getContractorEmployeeList, getContractorProjectList } from "api";
import { RestSelect } from "components";
import { useMemo } from "react";
import { listPageSizeWithoutPaging } from "utils";

export const ContractorEmployeeComponentUsingFormApi = ({
  type = "filter",
  field = "contractorEmployeeId",
  apiFilter = {},
}) => {
  // const formApi = useFormApi();
  const formState = useFormState();

  const contractorId = formState.values?.contractorId;
  const isContractorTouched = formState.touched?.contractorId;

  const { data: employeeListData } = useQuery({
    queryKey: [`getContractorEmployeeList`, contractorId],
    queryFn: () =>
      getContractorEmployeeList({
        pageNumber: 1,
        pageSize: listPageSizeWithoutPaging,
        filter: { contractorId: contractorId, ...apiFilter },
      }),
    enabled: !!(contractorId && isContractorTouched), // Only enable query when contractorId is present and touched
  });

  const employeeListOptions = useMemo(() => {
    return employeeListData?.data?.results ?? [];
  }, [employeeListData]);

  console.debug(employeeListOptions);

  return (
    <RestSelect
      options={employeeListOptions}
      placeholder="请选择员工"
      field={field}
      {...(type === "filter" ? { noLabel: true } : { label: "承包商员工" })}
      {...(type !== "filter" ? { isRequired: true } : {})}
    />
  );
};

export const ContractorProjectComponentUsingFormApi = ({
  type = "filter",
  field = "projectId",
  isRequired = false,
}) => {
  const formState = useFormState();

  const contractorId = formState.values?.contractorId;
  const isContractorTouched = formState.touched?.contractorId;

  const { data: projectListData } = useQuery({
    queryKey: [`getContractorProjectList`, contractorId],
    queryFn: () =>
      getContractorProjectList({
        pageNumber: 1,
        pageSize: listPageSizeWithoutPaging,
        filter: { contractorId: contractorId },
      }),
    enabled: !!(contractorId && isContractorTouched), // Only enable query when contractorId is present and touched
  });

  const projectListOptions = useMemo(() => {
    return projectListData?.data?.results ?? [];
  }, [projectListData]);

  return (
    <RestSelect
      options={projectListOptions}
      placeholder="请选择项目"
      field={field}
      {...(type === "filter" ? { noLabel: true } : { label: "承包商项目" })}
      {...(isRequired ? { isRequired: true } : {})}
    />
  );
};
