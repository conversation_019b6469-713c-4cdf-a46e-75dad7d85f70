import { ColumnConfig } from "components";
import { useAtom } from "jotai";
import { useEffect } from "react";
import { useLocation } from "react-router-dom";

// localStorage存储键的前缀
const TABLE_CONFIG_STORAGE_KEY = "table-config-";

// 获取存储键
const getStorageKey = (entity?: string, pathname?: string) => {
  if (entity) {
    return `${TABLE_CONFIG_STORAGE_KEY}${entity}`;
  }
  // 如果没有entity，使用路径作为存储键
  if (pathname) {
    const pathKey = pathname.replace(/\//g, "-").replace(/^-/, "");
    return `${TABLE_CONFIG_STORAGE_KEY}path-${pathKey}`;
  }
  // 最后的兜底方案
  return `${TABLE_CONFIG_STORAGE_KEY}default`;
};

// 从localStorage读取配置
const loadTableConfig = (
  entity?: string,
  pathname?: string
): Record<string, boolean> => {
  const storageKey = getStorageKey(entity, pathname);

  try {
    const stored = localStorage.getItem(storageKey);
    if (stored) {
      const config = JSON.parse(stored);
      return config.reduce((acc: Record<string, boolean>, item: any) => {
        acc[item.dataIndex] = item.isShow;
        return acc;
      }, {});
    }
  } catch (error) {
    console.error("读取表格配置失败:", error);
  }

  return {};
};

// 保存配置到localStorage
const saveTableConfig = (
  entity: string | undefined,
  columns: ColumnConfig[],
  pathname?: string
) => {
  const storageKey = getStorageKey(entity, pathname);

  try {
    const config = columns.map((col) => ({
      dataIndex: col.dataIndex,
      isShow: col.isShow,
    }));
    localStorage.setItem(storageKey, JSON.stringify(config));
  } catch (error) {
    console.error("保存表格配置失败:", error);
  }
};

/**
 * 表格配置持久化hook
 * @param columnsAtom - columns atom
 * @param entity - 实体名称，用于区分不同页面的配置
 * @returns [columns, setColumns] - 返回columns状态和设置函数
 */
export const useTableConfig = (columnsAtom: any, entity?: string) => {
  const [columns, setColumns] = useAtom(columnsAtom);
  const { pathname } = useLocation();

  // 初始化时从localStorage读取配置
  useEffect(() => {
    if (!columns?.length) return;

    const savedConfig = loadTableConfig(entity, pathname);
    if (Object.keys(savedConfig).length === 0) return; // 没有保存的配置，使用默认值

    // 应用保存的配置
    const updatedColumns = columns.map((col) => ({
      ...col,
      isShow:
        savedConfig[col.dataIndex] !== undefined
          ? savedConfig[col.dataIndex]
          : col.isShow,
    }));

    // 只有当配置有变化时才更新
    const hasChanges = updatedColumns.some(
      (col, index) => col.isShow !== columns[index].isShow
    );

    if (hasChanges) {
      setColumns(updatedColumns);
    }
  }, [columns, entity, pathname, setColumns]);

  // 包装setColumns函数，自动保存到localStorage
  const setColumnsWithStorage = (newColumns: ColumnConfig[]) => {
    setColumns(newColumns);
    saveTableConfig(entity, newColumns, pathname);
  };

  return [columns, setColumnsWithStorage];
};
