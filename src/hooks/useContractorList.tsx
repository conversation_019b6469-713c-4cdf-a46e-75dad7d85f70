import { useQuery } from "@tanstack/react-query";
import {
  getContractorEmployeeList,
  getContractorList,
  getContractorProjectList,
} from "api";
import { useMemo } from "react";
import { listPageSizeWithoutPaging } from "utils";

export function useContractorListOptions(apiFilter = {}) {
  const { data: contractorList } = useQuery({
    queryKey: ["getContractorList", apiFilter],
    queryFn: () =>
      getContractorList({
        pageNumber: 1,
        pageSize: listPageSizeWithoutPaging,
        filter: apiFilter,
      }),
  });

  const contractorListOptions = useMemo(() => {
    return contractorList?.data?.results ?? [];
  }, [contractorList]);

  return contractorListOptions;
}

export function useContractorEmployeeListOptions(apiFilter = {}) {
  const { data: contractorEmployeeList } = useQuery({
    queryKey: ["getContractorEmployeeList", apiFilter],
    queryFn: () =>
      getContractorEmployeeList({
        pageNumber: 1,
        pageSize: listPageSizeWithoutPaging,
        filter: apiFilter,
      }),
  });

  const contractorEmployeeListOptions = useMemo(() => {
    return contractorEmployeeList?.data?.results ?? [];
  }, [contractorEmployeeList]);

  return contractorEmployeeListOptions;
}

export function useContractorProjectListOptions(apiFilter = {}) {
  const { data: contractorProjectList } = useQuery({
    queryKey: ["getContractorProjectList", apiFilter],
    queryFn: () =>
      getContractorProjectList({
        pageNumber: 1,
        pageSize: listPageSizeWithoutPaging,
        filter: apiFilter,
      }),
  });

  const contractorProjectListOptions = useMemo(() => {
    return contractorProjectList?.data?.results ?? [];
  }, [contractorProjectList]);

  return contractorProjectListOptions;
}

// Example of how to use these custom hooks in a component:
/*
import React from 'react';
import { useContractorListOptions, useContractorEmployeeListOptions } from './contractorCommon';

const MyComponent = () => {
  const contractors = useContractorListOptions();
  const employees = useContractorEmployeeListOptions();

  // Use contractors and employees in your component

  return (
    <div>
      Render your component using the fetched data 
    </div>
  );
};

export default MyComponent;
*/
