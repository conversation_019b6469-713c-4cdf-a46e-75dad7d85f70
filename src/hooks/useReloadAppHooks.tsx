import { useQuery } from "@tanstack/react-query";
import { useEffect, useMemo } from "react";
import { useLocation } from "react-router-dom";

const getVersion = async () => {
  try {
    const url = window.location.origin == 'http://localhost:5173'
      ? 'https://dev.vren-tech.com'
      : window.location.origin
    const request_url = `${url}/version.json`;
    const res = await fetch(request_url)
    const txt = await res.text();
    const json = JSON.parse(txt.replace(/\n/g, '').replace(/\,/g, ''))
    return json;
  } catch {
    return {
      "commit": "",
    }
  }
};

export const useReloadAppHooks = () => {
  const { pathname } = useLocation()

  const { data, refetch, isLoading } = useQuery({
    queryKey: [`getVersion`],
    queryFn: getVersion,
  });

  useEffect(() => {
    refetch();
  }, [pathname])

  const dataSource = useMemo(() => {
    return data?.commit ?? ''
  }, [data])

  const setHashAndReload = () => {
    localStorage.setItem('vr-current-commit', dataSource);
    console.debug('旧版本触发刷新');
    window.location.reload();
  }

  useEffect(() => {
    if (!isLoading && dataSource) {
      const currentCommit = localStorage.getItem('vr-current-commit')

      if (currentCommit) {
        // diff
        if (currentCommit !== dataSource) {
          setHashAndReload()
        }
      }
      else {
        setHashAndReload()
      }
    }
  }, [dataSource, isLoading, localStorage.getItem('vr-current-commit')])

}