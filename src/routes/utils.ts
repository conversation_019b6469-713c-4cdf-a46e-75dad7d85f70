// 路由工具函数
// 从 App.tsx 提取的工具函数

import { RoleKeyPath } from "config";
import type { ChildrenMap } from "./types";

export const generateLoader = (
  list: ChildrenMap[],
  order: number
): ChildrenMap[] => {
  const addLoader: ChildrenMap[] = [];
  list.forEach((index: any, k) => {
    addLoader.push({
      ...index,
      loader: () => {
        const temporary = [];
        (index?.meta ?? []).forEach((o: any) => {
          temporary.push({
            // 这里的code是拿来关联path的
            code: `${index[RoleKeyPath]}${o?.action}`,
            name: o?.name,
            description: "按钮权限",
            requestType: 1,
            sort: Number.parseInt(order) + Number.parseInt(k),
            clientPath: `${index?.[RoleKeyPath]}_${o?.action}`,
            menuId: index[RoleKeyPath],
          });
        });
        return temporary;
      },
    });
  });
  return addLoader;
};
