// 应急管理相关路由
// 包含 EmergencyManagementMap

import {
  DutyInformationPage,
  EmergencyPlanPage,
  EmergencySupplyPage,
  EmergencyTeamPage,
} from "pages/emergencyManagement";
import { EmergencyRoutes } from "utils/routerConstants";
import type { ChildrenMap } from "./types";
import { generateLoader } from "./utils";

export const EmergencyManagementMap: ChildrenMap[] = generateLoader(
  [
    {
      path: EmergencyRoutes.ON_DUTY, // "/on_duty",
      element: <DutyInformationPage />,
      name: "值班信息",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "edit",
          name: "编辑",
        },
      ],
    },
    {
      path: EmergencyRoutes.EMERGENCY_TEAM, // "/emergency_team",
      element: <EmergencyTeamPage />,
      name: "应急队伍",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "edit",
          name: "编辑",
        },
      ],
    },
    {
      path: EmergencyRoutes.EMERGENCY_SUPPLY, // "/emergency_supply",
      element: <EmergencySupplyPage />,
      name: "应急物资",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "edit",
          name: "编辑",
        },
      ],
    },
    {
      path: EmergencyRoutes.EMERGENCY_PLAN, // "/emergency_plan",
      element: <EmergencyPlanPage />,
      name: "应急预案",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "edit",
          name: "编辑",
        },
      ],
    },
  ],
  600
);
