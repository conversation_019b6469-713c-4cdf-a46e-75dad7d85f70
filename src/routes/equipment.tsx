import {
  EquipmentManagementDetectionPage,
  EquipmentManagementEquipmentCategoryPage,
  EquipmentManagementEquipmentPage,
  EquipmentManagementMaintenancePage,
  EquipmentManagementRepairPage,
  EquipmentManagementResumePage,
  EquipmentManagementScrapPage,
  EquipmentManagementStopPage,
} from "pages/equipmentManagement";
import { EquipmentSensorPage } from "pages/equipmentManagement/equipmentSensorPage";
import { EquipmentManagementRoutes } from "utils/routerConstants";
import type { ChildrenMap } from "./types";
import { generateLoader } from "./utils";

// 设备档案模块
export const EquipmentArchiveMap: ChildrenMap[] = generateLoader(
  [
    {
      path: EquipmentManagementRoutes.EQUIPMENT_CATEGORY,
      element: <EquipmentManagementEquipmentCategoryPage />,
      name: "设备类型",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
      ],
    },
    {
      path: EquipmentManagementRoutes.EQUIPMENT,
      element: <EquipmentManagementEquipmentPage />,
      name: "设备信息",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "detail",
          name: "查看详情",
        },
        {
          action: "maintenance",
          name: "保养",
        },
        {
          action: "detection",
          name: "检测",
        },
        {
          action: "repair",
          name: "维修",
        },
        {
          action: "stop",
          name: "停用",
        },
        {
          action: "resume",
          name: "恢复",
        },
        {
          action: "scrap",
          name: "报废",
        },
        {
          action: "import",
          name: "导入",
        },
        {
          action: "export",
          name: "导出",
        },
      ],
    },
  ],
  600
);

// 设备管理模块
export const EquipmentManagementMap: ChildrenMap[] = generateLoader(
  [
    {
      path: EquipmentManagementRoutes.EQUIPMENT_MAINTAIN,
      element: <EquipmentManagementMaintenancePage />,
      name: "设备保养",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "export",
          name: "导出",
        },
      ],
    },
    {
      path: EquipmentManagementRoutes.EQUIPMENT_DETECTION,
      element: <EquipmentManagementDetectionPage />,
      name: "设备检测",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "export",
          name: "导出",
        },
      ],
    },
    //设备维修
    {
      path: EquipmentManagementRoutes.EQUIPMENT_REPAIR,
      element: <EquipmentManagementRepairPage />,
      name: "设备维修",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "export",
          name: "导出",
        },
      ],
    },
    //设备停用
    {
      path: EquipmentManagementRoutes.EQUIPMENT_STOP,
      element: <EquipmentManagementStopPage />,
      name: "设备停用",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "export",
          name: "导出",
        },
      ],
    },
    //设备恢复
    {
      path: EquipmentManagementRoutes.EQUIPMENT_RESUME,
      element: <EquipmentManagementResumePage />,
      name: "设备恢复",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "export",
          name: "导出",
        },
      ],
    },
    //设备报废
    {
      path: EquipmentManagementRoutes.EQUIPMENT_SCRAP,
      element: <EquipmentManagementScrapPage />,
      name: "设备报废",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "export",
          name: "导出",
        },
      ],
    },
    // 设备监测
    {
      path: EquipmentManagementRoutes.EQUIPMENT_SENSOR,
      element: <EquipmentSensorPage />,
      name: "设备监测",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "export",
          name: "导出",
        },
      ],
    },
  ],
  600
);
