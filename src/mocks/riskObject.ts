import Mock from "mockjs";
import type { MockMethod } from "vite-plugin-mock";

export default [
  {
    url: "/v1/double_guard/risk_object_list",
    method: "get",
    response: ({ query }) =>
      // return {
      //   a: 1
      // }
      Mock.mock({
        code: 0,
        message: "",
        data: {
          "totalCount|20-100": 1,
          currentPageCount: 2,
          currentPageNumber: 3,
          itemsPerPage: 10,
          "riskObjects|10": [
            {
              // 属性 id 是一个自增数，起始值为 1，每次增 1
              "id|+1": 1,
              "isMajorHazard|1": [0, 1],
              "name|1": ["风险对象1", "风险对象2", "风险未知"],
              area: {
                "id|number": 1,
                "name|1": ["车间2", "车间21", "车间1", "车间6"],
              },
              riskLiableDepartment: {
                id: 87,
                "name|1": ["车间2", "车间21", "车间1", "车间6"],
              },
              riskLiablePerson: {
                id: 30,
                name: Mock.Random.cname(),
              },
              "riskLevel|1-5": 3,
              riskReviewDate: Mock.mock('@datetime("yyyy-MM-dd HH:mm:ss")'),
            },
          ],
        },
      }),
  },
] as MockMethod[];
